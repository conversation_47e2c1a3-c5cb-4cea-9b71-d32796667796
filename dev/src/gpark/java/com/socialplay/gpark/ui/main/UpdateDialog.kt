package com.socialplay.gpark.ui.main

import android.content.DialogInterface
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.asFlow
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.UpdateAppInteractor
import com.socialplay.gpark.data.model.UpdateInfo
import com.socialplay.gpark.databinding.DialogUpdateBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.account.setting.SettingFragment
import com.socialplay.gpark.ui.account.setting.SettingViewModel
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.ui.dialog.DialogScene
import com.socialplay.gpark.ui.dialog.IDialogManager
import com.socialplay.gpark.ui.gamedetail.unify.PgcGameDetailFragment
import com.socialplay.gpark.util.MarketUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.sp
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber
import kotlin.system.exitProcess


/**
 * create by: bin on 2022/3/22
 */
class UpdateDialog : BaseDialogFragment(), IDialogManager {
    var onDismissCallback: ((Boolean) -> Unit)? = null
    var showData: UpdateInfo? = null
    var categoryId: Int = -1
    var gameId: String? = null

    override val binding by viewBinding(DialogUpdateBinding::inflate)

    companion object {
        var inShowedUpdateDialog = false

        fun showFromSetting(
            fragment: SettingFragment,
            viewModel: SettingViewModel
        ) {
            // GPark无逻辑
        }
    }

    override fun show(manager: FragmentManager, tag: String?) {
        if (inShowedUpdateDialog) {
            return
        }
        inShowedUpdateDialog = true
        super.show(manager, tag)
    }

    override fun init() {
        val info = showData ?: return dismissAllowingStateLoss()
        val force = info.isForce
        Analytics.track(EventConstants.UPDATE_DIALOG_SHOW) {
            put("reason", if (info.isEngineUpdate) "ds" else "normal")
            put("id", info.id.toString())
            if (categoryId != -1) {
                put("show_categoryid", categoryId)
            }
            if (gameId != null) {
                put("gameid", gameId.toString())
            }
            put("update_type", if (force) "force" else "weak")
        }
        binding.tvUpdate.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.UPDATE_BUTTON_CLICK) {
                put("reason", if (info.isEngineUpdate) "ds" else "normal")
                put("id", info.id.toString())
                if (categoryId != -1) {
                    put("show_categoryid", categoryId)
                }
                if (gameId != null) {
                    put("gameid", gameId.toString())
                }
                put("update_type", if (force) "force" else "weak")
            }
//            onDismissCallback?.invoke(false)
//            onDismissCallback = null
            //这里开始执行一个应用市场跳转逻辑
            MarketUtil.goto(this)
        }
        binding.tvExit.text = getString(if (force) R.string.exit_ else R.string.not_now)
        binding.tvExit.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.UPDATE_EXIT_CLICK) {
                put("reason", if (info.isEngineUpdate) "ds" else "normal")
                put("id", info.id.toString())
                if (categoryId != -1) {
                    put("show_categoryid", categoryId)
                }
                if (gameId != null) {
                    put("gameid", gameId.toString())
                }
                put("update_type", if (force) "force" else "weak")
            }
            if (!force) {
                onDismissCallback?.invoke(true)
                onDismissCallback = null
                dismissAllowingStateLoss()
                return@setOnAntiViolenceClickListener
            }
            onDismissCallback?.invoke(false)
            onDismissCallback = null
            exitProcess(0)
        }
        binding.tvTitle.text = info.title
        createLineLayout(info)
    }

    override fun onDismiss(dialog: DialogInterface) {
        onDismissCallback?.invoke(true)
        onDismissCallback = null
        super.onDismiss(dialog)
    }

    override fun isBackPressedDismiss(): Boolean {
        return false
    }

    override fun gravity(): Int {
        return Gravity.BOTTOM
    }

    override fun isCancelable(): Boolean {
        return false
    }

    override fun isClickOutsideDismiss(): Boolean {
        return false
    }

    override fun loadFirstData() {
    }

    private fun createLineLayout(info: UpdateInfo) {
        val width = ScreenUtil.getScreenWidth(requireContext())
        val height = calculateTextViewHeight(info.description, width, 14.sp * 1.0f)
        if (height > 161.dp) {
            binding.scrollviewUpdate.layoutParams.height = 161.dp
        }
        val descList = info.descList
        for (i in descList.indices) {
            val view = LayoutInflater.from(requireContext()).inflate(R.layout.update_item_text, binding.parentLinearLayoutId, false)
            val tvDes = view.findViewById<TextView>(R.id.tv_des)
            tvDes.text = descList[i]
            binding.parentLinearLayoutId.addView(view)
        }
    }

    private fun calculateTextViewHeight(text: String, width: Int, textSize: Float): Int {
        val paint = TextPaint()
        paint.textSize = textSize
        paint.typeface = Typeface.DEFAULT
        paint.color = Color.BLACK // Set the color if needed
        val layout = StaticLayout.Builder.obtain(text, 0, text.length, paint, width)
            .setAlignment(Layout.Alignment.ALIGN_NORMAL)
            .setLineSpacing(0.0f, 1.0f)
            .build()
        return layout.height
    }

    private val customScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    var updateAppInteractor: UpdateAppInteractor = GlobalContext.get().get()

    /**
     * 更新初始化
     * @param finishCallback 初始化完成回调，不卡其他弹框初始化
     * 海外GPark的更新流程：
     * 1. 请求Google获取更新信息，如果有，往下走，没有就中断
     * 2. 请求后端获取更新信息，如果有，往下走，没有就中断
     * 3. 判断更新弹框类型，看是引擎更新还是App更新
     */
    override suspend fun initData(finishCallback: (Boolean) -> Unit) {
        finishCallback.invoke(true)
        Timber.d("UpdateInfoCheck initData: init fetch update info $customScope")
        // 这里要单独起一个协程，不然会卡住(而且需要用全局的协程)
        customScope.launch(Dispatchers.IO) {
            updateAppInteractor.updateInfoLiveData.asFlow().collect {
                Timber.d("UpdateInfoCheck initData: init fetch update info success $it")
                it?.let {
                    showData = it
                }
                customScope.cancel()
            }
        }
    }

    override fun needShow(fragment: Fragment, scene: DialogScene, args: Bundle?, needShowCallback: (Boolean) -> Unit) {
        if (scene != DialogScene.MAIN_PAGE && scene != DialogScene.GAME_DETAIL_PAGE) {
            needShowCallback.invoke(false)
            return
        }
        Timber.d("UpdateInfoCheck needShow: start fetching update info $showData")
        if (showData != null) {
            needShowCallback.invoke(true)
            return
        }
        // 延时1秒再检查一下
        fragment.lifecycleScope.launch {
            delay(1200)
            Timber.d("UpdateInfoCheck needShow: delay 1000ms,fetch update info $showData")
            if (showData != null) {
                needShowCallback.invoke(true)
            } else {
                needShowCallback.invoke(false)
            }
        }
    }

    override fun showByDialogManager(fragment: Fragment, onDismissCallback: (Boolean) -> Unit) {
        if (!fragment.isAdded || fragment.isDetached) {
            return
        }
        if (fragment is PgcGameDetailFragment) {
            categoryId = fragment.args.resIdBean.getCategoryID()
            gameId = fragment.args.gId
        }
        this.onDismissCallback = onDismissCallback
        show(fragment.childFragmentManager, "update")
    }

    override fun exeDismiss() {
        this.dismissAllowingStateLoss()
    }
}