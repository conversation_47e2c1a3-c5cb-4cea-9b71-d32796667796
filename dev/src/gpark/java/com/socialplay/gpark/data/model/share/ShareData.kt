package com.socialplay.gpark.data.model.share

import android.content.Context
import android.os.Parcelable
import com.meta.lib.mwbiz.MWBizBridge
import com.google.gson.annotations.SerializedName
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.function.pandora.PandoraToggleWrapper
import com.socialplay.gpark.function.share.ShareWrapper
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.util.InstallUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.ifNullOrBlank
import com.socialplay.gpark.util.isHttp
import kotlinx.coroutines.flow.single
import kotlinx.parcelize.Parcelize
import org.koin.core.context.GlobalContext

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/01
 *     desc   :
 * </pre>
 */
data class SharePlatform(
    val platform: String,
    val iconRes: Int,
    val titleRes: Int,
    val code: Int,
) {

    companion object {
        // Post、Friends、Tiktok、Snapchat、Youtube、Image、Save、Copy Link、More
        const val PLATFORM_FRIEND = "friend"
        const val PLATFORM_LINK = "link"
        const val PLATFORM_SYSTEM = "system"
        const val PLATFORM_SAVE = "save"
        const val PLATFORM_LONG_IMAGE = "longImage"
        const val PLATFORM_POST = "post"

        const val PLATFORM_YOUTUBE = "youtube"
        const val PLATFORM_TIKTOK = "tiktok"
        const val PLATFORM_SNAPCHAT = "snapchat"

        fun platformTrackParam(platform: String?): String {
            return when (platform) {
                PLATFORM_FRIEND -> "friends"
                PLATFORM_LINK -> "url"
                PLATFORM_SYSTEM -> "more"
                PLATFORM_SAVE -> "save"
                PLATFORM_POST -> "post"
                PLATFORM_YOUTUBE -> "youtube"
                PLATFORM_TIKTOK -> "tiktok"
                PLATFORM_SNAPCHAT -> "snapchat"
                PLATFORM_LONG_IMAGE -> "image"
                else -> ""
            }
        }

        fun friend() = SharePlatform(
            PLATFORM_FRIEND,
            R.drawable.ic_share_friend,
            R.string.share_friend,
            1,
        )

        fun link() = SharePlatform(
            PLATFORM_LINK,
            R.drawable.ic_share_link,
            R.string.global_share_copy_link,
            2,
        )

        fun system() = SharePlatform(
            PLATFORM_SYSTEM,
            R.drawable.ic_share_system,
            R.string.global_share_system_share,
            3,
        )

        fun save() = SharePlatform(
            PLATFORM_SAVE,
            R.drawable.ic_share_download,
            R.string.share_save_album,
            4,
        )

        fun longImage() = SharePlatform(
            PLATFORM_LONG_IMAGE,
            R.drawable.ic_share_long_image,
            R.string.image,
            5,
        )

        fun youtube() = SharePlatform(
            PLATFORM_YOUTUBE,
            R.drawable.ic_share_youtube,
            R.string.share_you_tu_be,
            6,
        )

        fun tiktok() = SharePlatform(
            PLATFORM_TIKTOK,
            R.drawable.ic_share_tiktok,
            R.string.share_tiktok,
            7,
        )

        fun snapchat() = SharePlatform(
            PLATFORM_SNAPCHAT,
            R.drawable.ic_share_snapchat,
            R.string.mgs_share_way_snapchat,
            8,
        )

        fun post() = SharePlatform(
            PLATFORM_POST,
            R.drawable.ic_share_post,
            R.string.share_post,
            9,
        )
    }
}

data class ShareConfig(
    val youtube: Youtube? = null,
    val tiktok: Tiktok? = null,
    val snapchat: ShareData.Sticker? = null,
    @SerializedName(value = "system", alternate = ["systemShare"])
    val system: System? = null
) {

    companion object {
        fun default() = ShareConfig(
            youtube = Youtube(),
            tiktok = Tiktok(tags = listOf("GPark")),
            snapchat = ShareData.Sticker(),
            system = System()
        )

        fun replaceContent(
            input: String?,
            gameName: String? = null,
            authorName: String? = null,
            userName: String? = null,
            pvCount: Long? = null,
        ): String? {
            return if (input.isNullOrBlank()) {
                input
            } else {
                Regex("\\{([^{}]+?)\\}").replace(input) {
                    when (it.groupValues[1]) {
                        "gameName" -> gameName.orEmpty()
                        "authorName" -> authorName.orEmpty()
                        "userName" -> userName.orEmpty()
                        "pvCount" -> (pvCount ?: 0).toString()
                        else -> null
                    }.ifNullOrBlank { " " }
                }
            }
        }
    }

    fun titleByPlatform(platform: String): String? {
        return when (platform) {
            else -> {
                null
            }
        }
    }

    fun descByPlatform(platform: String): String? {
        return when (platform) {
            else -> {
                null
            }
        }
    }

    fun tagsByPlatform(platform: String): List<String>? {
        return when (platform) {
            else -> {
                null
            }
        }
    }

    data class Youtube(
        val title: String? = null
    )

    data class Tiktok(
        val tags: List<String>? = null
    )

    data class System(
        val title: String? = null,
        @SerializedName(value = "desc", alternate = ["content"])
        val desc: String? = null
    )
}

data class ShareData(
    val requestId: String,
    val platform: String,
    val mode: String,
    val images: List<String>? = null,
    val videos: List<String>? = null,
    val sticker: Sticker? = null,
    val extraTitle: String? = null,
    val extraContent: String? = null,
    val extraTags: List<String>? = null,
    val gameId: String? = null,
    val from: Int = 0
) {

    companion object {
        fun youtubeSingleVideo(
            requestId: String,
            videoPath: String,
            config: ShareConfig? = null,
            title: String? = null,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_YOUTUBE,
            ShareHelper.MODE_SINGLE_VIDEO,
            videos = listOf(videoPath),
            extraTitle = title ?: config?.youtube?.title,
            gameId = gameId
        )

        fun tiktokSingleImage(
            requestId: String,
            imagePath: String,
            config: ShareConfig? = null,
            tags: List<String>? = null,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_TIKTOK,
            ShareHelper.MODE_SINGLE_IMAGE,
            images = listOf(imagePath),
            extraTags = if (PandoraToggleWrapper.tiktokShareWithTags) {
                tags ?: config?.tiktok?.tags
            } else {
                null
            },
            gameId = gameId
        )

        fun tiktokMultiImages(
            requestId: String,
            imagePaths: List<String>,
            config: ShareConfig? = null,
            tags: List<String>? = null,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_TIKTOK,
            ShareHelper.MODE_MULTI_IMAGES,
            images = imagePaths,
            extraTags = if (PandoraToggleWrapper.tiktokShareWithTags) {
                tags ?: config?.tiktok?.tags
            } else {
                null
            },
            gameId = gameId
        )

        fun tiktokSingleVideo(
            requestId: String,
            videoPath: String,
            config: ShareConfig? = null,
            tags: List<String>? = null,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_TIKTOK,
            ShareHelper.MODE_SINGLE_VIDEO,
            videos = listOf(videoPath),
            extraTags = if (PandoraToggleWrapper.tiktokShareWithTags) {
                tags ?: config?.tiktok?.tags
            } else {
                null
            },
            gameId = gameId
        )

        fun snapchatSingleImage(
            requestId: String,
            imagePath: String,
            config: ShareConfig? = null,
            sticker: Sticker? = null,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_SNAPCHAT,
            ShareHelper.MODE_SINGLE_IMAGE,
            images = listOf(imagePath),
            sticker = if (PandoraToggleWrapper.snapchatShareWithSticker) {
                sticker ?: config?.snapchat
            } else {
                null
            },
            gameId = gameId
        )

        fun snapchatMultiImages(
            requestId: String,
            imagePaths: List<String>,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_SNAPCHAT,
            ShareHelper.MODE_MULTI_IMAGES,
            images = imagePaths,
            gameId = gameId
        )

        fun snapchatSingleVideo(
            requestId: String,
            videoPath: String,
            config: ShareConfig? = null,
            sticker: Sticker? = null,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_SNAPCHAT,
            ShareHelper.MODE_SINGLE_VIDEO,
            videos = listOf(videoPath),
            sticker = if (PandoraToggleWrapper.snapchatShareWithSticker) {
                sticker ?: config?.snapchat
            } else {
                null
            },
            gameId = gameId
        )
    }

    fun notifySuccess() {
        ShareResult.notifySuccess(requestId, platform)
    }

    fun notifyCancel() {
        ShareResult.notifyCancel(requestId, platform)
    }

    fun notifyFail(code: Int, msg: String?, sdkCode1: Int = 0, sdkCode2: Int = 0) {
        ShareResult.notifyFail(
            requestId,
            platform,
            code,
            msg,
            sdkCode1 = sdkCode1,
            sdkCode2 = sdkCode2
        )
    }

//    fun toDirectShareData4Test() = DirectShareData(
//        requestId,
//        platform,
//        mode,
//        images = images,
//        videos = videos,
//        sticker = sticker,
//        trackParams = null,
//        needCustomInfo = null,
//        customInfoTId = null,
//        customInfoTKey = null,
//        customInfoTitle = extraTitle,
//        customInfoDesc = extraContent,
//        customInfoTags = extraTags
//
//    )

    @Parcelize
    data class Sticker(
        val icon: String? = null,
        val desc: String? = null,
        val url: String? = null,
        val width: Float = 100.0f,
        val height: Float = 100.0f,
        val x: Float = 0.5f,
        val y: Float = 0.5f,
        val degree: Int = 0
    ) : Parcelable {

        fun validate(
            context: Context
        ): Sticker {
            if (icon?.isHttp() == true) {
                val newFile = ShareWrapper.cacheSticker(
                    context,
                    icon,
                    width = context.dp(width),
                    height = context.dp(height)
                )
                return if (newFile != null) {
                    copy(icon = newFile.absolutePath)
                } else {
                    copy(icon = null)
                }
            }
            return this@Sticker
        }
    }
}

data class DirectSharePlatforms(
    val singleImage: ArrayList<String> = arrayListOf(
        SharePlatform.PLATFORM_TIKTOK,
        SharePlatform.PLATFORM_SNAPCHAT
    ),
    val multiImages: ArrayList<String> = arrayListOf(
        SharePlatform.PLATFORM_TIKTOK,
        SharePlatform.PLATFORM_SNAPCHAT
    ),
    val singleVideo: ArrayList<String> = arrayListOf(
        SharePlatform.PLATFORM_TIKTOK,
        SharePlatform.PLATFORM_SNAPCHAT,
        SharePlatform.PLATFORM_YOUTUBE
    )
)

data class DirectShareData(
    val requestId: String,
    val platform: String,
    val mode: String,
    val images: List<String>? = null,
    val videos: List<String>? = null,
    val sticker: ShareData.Sticker? = null,
    val trackParams: Map<String, String>? = null,
    val needCustomInfo: Boolean? = null,
    val customInfoTId: Int? = null,
    val customInfoTKey: String? = null,
    val customInfoTitle: String? = null,
    val customInfoDesc: String? = null,
    val customInfoTags: List<String>? = null
) {

    fun toShareData(
        extraTitle: String? = customInfoTitle,
        extraContent: String? = customInfoDesc,
        extraTags: List<String>? = customInfoTags,
        sticker: ShareData.Sticker? = this.sticker
    ) = ShareData(
        requestId,
        platform,
        mode,
        images,
        videos,
        sticker,
        extraTitle = extraTitle,
        extraContent = extraContent,
        extraTags = extraTags
    )

    suspend fun toShareDataWithConfig(context: Context): ShareData {
        if (needCustomInfo == false) {
            return toShareData()
        }
        return when (platform) {
            SharePlatform.PLATFORM_YOUTUBE -> {
                if (customInfoTitle != null) {
                    toShareData()
                } else {
                    toShareDataWithConfigHelper {
                        toShareData(extraTitle = it.youtube?.title)
                    }
                }
            }

            SharePlatform.PLATFORM_TIKTOK -> {
                if (PandoraToggleWrapper.tiktokShareWithTags) {
                    if (customInfoTags != null) {
                        toShareData()
                    } else {
                        toShareDataWithConfigHelper {
                            toShareData(extraTags = it.tiktok?.tags)
                        }
                    }
                } else {
                    toShareData()
                }
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                if (mode == ShareHelper.MODE_MULTI_IMAGES) {
                    toShareData()
                } else if (PandoraToggleWrapper.snapchatShareWithSticker) {
                    if (sticker != null) {
                        toShareData(sticker = sticker.validate(context))
                    } else {
                        toShareDataWithConfigHelper {
                            if (it.snapchat != null) {
                                toShareData(sticker = it.snapchat.validate(context))
                            } else {
                                toShareData()
                            }
                        }
                    }
                } else {
                    toShareData()
                }
            }

            else -> {
                toShareData()
            }
        }
    }

    private suspend fun toShareDataWithConfigHelper(block: ShareConfigCallback): ShareData {
        val shareConfig = GlobalContext.get()
            .get<TTaiInteractor>()
            .getShareConfig(
                customInfoTId ?: TTaiKV.ID_KEY_ID_GAME_SHARE_CONFIG,
                customInfoTKey ?: MWBizBridge.currentGameId()
            ).single()
        return block(shareConfig)
    }

    fun validate(context: Context): Int {
        when (platform) {
            SharePlatform.PLATFORM_YOUTUBE -> {
                if (!InstallUtil.isInstalledYoutube(context)) {
                    return ShareHelper.CODE_NOT_INSTALLED
                }
                when (mode) {
                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    else -> {
                        return ShareHelper.CODE_UNSUPPORTED_MODE
                    }
                }
            }

            SharePlatform.PLATFORM_TIKTOK -> {
                if (!InstallUtil.isInstalledTiktokFamilies(context)) {
                    return ShareHelper.CODE_NOT_INSTALLED
                }
                when (mode) {
                    ShareHelper.MODE_SINGLE_IMAGE -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_MULTI_IMAGES -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    else -> {
                        return ShareHelper.CODE_UNSUPPORTED_MODE
                    }
                }
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                if (!InstallUtil.isInstalledSnapchat(context)) {
                    return ShareHelper.CODE_NOT_INSTALLED
                }
                when (mode) {
                    ShareHelper.MODE_SINGLE_IMAGE -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_MULTI_IMAGES -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    else -> {
                        return ShareHelper.CODE_UNSUPPORTED_MODE
                    }
                }
            }

            else -> {
                return ShareHelper.CODE_UNSUPPORTED_PLATFORM
            }
        }
        return ShareHelper.CODE_OK
    }
}