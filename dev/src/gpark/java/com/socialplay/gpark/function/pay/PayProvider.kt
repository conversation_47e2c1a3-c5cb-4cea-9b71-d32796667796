package com.socialplay.gpark.function.pay

import android.app.Application
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.kv.MetaKV

object PayProvider {

    const val ENABLE_RECHARGE = true
    const val ENABLE_DAILY = true

    /**
     * 是否启用会员
     */
    const val ENABLE_PREMIUM = true
    /**
     * 会员页 url 地址
     */
    val PREMIUM_PAGE_URL = BuildConfig.GPARK_PLUS_STATUS

    fun getPayInteractor(
        metaRepository: IMetaRepository,
        metaApp: Application,
        accountInteractor: AccountInteractor,
        metaKV: MetaKV
    ): IPayInteractor {
        return GparkPayInteractor(metaRepository, metaApp, accountInteractor, metaKV)
    }
}