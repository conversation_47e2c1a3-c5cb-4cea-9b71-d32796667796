package com.socialplay.gpark.util

import android.content.Context
import com.socialplay.gpark.util.DateUtil.formatWholeDate
import java.util.Calendar

object DateUtilWrapper {

    const val NEED_KEEP_DOUBLE_DIGITS = true

    fun getLabel4Year(context: Context): String = ""

    fun getLabel4Day(context: Context): String = ""

    fun getDefaultBirthCalendar(): Calendar {
        return Calendar.getInstance().apply {
            set(Calendar.YEAR, 1900)
            set(Calendar.MONTH, 0)
            set(Calendar.DATE, 1)
        }
    }

    fun getEarliestBirthCalendar(): Calendar {
        return Calendar.getInstance().apply {
            set(Calendar.YEAR, 1900)
            set(Calendar.MONTH, 0)
            set(Calendar.DATE, 1)
        }
    }

    fun getCreateFormatDate(context: Context, timeStamp: Long): String {
        return timeStamp.formatWholeDate()
    }

    fun getUpdateFormatDate(context: Context, timeStamp: Long): String {
        return timeStamp.formatWholeDate()
    }

    fun getFormatCommentDate(context: Context, timeStamp: Long): String {
        return timeStamp.formatWholeDate()
    }

    fun getTransactionDate(context: Context, timeStamp: Long): String {
        return timeStamp.formatWholeDate()
    }
}