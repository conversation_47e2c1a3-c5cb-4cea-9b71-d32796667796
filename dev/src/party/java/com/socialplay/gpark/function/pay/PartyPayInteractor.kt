package com.socialplay.gpark.function.pay

import android.app.Activity
import android.app.Application
import android.content.Context
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.bin.cpbus.CpEventBus
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.LifecycleInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.PartyEventConstants
import com.socialplay.gpark.data.model.Product
import com.socialplay.gpark.data.model.Recharge
import com.socialplay.gpark.data.model.event.PayResult
import com.socialplay.gpark.data.model.member.MemberRequest
import com.socialplay.gpark.data.model.member.MemberType
import com.socialplay.gpark.data.model.pay.CoinType
import com.socialplay.gpark.data.model.pay.CommonPayParams
import com.socialplay.gpark.data.model.pay.PayParamsParty
import com.socialplay.gpark.data.model.pay.PayResultEntity
import com.socialplay.gpark.data.model.pay.SubsData
import com.socialplay.gpark.data.model.pay.TakeOrderInfo
import com.socialplay.gpark.data.model.pay.TakeOrderInfoRuntime
import com.socialplay.gpark.data.model.pay.UserBalance
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.function.MetaRouterWrapper
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pay.way.GamePayResultEvent
import com.socialplay.gpark.function.startup.core.ProcessType
import com.socialplay.gpark.function.startup.core.StartupContext
import com.socialplay.gpark.ui.gamepay.PayChannel
import com.socialplay.gpark.ui.gamepay.PayInfo
import com.socialplay.gpark.ui.gamepay.PayType
import com.socialplay.gpark.ui.gamepay.platform.IThirdPayCallBack
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.ui.recharge.RechargeAmountConfirmDialogFragment
import com.socialplay.gpark.ui.recharge.RechargeViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import org.greenrobot.eventbus.Subscribe
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.util.UUID
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.abs

class PartyPayInteractor(
    val metaRepository: com.socialplay.gpark.data.IMetaRepositoryWrapper,
    val metaApp: Application,
    val accountInteractor: AccountInteractor,
    val metaKV: MetaKV
) : IPayInteractor {
    private val scopeIO = CoroutineScope(Dispatchers.IO)
    private var mIPayCallback: IThirdPayCallBack<GamePayResultEvent>? = null

    // 此字段为gpark才有的, party直接返回空数据
    override val products: LiveData<MutableList<Product>> by lazy { MutableLiveData() }

    // 此字段为gpark才有的, party直接返回空数据
    override val subsProducts: LiveData<MutableList<Product>> by lazy { MutableLiveData() }
    private val _userBalance = MutableLiveData<UserBalance?>()

    override val userBalance: LiveData<UserBalance?> = _userBalance

    private var isPayIng: AtomicBoolean = AtomicBoolean(false)
    private var lastUser: MetaUserInfo? = null
    private var rechargeViewModel: RechargeViewModel? = null
    private val accountChangeListener = Observer<MetaUserInfo?> {
        if (lastUser?.uuid != it?.uuid) {
            scopeIO.launch {
                getBalance(null)
            }
            lastUser = it
        }
    }

    override val supportPointsPayment: Boolean = false

    override fun preProcessRechargePayInfo(payInfo: PayInfo) {
        payInfo.rawData["sceneCode"] = 103
        payInfo.rawData["payType"] = 1
    }

    override suspend fun init(processType: ProcessType) {
        withContext(Dispatchers.Main) {
            accountInteractor.accountLiveData.observeForever(accountChangeListener)
        }
        getBalance(null)
        CpEventBus.register(this)
    }

    override fun checkConnect() {
        // 无需检查连接
    }

    override fun getRechargeViewData(
        balance: Long,
        rechargePayInfo: PayInfo,
        callback: (DataResult<RechargeViewData>) -> Unit
    ) {
        val request = MemberRequest(arrayListOf(MemberType.VIP_PLUS))
        combine(
            // 获取所有可供充值的挡位
            metaRepository.getRecharges(),
            // 获取会员信息
            metaRepository.getUserMemberInfoList(request)
        ) { rechargeResult, userInfoResult ->
            if (!rechargeResult.succeeded || rechargeResult.data == null) {
                callback(DataResult.Error(rechargeResult.code ?: 0, rechargeResult.message ?: ""))
                return@combine
            }
            if (!userInfoResult.succeeded || userInfoResult.data == null) {
                callback(DataResult.Error(userInfoResult.code ?: 0, userInfoResult.message ?: ""))
                return@combine
            }
            val needAdditional = (rechargePayInfo.payAmount - balance).coerceAtLeast(0L)
            val recharges = rechargeResult.data!!.sortedBy { recharge ->
                recharge.price ?: 0
            }
            // 选中合适的充值额度
            var targetRecharge = recharges.firstOrNull { recharge ->
                // 派对暂时没有会员赠送
                (recharge.baseCoinNum ?: 0L) + (recharge.awardCoinNum ?: 0L) >= needAdditional
            }
            if (targetRecharge == null && recharges.isNotEmpty()) {
                targetRecharge = recharges.last()
            }
            if (targetRecharge == null) {
                val context = GlobalContext.get().get<Context>()
                callback(
                    DataResult.Error(
                        0,
                        context.getString(R.string.iap_recharge_toast_get_products_from_server_failed)
                    )
                )
                return@combine
            }
            // 是否是会员
            val isMember = userInfoResult.data!!.firstOrNull()?.isActive() ?: false

            // 服务器下发的价格是以分为单位的价格, 需要除以100
            val price = (targetRecharge.price ?: 0) / 100f
            val formattedPrice = when {
                price.rem(1) == 0f -> "%.0f".format(price)
                else -> "%.2f".format(price)
            }

            callback(
                DataResult.Success(
                    RechargeViewData(
                        ourProductId = targetRecharge.id.toString(),
                        productId = targetRecharge.goodsId.toString(),
                        // Party 暂时没有会员买币加成
                        isMember = false,
                        needAdditional = needAdditional,
                        baseCoinNum = targetRecharge.baseCoinNum ?: 0L,
                        awardCoinNum = targetRecharge.awardCoinNum ?: 0L,
                        // Party 暂时没有会员赠送
                        memberRewardCoinNum = 0L,
                        // 此字段目前用于埋点, 派对始终为 CNY
                        currencyCode = "CNY",
                        price = targetRecharge.price ?: 0,
                        currencyCodePrice = "¥$formattedPrice",
                        showPrivacy = true,
                        // 充值协议地址
                        rechargePrivacyUrl = BuildConfig.RECHARGE_PROTOCOL_URL,
                        extra = targetRecharge
                    )
                )
            )
        }.stateIn(scopeIO, SharingStarted.Eagerly, false)
    }

    override fun loadRechargeProducts(callback: (DataResult<List<RechargeProductCompat>>) -> Unit) {
        scopeIO.launch {
            metaRepository.getRecharges().collect {
                if (it.succeeded && it.data != null) {
                    val rechargeProducts =
                        it.data!!.filter { product -> product.show ?: true }.map { targetProduct ->
                            // 服务器下发的价格是以分为单位的价格, 需要除以100
                            val price = (targetProduct.price ?: 0) / 100f
                            val formattedPrice = when {
                                price.rem(1) == 0f -> "%.0f".format(price)
                                else -> "%.2f".format(price)
                            }
                            RechargeProductCompat(
                                ourProductId = targetProduct.id.toString(),
                                productId = targetProduct.goodsId.toString(),
                                parentProductId = "",
                                baseCoinNum = targetProduct.baseCoinNum ?: 0L,
                                awardCoinNum = targetProduct.awardCoinNum ?: 0L,
                                // Party 暂时没有会员赠送
                                memberRewardCoinNum = 0,
                                // 此字段目前用于埋点, 派对始终为 CNY
                                currencyCode = "CNY",
                                price = targetProduct.price ?: 0,
                                currencyCodePrice = "¥$formattedPrice",
                                extra = targetProduct,
                            )
                        }
                    callback(DataResult.Success(rechargeProducts))
                } else {
                    callback(DataResult.Error(it.code ?: 0, it.message ?: ""))
                }
            }
        }
    }

    override fun needRealName(): Boolean {
        val realName = accountInteractor.accountLiveData.value?.bindIdCard == true
        return !realName
    }

    override fun gotoRealNamePage() {
        val activity = LifecycleInteractor.activityRef?.get() ?: return
        val fragment = if (activity is MainActivity) {
            val navHostFragment = activity.findNavHostFragment() ?: return
            if (!navHostFragment.isAdded) return
            navHostFragment.childFragmentManager.fragments.firstOrNull { it.isResumed && it.isVisible }
                ?: navHostFragment.childFragmentManager.fragments.firstOrNull { it.isVisible }
                ?: navHostFragment
        } else {
            null
        }
        MetaRouterWrapper.RealName.openRealName(fragment, activity)
    }

    /**
     * 代币兑换接口
     * TODO
     *    GPark: 会调用两个接口: metaApi.mwCreateOrder(hashMap) 和 metaApi.createPrepaidOrder
     *    Party: 仅仅只会调用: metaApi.mwPay
     */
    override suspend fun mwPay(
        hashMap: Map<String, Any?>,
        callback: suspend (DataResult<*>) -> Unit
    ) {
        metaRepository.partyMWPay(hashMap).collect {
            callback.invoke(it)
        }
    }

    fun setGamePayResultCallBack(iPayCallback: IThirdPayCallBack<GamePayResultEvent>?) {
        this.mIPayCallback = iPayCallback
    }

    @Subscribe
    fun onEvent(payResultEntity: GamePayResultEvent) {
        Timber.i(
            "收到支付结果: %s , 进程: %s extra: %s",
            payResultEntity.payStatus,
            StartupContext.get().processType,
            payResultEntity.payOrderId
        )
        mIPayCallback?.onPayResult(payResultEntity)
    }

    /**
     * 充值下单
     * 先请求充值下单接口, 再进行真正的充值
     */
    fun placeOrder(
        payChannel: Int,
        takeOrderInfo: TakeOrderInfo,
        callBack: (DataResult<PayResultEntity?>) -> Unit
    ) = scopeIO.launch {
        metaRepository.privilegePlaceOrder(payChannel, takeOrderInfo).collect {
            withContext(Dispatchers.Main) {
                callBack(it)
            }
        }
    }

    override fun cancelPay() {
        if (isPayIng.get()) {
            rechargeViewModel?.cancelPay()
            isPayIng.set(false)
        }
    }

    override fun getPayChannel(payType: PayType): Int? {
        return when (payType) {
            // 派对币支付
            PayType.COINS -> {
                68
            }
            // 积分支付
            PayType.POINTS -> {
                73
            }
        }
    }

    override fun startPay(
        activity: Activity,
        commonPayParams: CommonPayParams,
        extra: Any?,
        pageSource: String?,
        payResultCallback: ((payResult: PayResult) -> Unit)?
    ) {
        val fragmentActivity = (activity as? FragmentActivity) ?: return
        val fragmentManager = if (fragmentActivity is MainActivity) {
            val navHostFragment = fragmentActivity.findNavHostFragment() ?: return
            if (!navHostFragment.isAdded) return
            val f =
                navHostFragment.childFragmentManager.fragments.firstOrNull { it.isResumed && it.isVisible }
                    ?: navHostFragment.childFragmentManager.fragments.firstOrNull { it.isVisible }
                    ?: navHostFragment
            f.childFragmentManager
        } else {
            fragmentActivity.supportFragmentManager
        }

        if (isPayIng.get()) {
            Timber.e("pay is ing")
            onPayResult(
                payResultCallback,
                false,
                IPayInteractor.FAIL_OTHER,
                "isPayIng",
                commonPayParams,
                null
            )
            return
        }
        val recharge = extra as Recharge?
        if (recharge == null) {
            Timber.e("extra is null")
            onPayResult(
                payResultCallback,
                false,
                IPayInteractor.FAIL_OTHER,
                "extra is null",
                commonPayParams,
                null
            )
            return
        }
        isPayIng.set(true)

        Analytics.track(
            PartyEventConstants.EVENT_PARTY_CLIENT_PAY_SHOW,
            "gamecode" to (commonPayParams.gameId ?: "null"),
            "productid" to (commonPayParams.productId ?: ""),
            "price" to commonPayParams.price.toString(),
            "source" to (pageSource ?: "")
        )
        RechargeAmountConfirmDialogFragment.show(
            fragmentManager,
            commonPayParams.price,
        ) { isConfirm, type, viewModel ->
            rechargeViewModel = viewModel
            if (isConfirm) {
                viewModel.startPay(
                    recharge,
                    type,
                    commonPayParams.gameId,
                    pageSource = pageSource,
                ) { isSuccess, params, failedMessage ->
                    if (isSuccess) {
                        // 充值成功后, 刷新账户余额
                        scopeIO.launch {
                            getBalance(null) { userBalance ->
                                onPayResult(
                                    payResultCallback,
                                    true,
                                    200,
                                    "",
                                    commonPayParams,
                                    params,
                                    coinsBalance = userBalance?.leCoinNum
                                )
                            }
                        }
                    } else {
                        onPayResult(
                            payResultCallback,
                            false,
                            IPayInteractor.FAIL_OTHER,
                            failedMessage,
                            commonPayParams,
                            params
                        )
                    }
                }
            } else {
                // 用户关闭支付弹窗
                onPayResult(
                    payResultCallback,
                    false,
                    IPayInteractor.FAIL_CANCEL,
                    null,
                    commonPayParams,
                    null
                )
            }
        }
    }

    /**
     * 关闭订单
     * 三方支付失败时调用
     */
    fun closeOrder(orderId: String) = metaRepository.cancelOrder(orderId)

    /**
     * TODO 支付成功后, gpark 会将充值成功后的结果提交给服务器(充值成功后调用)
     *     但 party 没有此逻辑
     */
    fun payResultSubmit(payParams: PayParamsParty) {
    }

    /**
     * 支付结果轮训
     */
    suspend fun rechargeLoop(orderId: String): DataResult<Boolean?> {
        return metaRepository.rechargingLoopParty(orderId)
    }

    private fun onPayResult(
        payResultCallback: ((payResult: PayResult) -> Unit)?,
        isSuccess: Boolean,
        failedCode: Int,
        failedMsg: String? = null,
        commonPayParams: CommonPayParams,
        payParamsParty: PayParamsParty?,
        coinsBalance: Long? = null,
    ) {
        rechargeViewModel = null
        isPayIng.set(false)

        val map = hashMapOf<String, Any>()
        map["source"] = commonPayParams.source ?: ""
        map["page_type"] = commonPayParams.pageType.orEmpty()
        map["gamecode"] = commonPayParams.gameId ?: ""
        map["productid"] = commonPayParams.productId ?: ""
        // google 读出来的商品名称
        // map["product"] = ""
        map["currencycode"] = commonPayParams.currencyCode
        map["price"] = commonPayParams.price
        map["orderid"] = payParamsParty?.orderCode ?: ""
        map["scenecode"] = commonPayParams.sceneCode
        map["result"] = if (isSuccess) "1" else "2"
        map["base_gcoin"] = _userBalance.value?.leCoinBaseNum ?: 0
        map["bonus_gcoin"] = _userBalance.value?.leCoinAwardNum ?: 0
        if (!isSuccess) {
            map["failedcode"] = failedCode.toString()
            map["failedmsg"] = failedMsg ?: ""
        }
        // 应用商店维度订单id
        // map["service_orderid"] = ""
        // google 和 ios 才有此字段
        // map["purchasetype"] = payParams.tripartiteInfo?.purchaseType ?: ""
        Analytics.track(EventConstants.EVENT_PAY_RESULT, map)
        //需要通知支付结果
        payResultCallback?.invoke(
            PayResult(
                isSuccess,
                failedMsg ?: "",
                commonPayParams.scene,
                failedCode,
                commonPayParams.price.toLong(),
                coinsBalance = coinsBalance
            )
        )
        // TODO 支付完成后, 应该有埋点, 但原有的埋点是专属于 gpark 的, 分别是
        //      event_gpark_subscription_pay_result
        //      event_gpark_pay_result
        //      event_iap_google_pay_dialog_show
        //      event_iap_google_pay_all_time
    }

    override suspend fun loadProducts(
        scene: String,
        list: List<SubsData>?,
        callBack: (Pair<String?, MutableList<Product>>) -> Unit?
    ) {
        // party 没有此逻辑, 此方法仅会被 h5 调用
    }

    override suspend fun getBalance(
        messageId: Int?,
        callback: (suspend (userBalance: UserBalance?) -> Unit)?) {
        getArkNum(messageId ?: 0) { userBalance ->
            // party 渠道没有点数字段
            callback?.invoke(userBalance)
        }
    }

    suspend fun getArkNum(messageId: Int = 0, callback: (suspend (userBalance: UserBalance?) -> Unit)? = null) {
        metaRepository.getBalance(CoinType.partyCoin).collect { result ->
            val leCoinNum = result.data?.leCoinNum ?: 0L
            Timber.i("getBalance: $leCoinNum")
            if (result.succeeded && result.data != null) {
                // 这里的事件, 抛出到当前类的 fun onEvent(balance: UserBalance) 方法来接收
                CpEventBus.post(result.data!!)
            }
            callback?.invoke(result.data)
            arkCallback(leCoinNum, messageId)
        }
    }

    @Subscribe
    fun onEvent(balance: UserBalance) {
        _userBalance.postValue(balance)
    }

    private fun arkCallback(ark: Long, messageId: Int) {
        UGCProtocolSender.sendProtocol(
            ProtocolSendConstant.PROTOCOL_PAY_ARK_CALLBACK,
            messageId,
            HashMap<String, Any>().apply {
                this["ark"] = ark
            }
        )
    }

    private suspend fun repeatGetResult(orderId: String): Boolean {
        var result: Boolean? = false
        var retryTimes = 0
        //重试间隔
        val retryIntervals =
            listOf<Long>(0L, 1500L, 1500L, 2000L, 2000L, 3000L, 3000L, 5000L, 5000L, 5000L)
        val countTimes = retryIntervals.size
        while (retryTimes < countTimes) {
            delay(retryIntervals[retryTimes])
            Timber.i("round ${retryTimes + 1}, interval ${retryIntervals[retryTimes] / 1000.0f}s")
            result = withTimeoutOrNull(1000) {
                val result = rechargeLoop(orderId)
                if (result.succeeded) {
                    result.data == true
                } else {
                    false
                }
            }
            if (result != true) {
                retryTimes += 1
                Timber.i("polling result： ${result}, one more time")
            } else {
                Timber.i("polling end:$result")
                break
            }
        }
        return result == true
    }

    override fun coinsPay(payInfo: PayInfo, callback: (DataResult<String>) -> Unit) {
        val orderInfo = TakeOrderInfo()
        //商品原价
        orderInfo.amount = payInfo.payAmount.toInt()
        orderInfo.productCode = payInfo.productCode
        orderInfo.productName = payInfo.productName
        orderInfo.count = payInfo.productCount
        //实际支付价格
        orderInfo.payAmount = payInfo.payAmount.toInt()
        orderInfo.nonce = abs(UUID.randomUUID().leastSignificantBits).toString()
        //商品原价
        orderInfo.productPrice = payInfo.productPrice.toInt()
        orderInfo.sceneCode = (payInfo.sceneCode ?: 0).toString()
        orderInfo.runtime = TakeOrderInfoRuntime(gameId = payInfo.gameId)

        placeOrder(PayChannel.PARTY_COIN.value, orderInfo) { payResult ->
            if (payResult.succeeded && payResult.data != null) {
                val orderId = payResult.data!!.orderCode
                scopeIO.launch {
                    if (orderId.isNullOrEmpty()) {
                        // 购买成功
                        callback.invoke(DataResult.Success(orderId ?: ""))
                    } else {
                        val payResult = repeatGetResult(orderId)
                        if (payResult) {
                            callback(
                                DataResult.Success(orderId)
                            )
                        } else {
                            callback(
                                DataResult.Error(0, "")
                            )
                        }
                    }
                }
            } else {
                callback.invoke(
                    DataResult.Error(
                        payResult.code ?: 0, payResult.message ?: "", payResult.exception
                    )
                )
            }
        }
    }
}