package com.socialplay.gpark.function.share.callback

import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.SystemClock
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.data.model.share.ShareResult
import com.socialplay.gpark.data.model.share.ThirdShareParams
import com.socialplay.gpark.databinding.ActivityQqCallbackBinding
import com.socialplay.gpark.function.record.ScreenRecordAnalytics
import com.socialplay.gpark.function.share.DouYinShareUtil
import com.socialplay.gpark.function.share.ShareWrapper
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.function.share.platform.SystemShare
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.util.InstallUtil
import com.socialplay.gpark.util.extension.registerHermes
import com.socialplay.gpark.util.extension.resumeGameById
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.unregisterHermes
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import java.io.File

class DouYinShareCallbackActivity : BaseActivity() {

    private var needFinish: Boolean = false

    private var receiver: BroadcastReceiver? = null

    private var mFinishJob: Job? = null
    private var mPauseTs: Long = -1L

    private lateinit var args: ThirdShareParams

    override val binding by viewBinding(ActivityQqCallbackBinding::inflate)

    companion object {
        const val TO_FRIEND = "com.ss.android.ugc.aweme.share.ui.SystemShareNewActivity"

        const val ACTION_SHARE = "com.aweme.opensdk.action.stay.in.dy"
        const val ACTION_SHARE_IM = "com.aweme.opensdk.action.stay.in.dy.im"

        var reqId: String = ""
        var platform: String = ""
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val params = intent.getParcelableExtra<ThirdShareParams>(ShareWrapper.THIRD_PARAMS)
        if (params == null) {
            finish()
            return
        }
        args = params
        reqId = args.reqId
        platform = args.platform

        registerHermes()
        runCatching {
            val receiver = StayInReceiver()
            val intentFilter = IntentFilter()
            intentFilter.addAction(ACTION_SHARE)
            intentFilter.addAction(ACTION_SHARE_IM)
            registerReceiver(receiver, intentFilter)
            this.receiver = receiver
        }

        when (args.mode) {
            ShareHelper.MODE_SINGLE_VIDEO -> {
                shareVideoV2()
            }

            ShareHelper.MODE_MULTI_IMAGES -> {
                shareImages()
            }

            ShareHelper.MODE_SINGLE_IMAGE -> {
                shareImage()
            }

            ShareHelper.MODE_SINGLE_WEB_CARD -> {
                shareWeb()
            }

            else -> {
                unsupported()
                return
            }
        }
    }

    private fun shareImages() {
        val paths = args.paths
        if (paths.isNullOrEmpty()) {
            invalidParams()
            return
        }
        if (args.platform == SharePlatform.PLATFORM_DOUYIN_FRIEND) {
            shareViaSystem(paths.map { File(it) }, SystemShare.TYPE_IMAGE)
        } else {
            val success = DouYinShareUtil.shareImagesOrVideosWithGrant(
                this,
                paths,
                hasVideo = false,
                hasImage = true,
                hashtags = args.tags,
                title = args.content,
                shortTitle = args.title
            )
            if (!success) {
                finish()
            }
        }
    }

    private fun shareImage() {
        val path = args.path
        if (path.isNullOrEmpty()) {
            invalidParams()
            return
        }
        if (args.platform == SharePlatform.PLATFORM_DOUYIN_FRIEND) {
            val success = DouYinShareUtil.shareImageMessage(this, path)
            if (!success) {
                finish()
                return
            }
        } else {
            val success = DouYinShareUtil.shareImagesOrVideosWithGrant(
                this,
                listOf(path),
                hasVideo = false,
                hasImage = true,
                hashtags = args.tags,
                title = args.content,
                shortTitle = args.title
            )
            if (!success) {
                finish()
                return
            }
        }
    }

    private fun trackShareClick(isSuccess: Boolean) {
        if (args.isFromRecordingEnd) {
            ScreenRecordAnalytics.eventClickShareRecordEndDialog(
                args.gameId.orEmpty(),
                "DouYin",
                isSuccess
            )
        } else {
            ScreenRecordAnalytics.eventClickShareMyRecord(
                args.gameId.orEmpty(),
                "DouYin",
                isSuccess
            )
        }
    }

    fun shareVideoV2() {
        if (args.platform == SharePlatform.PLATFORM_DOUYIN_FRIEND) {
            val file = args.path?.let { File(it) }
            if (file == null) {
                if (args.isFromRecording) {
                    trackShareClick(false)
                }
                invalidParams()
                return
            }
            shareViaSystem(listOf(file), SystemShare.TYPE_VIDEO)
            if (args.isFromRecording) {
                trackShareClick(true)
            }
        } else {
            val path = args.path
            if (path.isNullOrBlank()) {
                if (args.isFromRecording) {
                    trackShareClick(false)
                }
                invalidParams()
                return
            }
            val success = DouYinShareUtil.shareImagesOrVideosWithGrant(
                activity = this,
                paths = listOf(path),
                hasVideo = true,
                hasImage = false,
                hashtags = args.tags,
                title = args.content,
                shortTitle = args.title
            )
            if (args.isFromRecording) {
                trackShareClick(success)
            }
            if (!success) {
                finish()
            }
        }
    }

    private fun shareWeb() {
        val webCard = args.sticker
        if (webCard == null) {
            invalidParams()
            return
        }
        val result = DouYinShareUtil.shareWeb(
            this,
            webCard.url,
            webCard.title,
            webCard.desc,
            webCard.icon
        )
        if (!result) {
            finish()
        }
    }

    private fun shareViaSystem(files: List<File>, type: String) {
        val (isInstalled, packageName) = InstallUtil.isInstalledDouyinFamilies4ShareSdk(this)
        if (!isInstalled || packageName.isNullOrEmpty()) {
            toast(R.string.not_installed_douyin)
            ShareResult.notifyFail(
                args.reqId,
                args.platform,
                ShareHelper.CODE_NOT_INSTALLED,
                "分享失败，应用未安装"
            )
            finish()
            return
        }
        if (SystemShare.shareMediasViaSystem(
                this,
                files,
                type = type,
                componentName = ComponentName(packageName, TO_FRIEND)
            )
        ) {
            ShareResult.notifySuccess(args.reqId, args.platform)
        } else {
            ShareResult.notifyFail(args.reqId, args.platform, -1, getString(R.string.share_fail))
            finish()
        }
    }

    @Subscribe
    fun onEvent(event: ShareResult) {
        if (event.match(reqId, platform)) {
            unregisterHermes()
            finishOrMark()
        }
    }

    private fun finishOrMark() {
        if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
            finish()
        } else {
            needFinish = true
        }
    }

    private fun unsupported() {
        ShareResult.notifyFail(
            args.reqId,
            args.platform,
            ShareHelper.CODE_UNSUPPORTED_PLATFORM,
            null
        )
        finish()
    }

    private fun invalidParams() {
        ShareResult.notifyFail(
            args.reqId,
            args.platform,
            ShareHelper.CODE_INVALID_PARAMS,
            null
        )
        finish()
    }

    override fun onResume() {
        super.onResume()
        if (needFinish) {
            if (SystemClock.elapsedRealtime() - mPauseTs > 200) {
                finish()
            } else {
                mFinishJob?.cancel()
                mFinishJob = lifecycleScope.launch {
                    delay(1_000)
                    finishOrMark()
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        needFinish = true
        mPauseTs = SystemClock.elapsedRealtime()
        mFinishJob?.cancel()
        mFinishJob = null
    }

    override fun finish() {
        args.gameId?.let {
            resumeGameById(it)
        }
        super.finish()
    }

    override fun onDestroy() {
        unregisterHermes()
        receiver?.runCatching {
            unregisterReceiver(this)
            receiver = null
        }
        super.onDestroy()
    }

    inner class StayInReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                ACTION_SHARE -> {
                    ShareResult.notifySuccess(reqId, SharePlatform.PLATFORM_DOUYIN_PUBLISH)
                    needFinish = true
                }

                ACTION_SHARE_IM -> {
                    ShareResult.notifySuccess(reqId, SharePlatform.PLATFORM_DOUYIN_FRIEND)
                    needFinish = true
                }
            }
            finishOrMark()
        }
    }
}