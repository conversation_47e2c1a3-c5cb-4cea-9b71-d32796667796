package com.socialplay.gpark.function.pay.way

import android.os.Bundle
import androidx.annotation.CallSuper
import com.alipay.sdk.app.PayTask
import com.bin.cpbus.CpEventBus
import com.socialplay.gpark.data.model.pay.AgentPayType
import com.socialplay.gpark.databinding.ActivityPayAlipayBinding
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import timber.log.Timber

open class BaseStartAliPayActivity : BaseActivity() {
    override val binding by viewBinding(ActivityPayAlipayBinding::inflate)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val extras = intent.extras
        if (extras == null) {
            finish()
            return
        }
        val orderInfo = extras.getString(AliPay.INFO)
        val orderId = extras.getString(AliPay.ORDER_ID)
        GlobalScope.launch(Dispatchers.IO) {
            val alipay = PayTask(this@BaseStartAliPayActivity)
            val result = alipay.payV2(orderInfo, true)
            val data = result["resultStatus"]
            Timber.d("alipay_result %s", data)
            onHandlePayResult(orderId, data, extras)
            finish()
        }
    }

    @CallSuper
    protected open suspend fun onHandlePayResult(orderId: String?, data: String?, extras: Bundle) {
        if (data.equals(AliPay.ALIPAY_PAY_SUCCESS)) {
            CpEventBus.post(GamePayResultEvent(0, orderId, AgentPayType.PAY_TYPE_ALI))
        } else if (data == "4000") {
            CpEventBus.post(GamePayResultEvent(-1, orderId, AgentPayType.PAY_TYPE_ALI))
        } else {
            CpEventBus.post(GamePayResultEvent(-2, orderId, AgentPayType.PAY_TYPE_ALI))
        }
    }
}