package com.socialplay.gpark.function

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.LoginPageSource
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.data.model.editor.UgcDetailInfo
import com.socialplay.gpark.function.deeplink.DeeplinkAnalysisUtil
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.router.MetaRouter.Control
import com.socialplay.gpark.ui.base.RootNavHostFragmentActivity
import com.socialplay.gpark.ui.compliance.ProtocolWebActivity
import com.socialplay.gpark.ui.login.LoginByPhoneFragmentArgs
import com.socialplay.gpark.util.ExternalLinkJumpUtils
import org.koin.core.context.GlobalContext
import kotlin.NotImplementedError

class MetaRouterWrapper {
    object Account {
        fun signUp(fragment: Fragment, loginSource: String) {
            throw NotImplementedError("Party not support signUp")
        }

        fun connectedAccounts(fragment: Fragment, source: LoginPageSource, gameId: String?) {
            throw NotImplementedError("Party not support connectedAccounts")
        }

        fun loginByPhone(fragment: Fragment, loginSource: String, phoneNumber: String?, onlyLogin: Boolean, successToMain: Boolean) {
            Control.navigate(
                fragment, R.id.login_by_phone,
                LoginByPhoneFragmentArgs(
                    source = loginSource,
                    phoneNumber = phoneNumber,
                    onlyLogin = onlyLogin,
                    successToMain = successToMain
                ).toBundle()
            )
        }
    }

    object Main {
        fun convertUri(uri: Uri): Uri {
            return when (uri.path) {
                "/detail/game" -> {
                    val gameId = uri.getQueryParameter("key_game_id")
                    val map = buildMap {
                        gameId?.let {
                            put("gameId", it)
                        }
                        uri.getQueryParameter("category_id")?.let {
                            put("categoryId", it)
                        }
                        uri.getQueryParameter("comment_id")?.let {
                            put("targetCommentId", it)
                        }
                        uri.getQueryParameter("reply_id")?.let {
                            put("targetReplyId", it)
                        }
                    }
                    if (gameId != null && UgcDetailInfo.identifyUgcGameByUgId(gameId)) {
                        buildLocalJumpUri(MetaDeepLink.ACTION_UGC_GAME_DETAIL, map)
                    } else {
                        buildLocalJumpUri(MetaDeepLink.ACTION_GAME_DETAIL, map)
                    }
                }

                "/web/web" -> {
                    val map = buildMap {
                        uri.getQueryParameter("url")?.let {
                            put("url", it)
                        }
                        uri.getQueryParameter("____landscape")?.let {
                            put("____landscape", it)
                        }
                        uri.getQueryParameter("____isTranslucentTop")?.let {
                            put("____isTranslucentTop", it)
                        }
                        uri.getQueryParameter("____isNativeTitleShow")?.let {
                            put("____isNativeTitleShow", it)
                        }
                    }
                    buildLocalJumpUri(MetaDeepLink.ACTION_WEB, map)
                }

                "/brower" -> {
                    val map = buildMap {
                        uri.getQueryParameter("url")?.let {
                            put("url", it)
                        }
                        put(MetaDeepLink.PARAM_BROWSER, "1")
                    }
                    buildLocalJumpUri(MetaDeepLink.ACTION_WEB, map)
                }

                "/player/home" -> {
                    val map = buildMap {
                        uri.getQueryParameter("playerId")?.let {
                            put("uid", it)
                        }
                        uri.getQueryParameter("source")?.let {
                            put("from", it)
                        }
                    }
                    buildLocalJumpUri(MetaDeepLink.ACTION_USER_HOME, map)
                }

                "/avatar/editor" -> {
                    val map = buildMap {
                        uri.getQueryParameter("data")?.let {
                            put("data", it)
                        }
                        uri.getQueryParameter("category_id")?.let {
                            put("categoryId ", it)
                        }
                    }
                    buildLocalJumpUri(MetaDeepLink.ACTION_JUMP_AVATAR_EDITOR, map)
                }

                else -> uri
            }
        }

        fun buildLocalJumpUri(bundle: Bundle, action: String, dest: String): Uri {
            DeeplinkAnalysisUtil.buildUri(action, true)
            val encoded = com.socialplay.gpark.function.deeplink.BundleConverter.encode(bundle)
            val args = Uri.encode(encoded)
            val deepLink = Uri.parse(BuildConfig.SCHEME_URI + "://${BuildConfig.SCHEME_HOST}/?action=$action&dest=$dest&data=$args")
            return deepLink
        }

        fun buildLocalJumpUri(map: Map<String, Any?>, action: String, dest: String): Uri {
            DeeplinkAnalysisUtil.buildUri(action, true)

            val builder = Uri.Builder()
                .scheme(BuildConfig.SCHEME_URI)
                .authority(BuildConfig.SCHEME_HOST)
                .path("/")
                .appendQueryParameter("action", action)
                .appendQueryParameter("dest", dest)

            map.entries.forEach {
                if (it.value != null) {
                    builder.appendQueryParameter(it.key, it.value.toString())
                }
            }
            return builder.build()
        }

        /**
         * 需要翻墙的deeplink
         */
        fun buildJumpUri(bundle: Bundle, action: String, dest: String): Uri {
            DeeplinkAnalysisUtil.buildUri(action, false)
            val encoded = com.socialplay.gpark.function.deeplink.BundleConverter.encode(bundle)
            val args = Uri.encode(encoded)
            val deepLink = Uri.parse("https://${BuildConfig.SCHEME_HOST}/?action=$action&dest=$dest&data=$args")
//            return GlobalContext.get().get<IFirebaseSdkBridge>().createDynamicLink(deepLink)
            return deepLink
        }


        fun buildLocalJumpUri(action: String, data: Map<String, Any?>): Uri {
            DeeplinkAnalysisUtil.buildUri(action, true)

            val builder = Uri.Builder()
                .scheme(BuildConfig.SCHEME_URI)
                .authority(BuildConfig.SCHEME_HOST)
                .appendQueryParameter("action", action)

            data.forEach { (k, v) ->
                if (v != null) {
                    builder.appendQueryParameter(k, v.toString())
                }
            }

            return builder.build()
        }

        /**
         * 需要翻墙的deeplink
         */
        fun buildJumpUri(action: String, data: Map<String, Any?>): Uri {
            DeeplinkAnalysisUtil.buildUri(action, false)

            val builder = Uri.Builder()
                .scheme("https")
                .authority(BuildConfig.SCHEME_HOST)
                .appendQueryParameter("action", action)

            data.forEach { (k, v) ->
                if (v != null) {
                    builder.appendQueryParameter(k, v.toString())
                }
            }

//            return GlobalContext.get().get<IFirebaseSdkBridge>().createDynamicLink(builder.build())
            return builder.build()
        }


        /**
         * 需要翻墙的deeplink
         */
        fun buildJumpUri(json: String, action: String, dest: String): Uri {
            DeeplinkAnalysisUtil.buildUri(action, false)
            val args = Uri.encode(json)
            val deepLink = Uri.parse("https://${BuildConfig.SCHEME_HOST}/?action=$action&dest=$dest&data=$args")
//            return GlobalContext.get().get<IFirebaseSdkBridge>().createDynamicLink(deepLink)
            return deepLink
        }

        /**
         * 不需要翻墙的deeplink
         */
        fun buildLocalJumpUri(json: String, action: String, dest: String): Uri {
            DeeplinkAnalysisUtil.buildUri(action, true)
            val args = Uri.encode(json)
            val deepLink = Uri.parse(BuildConfig.SCHEME_URI + "://${BuildConfig.SCHEME_HOST}/?action=$action&dest=$dest&data=$args")
            return deepLink
        }
    }

    object Web {

        val replaceChar by lazy { GlobalContext.get().get<Context>().getString(R.string.jinghao) }

        fun convertWebUri(url: String): Uri? {
            return kotlin.runCatching {
                Uri.parse(url.replace("#", replaceChar))
            }.getOrNull()
        }

        fun goProtocolWeb(context: Context, url: String, title: String = "") {
            val intent = Intent(context, ProtocolWebActivity::class.java)
            intent.putExtra("url", url)
            intent.putExtra("title", title)
            if (context !is Activity) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        }

        fun goRechargeProtocolWeb(context: Context) {
            MetaRouter.Web.navigate(context = context, fragment = null, url = BuildConfig.RECHARGE_PROTOCOL_URL, showTitle = true)
        }
    }

    object RealName {
        fun openRealName(fragment: Fragment? = null, context: Context? = null) {
            if (fragment != null) {
                navigate(fragment, R.id.real_name_party)
            } else if (context != null) {
                RootNavHostFragmentActivity.start(
                    context,
                    R.id.real_name_party,
                )
            }
        }

        fun showRealName(fragment: Fragment? = null, context: Context? = null) {
            if (fragment != null) {
                navigate(fragment, R.id.real_name_show)
            } else if (context != null) {
                RootNavHostFragmentActivity.start(
                    context,
                    R.id.real_name_show,
                )
            }
        }

        private fun navigate(
            fragment: Fragment,
            graphDestId: Int,
            navData: Bundle? = null,
            navOptions: NavOptions? = null
        ) {
            navOptions?.shouldRestoreState()
            fragment.findNavController().navigate(graphDestId, navData, navOptions)
        }
    }

    object ExternalLink {
        fun jump(fragment: Fragment, profileLinkInfo: ProfileLinkInfo, source: String) {
            kotlin.runCatching {
                val uri = Uri.parse(profileLinkInfo.url)
                if (uri.scheme == BuildConfig.SCHEME_URI) {
                    MetaRouter.Scheme.jumpScheme(fragment, uri, source)
                } else {
                    ExternalLinkJumpUtils.jumpExternalLink(
                        fragment.requireContext(),
                        profileLinkInfo
                    )
                }
            }
        }
    }
}