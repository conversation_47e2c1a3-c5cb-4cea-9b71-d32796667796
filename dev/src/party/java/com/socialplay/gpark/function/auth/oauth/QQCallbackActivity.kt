package com.socialplay.gpark.function.auth.oauth

import android.content.Intent
import android.os.Bundle
import androidx.lifecycle.lifecycleScope
import com.bin.cpbus.CpEventBus
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.share.QQShareFinishEvent
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.data.model.user.OAuthResponse
import com.socialplay.gpark.data.model.user.QQAuthInfo
import com.socialplay.gpark.databinding.ActivityQqCallbackBinding
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.property.viewBinding
import com.tencent.connect.common.Constants.*
import com.tencent.connect.share.QQShare
import com.tencent.connect.share.QzonePublish
import com.tencent.tauth.DefaultUiListener
import com.tencent.tauth.IUiListener
import com.tencent.tauth.Tencent
import com.tencent.tauth.UiError
import kotlinx.coroutines.launch
import org.json.JSONObject
import timber.log.Timber

class QQCallbackActivity : BaseActivity() {

    lateinit var tencent: Tencent

    //    private val oauthManager: OauthManager by inject()
    private val uiListener: IUiListener by lazy { getIUiListener() }
//    private val uniGameStatusInteractor by inject<UniGameStatusInteractor>()

    override val binding by viewBinding(ActivityQqCallbackBinding::inflate)
    var gameId: Long = -1
    private var isQQzone: Boolean = false
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val authority = BuildConfig.APPLICATION_ID + ".fileprovider"
        tencent = Tencent.createInstance(BuildConfig.QQ_APP_ID, this, authority)
        Tencent.setIsPermissionGranted(true)
        val qqBundle = intent.getParcelableExtra<Bundle>("qq")
        isQQzone =
            qqBundle?.getInt(QQShare.SHARE_TO_QQ_EXT_INT) == QQShare.SHARE_TO_QQ_FLAG_QZONE_AUTO_OPEN
        Timber.d("QQCallbackActivity qqBundle")
        if (qqBundle != null) {
            gameId = intent.getLongExtra("gameId", -1)
            val scene = intent.getStringExtra("scene")
            // TODO 注意一下，这里改了，派对用的是QQScene这个bean对象，这里直接用String了，使用SharePlatform.PLATFORM_QQ_FRIEND 和 SharePlatform.PLATFORM_QQ_ZONE进行区分
            if (isPublishVideoToQQZone(qqBundle)) {
                publishVideoToQQZone(qqBundle)
            } else if (isShareImageToQQZone(qqBundle)) {
                shareImageToQQZone(qqBundle)
            } else {
                shareToQQ(qqBundle, scene)
            }
        } else {
            loginByQQ()
        }
    }

    private fun shareToQQ(params: Bundle, scene: String?) {
        if (scene == SharePlatform.PLATFORM_QQ_ZONE) {
            tencent.shareToQzone(this, params, uiListener)
        } else {
            tencent.shareToQQ(this, params, uiListener)
        }
    }

    private fun isPublishVideoToQQZone(bundle: Bundle): Boolean {
        return bundle.getInt(QzonePublish.PUBLISH_TO_QZONE_KEY_TYPE) == QzonePublish.PUBLISH_TO_QZONE_TYPE_PUBLISHVIDEO
    }

    private fun publishVideoToQQZone(params: Bundle) {
        tencent.publishToQzone(this, params, uiListener)
    }

    private fun isShareImageToQQZone(bundle: Bundle): Boolean {
        return bundle.getInt(QzonePublish.PUBLISH_TO_QZONE_KEY_TYPE) == QzonePublish.PUBLISH_TO_QZONE_TYPE_PUBLISHMOOD
    }

    private fun shareImageToQQZone(params: Bundle) {
        tencent.publishToQzone(this, params, uiListener)
    }

    private fun loginByQQ() {
        tencent.login(this, uiListener, getLoginParams())
    }

    private fun backToGame() {
        if (gameId > 0) {
            lifecycleScope.launch {
                // TODO 这里不确定效果对不对，要改代码的
//                uniGameStatusInteractor.resumeOrLaunch(gameId, null, true)
            }
        }
    }

    private fun getIUiListener() = object : DefaultUiListener() {
        override fun onCancel() {
            Timber.d("QQShare onCancel")
            super.onCancel()
            CpEventBus.post(QQShareFinishEvent(2, "cancel", isQQzone))

            backToGame()
            finish()
            OAuthManager.callbacks.dispatchOnMainThread {
                this.onCancel(LoginWay.QQ)
            }
        }

        override fun onComplete(response: Any?) {
            Timber.d("QQShare onComplete")
            super.onComplete(response)
            CpEventBus.post(QQShareFinishEvent(1, "complete", isQQzone))
            backToGame()
            finish()
            OAuthManager.callbacks.dispatchOnMainThread {
                if (response == null) {
                    this.onFailed(LoginWay.QQ, getString(R.string.oauth_qq_failed_complete), -1)
                    return@dispatchOnMainThread
                }
                response as JSONObject
                if (response.length() == 0) {
                    this.onFailed(LoginWay.QQ, getString(R.string.oauth_qq_failed_complete), -1)
                    return@dispatchOnMainThread
                }
                val qqAuthInfo = GsonUtil.gsonSafeParse<QQAuthInfo>(response.toString())
                this.onSuccess(OAuthResponse(LoginWay.QQ, qqAuthInfo?.accessToken ?: "", qqAuthInfo?.openid ?: "").apply {
                    source = intent.getStringExtra("auth_source")
                    loginType = intent.getStringExtra("auth_type")
                })
            }
        }

        override fun onError(p0: UiError?) {
            super.onError(p0)
            Timber.d("QQShare onError")
            CpEventBus.post(
                QQShareFinishEvent(
                    0,
                    "error: ${p0?.errorCode}--${p0?.errorMessage}--${p0?.errorDetail}",
                    isQQzone
                )
            )

            backToGame()
            finish()
            OAuthManager.callbacks.dispatchOnMainThread {
                this.onFailed(LoginWay.QQ, "${p0?.errorCode}--${p0?.errorMessage}--${p0?.errorDetail}", 0)
            }
        }
    }


    private fun getLoginParams(): HashMap<String, Any> {
        val map = HashMap<String, Any>()
        map["key_scope"] = "all"
        map["key_qrcode"] = false
        map["key_enable_show_download_url"] = false
        return map
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == REQUEST_LOGIN || requestCode == REQUEST_QQ_SHARE || requestCode == REQUEST_QZONE_SHARE || requestCode == REQUEST_APPBAR) {
            Tencent.onActivityResultData(requestCode, resultCode, data, uiListener)
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, 0)
    }

}