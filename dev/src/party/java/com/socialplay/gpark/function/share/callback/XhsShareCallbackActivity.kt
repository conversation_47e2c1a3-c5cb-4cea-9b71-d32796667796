package com.socialplay.gpark.function.share.callback

import android.os.Bundle
import android.os.SystemClock
import android.webkit.URLUtil
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.share.ShareResult
import com.socialplay.gpark.data.model.share.ThirdShareParams
import com.socialplay.gpark.databinding.ActivityQqCallbackBinding
import com.socialplay.gpark.function.download.DownloadFileProvider
import com.socialplay.gpark.function.share.ShareWrapper
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.util.FileProviderUtil
import com.socialplay.gpark.util.ThumbnailUtil
import com.socialplay.gpark.util.extension.resumeGameById
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.property.viewBinding
import com.xingin.xhssharesdk.callback.XhsShareCallback
import com.xingin.xhssharesdk.core.XhsShareSdk
import com.xingin.xhssharesdk.model.sharedata.XhsImageInfo
import com.xingin.xhssharesdk.model.sharedata.XhsImageResourceBean
import com.xingin.xhssharesdk.model.sharedata.XhsNote
import com.xingin.xhssharesdk.model.sharedata.XhsVideoInfo
import com.xingin.xhssharesdk.model.sharedata.XhsVideoResourceBean
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/05/24
 *     desc   :
 * </pre>
 */
class XhsShareCallbackActivity : BaseActivity() {

    private var needFinish: Boolean = false

    private var result = false

    private var mFinishJob: Job? = null
    private var mPauseTs: Long = -1L

    private lateinit var args: ThirdShareParams

    override val binding by viewBinding(ActivityQqCallbackBinding::inflate)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val params = intent.getParcelableExtra<ThirdShareParams>(ShareWrapper.THIRD_PARAMS)
        if (params == null) {
            invalidParams()
            return
        }
        args = params

        XhsShareSdk.setShareCallback(object : XhsShareCallback {
            override fun onSuccess(sessionId: String?) {
                ShareResult.notifySuccess(args.reqId, args.platform)
                result = true
                finishOrMark()
            }

            override fun onError(
                sessionId: String,
                newErrorCode: Int,
                errorMessage: String,
                throwable: Throwable?
            ) {
            }

            override fun onError2(
                sessionId: String,
                newErrorCode: Int,
                oldErrorCode: Int,
                errorMessage: String,
                throwable: Throwable?
            ) {
                when (newErrorCode) {
                    -20400003 -> {
                        ShareResult.notifyCancel(args.reqId, args.platform)
                    }
                    else -> {
                        Timber.d("xhsShareFail: $newErrorCode $errorMessage")

                        when (newErrorCode) {
                            -20100002 -> {
                                toast(getString(R.string.xhs_version_is_too_low))
                            }

                            -20100007 -> {
                                toast(getString(R.string.xhs_share_incomplete))
                            }
                        }

                        ShareResult.notifyFail(
                            args.reqId,
                            args.platform, newErrorCode,
                            errorMessage,
                            sdkCode1 = oldErrorCode,
                            sdkCode2 = newErrorCode
                        )
                    }
                }
                result = true
                finishOrMark()
            }
        })

        when (args.mode) {
            ShareHelper.MODE_SINGLE_VIDEO -> {
                shareVideo()
            }

            ShareHelper.MODE_MULTI_IMAGES -> {
                shareImages()
            }

            ShareHelper.MODE_SINGLE_IMAGE -> {
                shareImage()
            }

            else -> {
                unsupported()
                return
            }
        }
    }

    private fun buildContent(): String? {
        val tagBuilder = StringBuilder()
        args.tags?.forEachIndexed { index, s ->
            if (index == 0) {
                tagBuilder.append("#")
            } else {
                tagBuilder.append(" #")
            }
            tagBuilder.append(s)
        }
        return if (tagBuilder.isBlank()) {
            args.content
        } else if (args.content.isNullOrBlank()) {
            tagBuilder.toString()
        } else {
            tagBuilder.insert(0, args.content).toString()
        }
    }

    private fun shareVideo() {
        val path = args.path
        if (path.isNullOrEmpty()) {
            invalidParams()
            return
        }
        val cover = generateVideoCover(path)
        val content = buildContent()
        val shareData = XhsNote()
            .setTitle(args.title)
            .setContent(content)
            .setVideoInfo(XhsVideoInfo(
                XhsVideoResourceBean.fromUrl(path),
                if (cover == null) null else XhsImageResourceBean(cover)
            ))
        XhsShareSdk.shareNote(this, shareData)
    }

    private fun shareImages(paths: List<String>? = args.paths) {
        if (paths.isNullOrEmpty()) {
            invalidParams()
            return
        }
        val content = buildContent()
        val shareData = XhsNote()
            .setTitle(args.title)
            .setContent(content)
            .setImageInfo(
                XhsImageInfo(paths.map {
                    if (URLUtil.isNetworkUrl(it)) {
                        XhsImageResourceBean.fromUrl(it)
                    } else {
                        XhsImageResourceBean(FileProviderUtil.getUriForFile(this, File(it)))
                    }
                })
            )
        XhsShareSdk.shareNote(this, shareData)
    }

    private fun shareImage() {
        val path = args.path
        if (path.isNullOrEmpty()) {
            invalidParams()
            return
        }
        shareImages(listOf(path))
    }

    private fun generateVideoCover(path: String): File? {
        return ThumbnailUtil.saveVideoCover(
            path,
            File(DownloadFileProvider.gameShareCacheDir, "xhs_video_cover.png")
        )
    }

    private fun finishOrMark() {
        if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
            finish()
        } else {
            needFinish = true
        }
    }

    private fun unsupported() {
        result = true
        ShareResult.notifyFail(
            args.reqId,
            args.platform,
            ShareHelper.CODE_UNSUPPORTED_PLATFORM,
            null
        )
        finish()
    }

    private fun invalidParams() {
        result = true
        ShareResult.notifyFail(
            args.reqId,
            args.platform,
            ShareHelper.CODE_INVALID_PARAMS,
            null
        )
        finish()
    }

    override fun onResume() {
        super.onResume()
        if (needFinish) {
            if (SystemClock.elapsedRealtime() - mPauseTs > 200) {
                finish()
            } else {
                mFinishJob?.cancel()
                mFinishJob = lifecycleScope.launch {
                    delay(1_000)
                    finishOrMark()
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        needFinish = true
        mPauseTs = SystemClock.elapsedRealtime()
        mFinishJob?.cancel()
        mFinishJob = null
    }

    override fun finish() {
        args.gameId?.let {
            resumeGameById(it)
        }
        super.finish()
    }

    override fun onDestroy() {
        XhsShareSdk.setShareCallback(null)
        if (!result) {
            ShareResult.notifyCancel(
                args.reqId,
                args.platform
            )
        }
        super.onDestroy()
    }
}