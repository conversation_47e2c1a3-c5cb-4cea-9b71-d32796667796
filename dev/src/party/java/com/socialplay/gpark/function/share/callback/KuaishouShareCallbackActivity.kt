package com.socialplay.gpark.function.share.callback

import android.content.ComponentName
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import com.kwai.auth.common.KwaiConstants
import com.kwai.opensdk.sdk.constants.KwaiPlatform
import com.kwai.opensdk.sdk.model.base.BaseReq
import com.kwai.opensdk.sdk.model.base.OpenSdkConfig
import com.kwai.opensdk.sdk.model.postshare.MultiPictureEdit
import com.kwai.opensdk.sdk.model.postshare.PostShareMediaInfo
import com.kwai.opensdk.sdk.model.postshare.SinglePicturePublish
import com.kwai.opensdk.sdk.model.postshare.SingleVideoEdit
import com.kwai.opensdk.sdk.model.socialshare.KwaiMediaMessage
import com.kwai.opensdk.sdk.model.socialshare.KwaiWebpageObject
import com.kwai.opensdk.sdk.model.socialshare.ShareMessage
import com.kwai.opensdk.sdk.openapi.IKwaiOpenAPI
import com.kwai.opensdk.sdk.openapi.KwaiOpenAPI
import com.meta.share.util.ThumbUtil
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.data.model.share.ShareResult
import com.socialplay.gpark.data.model.share.ThirdShareParams
import com.socialplay.gpark.databinding.ActivityQqCallbackBinding
import com.socialplay.gpark.function.record.ScreenRecordAnalytics
import com.socialplay.gpark.function.share.ShareWrapper
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.function.share.platform.SystemShare
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.util.InstallUtil
import com.socialplay.gpark.util.extension.resumeGameById
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File


/**
 * 快手视频分享
 */
class KuaishouShareCallbackActivity : BaseActivity() {

    private var needFinish: Boolean = false

    private val mKwaiOpenAPI: IKwaiOpenAPI by lazy {
        initKwaiOpenAPI()
    }

    private var mFinishJob: Job? = null
    private var mPauseTs: Long = -1L

    private lateinit var args: ThirdShareParams

    override val binding by viewBinding(ActivityQqCallbackBinding::inflate)

    companion object {
        const val TO_FRIEND = "com.yxcorp.gifshow.reminder.thirdpartyshare.ThirdPartyShareFriendsActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val params = intent.getParcelableExtra<ThirdShareParams>(ShareWrapper.THIRD_PARAMS)
        if (params == null) {
            invalidParams()
            return
        }
        args = params

        when (args.mode) {
            ShareHelper.MODE_SINGLE_VIDEO -> {
                shareVideoV2()
            }

            ShareHelper.MODE_MULTI_IMAGES -> {
                shareImages()
            }

            ShareHelper.MODE_SINGLE_IMAGE -> {
                shareImage()
            }

            ShareHelper.MODE_SINGLE_WEB_CARD -> {
                shareWeb()
            }

            else -> {
                unsupported()
                return
            }
        }

    }

    private fun trackShareClick(isSuccess: Boolean) {
        if (args.isFromRecordingEnd) {
            ScreenRecordAnalytics.eventClickShareRecordEndDialog(
                args.gameId.orEmpty(),
                "KuaiShou",
                isSuccess
            )
        } else {
            ScreenRecordAnalytics.eventClickShareMyRecord(
                args.gameId.orEmpty(),
                "KuaiShou",
                isSuccess
            )
        }
    }

    private fun initKwaiOpenAPI(): IKwaiOpenAPI = KwaiOpenAPI(this)
        .apply {
            val sdkConfig: OpenSdkConfig = OpenSdkConfig.Builder()
                .setGoToMargetAppNotInstall(false)
                .setGoToMargetAppVersionNotSupport(false)
                .setSetClearTaskFlag(false)
                .setSetNewTaskFlag(true)
                .setShowDefaultLoading(false)
                .build()
            setOpenSdkConfig(sdkConfig)

            // 业务请求回调结果监听
            addKwaiAPIEventListerer { resp ->
                resp.let {
                    when (resp.errorCode) {
                        1, 100 -> {
                            //KUAISHOU , errorCode= 100, errorMsg=  publish work success, cmd= CMD_MULTI_PICTURE_EDIT,   transaction= MultiPictureEdit, platform= kwai_app
                            // KUAISHOU , errorCode= 1, errorMsg=  share success, cmd= CMD_MULTI_PICTURE_EDIT,   transaction= MultiPictureEdit, platform= kwai_app
                            ShareResult.notifySuccess(
                                args.reqId,
                                args.platform
                            )
                            finishOrMark()
                        }

                        -1 -> {
                            ShareResult.notifyCancel(
                                args.reqId,
                                args.platform
                            )
                            finishOrMark()
                        }

                        else -> {
                            ShareResult.notifyFail(
                                args.reqId,
                                args.platform,
                                resp.errorCode,
                                resp.errorMsg.orEmpty(),
                                sdkCode1 = resp.errorCode
                            )
                            finishOrMark()
                        }
                    }
                    Timber.i(
                        "KUAISHOU , errorCode= ${resp.errorCode}, errorMsg= " +
                                " ${resp.errorMsg}, cmd= ${resp.command},  " +
                                " transaction= ${resp.transaction}, platform= ${resp.platform}"
                    )
                }
            }
        }

    private fun shareImage(path: String? = args.path) {
        if (path.isNullOrEmpty()) {
            invalidParams()
            return
        }
        if (args.platform == SharePlatform.PLATFORM_KUAISHOU_FRIEND) {
            shareViaSystem(listOf(File(path)), SystemShare.TYPE_IMAGE)
        } else {
            SinglePicturePublish.Req().apply {
                sessionId = mKwaiOpenAPI.openAPISessionId
                transaction = "SinglePicturePublish"
                platformArray = arrayOf(
                    KwaiConstants.Platform.KWAI_APP,
                    KwaiConstants.Platform.NEBULA_APP
                )
                mediaInfo = PostShareMediaInfo()
                val uris = fetchGrantedUriList(this@apply, listOf(path))
                mediaInfo.mMultiMediaAssets = uris
                //设置标签
                args.tags?.let {
                    val builder = StringBuilder()
                    for (item in it) {
                        builder.append("#").append(item)
                    }
                    mediaInfo.mTag = builder.toString()
                }
            }.let {
                mKwaiOpenAPI.sendReq(it, this)
            }
        }
    }

    /**
     * https://kuaishou.apifox.cn/doc-391264#5%E7%94%9F%E4%BA%A7%E6%96%B9%E5%90%91%E7%9B%B8%E5%85%B3%E7%9A%84%E4%B8%9A%E5%8A%A1%E8%AF%B7%E6%B1%82%E7%A4%BA%E4%BE%8B%E4%BB%A3%E7%A0%81
     */
    private fun shareImages() {
        val paths = args.paths
        if (paths.isNullOrEmpty()) {
            invalidParams()
            return
        }
        if (args.isFromRecording) {
            shareViaSystem(paths.map { File(it) }, SystemShare.TYPE_IMAGE)
        } else {
            if (paths.size == 1) {
                shareImage(paths[0])
                return
            }
            val req = MultiPictureEdit.Req().apply {
                sessionId = mKwaiOpenAPI.openAPISessionId
                transaction = "MultiPictureEdit"
                platformArray = arrayOf(
                    KwaiConstants.Platform.KWAI_APP,
                    KwaiConstants.Platform.NEBULA_APP
                )
                mediaInfo = PostShareMediaInfo()
                val uris = fetchGrantedUriList(this@apply, paths)
                mediaInfo.mMultiMediaAssets = ArrayList(uris)
                //设置标签
                args.tags?.let {
                    val builder = StringBuilder()
                    for (item in it) {
                        builder.append("#").append(item)
                    }
                    mediaInfo.mTag = builder.toString()
                }
            }
            if (mKwaiOpenAPI.isAppSupportEditMultiPicture(this@KuaishouShareCallbackActivity, req)) {
                mKwaiOpenAPI.sendReq(req, this@KuaishouShareCallbackActivity);
            } else {
                toast("快手版本不支持多图编辑，请更新快手")
                finish()
            }
        }
    }

    private fun shareVideoV2() {
        if (args.platform == SharePlatform.PLATFORM_KUAISHOU_FRIEND) {
            val file = args.path?.let { File(it) }
            if (file == null) {
                if (args.isFromRecording) {
                    trackShareClick(false)
                }
                invalidParams()
                return
            }
            shareViaSystem(listOf(file), SystemShare.TYPE_VIDEO)
            if (args.isFromRecording) {
                trackShareClick(true)
            }
        } else {
            val path = args.path
            if (path == null) {
                if (args.isFromRecording) {
                    trackShareClick(false)
                }
                invalidParams()
                return
            }
            val req: SingleVideoEdit.Req = SingleVideoEdit.Req().apply {
                sessionId = mKwaiOpenAPI.openAPISessionId
                transaction = "SingleVideoEdit"
                // 设置功能调起快手支持应用，KwaiPlatform.Platform.KWAI_APP（快手主站），KwaiPlatform.Platform.NEBULA_APP（快手极速版）
                // 按数组顺序检查应用安装和版本情况，从中选择满足条件的第一个应用调起，若不设置则默认启动快手主站应用
                platformArray = arrayOf(
                    KwaiConstants.Platform.KWAI_APP,
                    KwaiConstants.Platform.NEBULA_APP
                )

                mediaInfo = PostShareMediaInfo()

                //设置视频地址
                val videoFiles = fetchGrantedUriList(this, listOf(path))

                //设置分享的视频地址
                mediaInfo.mMultiMediaAssets = videoFiles

                //设置标签
                args.tags?.let {
                    val builder = StringBuilder()
                    for (item in it) {
                        builder.append("#").append(item)
                    }
                    mediaInfo.mTag = builder.toString()
                }
            }

            val success = mKwaiOpenAPI.sendReq(req, this)
            if (args.isFromRecording) {
                trackShareClick(success)
            }
            if (!success) {
                finish()
            }
        }
    }

    private fun fetchGrantedUriList(req: BaseReq, list: List<String>): ArrayList<String> {
        val paths = ArrayList<String>()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && mKwaiOpenAPI.isAppSupportUri(this@KuaishouShareCallbackActivity, req)) {
            // https://github.com/KwaiSocial/KwaiSocial-KwaiSDK-Demo-Android/blob/c4739e322a5d4cf7ffbaad76b0e85367a201db29/app/src/main/java/com/kwai/opensdk/demo/FileProviderUtil.java#L27
            list.forEach {
                val fileUri = ShareWrapper.kwaiGenerateFileUriPath(this@KuaishouShareCallbackActivity, File(it), req, mKwaiOpenAPI)
                if (fileUri != null) {
                    paths.add(fileUri.toString())
                }
            }
        }
        if (paths.isEmpty()) {
            paths.addAll(list)
        }
        return paths
    }

    fun shareWeb() {
        lifecycleScope.launch {
            val webCard = args.sticker
            if (webCard == null) {
                invalidParams()
                return@launch
            }
            val req = ShareMessage.Req()
            req.sessionId = mKwaiOpenAPI.openAPISessionId
            req.transaction = "sharemessage"
            req.platformArray = arrayOf(
                KwaiPlatform.Platform.KWAI_APP,
                KwaiPlatform.Platform.NEBULA_APP
            )

            req.message = KwaiMediaMessage()
            req.message.mediaObject = KwaiWebpageObject()
            (req.message.mediaObject as KwaiWebpageObject).webpageUrl = webCard.url
            req.message.title = webCard.title
            req.message.description = webCard.desc
            req.message.thumbData = withContext(Dispatchers.IO) {
                ThumbUtil.generateThumb(
                    this@KuaishouShareCallbackActivity,
                    webCard.icon,
                    defaultIconRes = R.drawable.ic_app_192
                )
            }

            mKwaiOpenAPI.sendReq(req, this@KuaishouShareCallbackActivity)
        }
    }

    private fun shareViaSystem(files: List<File>, type: String) {
        val (isInstalled, packageName) = InstallUtil.isInstalledKuaishouFamilies(this)
        if (!isInstalled || packageName.isNullOrEmpty()) {
            toast(R.string.not_installed_kuaishou)
            ShareResult.notifyFail(
                args.reqId,
                args.platform,
                ShareHelper.CODE_NOT_INSTALLED,
                "分享失败，应用未安装"
            )
            finish()
            return
        }
        if (SystemShare.shareMediasViaSystem(
                this,
                files,
                type = type,
                componentName = ComponentName(packageName, TO_FRIEND)
            )
        ) {
            ShareResult.notifySuccess(args.reqId, args.platform)
        } else {
            ShareResult.notifyFail(args.reqId, args.platform, -1, getString(R.string.share_fail))
            finish()
        }
    }

    private fun finishOrMark() {
        if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
            finish()
        } else {
            needFinish = true
        }
    }

    private fun unsupported() {
        ShareResult.notifyFail(
            args.reqId,
            args.platform,
            ShareHelper.CODE_UNSUPPORTED_PLATFORM,
            null
        )
        finish()
    }

    private fun invalidParams() {
        ShareResult.notifyFail(
            args.reqId,
            args.platform,
            ShareHelper.CODE_INVALID_PARAMS,
            null
        )
        finish()
    }

    override fun onResume() {
        super.onResume()
        if (needFinish) {
            if (SystemClock.elapsedRealtime() - mPauseTs > 200) {
                finish()
            } else {
                mFinishJob?.cancel()
                mFinishJob = lifecycleScope.launch {
                    delay(1_000)
                    finishOrMark()
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        needFinish = true
        mPauseTs = SystemClock.elapsedRealtime()
        mFinishJob?.cancel()
        mFinishJob = null
    }

    override fun finish() {
        args.gameId?.let {
            resumeGameById(it)
        }
        super.finish()
    }

    override fun onDestroy() {
        mKwaiOpenAPI.removeKwaiAPIEventListerer()
        super.onDestroy()
    }
}