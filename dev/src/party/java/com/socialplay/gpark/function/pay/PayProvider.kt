package com.socialplay.gpark.function.pay

import android.app.Application
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeApi
import org.json.JSONArray

object PayProvider {

    const val ENABLE_RECHARGE = true
    const val ENABLE_DAILY = false

    /**
     * 是否启用会员
     */
    const val ENABLE_PREMIUM = false
    /**
     * 会员页 url 地址
     */
    const val PREMIUM_PAGE_URL = ""

    /**
     * 用户充值协议
     */
    val BUY_COINS_PAGE_RECHARGE_URL = BuildConfig.RECHARGE_PROTOCOL_URL

    fun getPayInteractor(
        metaRepository: com.socialplay.gpark.data.IMetaRepositoryWrapper,
        metaApp: Application,
        accountInteractor: AccountInteractor, metaKV: MetaKV
    ): IPayInteractor {
        return PartyPayInteractor(metaRepository, metaApp, accountInteractor, metaKV)
    }

    fun getIapJsApi(jsBridgeApi: JsBridgeApi): Map<String, (suspend (param: JSONArray) -> String?)> {
        // TODO 注入 party 的 js 回调处理逻辑, 并将回调接口加入到 JsBridgeApi 中
        return emptyMap()
    }
}

//fun JsBridgeApi.startCommonPay(paramArray: JSONArray):String{
//    Timber.d("member_startCommonPay %s", paramArray)
//    val data = paramArray.optString(0)
//    val commonMemberParams = GsonUtil.gsonSafeParseCollection<CommonMemberParams>(data)
//    commonMemberParams?.let { helper.startPay(it) }
//    return createSuccessResult()
//}
//
///**
// * 开始下单
// */
//fun JsBridgeApi.startPay(paramArray: JSONArray): String {
//    Timber.d("ad_free_startPay %s", paramArray)
//    val payType: Int = paramArray.optInt(0)
//    val productCode: String = paramArray.optString(1)
//    val amount = paramArray.optInt(2)
//    val productName: String = paramArray.optString(3)
//    val memberType: String = paramArray.optString(4)
//    val happyCoin: String = paramArray.optString(5)
//    val sceneCode: String = paramArray.optString(6)
//    val cpExtra: String = paramArray.optString(7)
//    val memberParams = CommonMemberParams(
//        price = amount,
//        productCode = productCode,
//        pay_type = payType,
//        grade = productCode,
//        fun_id = memberType,
//        happyCoin = happyCoin,
//        productName = productName,
//        sceneCode = if (sceneCode.isEmpty()) AgentPayType.PAY_OWN_SENCECODE else sceneCode,
//        cpExtra = cpExtra
//    )
//    helper.startPay(memberParams)
//    return createSuccessResult()
//}
