package com.socialplay.gpark.ui.login

import android.os.Bundle
import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.navArgs
import com.airbnb.mvrx.fragmentViewModel
import com.meta.pandora.Pandora
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.databinding.FragmentLoginByPhoneBinding
import com.socialplay.gpark.databinding.LayoutLoginPhoneBinding
import com.socialplay.gpark.databinding.LayoutSmsCodeBinding
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.LoginCompleteRouter
import com.socialplay.gpark.ui.compliance.PartyProtocolDialogBottomFragment
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.view.InterceptClickEventLinkMovementMethod
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.RegexUtils
import com.socialplay.gpark.util.SoftKeyboardUtil
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.TextWatcherAdapter
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject

class LoginByPhoneFragment : BaseFragment<FragmentLoginByPhoneBinding>(R.layout.fragment_login_by_phone) {

    var isAgreeLogin = false

    private val viewModel: PhoneLoginViewModel by fragmentViewModel()

    //    private val viewModel: LoginViewModel by viewModel<LoginViewModel>()
    private var loginPhoneBinding: LayoutLoginPhoneBinding? = null
    private var smsCodeBinding: LayoutSmsCodeBinding? = null
    private var codeInputTextWatcher: CodeInputTextWatcher? = null
    private var phoneNumCache: String? = null

    val h5PageConfigInteractor by inject<H5PageConfigInteractor>()

    private val args by navArgs<LoginByPhoneFragmentArgs>()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentLoginByPhoneBinding? {
        return FragmentLoginByPhoneBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initData()
    }

    override fun onResume() {
        super.onResume()
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, backPressedCallback)
    }

    private fun trackClick() {
//        Pandora.send(EventConstants.EVENT_LOGIN_CLICK) {
//            put("type", type.value)
//        }
    }

    private fun initView() {
        binding.cbAgree.isChecked = isAgreeLogin
        binding.tvAgreement.text = LoginSpecialWrapper.getAgreementStringBuilder(this, h5PageConfigInteractor)
        binding.tvAgreement.movementMethod = InterceptClickEventLinkMovementMethod(binding.tvAgreement)

        binding.cbAgree.setOnCheckedChangeListener { buttonView, isChecked ->
            isAgreeLogin = isChecked
        }

        binding.vHotCheck.setOnClickListener {
            binding.cbAgree.isChecked = !binding.cbAgree.isChecked
        }
    }

    private fun checkCanLogin(cont: () -> Unit) {
        if (!NetUtil.isNetworkAvailable()) {
            toast(R.string.net_unavailable)
            return
        }
        if (binding.cbAgree.isChecked) {
            cont()
            return
        }
        PartyProtocolDialogBottomFragment.show(this.parentFragmentManager, nope = {
            Pandora.send(EventConstants.EVENT_CONTRACT_ACCESS) {
                put("state", 1)
            }
        }, agree = {
            isAgreeLogin = true
            binding.cbAgree.isChecked = true
            Pandora.send(EventConstants.EVENT_CONTRACT_ACCESS) {
                put("state", 0)
            }
            cont()
        })
    }


    private fun initData() {
        viewModel.onEach(LoginStateV::viewState, action = ::setViewSate)
        viewModel.onEach(LoginStateV::isLoading, action = ::setLoadingState)
        viewModel.onEach(LoginStateV::success, action = ::loginSuccess)
        viewModel.onEach(LoginStateV::loginFailedState, action = ::onLoginFailed)
        viewModel.registerToast(LoginStateV::toast)
    }

    private fun loginSuccess(userInfo: MetaUserInfo?) {
        if (userInfo != null) {
            // 登录完成
            goBack(true, userInfo)
        }
    }

    private fun goBack(loginComplete: Boolean, userInfo: MetaUserInfo? = null) {
        if (!loginComplete) {
            navigateUp()
            return
        }
        if (requireActivity() is LoginActivity) {
            //如果是LoginActivity就直接干掉
            requireActivity().finish()
        } else if (args.successToMain) {
            viewModel.setLoginViewState(LoginViewState.PhoneLogin(phoneNumCache ?: ""))
            viewModel.resetLoginVState()
            LoginCompleteRouter.router(requireActivity(), this, userInfo)
        } else {
            // 上一页
            navigateUp()
        }
    }

    private fun onLoginFailed(failed: String) {
        if (viewModel.oldState.viewState is LoginViewState.PhoneCode) {
            smsCodeBinding?.apply {
                failed?.let { tvCodeErrorTip.text = it }
                tvCodeErrorTip.visible(true)
            }
        }
    }

    private fun setLoadingState(isLogging: Boolean) {
        if (isLogging) {
            binding.loading.showLoading(true, "")
        } else {
            binding.loading.hide()
        }
    }

    private fun setViewSate(state: LoginViewState) {
        backPressedCallback.isEnabled = state != viewModel.firstLoginViewState
        when (state) {
            is LoginViewState.PhoneLogin -> {
                showPhoneLoginView(state.phoneNumber)
                smsCodeBinding?.root?.visible(false)
            }

            is LoginViewState.PhoneCode -> {
                showSmsCodeView(state.phoneNumber)
                loginPhoneBinding?.root?.visible(false)
            }
        }
    }

    private fun showPhoneLoginView(phoneNumber: String) {
        if (loginPhoneBinding == null)
            loginPhoneBinding = LayoutLoginPhoneBinding.bind(binding.vsLoginPhone.inflate()).apply {
                titleBar.setOnBackClickedListener {
                    goBack(false)
                }
                etPhone.addTextChangedListener {
                    val phoneText = etPhone.phoneText
                    loginPhoneBinding?.pbLogin?.isEnabled = phoneText.isNotBlank()
                            && phoneText.count { it in '0'..'9' } == 11
                    loginPhoneBinding?.ivClear?.visible(phoneText.isNotEmpty())
                }
                ivClear.setOnAntiViolenceClickListener {
                    etPhone.setText("")
                }
                pbLogin.setOnAntiViolenceClickListener {
                    val phone = etPhone.phoneText
                    if (!RegexUtils.isMobileSimple(phone)) {
                        toast(R.string.phone_login_toast_phone_again)
                        return@setOnAntiViolenceClickListener
                    }
                    phoneNumCache = phone
                    checkCanLogin { viewModel.getLoginPhoneCode(phone) }

                }

                if (!args.phoneNumber.isNullOrBlank()) {
                    etPhone.setText(args.phoneNumber)
                    pbLogin.isEnabled = etPhone.phoneText.isNotBlank()
                    SoftKeyboardUtil.hideSoftKeyboard(this@LoginByPhoneFragment)
                } else {
                    // 没有上次登录手机号，可以自动获取焦点，并弹出键盘
                    InputUtil.showSoftBoard(etPhone)
                }
            }

        loginPhoneBinding?.apply {
            root.visible(true)
            val phone = etPhone.phoneText
            if (phone.isEmpty() || phoneNumber.isNotEmpty()) {//上次登录是手机号，从输入验证码页面返回不覆盖
                etPhone.setText(phoneNumber)
            }

            // 处理回来的时候，如果输入框有内容了，就不用展示键盘了，藏起来，避免干扰视线
            if (!etPhone.text.isNullOrBlank()) {
                SoftKeyboardUtil.hideSoftKeyboard(this@LoginByPhoneFragment)
            }
        }
    }

    private fun showSmsCodeView(phoneNumber: String) {
        if (smsCodeBinding == null)
            smsCodeBinding = LayoutSmsCodeBinding.bind(binding.vsLoginPhoneCode.inflate()).apply {

                titleBar.setOnBackClickedListener {
                    viewModel.resetLoginFailedState()
                    viewModel.setLoginViewState(LoginViewState.PhoneLogin())
                }

            }
        smsCodeBinding?.apply {
            root.visible(true)
            tvCodeSentTip.text = getString(R.string.party_code_error_tip, phoneNumber)
            btnReSend.setOnAntiViolenceClickListener {
                codeCountDownTimer.tryStart()
                viewModel.resetLoginFailedState()
                viewModel.getLoginPhoneCode(phoneNumber)
            }
            inputCode.setText("")
            tvCodeErrorTip.visible(false)
            codeInputTextWatcher?.let { inputCode.removeTextChangedListener(it) }
            codeInputTextWatcher = CodeInputTextWatcher(phoneNumber)

            inputCode.addTextChangedListener(codeInputTextWatcher)
            viewLifecycleOwner.lifecycleScope.launch {
                delay(100)
                inputCode.requestFocus()
                InputUtil.showSoftBoard(inputCode)
            }
        }

        codeCountDownTimer.tryStart()
    }


    private inner class CodeInputTextWatcher(val phoneNumber: String) : TextWatcherAdapter() {
        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            val code = s?.toString().orEmpty()
            if (code.length == 6) {
                viewModel.loginByPhone(phoneNumber, code)
            }
        }
    }

    private val codeCountDownTimer = CodeCountDownTimer(
        onTicked = {
            smsCodeBinding?.apply {
                btnReSend.text = getSmsButtonText(it)
                btnReSend.isEnabled = false
            }
        },
        onFinished = {
            smsCodeBinding?.apply {
                btnReSend.text = getString(R.string.party_resend)
                btnReSend.isEnabled = true
            }
            viewModel.finishedSmsCodeCountDown()
        }
    )

    private fun getSmsButtonText(sNum: Long): SpannableStringBuilder {
        val spannable = SpannableHelper.Builder()
            .text(getString(R.string.party_resend)).textAppearance(requireContext(), R.style.Button_S18)
            .text("${sNum}S").textAppearance(requireContext(), R.style.Button_S18_PoppinsBold600)
            .build()
        return spannable
    }


    private val backPressedCallback = object : OnBackPressedCallback(false) {
        override fun handleOnBackPressed() {

            when (viewModel.oldState.viewState) {
                is LoginViewState.PhoneCode -> {
                    viewModel.resetLoginFailedState()
                    viewModel.setLoginViewState(LoginViewState.PhoneLogin())
                }

                else -> viewModel.setLoginViewState(viewModel.firstLoginViewState)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        smsCodeBinding?.inputCode?.let { v ->
            codeInputTextWatcher?.let { v.removeTextChangedListener(it) }
        }
        codeCountDownTimer.stop()
        backPressedCallback.remove()
        loginPhoneBinding = null
        smsCodeBinding = null
    }

    override fun invalidate() {

    }

    override fun getPageName(): String {
        return PageNameConstants.FRAGMENT_NAME_LOGIN_BY_PHONE
    }

}