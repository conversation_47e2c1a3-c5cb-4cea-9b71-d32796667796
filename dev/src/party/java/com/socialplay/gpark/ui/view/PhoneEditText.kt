package com.socialplay.gpark.ui.view

import android.content.Context
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatEditText
import timber.log.Timber
import java.util.*

/**
 * 实现自定义手机号输入框，手机号码效果为344效果，例如111 1111 1111
 */
class PhoneEditText : AppCompatEditText, TextWatcher {
    private var preCharSequence: String? = null

    constructor(context: Context?) : super(context!!) {}
    constructor(context: Context?, attrs: AttributeSet?) : super(context!!, attrs) {}
    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(context!!, attrs, defStyleAttr) {}

    interface OnPhoneEditTextChangeListener {
        /**
         * 对外提供接口监听
         *
         * @param s        字符串
         * @param isEleven 现在是否是11位数字
         */
        fun onTextChange(s: String?, isEleven: Boolean)
    }

    override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
    override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
        super.onTextChanged(s, start, before, count)
        if (TextUtils.equals(preCharSequence, s) || s.isEmpty()) {
            return
        }
        var newInput = s.toString()
        var oldLen = 0
        if (preCharSequence != null) {
            oldLen = preCharSequence!!.length
        } // 输入的时候才做加空格处理
        if (newInput.length > oldLen && before == 0) {
            newInput = handleInput(newInput)
        }
        Timber.d("PhoneEditText: %s", newInput) // 赋值
        preCharSequence = newInput
        setText(newInput)
        setSelection(length())
    }

    override fun afterTextChanged(s: Editable) {}

    /**
     * 处理输入的情况
     */
    private fun handleInput(input: String): String {
        var input = input
        input = replaceBlank(input)
        Timber.d("PhoneEditText clear all blank:  %s", input)
        val len = input.length
        val newPhoneNum: String
        newPhoneNum = if (len <= PHONE_INDEX_3) { // 小于等于三位数，原路返回
            input
        } else if (len < PHONE_INDEX_8) { // 大于3位数，小于8位数（xxx xxxx）
            input.replace(REGEX_GROUP_TWO.toRegex(), ADD_ONE_BLANK)
        } else { // 大于等于8位数（xxx xxxx xxxx）
            input.replace(REGEX_GROUP_THREE.toRegex(), ADD_TWO_BLANK)
        }
        return newPhoneNum
    }

    // 获得不包含空格的手机号
    val phoneText: String
        get() {
            val str = Objects.requireNonNull(text).toString()
            return replaceBlank(str)
        }

    private fun replaceBlank(str: String): String {
        return str.replace(REGEX_BLANK.toRegex(), "")
    }

    companion object {
        // 特殊下标位置
        private const val PHONE_INDEX_3 = 3
        private const val PHONE_INDEX_8 = 8

        // 空格的正则表达式
        private const val REGEX_BLANK = "\\s"

        // 分两组的正则表达式
        private const val REGEX_GROUP_TWO = "(\\d{3})(\\d*)"

        // 分三组的正则表达式
        private const val REGEX_GROUP_THREE = "(\\d{3})(\\d{0,4})(\\d{0,4})"

        // 在分组中间加空格
        private const val ADD_ONE_BLANK = "$1 $2"
        private const val ADD_TWO_BLANK = "$1 $2 $3"
    }
}