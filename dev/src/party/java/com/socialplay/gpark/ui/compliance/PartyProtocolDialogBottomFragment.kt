package com.socialplay.gpark.ui.compliance

import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.databinding.PartyDialogProtocolBottomFragmentBinding
import com.socialplay.gpark.function.MetaRouterWrapper
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.SpanClick
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.extension.getString
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding
import org.koin.android.ext.android.inject

/**
 * @author: ning.wang
 * @date: 2021-05-30 9:17 下午
 * @desc: 合规协议弹窗
 */
open class PartyProtocolDialogBottomFragment : BaseDialogFragment() {

    companion object {
        fun show(fm: FragmentManager, nope: () -> Unit = {}, agree: () -> Unit) {
            PartyProtocolDialogBottomFragment().apply {
                this.agree = agree
                this.nope = nope
            }.show(
                fm,
                "ProtocolDialogFragment"
            )
        }
    }

    override var navColorRes = R.color.white

    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT
    override val binding by viewBinding(PartyDialogProtocolBottomFragmentBinding::inflate)

    // 同意callback
    var agree: (() -> Unit)? = null

    // 不同意callback
    var nope: (() -> Unit)? = null
    val h5PageConfigInteractor by inject<H5PageConfigInteractor>()

    override fun init() {
        isCancelable = false
        binding.apply {
            tvContent.text = createProtocolContent()
            tvContent.movementMethod = LinkMovementMethod.getInstance()

            tvAgree.setOnAntiViolenceClickListener {
                agree?.invoke()
                dismissAllowingStateLoss()
            }
            ivClose.setOnAntiViolenceClickListener { dismissAllowingStateLoss() }
            title.text = getString(R.string.please_read_and_agree)
            title.setTextColor(Color.BLACK)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        dismissAllowingStateLoss()
        agree = null
        nope = null
    }

    private val userSpan1 = SpanClick {
        val h5PageItem = h5PageConfigInteractor.getH5PageConfigItem(H5PageConfigInteractor.USER_AGREEMENT)
        jumpWeb(h5PageItem.url, h5PageItem.title)
    }

    private val personalSpan1 = SpanClick {
        val h5PageItem = h5PageConfigInteractor.getH5PageConfigItem(H5PageConfigInteractor.PRIVACY_AGREEMENT)
        jumpWeb(h5PageItem.url, h5PageItem.title)
    }

    private val childrenSpan1 = SpanClick {
        val h5PageItem = h5PageConfigInteractor.getH5PageConfigItem(H5PageConfigInteractor.CHILDREN_PROTOCOL)
        jumpWeb(h5PageItem.url, h5PageItem.title)
    }

    private fun jumpWeb(url: String, title: String) {
        MetaRouterWrapper.Web.goProtocolWeb(requireActivity(), url, title)
    }

    private fun createProtocolContent(): SpannableStringBuilder {
        val color = ContextCompat.getColor(requireContext(), R.color.color_0C75FF)
        return SpannableHelper.Builder()
            .text(getString(R.string.user_protocol_with_brackets_no_space)).click(userSpan1).color(color)
            .text(getString(R.string.dunhao))
            .text(getString(R.string.privacy_protocol_with_brackets_no_space)).click(personalSpan1).color(color)
            .text(getString(R.string.dunhao))
            .text(getString(R.string.children_protocol_with_brackets_no_space)).click(childrenSpan1).color(color)
            .build()
    }


    /**
     * 富文本初始化
     */
    fun getProtocol(fragment: Fragment, content: String): CharSequence {
        val contentSpan = SpannableStringBuilder(content)
        contentSpan.setSpan(object : ClickableSpan() {
            override fun onClick(widget: View) {
                MetaRouter.Web.navigate(fragment, null, h5PageConfigInteractor.getH5PageUrl(H5PageConfigInteractor.USER_AGREEMENT), false)
            }

            override fun updateDrawState(ds: TextPaint) {
                ds.color = Color.parseColor("#FF5000")
                ds.bgColor = Color.parseColor("#FFFFFF")
            }

        }, content.indexOf("《"), content.indexOf("》") + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

        contentSpan.setSpan(
            object : ClickableSpan() {
                override fun onClick(widget: View) {
                    MetaRouter.Web.navigate(fragment, null, h5PageConfigInteractor.getH5PageUrl(H5PageConfigInteractor.PRIVACY_AGREEMENT), false)
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.color = Color.parseColor("#FF5000")
                    ds.bgColor = Color.parseColor("#FFFFFF")
                }

            },
            content.lastIndexOf("《"),
            content.lastIndexOf("》") + 1,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        return contentSpan
    }

    override fun gravity(): Int {
        return Gravity.BOTTOM
    }

    override fun loadFirstData() {
    }
}