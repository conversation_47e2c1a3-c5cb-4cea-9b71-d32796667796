package com.socialplay.gpark.ui.recharge

import android.view.Gravity
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.navArgs
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.pay.AgentPayType
import com.socialplay.gpark.databinding.DialogPartyRechargeAmountConfirmBinding
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.ui.recharge.RechargeChannelConfirmDialogFragment.Companion.KEY_RECHARGE_CHANNEL
import com.socialplay.gpark.util.InstallUtil
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/02/16
 *     desc   :
 * </pre>
 */
class RechargeAmountConfirmDialogFragment : BaseDialogFragment() {
    var callback: (Boolean, Int, RechargeViewModel) -> Unit = { isConfirm, type, viewModel -> }

    companion object {
        const val KEY_RECHARGE_CONFIRM = "KEY_RECHARGE_CONFIRM"
        fun show(
            fragmentManager: FragmentManager,
            price: Int,
            callback: (Boolean, Int, RechargeViewModel) -> Unit
        ) {
            RechargeAmountConfirmDialogFragment().apply {
                this.callback = callback
                arguments = RechargeAmountConfirmDialogFragmentArgs(price).toBundle()
            }.show(fragmentManager, "RechargeAmountConfirmDialogFragment")
        }
    }

    val adapter = RechargeChannelAdapter()
    private val viewModel by viewModel<RechargeViewModel>()

    override val binding by viewBinding(DialogPartyRechargeAmountConfirmBinding::inflate)
    private val args by navArgs<RechargeAmountConfirmDialogFragmentArgs>()

    override fun gravity(): Int {
        return Gravity.CENTER
    }

    override fun isBackPressedDismiss() = false
    override fun isClickOutsideDismiss() = false

    override fun init() {
        // 要等支付渠道加载出来, 才显示 Dialog, 否则界面会跳动
        binding.root.visible(false)
        binding.rv.adapter = adapter
        viewModel.channelLiveData.observe(viewLifecycleOwner) {
            adapter.setList(it)
            binding.root.visible(true)
        }
        binding.ivClose.setOnAntiViolenceClickListener {
            dismissWithResult(false)
        }
        adapter.setOnItemClickListener { view, position ->
            if (!checkPayWayInstalled(adapter.getItem(position).payChannel ?: 0)) {
                //未安装的方式拦截
                return@setOnItemClickListener
            }
            viewModel.setSelected(position)
        }
        setFragmentResultListener(KEY_RECHARGE_CHANNEL) { _, bundle ->
            val channelType = bundle.getInt(KEY_RECHARGE_CHANNEL)
            dismissWithResult(true, channelType)
        }
        binding.apply {
            // 价格是以分为单位的价格, 需要除以100
            val price = args.price / 100f
            val formattedPrice = when {
                price.rem(1) == 0f -> "%.0f".format(price)
                else -> "%.2f".format(price)
            }
            tvPayAmount.text = formattedPrice
            tvConfirm.text = resources.getString(R.string.iap_third_pay_confirm_text,formattedPrice)
            tvConfirm.setOnAntiViolenceClickListener {
                if (NetUtil.isNetworkAvailable()) {
                    val type = adapter.data.firstOrNull { it.isSelected }?.payChannel
                    if (type == null) {
                        ToastUtil.showShort(R.string.iap_toast_third_pay_select_channel)
                    } else {
                        if (!checkPayWayInstalled(type)) {
                            //未安装的方式拦截
                            return@setOnAntiViolenceClickListener
                        }
                        dismissWithResult(true, type)
                    }
                } else {
                    ToastUtil.showShort(R.string.net_unavailable)
                }
            }
            rechargePrivacy.setOnAntiViolenceClickListener {
                MetaRouter.Web.navigate(
                    this@RechargeAmountConfirmDialogFragment,
                    title = null,
                    url = BuildConfig.RECHARGE_PROTOCOL_URL,
                    showTitle = false,
                )
            }
        }
    }

    private fun checkPayWayInstalled(payType: Int): Boolean {
        val metaApp = requireContext()
        when (payType) {
            AgentPayType.PAY_TYPE_WX -> {
                val installed: Boolean = InstallUtil.isInstalledWX(metaApp)
                if (!installed) {
                    ToastUtil.showShort(R.string.pay_not_install_weixin)
                }
                return installed
            }

            AgentPayType.PAY_TYPE_ALI -> {
                val installed: Boolean = InstallUtil.isInstalledAliPay(metaApp)
                if (!installed) {
                    ToastUtil.showShort(R.string.pay_not_install_alipay)
                }
                return installed
            }

            AgentPayType.PAY_TYPE_QQ -> {
                val installed: Boolean = InstallUtil.isInstalledQQ(metaApp)
                if (!installed) {
                    ToastUtil.showShort(R.string.pay_not_install_qq)
                }
                return installed
            }
            // 其他的类型默认能支付
            else -> return true
        }
    }

    override fun loadFirstData() {
        viewModel.getPayChannel(activity?.packageName ?: BuildConfig.APPLICATION_ID)
    }

    private fun dismissWithResult(isConfirm: Boolean, type: Int? = null) {
        callback.invoke(isConfirm, type ?: 0, viewModel)
        dismissAllowingStateLoss()
    }

    override fun onBackPressed(): Boolean {
        dismissWithResult(false)
        return super.onBackPressed()
    }
}