package com.socialplay.gpark.ui.login

import android.os.CountDownTimer

class CodeCountDownTimer(
    millisInFuture: Long = 60_000,
    countDownInterval: Long = 1000,
    val onTicked: (Long) -> Unit = {}, val onFinished: () -> Unit = {}
) : CountDownTimer(millisInFuture, countDownInterval) {
    private var isRunning = false

    fun tryStart() {
        if (isRunning) {
            return
        }
        isRunning = true
        start()
    }

    fun stop() {
        isRunning = false
        cancel()
    }

    override fun onTick(millisUntilFinished: Long) {
        val restTime = millisUntilFinished / 1000
        onTicked(restTime)
    }

    override fun onFinish() {
        isRunning = false
        onFinished()
    }
}