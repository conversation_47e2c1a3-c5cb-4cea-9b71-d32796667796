package com.socialplay.gpark.ui.realname

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.FragmentRealNameShowBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.util.extension.toast

class RealNameShowFragment : BaseFragment<FragmentRealNameShowBinding>(R.layout.fragment_real_name_show) {
    private val viewModel: RealNameViewModel by fragmentViewModel()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentRealNameShowBinding? {
        return FragmentRealNameShowBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        binding.ivClose.setOnBackClickedListener(View.OnClickListener {
            findNavController().popBackStack()
        })
    }

    private fun initData() {
        viewModel.realNameDetail {
            if (it != null) {
                if (it.realName?.isBlank() == true && it.cardNo?.isBlank() == true) {
                    toast(R.string.load_timeout_try_again_later)
                    return@realNameDetail
                }
                binding.tvRealNameValue.text = it.realName ?: ""
                binding.tvCardId.text = it.cardNo ?: ""
            } else {
                toast(R.string.load_timeout_try_again_later)
            }

        }
    }

    override fun invalidate() {}

    override fun getPageName(): String {
        return PageNameConstants.FRAGMENT_NAME_SHOW_REAL_NAME
    }
}