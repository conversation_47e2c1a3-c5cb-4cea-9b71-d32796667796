package com.socialplay.gpark.data.interactor

import android.content.Context
import androidx.annotation.LongDef
import com.meta.box.biz.h5config.ConfigurableH5Biz
import com.meta.box.biz.h5config.model.H5PageConfigItem
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.ACTIVITY_ENTRANCE
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.APP_DOWNLOAD
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.BALANCE
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.CHILDREN_PROTOCOL
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.DAILY_SIGN_WEB_URL
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.PRIVACY_AGREEMENT
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.RECHARGE
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.USER_AGREEMENT
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.VIP_PLUS
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.VIP_STATUS
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.DISCLAIMER_PROTOCOL
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.DESTROY_ACCOUNT_PROTOCOL
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.CREATOR_PROTOCOL
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.COMPLAINING_INFRINGEMENT_GUIDELINES
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.GROUP_CHAT_AGREEMENT
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.THIRD_SDK_LIST_PROTOCOL
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.REALNAME_NEED_PROTOCOL
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.SPARK_ACCOUNT
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.SPARK_INSTRUCTION
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.SEND_FLOWER_FEATURE_URL
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.SEND_FLOWER_FLOWER_RANK_README_URL
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.SEND_FLOWER_NOTICE_URL
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.SEND_FLOWER_README_URL
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.TRANSACTION_DETAILS
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.WEB_URL_RECORD
import com.socialplay.gpark.data.kv.MetaKV
import org.koin.core.context.GlobalContext
import retrofit2.Retrofit

@LongDef(
    RECHARGE,
    BALANCE,
    USER_AGREEMENT,
    PRIVACY_AGREEMENT,
    VIP_PLUS,
    VIP_STATUS,
    APP_DOWNLOAD,
    ACTIVITY_ENTRANCE,
    DAILY_SIGN_WEB_URL,
    CHILDREN_PROTOCOL,
    DISCLAIMER_PROTOCOL,
    DESTROY_ACCOUNT_PROTOCOL,
    CREATOR_PROTOCOL,
    COMPLAINING_INFRINGEMENT_GUIDELINES,
    THIRD_SDK_LIST_PROTOCOL,
    REALNAME_NEED_PROTOCOL,
    GROUP_CHAT_AGREEMENT,
    WEB_URL_RECORD,
    SPARK_ACCOUNT,
    SPARK_INSTRUCTION,
    SEND_FLOWER_FEATURE_URL,
    SEND_FLOWER_NOTICE_URL,
    SEND_FLOWER_FLOWER_RANK_README_URL,
    SEND_FLOWER_README_URL,
    TRANSACTION_DETAILS,
)
@Retention(AnnotationRetention.SOURCE)
annotation class H5PageCode


class H5PageConfigInteractor(context: Context, kv: MetaKV) {

    // 配置详情见文档：https://meta.feishu.cn/wiki/AtJbwVv6eih42QkMg9bcHx13nHc
    companion object {
        const val USER_AGREEMENT = 251L
        const val PRIVACY_AGREEMENT = 250L
        const val RECHARGE = 50L
        const val BALANCE = 51L
        const val VIP_STATUS = 52L
        const val VIP_PLUS = 54L
        const val CHILDREN_PROTOCOL = 252L  // 儿童隐私保护指引
        const val DISCLAIMER_PROTOCOL = 253L  // 免责声明
        const val DESTROY_ACCOUNT_PROTOCOL = 254L  // 账号注销协议
        const val CREATOR_PROTOCOL = 256L  // 创作者协议
        const val COMPLAINING_INFRINGEMENT_GUIDELINES = 255L    // 投诉侵权指引
        const val THIRD_SDK_LIST_PROTOCOL = 257L    // 第三方SDK列表
        const val REALNAME_NEED_PROTOCOL = 258L    // 实名须知
        const val GROUP_CHAT_AGREEMENT = 259L    // 群聊服务规范
        const val ACTIVITY_ENTRANCE = 368L
        const val APP_DOWNLOAD = 369L
        const val DAILY_SIGN_WEB_URL = 370L
        const val SPARK_ACCOUNT = 400L
        const val SPARK_INSTRUCTION = 401L
        const val WEB_URL_RECORD = 202502028L   // 备案网址

        /**
         * 赠花功能协议-赠花功能弹窗使用
         */
        const val SEND_FLOWER_FEATURE_URL = 1200L

        /**
         * 送花须知
         */
        const val SEND_FLOWER_NOTICE_URL = 1201L

        /**
         * 鲜花榜单功能说明
         */
        const val SEND_FLOWER_FLOWER_RANK_README_URL = 1202L

        /**
         * 送花弹窗-标题的问号点击后跳转的页面
         */
        const val SEND_FLOWER_README_URL = 1203L

        /**
         * 交易明细
         */
        const val TRANSACTION_DETAILS = 420L
    }

    private val defH5ConfigPages by lazy {
        setOf(
            H5PageConfigItem(RECHARGE, "", BuildConfig.RECHARGE_URL),
            H5PageConfigItem(BALANCE, "", BuildConfig.RECHARGE_DIALOG_URL),
            H5PageConfigItem(USER_AGREEMENT, context.getString(R.string.terms_of_service_text), BuildConfig.USER_AGREEMENT),
            H5PageConfigItem(PRIVACY_AGREEMENT, context.getString(R.string.privacy_protocol), BuildConfig.PRIVACY_AGREEMENT),
            H5PageConfigItem(VIP_PLUS, "", BuildConfig.GPARK_PLUS),
            H5PageConfigItem(VIP_STATUS, "", BuildConfig.GPARK_PLUS_STATUS),
            H5PageConfigItem(APP_DOWNLOAD, "", BuildConfig.APP_DOWNLOAD),
            H5PageConfigItem(ACTIVITY_ENTRANCE, "", BuildConfig.GPARK_ACTIVITY_ENTRANCE),
            H5PageConfigItem(DAILY_SIGN_WEB_URL, "", BuildConfig.DAILY_SIGN_WEB_URL),
            H5PageConfigItem(CHILDREN_PROTOCOL, context.getString(R.string.children_protocol), BuildConfig.H5_CONFIG_ID_CHILDREN_PROTOCOL),
            H5PageConfigItem(DISCLAIMER_PROTOCOL, context.getString(R.string.disclaimer), BuildConfig.H5_CONFIG_ID_DISCLAIMER_PROTOCOL),
            H5PageConfigItem(DESTROY_ACCOUNT_PROTOCOL, "", BuildConfig.H5_CONFIG_ID_DESTROY_ACCOUNT_PROTOCOL),
            H5PageConfigItem(CREATOR_PROTOCOL, "", BuildConfig.H5_CONFIG_ID_CREATOR_PROTOCOL),
            H5PageConfigItem(COMPLAINING_INFRINGEMENT_GUIDELINES, context.getString(R.string.complaints_guide), BuildConfig.H5_CONFIG_ID_COMPLAINING_INFRINGEMENT_GUIDELINES),
            H5PageConfigItem(THIRD_SDK_LIST_PROTOCOL, context.getString(R.string.third_part_sdk), BuildConfig.H5_CONFIG_ID_THIRD_SDK_LIST_PROTOCOL),
            H5PageConfigItem(GROUP_CHAT_AGREEMENT, context.getString(R.string.setting_item_title_group_chat_agreement), BuildConfig.GROUP_CHAT_AGREEMENT),
            H5PageConfigItem(REALNAME_NEED_PROTOCOL, context.getString(R.string.real_name_web_title), BuildConfig.H5_CONFIG_ID_REALNAME_NEED_PROTOCOL),
            H5PageConfigItem(WEB_URL_RECORD, "", BuildConfig.H5_CONFIG_ID_WEB_URL_RECORD),
            H5PageConfigItem(SPARK_ACCOUNT, "", BuildConfig.SPARK_ACCOUNT),
            H5PageConfigItem(SPARK_INSTRUCTION, "", BuildConfig.SPARK_INSTRUCTION),
            H5PageConfigItem(SEND_FLOWER_FEATURE_URL, context.getString(R.string.send_flower_feature_h5_title), BuildConfig.SEND_FLOWER_FEATURE_URL),
            H5PageConfigItem(SEND_FLOWER_NOTICE_URL, context.getString(R.string.send_flower_notice_h5_title), BuildConfig.SEND_FLOWER_NOTICE_URL),
            H5PageConfigItem(SEND_FLOWER_FLOWER_RANK_README_URL, context.getString(R.string.send_flower_rank_readme_h5_title), BuildConfig.SEND_FLOWER_FLOWER_RANK_README_URL),
            H5PageConfigItem(SEND_FLOWER_README_URL, context.getString(R.string.send_flower_readme_h5_title), BuildConfig.SEND_FLOWER_README_URL),
            H5PageConfigItem(TRANSACTION_DETAILS, context.getString(R.string.transaction_details_page_title), BuildConfig.TRANSACTION_DETAILS_URL),
        )
    }

    suspend fun init(isMainProcess: Boolean) {
        val retrofit = GlobalContext.get().get<Retrofit>()
        ConfigurableH5Biz.init(isMainProcess, retrofit, defH5ConfigPages, true)
    }

    fun getH5PageConfigItem(@H5PageCode code: Long): H5PageConfigItem {
        return ConfigurableH5Biz.getH5PageConfigItem(code)
    }

    fun getH5PageUrl(@H5PageCode code: Long): String {
        return getH5PageConfigItem(code).url
    }
}