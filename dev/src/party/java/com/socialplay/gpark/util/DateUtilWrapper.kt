package com.socialplay.gpark.util

import android.content.Context
import com.socialplay.gpark.R
import com.socialplay.gpark.util.DateUtil.DAY
import com.socialplay.gpark.util.DateUtil.HOUR
import com.socialplay.gpark.util.DateUtil.MINUTE
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.GregorianCalendar
import java.util.Locale
import java.util.TimeZone

object DateUtilWrapper {

    private val dateFormatPattern = "yyyy-MM-dd HH:mm:ss"

    const val NEED_KEEP_DOUBLE_DIGITS = false

    fun getLabel4Year(context: Context): String = context.getString(R.string.year)

    fun getLabel4Day(context: Context): String = context.getString(R.string.sun)

    fun getCreateFormatDate(context: Context, timeStamp: Long): String {
        return getCZHTime(context, Date(timeStamp), true)
    }

    fun getUpdateFormatDate(context: Context, timeStamp: Long): String {
        return getTimeString(Date(timeStamp), context.getString(R.string.detail_date_format))
    }

    fun getFormatCommentDate(context: Context, timeStamp: Long): String {
        return getCZHTime(context, Date(timeStamp), true)
    }

    fun getDefaultBirthCalendar(): Calendar {
        return Calendar.getInstance()
    }

    fun getEarliestBirthCalendar(): Calendar {
        return Calendar.getInstance().apply {
            set(Calendar.YEAR, 1900)
            set(Calendar.MONTH, 0)
            set(Calendar.DATE, 1)
        }
    }

    fun getTransactionDate(context: Context, timeStamp: Long): String {
        return getTimeString(Date(timeStamp), dateFormatPattern)
    }

    /**
     * 格式化时间（xx前）
     */
    private fun getCZHTime(context: Context, srcDate: Date, mustIncludeTime: Boolean): String {
        var ret = ""
        val gcCurrent = GregorianCalendar()
        gcCurrent.time = Date()
        val currentYear = gcCurrent[GregorianCalendar.YEAR]
        val currentMonth = gcCurrent[GregorianCalendar.MONTH] + 1
        val currentDay = gcCurrent[GregorianCalendar.DAY_OF_MONTH]
        val gcSrc = GregorianCalendar()
        gcSrc.time = srcDate
        val srcYear = gcSrc[GregorianCalendar.YEAR]
        val srcMonth = gcSrc[GregorianCalendar.MONTH] + 1
        val srcDay = gcSrc[GregorianCalendar.DAY_OF_MONTH]

        // 要额外显示的时间分钟
        val timeExtraStr = if (mustIncludeTime) " " + getTimeString(srcDate, "HH:mm") else ""

        // 当年
        if (currentYear == srcYear) {
            val currentTimestamp = gcCurrent.timeInMillis
            val srcTimestamp = gcSrc.timeInMillis

            // 相差时间（单位：毫秒）
            val delta = currentTimestamp - srcTimestamp

            // 当天（月份和日期一致才是）
            if (currentMonth == srcMonth && currentDay == srcDay) {

                // 时间相差60秒以内
                if (delta < MINUTE) ret =
                    context.getString(R.string.community_just_now) else if (delta in MINUTE..HOUR) {
                    ret = getTimeString(
                        Date(delta),
                        "mm"
                    ) + context.getString(R.string.community_minutes_ago)
                    if (ret.startsWith("0")) {
                        ret = ret.substring(1, ret.length)
                    }
                } else if (delta in HOUR..DAY) {
                    try {
                        val sdf = getSimpleDateFormat("HH") //"yyyy-MM-dd HH:mm:ss"
                        sdf.timeZone = TimeZone.getTimeZone("GMT")
                        ret = sdf.format(delta) + context.getString(R.string.community_hours_ago)
                        if (ret.startsWith("0")) {
                            ret = ret.substring(1, ret.length)
                        }
                    } catch (e: Exception) {
                        return ""
                    }
                } else ret = getTimeString(srcDate, "HH:mm")
            } else {

                // 昨天（以“现在”的时候为基准-1天）
                val yesterdayDate = GregorianCalendar()
                yesterdayDate.add(GregorianCalendar.DAY_OF_MONTH, -1)

                // 前天（以“现在”的时候为基准-2天）
                val beforeYesterdayDate = GregorianCalendar()
                beforeYesterdayDate.add(GregorianCalendar.DAY_OF_MONTH, -2)

                // 用目标日期的“月”和“天”跟上方计算出来的“昨天”进行比较，是最为准确的（如果用时间戳差值
                // 的形式，是不准确的，比如：现在时刻是2019年02月22日1:00、而srcDate是2019年02月21日23:00，
                // 这两者间只相差2小时，直接用“delta/(3600 * 1000)” > 24小时来判断是否昨天，就完全是扯蛋的逻辑了）
                ret =
                    if (srcMonth == yesterdayDate[GregorianCalendar.MONTH] + 1 && srcDay == yesterdayDate[GregorianCalendar.DAY_OF_MONTH]) {
                        context.getString(R.string.community_yesterday) + timeExtraStr // -1d
                    } else if (srcMonth == beforeYesterdayDate[GregorianCalendar.MONTH] + 1 && srcDay == beforeYesterdayDate[GregorianCalendar.DAY_OF_MONTH]) {
                        context.getString(R.string.community_day_before_yesterday) + timeExtraStr // -2d
                    } else {
                        getTimeString(
                            srcDate,
                            context.getString(R.string.community_time_format_md)
                        ) + timeExtraStr
                    }
            }
        } else {
            ret = getTimeString(
                srcDate,
                context.getString(R.string.community_time_format_ymdh)
            ) + timeExtraStr
        }
        return ret
    }

    fun getTimeString(dt: Date, pattern: String): String {
        return kotlin.runCatching {
            val sdf = getSimpleDateFormat(pattern) //"yyyy-MM-dd HH:mm:ss"
            sdf.timeZone = TimeZone.getDefault()
            sdf.format(dt)
        }.getOrElse { "" }
    }

    private fun getSimpleDateFormat(pattern: String): SimpleDateFormat {
        return SimpleDateFormat(pattern, Locale.getDefault())
    }
}