package com.socialplay.gpark.util

import android.content.Context
import com.socialplay.gpark.R
import java.util.concurrent.TimeUnit

object UnitUtilWrapper {
    /**
     * 1亿
     */
    private const val HUNDRED_MILLION = 100_000_000L

    /**
     * 统一的代币数量显示转换方法
     */
    fun formatCoinCont(num: Long): String {
        return when {
            num >= HUNDRED_MILLION * 1000 -> {
                getStringByGlobal(
                    R.string.x_100_million_short,
                    String.format("%.2f", 999.99)
                )
            }

            num >= HUNDRED_MILLION -> {
                getStringByGlobal(
                    R.string.x_100_million_short,
                    String.format("%.2f", num.toDouble() / HUNDRED_MILLION)
                )
            }

            // 千分位加逗号
            else -> String.format("%,d", num)
        }
    }

    fun formatPlayTime(context: Context, sec: Long, default: String? = null): String {
        val time: StringBuilder = StringBuilder()
        val timeUnitPair = getPlayTimeWithoutUnit(sec)
        when (timeUnitPair.second) {
            null -> {
                return if (default.isNullOrEmpty()) context.getString(R.string.not_played) else default
            }

            TimeUnit.MINUTES -> {
                time.append("${timeUnitPair.first}").append(context.getString(R.string.miniut))
            }

            TimeUnit.HOURS -> {
                time.append("${timeUnitPair.first}").append(context.getString(R.string.hour))
            }

            else -> {}
        }
        return time.toString()
    }

    /**
     * 格式化游戏时间，单位：毫秒
     */
    fun getPlayTimeWithoutUnit(playTime: Long): Pair<Long, TimeUnit?> {
        return when {
            playTime <= 0 -> {
                0L to null
            }

            playTime < 60 -> {
                0L to TimeUnit.MINUTES
            }

            playTime < 3600 -> {
                (playTime / 60) to TimeUnit.MINUTES
            }

            else -> {
                (playTime / 3600) to TimeUnit.HOURS
            }
        }
    }
}