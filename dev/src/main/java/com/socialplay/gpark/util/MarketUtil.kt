package com.socialplay.gpark.util

import android.content.Intent
import android.net.Uri
import androidx.fragment.app.Fragment
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.util.extension.toast

/**
 * xingxiu.hou
 * 2023/7/5
 */
object MarketUtil {

    fun goto(fragment: Fragment) {
        kotlin.runCatching {
            var intent = Intent(Intent.ACTION_VIEW)
            intent.data =
                Uri.parse(BuildConfig.APP_MARKET_URI.replaceFirst("{}", fragment.requireContext().packageName)) //跳转到应用市场，非Google Play市场一般情况也实现了这个接口
            intent.`package` = "com.android.vending"
            //存在手机里没安装应用市场的情况，跳转会包异常，做一个接收判断
            if (intent.resolveActivity(fragment.requireContext().packageManager) != null) { //可以接收
                fragment.startActivity(intent)
            } else { //没有应用市场，我们通过浏览器跳转到Google Play
                intent = Intent(Intent.ACTION_VIEW)
                intent.data =
                    Uri.parse(BuildConfig.APP_MARKET_URL.replaceFirst("{}", fragment.requireContext().packageName))
                //这里存在一个极端情况就是有些用户浏览器也没有，再判断一次
                if (intent.resolveActivity(fragment.requireContext().packageManager) != null) { //有浏览器
                    fragment.startActivity(intent)
                } else {
                    fragment.toast(R.string.please_install_google_play)
                }
            }
        }.getOrElse {
            fragment.toast(fragment.getString(R.string.please_install_google_play) + " $it")
        }
    }

}