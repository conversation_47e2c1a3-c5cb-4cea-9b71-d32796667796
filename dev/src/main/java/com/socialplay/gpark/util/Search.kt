package com.socialplay.gpark.util

fun String.runes(): List<String> {
    val runes = mutableListOf<String>()
    var i = 0
    while (i < this.length) {
        val codePoint = if (Character.isHighSurrogate(this[i]) && i + 1 < this.length) {
            // 处理代理对
            Character.toCodePoint(this[i], this[i + 1]).also { i += 2 }
        } else {
            this[i].code.also { i++ }
        }
        val charStr = String(Character.toChars(codePoint))
        runes.add(charStr)
    }
    return runes
}

data class Range(
    val start: Int,
    val end: Int,
)

data class MutableRange(
    var start: Int,
    var end: Int,
) {
    fun toRange(): Range {
        return Range(start, end)
    }
}

data class SearchConfig(
    val ignoreCase: Boolean = true,
    /**
     * 是否支持拼音首字母搜索
     */
    val supportShortPinyin: Boolean = true,
    /**
     * 是否支持拼音搜索
     */
    val supportPinyin: Boolean = true,
)

data class SearchKey(
    val config: SearchConfig,
    val searchKey: String,
    val searchKeyLowercase: String,
    /**
     * 打散后的 searchKey, 调用 searchKey.runes() 生成
     * 有些字符占有两个位置如: "😂"
     * 所以使用 List<String> 存储
     */
    val searchKeyRunes: List<String>,
    val searchKeyLowercaseRunes: List<String>,
) {
    fun actualKey(): String {
        return if (config.ignoreCase) {
            searchKeyLowercase
        } else {
            searchKey
        }
    }

    fun actualKeyRunes(): List<String> {
        return if (config.ignoreCase) {
            searchKeyLowercaseRunes
        } else {
            searchKeyRunes
        }
    }
}

data class SearchItem<T>(
    val data: T,
    /**
     * 当前显示给用户看的字符内容
     */
    val name: String,
    val nameLowercase: String,
    /**
     * 当前显示给用户看的字符内容, 打散后的结果, 调用 name.runes() 生成
     * 有些字符占有两个位置如: "😂"
     * 所以使用 List<String> 存储
     */
    val nameRunes: List<String>,
    val nameLowercaseRunes: List<String>,
    /**
     * 当前的拼音是否可用
     * 当拼音不可用时, shortPinyin 和 pinyin 为 empty
     */
    val enablePinyin: Boolean,
    /**
     * 拼音首字母, 字符串长度与 searchKey 相等, 注意都传小写
     */
    val shortPinyin: String,
    /**
     * 完整的拼音, list 长度与 searchKey 长度相等, 注意都传小写
     */
    val pinyin: List<String>,
) {
    fun search(searchKey: SearchKey): List<Range> {
        var result = emptyList<Range>()
        if (searchKey.searchKey.isEmpty() || name.isEmpty()) {
            return result
        }
        result = searchByKeyword(searchKey)
        if (result.isNotEmpty()) {
            return result
        }
        if (searchKey.config.supportShortPinyin) {
            result = searchByShortPinyin(searchKey)
            if (result.isNotEmpty()) {
                return result
            }
        }
        if (searchKey.config.supportPinyin) {
            result = searchByPinyin(searchKey)
            if (result.isNotEmpty()) {
                return result
            }
        }
        return result
    }

    private fun searchByKeyword(searchKey: SearchKey): List<Range> {
        val result = mutableListOf<MutableRange>()
        if (searchKey.searchKey.isEmpty() || name.isEmpty()) {
            return emptyList()
        }
        val content = if (searchKey.config.ignoreCase) {
            nameLowercase
        } else {
            name
        }
        val key = searchKey.actualKey()
        // 先尝试直接使用关键字搜索, 不打散搜索关键字
        val index = content.indexOf(key)
        if (index >= 0) {
            return listOf(Range(start = index, end = index + key.length))
        }

        // 后面为模糊搜索, 将搜索关键字打散搜索
        val keys = searchKey.actualKeyRunes()
        var flag = 0
        var last: MutableRange? = null
        for (i in keys.indices) {
            flag = content.indexOf(keys[i], flag)
            if (flag < 0) {
                break
            }
            if (last != null && last.end >= flag) {
                // 合并相邻的搜索结果
                last.end += keys[i].length
            } else {
                last = MutableRange(
                    start = flag,
                    end = flag + keys[i].length
                )
                result.add(last)
            }
            flag += keys[i].length
        }
        if (flag >= 0) {
            return result.map { it.toRange() }
        }
        return emptyList()
    }

    /**
     * 根据拼音首字母搜索
     */
    private fun searchByShortPinyin(searchKey: SearchKey): List<Range> {
        val result = mutableListOf<MutableRange>()

        if (!enablePinyin
            || this.shortPinyin.isEmpty()
            || searchKey.searchKeyLowercaseRunes.isEmpty()
            || this.shortPinyin.length != nameRunes.size
        ) {
            return emptyList()
        }

        val keys = searchKey.searchKeyLowercaseRunes
        var flag = 0
        var last: MutableRange? = null
        for (i in keys.indices) {
            flag = this.shortPinyin.indexOf(keys[i], flag)
            if (flag < 0) {
                break
            }
            if (last != null && last.end >= flag) {
                // 合并相邻的搜索结果（如果当前匹配与上一个匹配相邻或重叠）
                last.end += nameRunes[i].length
            } else {
                last = MutableRange(
                    start = flag,
                    end = flag + nameRunes[i].length
                )
                result.add(last)
            }
            flag += nameRunes[i].length
        }
        if (flag >= 0) {
            return result.map { it.toRange() }
        }
        return emptyList()
    }

    private fun searchByPinyin(searchKey: SearchKey): List<Range> {
        val result = mutableListOf<MutableRange>()
        if (!enablePinyin
            || this.pinyin.isEmpty()
            || searchKey.searchKeyLowercase.isEmpty()
            || this.pinyin.size != nameRunes.size
        ) {
            return emptyList()
        }
        val pinyinList = this.pinyin
        val key = searchKey.searchKeyLowercase

        var flag = 0
        var last: MutableRange? = null
        for (i in pinyinList.indices) {
            val sameStartCount = sameStartCount(pinyinList[i], 0, key, flag)
            if (sameStartCount > 0) {
                flag += sameStartCount
                if (last != null && last.end >= i) {
                    // 合并相邻的搜索结果
                    last.end += nameRunes[i].length
                } else {
                    last = MutableRange(start = i, end = i + nameRunes[i].length)
                    result.add(last)
                }
                if (flag >= key.length) {
                    return result.map { it.toRange() }
                }
            }
        }
        return emptyList()
    }

    private fun sameStartCount(
        key1: String,
        index1: Int,
        key2: String,
        index2: Int
    ): Int {
        var count = 0
        val max = (key1.length - index1).coerceAtMost(key2.length - index2)
        while (count < max) {
            if (key1[index1 + count] == key2[index2 + count]) {
                count++
            } else {
                break
            }
        }
        return count
    }
}