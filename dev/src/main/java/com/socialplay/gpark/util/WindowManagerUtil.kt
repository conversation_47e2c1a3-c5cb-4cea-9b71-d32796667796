package com.socialplay.gpark.util

import android.annotation.SuppressLint
import android.app.Activity
import android.graphics.PixelFormat
import android.os.Build
import android.view.Gravity
import android.view.WindowManager

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/10/11
 * desc   :
 * </pre>
 */


object WindowManagerUtil {

    @SuppressLint("WrongConstant")
    fun generateLayoutParams(width: Int = WindowManager.LayoutParams.MATCH_PARENT, height: Int = WindowManager.LayoutParams.MATCH_PARENT, activity: Activity? = null): WindowManager.LayoutParams {
        val layoutParams = WindowManager.LayoutParams(width, height)
        layoutParams.flags = (WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                or WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR
                or WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                or WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH
                or WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS // 防止状态栏遮挡通知
                or WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION) // 防止导航栏遮挡通知

        layoutParams.type = WindowManager.LayoutParams.LAST_APPLICATION_WINDOW
        activity?.let {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                // 和当前的activity的window异形屏展示保持一致
                layoutParams.layoutInDisplayCutoutMode = activity.window.attributes.layoutInDisplayCutoutMode
            }
        }
        layoutParams.gravity = Gravity.LEFT or Gravity.TOP
        layoutParams.format = PixelFormat.RGBA_8888
        return layoutParams
    }
}