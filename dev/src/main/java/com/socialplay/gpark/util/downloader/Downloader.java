package com.socialplay.gpark.util.downloader;

import android.text.TextUtils;

import com.meta.verse.ext.Maps;
import com.meta.verse.ext.TryCatch;
import com.meta.verse.lib.util.L;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLConnection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * xingxiu.hou
 * 2021/11/9
 */
public class Downloader {


    interface OnUse {
        void on(InputStream input, Header header) throws IOException;
    }

    public interface OnDownloadListener {
        default void onProgress(float percent) {
        }

        void onFinish(File file,Throwable throwable);
    }

    interface OnFilerUse {
        void on(Filer filer, long curBytes) throws IOException;
    }


    /**
     * 下载
     */
    public static void download(String url, File file, OnDownloadListener listener) {
        TryCatch.tryCatch(() -> {
            new Filer(file, listener).use((filer, curBytes) -> new Request(url, curBytes).use((input, header) -> filer.write(header.getContentLength(), input)));
            listener.onFinish(file, null);
            return null;
        }).getOrElse(throwable -> {
            L.d(TAG, "ERROR", throwable);
            listener.onFinish(null, throwable);
            return null;
        });
    }

    /**
     * 下载
     */
    public static File download(String url, File file) throws IOException {
        new Filer(file, null).use((filer, curBytes) -> new Request(url, curBytes).use((input, header) -> filer.write(header.getContentLength(), input)));
        return file;
    }


    /**
     * 写文件
     */
    private static class Filer {

        private final File file;
        private final OnDownloadListener listener;

        private final byte[] byteArray = new byte[1024 * 16];

        private long writeBytes = 0L;

        private long currentBytes = 0L;

        private File tempFile;

        private RandomAccessFile raf;

        public Filer(File file, OnDownloadListener listener) {
            this.file = file;
            this.listener = listener;
        }

        void use(OnFilerUse call) throws IOException {
            tempFile = new File(file.getParentFile(), file.getName() + ".d6d");
            if (!file.exists()) {
                file.getParentFile().mkdirs();
                if (tempFile.exists()) {
                    currentBytes = tempFile.length();
                    writeBytes = currentBytes;
                }
                call.on(this, currentBytes);
            }
        }

        void write(long total, InputStream ins) throws IOException {
            int len;
            try {
                raf = new RandomAccessFile(tempFile, "rw");
                long totalBytes = total <= 0 ? ins.available() : total;
                if (totalBytes <= 0 && listener != null) {
                    listener.onProgress(1F);
                } else {
                    totalBytes += currentBytes;
                    raf.seek(currentBytes);
                    while ((len = ins.read(byteArray)) > 0) {
                        raf.write(byteArray, 0, len);
                        writeBytes += len;
                        if (listener != null) {
                            listener.onProgress(writeBytes * 1F / totalBytes);
                        }
                    }
                }
            } finally {
                try {
                    raf.close();
                    ins.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (tempFile.renameTo(file)) {
                boolean ignore = tempFile.delete();
            }
        }
    }


    /**
     * 网络请求
     */
    private static class Request {

        private final String url;
        private long startBytes;

        public Request(String url, long startBytes) {
            this.url = url;
            this.startBytes = startBytes;
        }

        private Header header;

        private HttpURLConnection conn;

        void use(OnUse use) throws IOException {
            HttpURLConnection conn = openConnection(url);
            if (startBytes > 0 && !header.hasRange()) {
                use.on(null, header);
            } else {
                use.on(conn.getInputStream(), header);
            }
            conn.disconnect();
        }

        /**
         * 重定向
         */
        private HttpURLConnection redirect(Header header) throws IOException {
            String redirectUrl = header.getRedirectUrl();
            if (!TextUtils.isEmpty(redirectUrl)) {
                return openConnection(redirectUrl);
            } else {
                throw new ConnectException("not find redirect url");
            }
        }

        /**
         * 打开URL链接
         */
        private HttpURLConnection openConnection(String url) throws IOException {
            if (conn == null) {
                URLConnection urlConnection = new URL(url).openConnection();
                if (urlConnection instanceof HttpURLConnection) {
                    HttpURLConnection conn = (HttpURLConnection) urlConnection;
                    conn.setConnectTimeout(30_000);
                    conn.setReadTimeout(30_000);
                    conn.setRequestMethod("GET");
                    if (startBytes > 0) {
                        conn.setRequestProperty("RANGE", "bytes=" + startBytes + "-");
                    }
                    header = Header.parse(conn.getHeaderFields()).print();
                    switch (conn.getResponseCode()) {
                        case HttpURLConnection.HTTP_OK:
                        case HttpURLConnection.HTTP_PARTIAL:
                            this.conn = conn;
                            break;
                        case HttpURLConnection.HTTP_MOVED_TEMP:
                            this.conn = redirect(header);
                            break;
                        default:
                            throw new ConnectException(conn.getResponseMessage());
                    }
                }
            }
            return conn;
        }

    }


    /**
     * 请求头
     */
    private static class Header {

        private final Map<String, String> params = new HashMap<>();

        public Header() {
            params.put("Accept-Ranges", "");
            params.put("Connection", "");
            params.put("Content-Length", "");
            params.put("Content-MD5", "");
            params.put("Content-Type", "");
            params.put("Date", "");
            params.put("ETag", "");
            params.put("Location", "");//重定向地址
            params.put("Content-Range", "");//断点续传 Content-Range : bytes 75725516-110759007/110759008
            params.put("Last-Modified", "");
            params.put("Server", "");
            params.put("X-Android-Received-Millis", "");
            params.put("X-Android-Response-Source", "");
            params.put("X-Android-Selected-Protocol", "");
            params.put("X-Android-Sent-Millis", "");
            params.put("x-oss-hash-crc64ecma", "");
            params.put("x-oss-object-type", "");
            params.put("x-oss-request-id", "");
            params.put("x-oss-server-time", "");
            params.put("x-oss-storage-class", "");
        }

        static Header parse(Map<String, List<String>> params) {
            Header header = new Header();
            Set<String> keySet = params.keySet();
            for (String key : keySet) {
                List<String> values = params.get(key);
                if (values != null) {
                    for (String v : values) {
                        header.params.put(key, v);
                    }
                }
            }
            return header;
        }

        String getRedirectUrl() {
            return Maps.get(params, "Location", "");
        }

        long getContentLength() {
            return TryCatch.tryCatch((TryCatch.OnTryCatch<Long>) () -> {
                String value = Maps.get(params, "Content-Length", "");
                if (!TextUtils.isEmpty(value)) {
                    return Long.parseLong(value);
                }
                return 0L;
            }).getOrElse(throwable -> 0L);
        }

        /**
         * 是否是断点续传
         */
        boolean hasRange() {
            return TryCatch.tryCatch(() -> {
                String value = Maps.get(params, "Content-Range", "");
                return !TextUtils.isEmpty(value);
            }).getOrElse(throwable -> false);
        }

        Header print() {
            Set<String> keySet = params.keySet();
            for (String key : keySet) {
                L.d(TAG + " HEAD " + key + " : " + params.get(key));
            }
            return this;
        }

    }

    private final static String TAG = "VERSE-DOWNLOAD::";

}
