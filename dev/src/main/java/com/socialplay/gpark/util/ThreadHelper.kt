package com.socialplay.gpark.util

import android.os.Looper
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

object ThreadHelper {
    private val mainScope = MainScope()
    fun isMainThread(): Boolean {
        return Looper.myLooper() == Looper.getMainLooper()
    }

    fun runOnUiThread(run: () -> Unit) {
        if (isMainThread()) {
            run()
        } else {
            mainScope.launch {
                run()
            }
        }
    }

    /**
     * 切回主线程进行 ui 操作时, 可能ui已被销毁, 导致闪退
     */
    fun runOnUiThreadCatching(run: () -> Unit) {
        if (isMainThread()) {
            run()
        } else {
            mainScope.launch {
                runCatching { run() }
            }
        }
    }
}