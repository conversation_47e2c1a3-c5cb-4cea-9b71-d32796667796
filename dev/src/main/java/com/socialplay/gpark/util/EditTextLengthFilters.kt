package com.socialplay.gpark.util

import android.text.InputFilter
import android.text.Spanned
import kotlin.math.min

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2022/9/9 4:31 下午
 * @describe:
 */
class EditTextLengthFilters(max: Int) : InputFilter {
    private val mMax: Int = max

    override fun filter(source: CharSequence, start: Int, end: Int, dest: Spanned, dstart: Int, dend: Int): CharSequence? {
        val mSourceLength = StringUtil.getStringCharCount(source.toString())
        val mDestSpannedLength = StringUtil.getStringCharCount(dest.toString())
        var keep = mMax - mDestSpannedLength

        val handle= if (keep <= 0) {
            ""
        } else if (keep >= mSourceLength) {
            null
        } else {
            var subStr = ""
            val len = min(keep, source.length)
            (0 until len).forEach {
                val char = source[it]
                val charLen = StringUtil.getStringCharCount(char.toString())
                if (charLen + StringUtil.getStringCharCount(subStr) <= keep) subStr += char
                if (StringUtil.getStringCharCount(subStr) >= keep) {
                    return@forEach
                }
            }
            subStr
        }
        return handle
        //        var keep = mMax - (dest.length - (dend - dstart)) //剩余可以输入的字数
        //        Timber.d("xxaswf filter $keep ; $source $start $end ; $dest $dstart $dend ")
        //        return if (keep <= 0) {
        //            "" //如果没有剩余可输入字数   则保持原有文本不变
        //        } else if (keep >= end - start) {
        //            // keep original   如果剩余可输入字数大于当前正在输入的字数    则返回当前输入
        //            //（相当于 return source，不过不建议这么写）
        //            null
        //        } else { //如果当前输入大于剩余可输入字数，则截取当前输入的剩余可输入字数的字符作为返回值
        //            keep += start
        //            if (Character.isHighSurrogate(source[keep - 1])) {
        //                --keep
        //                if (keep == start) {
        //                    return ""
        //                }
        //            }
        //            source.subSequence(start, keep)
        //        }
    }

    /**
     * @return the maximum length enforced by this input filter
     */
    fun getMax(): Int {
        return mMax
    }

}

interface MaxLengthFilterCallBack {
    fun onMaxLength(maxLength: Int)
}
