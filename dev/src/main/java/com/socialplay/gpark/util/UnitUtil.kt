package com.socialplay.gpark.util

import android.content.Context
import com.socialplay.gpark.R
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.DecimalFormat
import kotlin.math.pow


object UnitUtil {
    private const val ONE_KB = 1024
    private const val ONE_M = 1024 * ONE_KB
    private const val ONE_G = 1024 * ONE_M

    private const val TEN = 10L
    private const val HUNDRED = 100L
    private const val THOUSAND = 1_000L
    private const val TEN_THOUSAND = 10_000L
    const val MILLION = 1_000_000L
    private const val BILLION = 1_000_000_000L

    private const val MIN = 60
    const val HOUR = 3600

    fun formatSize(value: Long): String {
        return if (value >= ONE_G) {
            getStringByGlobal(R.string.x_gb_short_cap, formatNumber(value.toDouble() / ONE_G, 2))
        } else if (value >= ONE_M) {
            getStringByGlobal(R.string.x_mb_short_cap, formatNumber(value.toDouble() / ONE_M, 2))
        } else {
            getStringByGlobal(R.string.x_kb_short_cap, formatNumber(value.toDouble() / ONE_KB, 2))
        }
    }

    private fun formatNumber(value: Double, length: Int): String {
        val unit = 10.0.pow(length.toDouble())
        val result: Long = (value * unit).toLong()
        val value = result.toDouble() / unit
        return BigDecimal(value.toString()).toPlainString()
    }

    /**
     * 秒 -> 小时
     */
    fun formatPlayTime(context: Context, secondValue: Long): String {
        val hour = 60 * 60
        if (secondValue == 0L) {
            return ""
        }
        val hourStr = String.format("%.1f", secondValue / hour.toDouble())
        return if (hourStr == "1.0") {
            context.getString(R.string.x_hour, hourStr)
        } else {
            context.getString(R.string.x_hours, hourStr)
        }
    }

    @Deprecated("Use UnitUtilWrapper.formatPlayDate instead")
    fun formatPlayTimeV2(context: Context, sec: Long): String {
        val result = StringBuilder()
        val residual = sec % HOUR
        val hour = sec / HOUR
        if (hour > 0) {
            result.append(context.getString(R.string.x_hour_shorthand, hour.toString())).append(" ")
        }
        val min = residual / 60
        result.append(context.getString(R.string.x_minute_shorthand, min.toString()))
        return result.toString()
    }

    /**
     * 统一的数字转换方法
     */
    fun formatKMCount(
        num: Long, kCaps: Boolean = true, mCaps: Boolean = true, startFromK: Boolean = true
    ): String {
        return when {
            num >= BILLION -> {
                getStringByGlobal(
                    if (mCaps) R.string.x_more_than_million_short_cap else R.string.x_more_than_million_short,
                    "999.9"
                )
            }

            num >= MILLION -> {
                val millionStr = String.format("%.1f", num.toDouble() / MILLION)
                getStringByGlobal(
                    if (mCaps) R.string.x_million_short_cap else R.string.x_million_short,
                    millionStr
                )
            }

            (startFromK && num > THOUSAND) || (!startFromK && num >= TEN_THOUSAND) -> {
                val thousandStr = String.format("%.1f", num.toDouble() / THOUSAND)
                getStringByGlobal(
                    if (mCaps) R.string.x_thousand_short_cap else R.string.x_thousand_short,
                    thousandStr
                )
            }

            else -> num.toString()
        }
    }

    /**
     * 统一的数字转换方法, 保留两位小数
     */
    fun formatKMCount2(
        num: Long, kCaps: Boolean = true, mCaps: Boolean = true, startFromK: Boolean = true
    ): String {
        return when {
            num >= BILLION -> {
                getStringByGlobal(
                    if (mCaps) R.string.x_more_than_million_short_cap else R.string.x_more_than_million_short,
                    "999.9"
                )
            }

            num >= MILLION -> {
                val millionStr = String.format(
                    "%.2f", num.toDouble() / MILLION
                )
                getStringByGlobal(
                    if (mCaps) R.string.x_million_short_cap else R.string.x_million_short,
                    millionStr
                )
            }

            (startFromK && num > THOUSAND) || (!startFromK && num >= TEN_THOUSAND) -> {
                val thousandStr = String.format("%.2f", num.toDouble() / THOUSAND)
                getStringByGlobal(
                    if (mCaps) R.string.x_thousand_short_cap else R.string.x_thousand_short,
                    thousandStr
                )
            }

            else -> num.toString()
        }
    }

    /**
     * 保留两位小数, 不四舍五入
     */
    fun formatScore(num: Double): String {
        val format = DecimalFormat("#.##")
        format.roundingMode = RoundingMode.FLOOR
        return format.format(num)
    }

    fun formatPlayerCount(num: Long): String {
        return if (num == 1L) {
            getStringByGlobal(R.string.x_player, formatKMCount(num))
        } else {
            getStringByGlobal(R.string.x_players, formatKMCount(num))
        }
    }

    fun Float.formatScore(): String {
        return try {
            String.format("%.1f", this)
        } catch (e: Exception) {
            "0.0"
        }
    }

    /**
     * 统一的数字转换方法
     */
    fun formatBalanceKMCount(
        num: Long, kCaps: Boolean = true, mCaps: Boolean = true, startFromK: Boolean = true
    ): String {
        return when {
            num >= BILLION -> {
                getStringByGlobal(
                    if (mCaps) R.string.x_more_than_million_short_cap else R.string.x_more_than_million_short,
                    "999.9"
                )
            }

            num >= MILLION -> {
                val millionStr = String.format(
                    "%.2f", num.toDouble() / MILLION
                )
                getStringByGlobal(
                    if (mCaps) R.string.x_million_short_cap else R.string.x_million_short,
                    millionStr
                )
            }

            (startFromK && num > THOUSAND) || (!startFromK && num >= TEN_THOUSAND) -> {
                val thousandStr = String.format("%.2f", num.toDouble() / THOUSAND)
                getStringByGlobal(
                    if (mCaps) R.string.x_thousand_short_cap else R.string.x_thousand_short,
                    thousandStr
                )
            }

            else -> num.toString()
        }
    }

}