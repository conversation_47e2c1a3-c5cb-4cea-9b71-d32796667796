package com.socialplay.gpark.util.ime

import android.annotation.SuppressLint
import android.os.Build
import android.view.View
import android.view.Window
import android.view.WindowInsetsController
import android.view.WindowManager
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.lifecycle.LifecycleOwner
import com.socialplay.gpark.util.extension.observeOnMainThreadWhenNotDestroyed

class WindowInsetsHelper {
    private var imeInsetsPopupWindow: IMEInsetsPopupWindow? = null
    private var window: Window? = null
    private var softInputModeBackup: Int? = null
    // private var decorFitsSystemWindowsBackup = false

    fun apply(
        owner: LifecycleOwner,
        window: Window,
        rootView: View,
        keyboardListener: KeyboardListener
    ) {
        owner.observeOnMainThreadWhenNotDestroyed(
            register = {
                apply(window, rootView, keyboardListener)
            },
            unregister = {
                unApply()
            }
        )
    }

    @SuppressLint("ObsoleteSdkInt")
    fun apply(
        window: Window,
        rootView: View,
        keyboardListener: KeyboardListener
    ) {
        this.window = window

        softInputModeBackup = window.attributes.softInputMode
        // 这里将 softInputMode 改成了 adjustNothing, 表示键盘弹起时, 页面内容不做任何变化
        // 而由App来监听键盘的高度变化, 然后调整页面的布局
        // 问题:
        // 在 Android 10 及以下时, 设置 adjustNothing 之后, 通过 ViewCompat.setWindowInsetsAnimationCallback 就监听不到键盘的高度了
        // 而设置为 adjustUnspecified 之后就正常
        // adjustUnspecified 是系统默认行为, 在不同的的手机上可能会表现出不一样的效果
        // 有的手机表现出 adjustNothing 的效果, 有的手机表现出 adjustPan 的效果(暂未发现表现出 adjustResize 的设备), 界面不好处理
        // 所以采用 adjustNothing
        // 在 Android 11 及以上时, 通过 ViewCompat.setWindowInsetsAnimationCallback 监听键盘高度
        // 在 Android 10 及以下时, 通过不可见的 popupWindow 监听键盘高度
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)

//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
//            window.insetsController?.apply {
//                decorFitsSystemWindowsBackup =
//                    systemBarsBehavior == WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
//            }
//        } else {
//            decorFitsSystemWindowsBackup = window.decorView.fitsSystemWindows
//        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // 设置为边到边后, 控件将会显示在顶部状态栏和底部导航栏的下面
            WindowCompat.setDecorFitsSystemWindows(window, false)

            // IMEInsetsCallback 内部会调整 rootView 的 padding, 以防止 rootView 被遮挡
            val imeInsetsCallback = IMEInsetsCallback(keyboardListener)
            ViewCompat.setOnApplyWindowInsetsListener(rootView, imeInsetsCallback)
            ViewCompat.setWindowInsetsAnimationCallback(rootView, imeInsetsCallback)
        } else {
            // 兼容方案
            rootView.post {
                val imeInsetsPopupWindow = IMEInsetsPopupWindow(rootView, keyboardListener)
                imeInsetsPopupWindow.listenKeyboard()
                this.imeInsetsPopupWindow = imeInsetsPopupWindow
            }
        }
    }

    /**
     * apply 方法调用了 WindowCompat.setDecorFitsSystemWindows(window, true)
     * unApply 时, 需要将其恢复
     * 目前没办法准确读取读取 decorFitsSystemWindows 值, 所以需要由外部传入才行
     */
    fun unApply(decorFitsSystemWindows: Boolean = true) {
        // 将之前对window的修改进行恢复
        window?.also { window ->
            if (softInputModeBackup != null) {
                window.setSoftInputMode(softInputModeBackup!!)
            }
//            WindowCompat.setDecorFitsSystemWindows(window, decorFitsSystemWindowsBackup)
            WindowCompat.setDecorFitsSystemWindows(window, decorFitsSystemWindows)
        }
        window = null
        imeInsetsPopupWindow?.release()
    }
}