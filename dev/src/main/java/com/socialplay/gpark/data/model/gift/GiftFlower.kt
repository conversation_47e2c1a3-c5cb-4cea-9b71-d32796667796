package com.socialplay.gpark.data.model.gift

import android.os.Parcelable
import com.socialplay.gpark.util.toIntOrZero
import kotlinx.parcelize.Parcelize

data class GiftFlowers(
    val list: List<GiftFlower>? = null
)

data class GiftFlower(
    /**
     * 商品id
     */
    val id: Long?,
    /**
     * 商品名称
     */
    val name: String?,
    /**
     * 业务场景编号（内购、联运游戏支付、代币充值、免广告充值）
     */
    val sceneCode: Int?,
    /**
     * 商品类型【枚举  1：免广告】
     */
    val type: Int?,
    /**
     * 是否连续（0:单次，1:持续性包月等套餐）
     */
    val ceaseless: Boolean?,
    /**
     * 原价（单位：分）
     */
    val originalPrice: Long?,
    /**
     * 卖价（单位：分）
     */
    val price: Long?,
    /**
     * 二期：乐币奖励
     */
    val leCoinReward: Long?,
    /**
     * 送钥匙数
     */
    val keyReward: Long?,
    /**
     * 角标文案
     */
    val cornerText: String?,
    /**
     * 预留业务透传字段，后期通过优惠券实现变价可加入此字段
     */
    val attachJson: String?,
    /**
     * 0:普通会员商品，1:限时优惠商品
     */
    val mark: Int?,
    /**
     * 商品图标
     */
    val icon: String?,
    /**
     * 商品渠道标识
     */
    val pack: String?,
    /**
     * 礼物基本个数
     */
    val baseNum: Int?,
) {
    fun getSingleGiftPrice(): Long {
        return price ?: originalPrice ?: 0
    }
}
/*
{
  "ceaseless": false,
  "originalPrice": 100,
  "goodsId": "",
  "icon": "https://ali-oss-test.233leyuan.com/test1010/whej5ffdQxcH1747193293689.gif",
  "show": true,
  "attachJson": "{}",
  "type": 0,
  "pack": "233party",
  "sceneCode": 175,
  "price": 100,
  "name": "花",
  "id": 123606,
  "mark": 0
}
*/

data class SendGiftConditionsInfoResult(
    /**
     * 赠礼配置
     */
    val giveaway: SendGiftConditionsInfo?
)

@Parcelize
data class SendGiftConditionsInfo(
    /**
     * 开关，true：打开，false：关闭
     */
    val flag: Boolean?,
    /**
     * 是否满足赠礼条件，true：满足，false：不满足
     */
    val met: Boolean?,
    /**
     * 赠礼条件
     */
    val conditions: List<SendGiftCondition>?,
) : Parcelable {
    fun isEnable(): Boolean {
        return flag == true
    }
}

@Parcelize
data class SendGiftCondition(
    /**
     * 条件描述
     */
    val name: String?,
    /**
     * 完成状态，0：未完成，1：已完成
     */
    val status: Int?,
    /**
     * 跳转配置
     */
    val schema: GiftConditionSchema?,
) : Parcelable {
    fun isComplete(): Boolean {
        return status == 1
    }
}

@Parcelize
data class GiftConditionSchema(
    /**
     * 跳转按钮文案
     */
    val name: String?,
    /**
     * 跳转链接
     */
    val link: String?,
) : Parcelable

/**
 * 送礼物的信息
 */
data class SendGiftData(
    /**
     * 送礼人的用户id
     */
    val uid: String?,
    /**
     * 送礼人的用户昵称
     */
    val nickname: String?,
    /**
     * 送礼人的头像
     */
    val portrait: String?,
    /**
     * 送礼物的数量
     */
    val tippingCnt: String?,
    /**
     * 是否是我送的礼物(本地的字段)
     */
    var isMe: Boolean = false,
) {
    val sendGiftCount get() = tippingCnt.toIntOrZero
}