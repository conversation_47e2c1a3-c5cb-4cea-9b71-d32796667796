package com.socialplay.gpark.data.kv

import com.socialplay.gpark.data.model.TTaiConfig
import com.socialplay.gpark.function.share.ShareWrapper
import com.socialplay.gpark.util.property.MMKVScope
import com.socialplay.gpark.util.property.kvProperty
import com.tencent.mmkv.MMKV

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2022/05/05
 * desc   :
 * </pre>
 */


class TTaiKV(override val mmkv: MMKV) : MMKVScope {

    companion object {
        const val ID_SUGGEST_GAME_ID = 101
        const val ID_OPEN_SUGGESTION = 102
        const val ID_KEY_MW_GAME_PRE_DOWNLOAD = 121 // 游戏预下载
        const val ID_AD_CONFIG = 227    // 广告配置
        const val ID_GUIDE_CREATE_THEME_LIST_CONFIG = 311   // 新手引导-兴趣选择界面 json配置
        const val ID_SELECT_MODE_GAME_INFO = 1001
        const val ID_COMMUNITY_CIRCLE_ID = 1008 // 社区写死的游戏圈id
        const val ID_KEY_VIDEO_PUBLISH_CIRCLE_ID = 1527 // 视频流发布游戏圈ID
        const val ID_FEEDBACK_DISCORD = 1774    // 意见反馈discord的web链接
        const val ID_EDITOR_HOME_POPULAR_LOCKED = 2013  // 角编首页列表数据 popular 锁区模式下
        const val ID_EDITOR_HOME_POPULAR = 2014 // 角编首页列表数据 popular
        const val ID_EDITOR_HOME_MOMENTS = 2015 // 角编首页列表数据 moments
        const val ID_KEY_AI_BOT_LABEL = 2016
        const val ID_REPORT_REVIEW_REASON = 8015
        const val ID_REPORT_GROUP_CHAT_REASON = 20250417
        const val ID_REPORT_USER_REASON = 8016
        const val ID_DEFAULT_ROLE_LIST = 10086
        const val ID_CLEAN_TEMPLATE_ID_LIST = 10087 // 清理用模板id列表-备用
        const val ID_DEFAULT_ROLE_LIST_V2 = 10091
        const val ID_LINK_PROFILE = 11011   // 主页link平台-支持多url校验（原单URL校验：19951108）
        const val ID_KEY_ID_GAME_SHARE_CONFIG = ShareWrapper.TTAI_ID_GAME_SHARE_CONFIG
        const val ID_KEY_ID_SCENE_SHARE_CONFIG = ShareWrapper.TTAI_ID_SCENE_SHARE_CONFIG
        const val ID_APP_STORE_REVIEW_LOCK = 24105  // 上架审核锁
        const val ID_EDITOR_ROLE_GAME_LOAD_TIPS = 30010 // 角色编辑器加载过程中的提示语
        const val ID_EDITOR_ROLE_GAME_LOAD_ART_IMGS = 30011 // 角色编辑器加载过程中轮播图
        const val ID_AVATAR_SAVE_SHARE_BACKGROUND_LIST = 30040  // 角色保存分享弹窗背景列表配置
        const val ID_QR_TRUST_DOMAIN_NAME = 100000
        const val ID_SYSTEM_NOTIFICATION = 100001
        const val ID_GAME_REVIEW_FAST_TAG = 100101
        const val ID_EDITOR_PLAZA_INFO = 100104
        const val ID_MIGRATE_EDITOR_TEMPLATE_MAP = 100303    // 国内外迁移工程文件对照表
        const val ID_TEMPLATE_ID_LIST = 100330  // 清理用模板id列表
        const val ID_UGC_SINGLE_MODE = 100333 // 单模板UGC建造页面
        const val ID_DRAW_TEMPLATE_ID = 1000336 // 画板模板id
        const val ID_ADD_TEMPLATE = 100338 // 底栏加号模板配置
        const val ID_PING_DOMAIN_LIST = 100501  // 需要Ping的域名列表
        const val ID_QUICK_INPUT_CONFIG = 117001    // 评论输入弹窗-快捷输入行 json 配置
        const val ID_AGE_LIMIT = 187807
        const val ID_HOME_ROOM_GAME = 187818    // 首页获取房间列表的游戏信息
        const val ID_SUGGEST_MALE_FEMALE_GAME_ID = 187850
        const val ID_GAME_REVIEW_CONFIG = 202377
        const val ID_RECORD_SHARE_PLATFORM_CONFIG = 231008  // 录屏结束后第三方平台账号和链接配置
        const val ID_KEY_POSITIVE_COMMENT = 240816  // 好评引导
        const val ID_SEARCH_FILTER = 240825
        const val ID_ROOM_TAG = 253001  // 创建聊天房时候的供选择的TAG
        const val ID_DS_ROOM_DETAIL = 800003    // 小屋详情页背景
        const val ID_DS_ROOM_ITEM = 800004
        const val ID_PGC_GAME_LIST = 1314520
        const val ID_TS_GAME_PRELOAD_AD_CONFIG = 20240106   // 启动游戏预加载广告的列表配置
        const val ID_KEY_AI_BOT_CREATE_COUNT = 20240826
        const val ID_IM_TIPS_TIME = 20240913
        const val ID_REPORT_LIBRARY_REASON = 20241107
        const val ID_OAID_CERT_ID = 20250225    // OAID 证书字符串
        const val ID_MODULE_TEMPLATE_TEMPLATE_ID = 20250311
        const val ID_UGC_ASSET_FEED_NOTICE = 20250330   // 资源feed公告
        const val ID_REALNAME_APP_LOCK = 20250408   // 可以针对渠道的审核锁，主要锁未成年实名，不让进App
        const val ID_SHARE_CONFIG = 25040201   // 可以针对渠道的审核锁，主要锁未成年实名，不让进App

        const val ID_MODULE_GUIDE_ID = 20250331
        const val ID_GUIDE_COMMENT_DIALOG_ID = 20250416 // 引导去商店评论弹框
    }

    val qRTrustedDomainValue by kvProperty(com.socialplay.gpark.BuildConfig.TRUST_QR_HOST_DEFAULT, getKeyById(ID_QR_TRUST_DOMAIN_NAME))

    val gameReviewFastTag by kvProperty("", getKeyById(ID_GAME_REVIEW_FAST_TAG))

    var systemNotification by kvProperty<String?>(null, getKeyById(ID_SYSTEM_NOTIFICATION))

    var suggestGameIdList by kvProperty<String?>(null, getKeyById(ID_SUGGEST_GAME_ID))
    var suggestGameInfoListText by kvProperty<String?>(null, getKeyById(ID_SUGGEST_MALE_FEMALE_GAME_ID))

    val plazaInfo by kvProperty("", getKeyById(ID_EDITOR_PLAZA_INFO))

    val reportUserReason by kvProperty("", getKeyById(ID_REPORT_USER_REASON))
    val reportReviewReason by kvProperty("", getKeyById(ID_REPORT_REVIEW_REASON))
    val reportGroupChatReason by kvProperty("", getKeyById(ID_REPORT_GROUP_CHAT_REASON))
    val reportLibraryReason by kvProperty("", getKeyById(ID_REPORT_LIBRARY_REASON))
    val defaultRoleList by kvProperty("", getKeyById(ID_DEFAULT_ROLE_LIST))
    val defaultRoleListV2 by kvProperty("", getKeyById(ID_DEFAULT_ROLE_LIST_V2))

    val openSuggestionGame by kvProperty("", getKeyById(ID_OPEN_SUGGESTION))

    val editorMigrateMap by kvProperty("", getKeyById(ID_MIGRATE_EDITOR_TEMPLATE_MAP))

    val gameReviewConfig by kvProperty("", getKeyById(ID_GAME_REVIEW_CONFIG))

    val homeRoomGame by kvProperty("", getKeyById(ID_HOME_ROOM_GAME))
    val roomTags by kvProperty("", getKeyById(ID_ROOM_TAG))
    val ageLimit by kvProperty("", getKeyById(ID_AGE_LIMIT))
    val feedbackDiscord by kvProperty("", getKeyById(ID_FEEDBACK_DISCORD))
    val recordSharePlatformConfig by kvProperty("", getKeyById(ID_RECORD_SHARE_PLATFORM_CONFIG))

    val roleGameLoadTips by kvProperty("", getKeyById(ID_EDITOR_ROLE_GAME_LOAD_TIPS))
    val roleGameLoadArtImgs by kvProperty("", getKeyById(ID_EDITOR_ROLE_GAME_LOAD_ART_IMGS))

    // 区分环境
    val communityCircleId by kvProperty("", getKeyById(ID_COMMUNITY_CIRCLE_ID))

    // 视频发布游戏圈ID
    val videoPublishCircleId by kvProperty("", getKeyById(ID_KEY_VIDEO_PUBLISH_CIRCLE_ID))

    val dsDetail by kvProperty("", getKeyById(ID_DS_ROOM_DETAIL))
    val dsItem by kvProperty("", getKeyById(ID_DS_ROOM_ITEM))
    val preloadAdGameItems by kvProperty("", getKeyById(ID_TS_GAME_PRELOAD_AD_CONFIG))
    val adConfig by kvProperty("", getKeyById(ID_AD_CONFIG))
    val selectModeGameInfo by kvProperty("", getKeyById(ID_SELECT_MODE_GAME_INFO))
    val aiBotList by kvProperty("", getKeyById(ID_KEY_AI_BOT_LABEL))
    val imTips by kvProperty("", getKeyById(ID_IM_TIPS_TIME))
    val oaidCert by kvProperty("", getKeyById(ID_OAID_CERT_ID))
    val guideCreateThemeConfig by kvProperty("", getKeyById(ID_GUIDE_CREATE_THEME_LIST_CONFIG))
    val quickInputConfig by kvProperty("", getKeyById(ID_QUICK_INPUT_CONFIG))
    val moduleGuideConfig by kvProperty("", getKeyById(ID_MODULE_GUIDE_ID))
    val guideCommentDialog by kvProperty("", getKeyById(ID_GUIDE_COMMENT_DIALOG_ID))

    fun saveConfigs(configs: List<TTaiConfig>) {
        if (configs.isEmpty()) return
        for (config in configs) {
            saveConfig(config)
        }
    }

    fun saveConfig(config: TTaiConfig) {
        mmkv.putString(getKeyById(config.id), config.value)
    }

    fun getConfig(id: Int, default: String = ""): String {
        return mmkv.getString(getKeyById(id), default).orEmpty()
    }

    fun removeConfig(id: Int) {
        mmkv.remove(getKeyById(id))
    }

    private fun getKeyById(id: Int): String {
        return "ttai_key_id_$id"
    }
}