package com.socialplay.gpark.data.kv

import com.tencent.mmkv.MMKV

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2021/06/24
 *     desc   : 好友KV
 */
class FriendKV(private val mmkv: MMKV, private val accountKV: AccountKV) {

    companion object {
        //好友列表缓存
        private const val KEY_FRIEND_LIST_PREFIX = "key_friend_list_"
        // 最新好友列表缓存
        private const val KEY_NEWEST_FRIEND_LIST_PREFIX = "key_newest_friend_list"
        // 小屋功能是否24小时内邀请过好友
        private const val KEY_HAS_INVITE_FRIEND_IN_ONE_DAY = "key_has_invite_friend_in_one_day_"
        // 小屋功能是否24小时内添加过好友
        private const val KEY_HAS_ADD_FRIEND_IN_ONE_DAY = "key_has_add_friend_in_one_day_"
    }

    fun updateHutFriendAddTime(otherUuid: String, time: Long) {
        mmkv.putLong("$KEY_HAS_ADD_FRIEND_IN_ONE_DAY${accountKV.uuid}_${otherUuid}", time)
    }

    fun getHutFriendAddTime(otherUuid: String): Long {
        return mmkv.getLong("$KEY_HAS_ADD_FRIEND_IN_ONE_DAY${accountKV.uuid}_${otherUuid}", 0)
    }

    fun updateHutFriendInvitedTime(otherUuid: String, time: Long) {
        mmkv.putLong("$KEY_HAS_INVITE_FRIEND_IN_ONE_DAY${accountKV.uuid}_${otherUuid}", time)
    }

    fun getHutFriendInvitedTime(otherUuid: String): Long {
        return mmkv.getLong("$KEY_HAS_INVITE_FRIEND_IN_ONE_DAY${accountKV.uuid}_${otherUuid}", 0)
    }

    fun getFriendList(uuid: String): String? {
        return mmkv.getString("$KEY_FRIEND_LIST_PREFIX${uuid}", null)
    }

    fun saveFriendList(uuid: String, value: String) {
        mmkv.putString("$KEY_FRIEND_LIST_PREFIX${uuid}", value)
    }

    fun getNewestFriendWithStateFromLocal(uuid: String): String? {
        return mmkv.getString("$KEY_NEWEST_FRIEND_LIST_PREFIX${uuid}", null)
    }

    fun saveNewestFriendWithStateToLocal(uuid: String, value: String) {
        mmkv.putString("$KEY_NEWEST_FRIEND_LIST_PREFIX${uuid}", value)
    }

}