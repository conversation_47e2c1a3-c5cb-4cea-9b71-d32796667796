package com.socialplay.gpark.data.api

import com.google.gson.JsonElement
import com.meta.biz.mgs.data.model.MgsPlayerInfo
import com.meta.biz.mgs.data.model.MgsUserInfo
import com.meta.biz.ugc.model.EditorTemplate
import com.meta.box.biz.friend.model.FriendInfo
import com.socialplay.gpark.data.base.ApiResult
import com.socialplay.gpark.data.base.PagingApiResult
import com.socialplay.gpark.data.model.BodyRequestOrder
import com.socialplay.gpark.data.model.GameRoomList
import com.socialplay.gpark.data.model.GameRoomStatus
import com.socialplay.gpark.data.model.GameSuggestionInfo
import com.socialplay.gpark.data.model.HomeCustomRecommend
import com.socialplay.gpark.data.model.HomeMapsNewest
import com.socialplay.gpark.data.model.HomeRecommend
import com.socialplay.gpark.data.model.HomeRecommendOperation
import com.socialplay.gpark.data.model.HomeRecommendOperationBody
import com.socialplay.gpark.data.model.MWCreateOrderResult
import com.socialplay.gpark.data.model.MetaVerseVersions
import com.socialplay.gpark.data.model.PayOrderInfo
import com.socialplay.gpark.data.model.QrResult
import com.socialplay.gpark.data.model.RefreshApiTokenApiResult
import com.socialplay.gpark.data.model.ReviewGameInfo
import com.socialplay.gpark.data.model.SearchGameApiResult
import com.socialplay.gpark.data.model.SnsInfo
import com.socialplay.gpark.data.model.SysActivitiesInfo
import com.socialplay.gpark.data.model.SysHeaderInfo
import com.socialplay.gpark.data.model.TTaiConfig
import com.socialplay.gpark.data.model.UGGameSuggestionInfo
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.account.DeviceParamsRequest
import com.socialplay.gpark.data.model.account.EditUserInfoRequest
import com.socialplay.gpark.data.model.account.PrivacySwitch
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.data.model.account.UserCreateInfo
import com.socialplay.gpark.data.model.aibot.AIBotCreateImageResult
import com.socialplay.gpark.data.model.aibot.AIBotCreateRequest
import com.socialplay.gpark.data.model.aibot.AIBotCreateResult
import com.socialplay.gpark.data.model.aibot.AIBotGenerateInfo
import com.socialplay.gpark.data.model.aibot.AIBotStyle
import com.socialplay.gpark.data.model.aibot.AiBotConversationResult
import com.socialplay.gpark.data.model.aibot.AiBotFollowResult
import com.socialplay.gpark.data.model.aibot.AiBotListResult
import com.socialplay.gpark.data.model.aibot.BotInfo
import com.socialplay.gpark.data.model.aibot.BotInfoCreate
import com.socialplay.gpark.data.model.asset.MyUgcModulesResponse
import com.socialplay.gpark.data.model.auth.QRCodeAuthScanResult
import com.socialplay.gpark.data.model.choice.CardAll
import com.socialplay.gpark.data.model.choice.ChoiceCardListApiResult
import com.socialplay.gpark.data.model.choice.Recommend
import com.socialplay.gpark.data.model.choice.RecommendRequestBody
import com.socialplay.gpark.data.model.community.UserMuteStatus
import com.socialplay.gpark.data.model.creator.CreatorFrameResult
import com.socialplay.gpark.data.model.creator.RecommendCreatorRequest
import com.socialplay.gpark.data.model.creator.RecommendCreatorResult
import com.socialplay.gpark.data.model.creator.RecommendKolUgcResult
import com.socialplay.gpark.data.model.creator.RecommendUgcResult
import com.socialplay.gpark.data.model.creator.label.UgcPublishLabel
import com.socialplay.gpark.data.model.creator.morecreator.LabelCreatorRequest
import com.socialplay.gpark.data.model.creator.morecreator.LabelCreatorResult
import com.socialplay.gpark.data.model.creator.morecreator.TypeCreatorRequest
import com.socialplay.gpark.data.model.creator.morecreator.TypeCreatorResult
import com.socialplay.gpark.data.model.creator.moreugc.FollowedCreatorUgcRequest
import com.socialplay.gpark.data.model.creator.moreugc.FollowedCreatorUgcWrapper
import com.socialplay.gpark.data.model.editor.Balance
import com.socialplay.gpark.data.model.editor.DeleteRoleStyleRequest
import com.socialplay.gpark.data.model.editor.DeleteRoleStyleResponse
import com.socialplay.gpark.data.model.editor.EditorLocalStatusInfo
import com.socialplay.gpark.data.model.editor.EditorNotice
import com.socialplay.gpark.data.model.editor.FormworkList
import com.socialplay.gpark.data.model.editor.LightUpBody
import com.socialplay.gpark.data.model.editor.LikeRoleStyleRequest
import com.socialplay.gpark.data.model.editor.LikeRoleStyleResponse
import com.socialplay.gpark.data.model.editor.MyCreationsV3Request
import com.socialplay.gpark.data.model.editor.MyCreationsV4Request
import com.socialplay.gpark.data.model.editor.PgcGameDetailResponse
import com.socialplay.gpark.data.model.editor.PinPgcRequest
import com.socialplay.gpark.data.model.editor.PinUgcRequest
import com.socialplay.gpark.data.model.editor.QueryPositiveComment
import com.socialplay.gpark.data.model.editor.ReqFormWorkArchiveBody
import com.socialplay.gpark.data.model.editor.ReqFormWorkV4Body
import com.socialplay.gpark.data.model.editor.RoleOtherStyleListRequest
import com.socialplay.gpark.data.model.editor.RoleStyleListResponse
import com.socialplay.gpark.data.model.editor.SparkLightUpData
import com.socialplay.gpark.data.model.editor.TSTypeInfo
import com.socialplay.gpark.data.model.editor.UgcBannerInfo
import com.socialplay.gpark.data.model.editor.UgcDetailInfo
import com.socialplay.gpark.data.model.editor.UgcFormWorkArchiveData
import com.socialplay.gpark.data.model.editor.UgcFormWorkV4Data
import com.socialplay.gpark.data.model.editor.UgcGameConfig
import com.socialplay.gpark.data.model.editor.UgcGameDetailResponse
import com.socialplay.gpark.data.model.editor.UgcGameInfo
import com.socialplay.gpark.data.model.editor.UserBalanceRequestBody
import com.socialplay.gpark.data.model.editor.cloud.GidPkg
import com.socialplay.gpark.data.model.editor.cloud.ProjectLimit
import com.socialplay.gpark.data.model.editor.cloud.UgcBackupInfo
import com.socialplay.gpark.data.model.editor.cloud.UgcCloudProject
import com.socialplay.gpark.data.model.entity.GameDetailEntity
import com.socialplay.gpark.data.model.entity.HomePageEntity
import com.socialplay.gpark.data.model.feedback.FeedbackConfigItem
import com.socialplay.gpark.data.model.feedback.FeedbackRequest
import com.socialplay.gpark.data.model.feedback.SubmitNewFeedbackRequest
import com.socialplay.gpark.data.model.flower.FlowerLeaderboardData
import com.socialplay.gpark.data.model.flower.FlowerLeaderboardRequest
import com.socialplay.gpark.data.model.friend.CommonShareRequest
import com.socialplay.gpark.data.model.friend.CommonShareResult
import com.socialplay.gpark.data.model.friend.FriendSearchInfo
import com.socialplay.gpark.data.model.game.OperationList
import com.socialplay.gpark.data.model.gamedetail.LikeAndPlayerListData
import com.socialplay.gpark.data.model.gamedetail.LikeAndPlayerListRequest
import com.socialplay.gpark.data.model.gamereview.AddAppraiseReplyRequest
import com.socialplay.gpark.data.model.gamereview.AppraiseReplyListRequest
import com.socialplay.gpark.data.model.gamereview.AppraiseReplyListResult
import com.socialplay.gpark.data.model.gamereview.AttentionRequest
import com.socialplay.gpark.data.model.gamereview.CheckAppraisedRequest
import com.socialplay.gpark.data.model.gamereview.FindAppraiseRequest
import com.socialplay.gpark.data.model.gamereview.GameAppraiseData
import com.socialplay.gpark.data.model.gamereview.GameReviewResult
import com.socialplay.gpark.data.model.gamereview.GameScoreResult
import com.socialplay.gpark.data.model.gamereview.PublishAppraiseRequest
import com.socialplay.gpark.data.model.gamereview.RequestGameReviewsParam
import com.socialplay.gpark.data.model.gift.GetSendGiftUserRequestBody
import com.socialplay.gpark.data.model.gift.GiftFlowers
import com.socialplay.gpark.data.model.gift.SendGiftConditionsInfoResult
import com.socialplay.gpark.data.model.gift.SendGiftConditionsRequestBody
import com.socialplay.gpark.data.model.gift.SendGiftData
import com.socialplay.gpark.data.model.gift.SwitchSendGiftRequestBody
import com.socialplay.gpark.data.model.groupchat.GroupChatAddMembers
import com.socialplay.gpark.data.model.groupchat.GroupChatApplyInfoList
import com.socialplay.gpark.data.model.groupchat.GroupChatCount
import com.socialplay.gpark.data.model.groupchat.GroupChatDetailInfo
import com.socialplay.gpark.data.model.groupchat.GroupChatInfo
import com.socialplay.gpark.data.model.groupchat.GroupChatInfoPage
import com.socialplay.gpark.data.model.groupchat.GroupChatMembersPage
import com.socialplay.gpark.data.model.groupchat.GroupSimpleInfo
import com.socialplay.gpark.data.model.groupchat.MgsGetSimpleGroupInfoByImIdsRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupApplyPageRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatApplyJoinProcessRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatApplyJoinRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatCreateRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatEditMemberPowerRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatEditNotificationRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatEditRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatIdRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatInviteMembersRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatListRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatMembersRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatRemoveMemberRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatSearchRequest
import com.socialplay.gpark.data.model.im.HomeOperationNotification
import com.socialplay.gpark.data.model.im.ImInfo
import com.socialplay.gpark.data.model.im.ReviewTextRiskResult
import com.socialplay.gpark.data.model.im.RiskQueryResult
import com.socialplay.gpark.data.model.im.SystemNotificationList
import com.socialplay.gpark.data.model.im.request.CreateImgRiskCheckTaskRequest
import com.socialplay.gpark.data.model.im.request.IMCheckRequest
import com.socialplay.gpark.data.model.im.request.RiskTaskQueryRequest
import com.socialplay.gpark.data.model.member.MemberInfo
import com.socialplay.gpark.data.model.member.MemberRequest
import com.socialplay.gpark.data.model.mgs.MgsSceneConfig
import com.socialplay.gpark.data.model.moments.MomentsTemplate
import com.socialplay.gpark.data.model.moments.MomentsTemplateBody
import com.socialplay.gpark.data.model.moments.PlotListBody
import com.socialplay.gpark.data.model.moments.PlotMainList
import com.socialplay.gpark.data.model.outfit.ProfileCurrentCloth
import com.socialplay.gpark.data.model.outfit.UgcAssetEntrance
import com.socialplay.gpark.data.model.outfit.UgcAssetProfileEntrance
import com.socialplay.gpark.data.model.outfit.UgcDesignDetail
import com.socialplay.gpark.data.model.outfit.UgcDesignFeedResponse
import com.socialplay.gpark.data.model.outfit.UgcDesignProfileResponse
import com.socialplay.gpark.data.model.outfit.UgcDesignProfileTag
import com.socialplay.gpark.data.model.pay.CoinsRecordRequestBody
import com.socialplay.gpark.data.model.pay.CoinsRecords
import com.socialplay.gpark.data.model.pay.SubmitResult
import com.socialplay.gpark.data.model.pay.SubscribeProductInfo
import com.socialplay.gpark.data.model.pay.TakeOderResult
import com.socialplay.gpark.data.model.pay.TripartiteInfo
import com.socialplay.gpark.data.model.pay.UserBalance
import com.socialplay.gpark.data.model.plot.PlotTemplateList
import com.socialplay.gpark.data.model.post.CommunityBlock
import com.socialplay.gpark.data.model.post.CommunityFeedWrapper
import com.socialplay.gpark.data.model.post.CommunityTopicBlockWrap
import com.socialplay.gpark.data.model.post.IDevelopedPostCardRequest
import com.socialplay.gpark.data.model.post.OpinionRequestBody
import com.socialplay.gpark.data.model.post.PostCardResult
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostCommentListRequestBody
import com.socialplay.gpark.data.model.post.PostCommentRequestBody
import com.socialplay.gpark.data.model.post.PostDetail
import com.socialplay.gpark.data.model.post.PostOutfitVisitRequest
import com.socialplay.gpark.data.model.post.PostPublishResult
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.data.model.post.PostReplyListRequestBody
import com.socialplay.gpark.data.model.post.PostReplyRequestBody
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.PublishPostRequest
import com.socialplay.gpark.data.model.post.SearchPostCardRequest
import com.socialplay.gpark.data.model.post.request.AddPvBatchRequest
import com.socialplay.gpark.data.model.post.request.AddPvRequest
import com.socialplay.gpark.data.model.post.request.FetchBlockRequest
import com.socialplay.gpark.data.model.post.request.FetchTopicRequest
import com.socialplay.gpark.data.model.post.request.PostProfileFeedRequest
import com.socialplay.gpark.data.model.post.request.PostTagFeedRequest
import com.socialplay.gpark.data.model.post.request.QueryTopicsRequest
import com.socialplay.gpark.data.model.post.topic.TopicDetailInfo
import com.socialplay.gpark.data.model.profile.RelationCountResult
import com.socialplay.gpark.data.model.profile.RelationListResult
import com.socialplay.gpark.data.model.profile.UserProfileInfo
import com.socialplay.gpark.data.model.profile.friend.OthersFriendList
import com.socialplay.gpark.data.model.profile.recent.RecentPlayList
import com.socialplay.gpark.data.model.profile.recent.RecentPlayListV2Request
import com.socialplay.gpark.data.model.profile.recent.RecentPlayListV2Response
import com.socialplay.gpark.data.model.profile.request.RelationCountRequest
import com.socialplay.gpark.data.model.profile.request.RelationListRequest
import com.socialplay.gpark.data.model.qrcode.QrCodeCreateRequest
import com.socialplay.gpark.data.model.qrcode.QrCodeCreateResponse
import com.socialplay.gpark.data.model.qrcode.QrCodeResolveApiRequest
import com.socialplay.gpark.data.model.qrcode.QrCodeResolveApiResponse
import com.socialplay.gpark.data.model.qrcode.QrCodeResolveResponse
import com.socialplay.gpark.data.model.reportBlock.ReportRequest
import com.socialplay.gpark.data.model.room.CanJoinRoom
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.data.model.room.CottageRoomInfo
import com.socialplay.gpark.data.model.room.CottageRoomList
import com.socialplay.gpark.data.model.room.CottageVisitor
import com.socialplay.gpark.data.model.room.CreateRoomBody
import com.socialplay.gpark.data.model.room.GetRoomListBody
import com.socialplay.gpark.data.model.room.GetRoomListResult
import com.socialplay.gpark.data.model.room.House
import com.socialplay.gpark.data.model.room.RoomTemplate
import com.socialplay.gpark.data.model.sdk.AppAccessToken
import com.socialplay.gpark.data.model.sdk.AuthAppInfo
import com.socialplay.gpark.data.model.share.RelayData
import com.socialplay.gpark.data.model.share.RoBuxRecordInfo
import com.socialplay.gpark.data.model.task.DailyTaskInfo
import com.socialplay.gpark.data.model.task.FinishDailyTaskBody
import com.socialplay.gpark.data.model.user.AuthInfoApiResult
import com.socialplay.gpark.data.model.user.ContinueAccountInfo
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.RedBadgeData
import com.socialplay.gpark.data.model.user.RedBadgeRequest
import com.socialplay.gpark.data.model.user.ThirdBindRequest
import com.socialplay.gpark.data.model.user.ThirdLoginRequest
import com.socialplay.gpark.data.model.user.VisitorInfoApiResult
import com.socialplay.gpark.data.model.videofeed.RecommendVideoFeedListRequest
import com.socialplay.gpark.data.model.videofeed.VideoFeedApiResult
import com.socialplay.gpark.function.mw.bean.MWGameResourceRequest
import com.socialplay.gpark.function.mw.bean.MWGameResourceResponse
import com.socialplay.gpark.function.mw.bean.MWLaunchGameExpand
import com.socialplay.gpark.function.mw.bean.MWLaunchMgsInfo
import retrofit2.http.Body
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.HeaderMap
import retrofit2.http.POST
import retrofit2.http.Query
import retrofit2.http.QueryMap
import retrofit2.http.Url

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/10
 * desc   :
 * </pre>
 */


interface MetaApi {

    /**
     * 根据id获取游戏详情
     */
    @GET("/repository/v3/game/info")
    suspend fun getGameInfoByGameId(@Query("id") id: String): ApiResult<GameDetailEntity>


    @GET("/repository/v3/home/<USER>")
    suspend fun getHomePage(
        @Query("sinceId") sinceId: String?,
        @Query("pageSize") pageSize: Int = 20
    ): ApiResult<ArrayList<HomePageEntity>>

    @POST("common/search/v1/query/by/keyword")
    @JvmSuppressWildcards
    suspend fun getSearchGameInfo(
        @Body keyword: Map<String, Any?>,
    ): ApiResult<SearchGameApiResult>

    @POST("common/search/v1/query/by/association")
    @JvmSuppressWildcards
    suspend fun getSearchGameInfoById(
        @Body keyword: Map<String, Any?>,
    ): ApiResult<SearchGameApiResult>

    @POST("common/search/v1/association/query")
    @JvmSuppressWildcards
    suspend fun getSearchRelate(
        @Body keyword: Map<String, Any?>,
    ): ApiResult<SearchGameApiResult>

    @POST("common/search/v1/query/client/report")
    @JvmSuppressWildcards
    suspend fun postSearchReport(
        @Body map: Map<String, Any?>,
    ): ApiResult<SearchGameApiResult>

    /**
     * 双token：游客登录，token为空比较好（防止后端网关直接拦截报401）
     * https://mock.metaapp.cn/project/811/interface/api/45049
     */
    @POST("authorize/v2/dtoken/visitor/sign")
    suspend fun visitorLogin(): ApiResult<VisitorInfoApiResult>

    /**
     * 废弃：刷新单token
     */
    @Deprecated("Abandon the method of using single token")
    @POST("/authorize/v2/token/renew")
    suspend fun refreshApiToken(): ApiResult<RefreshApiTokenApiResult>

    /**
     * 双token：三方登录
     * https://mock.metaapp.cn/project/811/interface/api/45047
     */
    @POST("/authorize/v2/dtoken/user/sign")
    suspend fun loginByThird(@Body request: ThirdLoginRequest): ApiResult<AuthInfoApiResult>

    /**
     * 获取用户信息
     *
     */
    @POST("/user/v2/self")
    suspend fun getMetaUserInfo(): ApiResult<MetaUserInfo>

    @POST("/authorize/v2/third/binding")
    suspend fun bindByThird(@Body thirdBindRequest: ThirdBindRequest): ApiResult<Boolean>

    @POST("/authorize/v2/third/unbinding")
    suspend fun unBindByThird(@Body map: Map<String, String>): ApiResult<Boolean>

    @POST("/authorize/v2/email/modify")
    suspend fun bindEmailChange(@Body map: Map<String, String>): ApiResult<Boolean>

    /**
     * 获取二维码
     */
    @GET("/qr/v2/myself")
    suspend fun getQrCode(): ApiResult<String>

    /**
     * 获取融云/阿里云/腾讯云/七牛 key and token
     * https://mock.metaapp.cn/project/189/interface/api/29303
     * @return
     */
    @GET("/chat/v1/token/take")
    suspend fun getImInfo(): ApiResult<ImInfo>

    /**
     * 查询好友信息
     */
    @GET("buddy/v2/info/detail")
    suspend fun queryFriendInfo(@Query("friendId") uuid: String): ApiResult<FriendInfo>

    /**
     * 好友搜索
     */
    @GET("/user/v2/find")
    suspend fun searchFriends(
        @Query("keyword") keyword: String,
        @Query("pageNum") pageNum: Int,
        @Query("pageSize") pageSize: Int
    ): ApiResult<PagingApiResult<FriendSearchInfo>>

    /**
     * 用户游戏心跳上报
     */
    @POST("/player/game/v2/beat/submit")
    suspend fun updateUserPlayGame(@Body map: Map<String, String>): ApiResult<String?>

    /**
     * 开始游戏（游戏切到前台）上报
     */
    @POST("/player/game/v2/game/begin")
    suspend fun updateUserStartGame(@Body map: Map<String, String>): ApiResult<String?>

    /**
     * 结束游戏（游戏切到后台）上报
     */
    @POST("/player/game/v2/game/stop")
    suspend fun updateUserStopGame(@Body map: Map<String, String>): ApiResult<String?>

    /**
     * 用uuid查询用户资料卡片
     */
    @GET("/user/v2/homepage")
    suspend fun getPlayerInfoByUuId(@Query("gameId") gameId: String, @Query("uid") openId: String): ApiResult<MgsPlayerInfo>


    /**
     * 获取MW引擎列表
     */
    @GET
    suspend fun getMWEngineVersions(@Url url: String): MetaVerseVersions

    /**
     * 扫码登录：扫码
     */
    @GET
    suspend fun requestScanQRCode(@Url url: String): ApiResult<QRCodeAuthScanResult>

    /**
     * 扫码登录：确认登录
     */
    @GET
    suspend fun confirmLogin(@Url url: String): ApiResult<Any>


    // 根据审核密文(id)查询单个游戏信息
    @GET("/developer/v3/previewer/info")
    suspend fun searchReviewGameById(@Query("code") code: String): ApiResult<ReviewGameInfo>

    /**
     * T台-单个查询
     */
    @GET("tt/v2/resource/get")
    suspend fun getTTaiConfig(@Query("resourceId") resourceId: Int): ApiResult<TTaiConfig>

    /**
     * T台-批量查询
     */
    @GET("tt/v2/resource/list")
    suspend fun getTTaiConfigs(@Query("ids") resourceId: String): ApiResult<List<TTaiConfig>>

    @GET
    suspend fun getQrResultByUrl(@Url url: String): ApiResult<QrResult>

    /**
     * 提交游戏反馈
     *     @Deprecated("Abandon function")
     *     @POST("/suggest/gsom/report")
     */

    /**
     * 提交反馈
     */
    @POST("/feedback/v1/commit")
    suspend fun submitNewFeedback(@Body feedback: SubmitNewFeedbackRequest): ApiResult<Boolean>

    /**
     * 获取反馈类型列表
     * [mock] https://mock.metaapp.cn/project/811/interface/api/59781
     */
    @POST("/feedback/v1/config")
    suspend fun fetchFeedbackTypeList(@Body body: FeedbackRequest): ApiResult<List<FeedbackConfigItem>?>

    /**
     * 发送email
     */
    @POST("/authorize/v2/email/sending")
    suspend fun sendEmail(@Body map: Map<String, String>): ApiResult<Boolean>

    /**
     * 验证email
     */
    @POST("/authorize/v2/email/verification")
    suspend fun checkEmail(@Body map: Map<String, String>): ApiResult<Boolean>

    /**
     * 重置密码
     */
    @POST("/authorize/v2/cipher/reset")
    suspend fun passwordReset(@Body map: Map<String, String>): ApiResult<Boolean>

    /**
     * 修改密码
     */
    @POST("/authorize/v2/cipher/modify")
    suspend fun passwordChange(@Body map: Map<String, String>): ApiResult<Boolean>

//    /**
//     * 双token：登录/注册账户
//     * https://mock.metaapp.cn/project/811/interface/api/45047
//     */
//    @POST("/authorize/v2/dtoken/user/sign")
//    suspend fun accounLoginOrSignup(@Body body: Map<String, String?>): ApiResult<AuthInfoApiResult>

    /**
     * 修改密码
     */
//    @POST("/auth/v1/profile/edit") 废弃
    @POST("/user/v2/profile/modify")
    suspend fun updateUserInfo(@Body map: EditUserInfoRequest): ApiResult<Boolean>

    @POST("/authorize/v2/dieout/apply")
    suspend fun ditout(@Body map: Map<String, String>): ApiResult<Boolean>


    /**
     * 发布游戏评论
     */
    @POST("/community/comment/v2/save")
    suspend fun publishAppraise(@Body request: PublishAppraiseRequest): ApiResult<String?>

    /**
     * 更新游戏评论
     */
    @POST("/community/comment/v2/update")
    suspend fun updateGameReview(@Body data: PublishAppraiseRequest): ApiResult<Boolean>

    /**
     * 添加评论点赞
     */
    @POST("/community/v2/opinion/save")
    suspend fun attitudeGameReview(@Body data: AttentionRequest): ApiResult<Boolean>

    /**
     * 根据uid查询用户评论
     */
    @POST("/community/comment/v1/user/query")
    suspend fun queryUserAppraiseByUid(@Body request: FindAppraiseRequest): ApiResult<GameReviewResult>

    /**
     * 查询游戏评分信息
     */
    @POST("/community/score/v1/query")
    suspend fun queryGameScore(@Body map: Map<String, String>): ApiResult<GameScoreResult?>

    /**
     * 分页查询评论
     * https://mock.metaapp.cn/project/595/interface/api/36015
     */
    @Deprecated("replaced by getPostCommentListV2")
    @POST("/community/comment/v1/query")
    suspend fun fetchOthersReviewList(@Body data: RequestGameReviewsParam): ApiResult<PagingApiResult<GameAppraiseData>?>

    /**
     * 获取游戏评价回复列表
     */
    @POST("/community/reply/v1/query")
    suspend fun getAppraiseReplyList(@Body request: AppraiseReplyListRequest): ApiResult<AppraiseReplyListResult?>

    /**
     * 发布游戏评价回复
     */
    @POST("/community/reply/v1/save")
    suspend fun addAppraiseReply(@Body request: AddAppraiseReplyRequest): ApiResult<String?>

    /**
     * 删除评论
     */
    @POST("/community/comment/v2/delete")
    suspend fun deleteComment(@Body map: HashMap<String, String>): ApiResult<Boolean>

    /**
     * 删除评论
     * https://mock.metaapp.cn/project/595/interface/api/38199
     */
    @POST("/community/reply/v1/delete")
    suspend fun deleteReply(@Body map: HashMap<String, String>): ApiResult<Boolean>

    /**
     * 置顶评论
     */
    @POST("/community/comment/v1/top")
    suspend fun topGameAppraise(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResult<String>

    /**
     * 是否评论过
     */
    @POST("/community/comment/v1/user/self")
    suspend fun checkIfHaveComment(@Body body: CheckAppraisedRequest): ApiResult<Boolean>

    /**
     * 查询游戏点赞
     */
    @GET("/discuss/v1/game/love/query")
    suspend fun fetchLikeByGameId(@Query("gameId") gameId: String): ApiResult<SnsInfo>

    /**
     * 根据gameCode列表批量获取游戏信息
     */
    @POST("/repository/v3/codes")
    suspend fun getGameListByIds(@Body body: List<String>): ApiResult<List<GameSuggestionInfo>>

    /**
     * 获取ugc游戏id配置
     */
    @GET("/game/v1/common/config")
    suspend fun getUgcGameConfig(): ApiResult<UgcGameConfig>

    /**
     * 获取首页ugc游戏列表
     */
    @POST("/ugc/content/v2/ts/game/search")
    suspend fun getUgcGameList(@Body map: Map<String, Int>): ApiResult<UgcGameInfo>

    /**
     * Mgs场景点赞
     */
    @POST("/scene/gsom/love")
    suspend fun mgsSceneLike(@Body map: Map<String, String>): ApiResult<Boolean>

    /**
     * Mgs场景取消点赞
     */
    @POST("/scene/gsom/dislove")
    suspend fun mgsSceneUnLike(@Body map: Map<String, String>): ApiResult<Boolean>

    /**
     * 获取我喜欢的ugc游戏列表
     */
    @POST("/ugc/content/v2/ts/my/love/search")
    suspend fun getUgcGameLikeList(@Body map: Map<String, Int>): ApiResult<UgcGameInfo>

    /**
     * 获取本地模板状态
     */
    @POST("ugc/content/v2/ts/my/build/status/list")
    suspend fun getEditorLocalStatus(@Body bizidList: HashMap<String, List<String>>): ApiResult<EditorLocalStatusInfo>

    /**
     * 获取已发布游戏
     */
    @POST("ugc/content/v2/ts/my/released/search")
    suspend fun getEditorPublish(@Body map: Map<String, String?>): ApiResult<UgcGameInfo>

    /**
     * 获取已发布游戏（按更新时间排序）
     */
    @POST("ugc/content/v2/ts/my/released/search/new")
    suspend fun getEditorPublishByUpdateTime(@Body map: Map<String, String?>): ApiResult<UgcGameInfo>

    /**
     * 获取已发布游戏V3
     */
    @POST("/ugc/content/v2/ts/my/released/queryV3")
    suspend fun getMyCreationsV3(@Body body: MyCreationsV3Request): ApiResult<UgcGameInfo>

    /**
     * 获取已发布游戏V4 pgc和ugc混排
     */
    @POST("/ugc/content/v2/ts/my/released/queryV4")
    suspend fun getMyCreationsV4(@Body body: MyCreationsV4Request): ApiResult<UgcGameInfo>

    /**
     * 置顶已发布ugc游戏
     */
    @POST("/ugc/content/v2/ts/my/released/topOn")
    suspend fun pinUgcGame(@Body body: PinUgcRequest): ApiResult<Boolean>

    /**
     * 置顶已发布pgc游戏
     */
    @POST("/developer-web/developer/game/top")
    suspend fun pinPgcGame(@Body body: PinPgcRequest): ApiResult<Boolean>

    /**
     * 删除编辑器已发布游戏
     */
    @POST("ugc/content/v2/audit/remove")
    suspend fun deleteEditorPublished(@Body map: HashMap<String, String>): ApiResult<Boolean>

    /**
     * 获取ugc模板列表
     */
    @POST("gameServer/scene/getTempSceneList")
    suspend fun getUgcTemplateList(
        @Query("pageSize") pageSize: Int,
        @Query("sinceId") sinceId: String?,
        @Query("currentPage") currentPage: Int?
    ): ApiResult<ArrayList<EditorTemplate>>

    /**
     * 判断ts游戏的id对应的ugc游戏、mgs功能
     */
    @GET("/game/v2/simple/info")
    suspend fun getTsTypeInfo(@Query("gameCode") id: String): ApiResult<TSTypeInfo>

    /**
     * 标记所有的通知为已读
     */
    @POST("/inform/v3/storehouse/unread/clear")
    suspend fun markAllNoticeAsRead(@Body map: HashMap<String, Int>): ApiResult<Boolean>

    /**
     * 获取未读的通知数量
     */
    @GET("/inform/v3/storehouse/unread/num")
    suspend fun getUnreadNoticeCount(@Query("resType") resType: Int): ApiResult<Int>

    /**
     * 移动编辑器通知分页列表
     */
    @GET("/inform/v3/storehouse/list")
    suspend fun getEditorNotice(
        @Query("pageNum") pageNum: Int,
        @Query("pageSize") pageSize: Int,
        @Query("resType") resType: Int
    ): ApiResult<EditorNotice>

    /**
     * 获取当前MGS用户信息
     */
    @POST("/user/gsom/sign")
    suspend fun getMgsUserInfo(@Body body: Map<String, String>): ApiResult<MgsUserInfo>

    /**
     * 获取游戏房间列表（TS）
     */
    @POST("/metaworld/room/list")
    suspend fun getGameRoomList(@Body data: Map<String, String>): ApiResult<GameRoomList>

    /**
     * 获取ugcId
     */
    @GET("gameServer/scene/getUgcSceneId")
    suspend fun getUgcIdByPackageName(@Query("packageName") packageName: String): ApiResult<String?>

    /**
     * 获取MGS场景相关功能配置
     */
    @POST("/scene/gsom/dispose/query")
    suspend fun getMgsSceneConfig(@Body map: Map<String, String>): ApiResult<MgsSceneConfig>


    /**
     * 精选首页
     * [mock] https://mock.metaapp.cn/project/189/interface/api/23816
     */
    @GET("/omnibus/v1/card/page")
    suspend fun getChoiceCardList(@Query("sceneCode") sceneCode: String): ApiResult<ChoiceCardListApiResult>

    @GET("/omnibus/v1/rec/scroll/query")
    suspend fun getRecommend(@Query("offset") offset: Int = -1, @Query("pageSize") pageSize: Int = 30): ApiResult<Recommend>

    @POST("/rec/gpark/feed/list")
    suspend fun postRecommend2(
        @Body rec: RecommendRequestBody? = RecommendRequestBody(offset = null),
        @HeaderMap(allowUnsafeNonAsciiValues = true) headers: Map<String, String>
    ): ApiResult<Recommend>

    @GET("/omnibus/v1/card/content/scroll/query")
    suspend fun getCardAll(
        @Query("cardId") cardId: String,
        @Query("offset") offset: Int,
        @Query("pageSize") pageSize: Int
    ): ApiResult<CardAll>

    @GET("/gamer/v2/games/myself")
    suspend fun getRecentPlayGameList(
        @Query("pageNum") pageNum: Int,
        @Query("pageSize") pageSize: Int,
        @Query("uuid") uuid: String?
    ): ApiResult<RecentPlayList>

    @POST("/gamer/v2/games/recent/play")
    suspend fun getRecentPlayGameListV2(
        @Body body: RecentPlayListV2Request
    ): ApiResult<RecentPlayListV2Response>

    @GET("/user/v2/profile/find")
    suspend fun queryUserProfile(@Query("uid") uuid: String?): ApiResult<UserProfileInfo>

    /**
     * 徽章兑换
     */
    @GET("/badge/v1/receive")
    suspend fun receiveBadge(@Query("badgeCode") code: String): ApiResult<Boolean>

    /**
     * 查询客态好友列表 先放到项目里 有需要再移动到lib里
     */
    @GET("/buddy/v2/guest/list")
    suspend fun getOthersFriendList(
        @Query("uid") uid: String,
        @Query("pageNum") pageNum: Int,
        @Query("pageSize") pageSize: Int,
    ): ApiResult<OthersFriendList?>


    @GET("/sso/empower/v2/app/verification")
    suspend fun authAppCheck(@Header("appkey") appkey: String): ApiResult<AuthAppInfo>


    @GET("/sso/empower/v2/access/token/get")
    suspend fun getAppAccessToken(@Header("appkey") appkey: String, @Query("pack") pack: String): ApiResult<AppAccessToken>

    /**
     * 获取MW拉起需要的MGS参数
     */
    @GET("/ts/gsom/find")
    suspend fun getLaunchMWMgsInfo(@Query("gameId") gameId: String): ApiResult<MWLaunchMgsInfo>

    /**
     * 获取MW拉起需要的扩展参数
     */
    @GET("game/v2/mw/detail")
    suspend fun getLaunchMWGameExpand(@Query("gameCode") gameCode: String, @QueryMap params: Map<String, String>): ApiResult<MWLaunchGameExpand>


    /**
     * 各种类型举报
     * [mock] https://mock.metaapp.cn/project/811/interface/api/44057
     */
    @POST("report/v2/add")
    suspend fun reportAdd(@Body request: ReportRequest): ApiResult<Boolean>

    /**
     * 用户关系
     * https://mock.metaapp.cn/project/189/interface/api/cat_7938
     */
    @POST("/relation/v2/increase")
    suspend fun relationAdd(@Body map: Map<String, String>): ApiResult<Boolean>

    @POST("/relation/v2/remove")
    suspend fun relationDel(@Body map: Map<String, String>): ApiResult<Boolean>

    // 获取与对方的各种关系
    @POST("/relation/v2/find")
    suspend fun getUserRelation(@Body map: Map<String, String>): ApiResult<Int>

    //提交支付凭证
    @POST("/pay/v2/pay/cert/save")
    suspend fun payResultSubmit(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResult<SubmitResult>

    /**
     * 海外-自有平台下单
     * https://mock.metaapp.cn/project/27/interface/api/24194
     * /order/v1/oversea/create 和 /order/v3/new/take 对服务器来说是同一个接口
     */
    @POST("/order/v3/new/take")
    suspend fun privilegePlaceOrder(@Body body: BodyRequestOrder): ApiResult<TakeOderResult>

    /**
     * 轮询充值结果
     */
    @POST("/order/v3/pay/state")
    suspend fun rechargingLoop(@Query("orderCode") orderId: String): ApiResult<Boolean>

    /**
     * 查询用户余额
     * coinType:
     * 1:乐园的乐币
     * 2：口袋方舟乐币
     * 3.Gpark币
     * 4派对币
     * 5钥匙币
     * 6乐积分
     */
    @POST("/coin/v2/platform/coin/account/balance")
    suspend fun getBalance(@Body map: HashMap<String, Int>): ApiResult<UserBalance>

    /**
     * 查询用户余额
     */
    @POST("/coin/v2/platform/coin/account/balance")
    suspend fun getPoint(@Body map: HashMap<String, Int> = HashMap<String, Int>().apply { this["coinType"] = 7 }): ApiResult<UserBalance>

    /**
     * 游戏内购买道具
     * 1. 创建订单
     */
    @POST("order/v1/pd/mw/createOrder")
    suspend fun mwCreateOrder(@Body map: Map<String, @JvmSuppressWildcards Any?>): ApiResult<MWCreateOrderResult>

    /**
     * 游戏内购买道具
     * 3. 订单支付发货状态
     */
    @POST("order/v1/query/state")
    @FormUrlEncoded
    suspend fun mwPayState(@Field("orderCode") orderCode: String): ApiResult<Map<String, Boolean>>


    /**
     * 获取首页重要通知
     */
    @GET("/operation/config/v2/im/get")
    suspend fun fetchHomeNotice(): ApiResult<HomeOperationNotification?>

    /**
     * 查询IM运营卡片
     * 当前接口已被服务器弃用, 改为 https://mock.metaapp.cn/project/623/interface/api/29094
     * 改为使用 /supper/operation/v2/position/query,并且 operatingPosition 传 38
     */
    @Deprecated("这个接口已经被服务器弃用了, 改用 supper/operation/v2/position/query这个接口,并且operatingPosition传38")
    @GET("/operation/config/v2/homepage/get")
    suspend fun getOperationNoticeList(
        @Query("pageNum") pageNum: Int,
        @Query("pageSize") pageSize: Int,
    ): ApiResult<SystemNotificationList>

    /**
     * 选择默认角色形象
     */
    @POST("/user/v2/appearance/default/install")
    suspend fun chooseDefaultRole(@Body map: Map<String, String>): ApiResult<Boolean>

    /**
     * 获取TS游戏房间信息
     */
    @POST("/room/gsom/metaworld/room/status")
    suspend fun getTsGameRoomInfo(@Body data: Map<String, String>): ApiResult<GameRoomStatus>

    /**
     * 获取ugc横幅列表
     */
    @GET("/hp/v2/banners/config")
    suspend fun fetchUgcBannerList(): ApiResult<List<UgcBannerInfo>?>

    /**
     * 获取会员信息数组
     */
    @POST("/member/v2/member/infos")
    suspend fun getUserMemberInfos(@Body request: MemberRequest): ApiResult<List<MemberInfo>>


    /**
     * 用gameCode获取模板信息
     */
    @GET("/gameServer/scene/getTempScene")
    suspend fun fetchTemplateInfoByCode(@Query("crGameId") gameCode: String): ApiResult<EditorTemplate?>

    /**
     * 设置主小屋
     */
    @POST("ugc/content/v2/ts/small/room/change")
    suspend fun setMyHome(@Body map: Map<String, String>): ApiResult<Boolean>

    /**
     * 用uid查询用户的主小屋信息
     */
    @POST("ugc/content/v2/ts/small/room/search")
    suspend fun fetchHomeInfo(@Body map: Map<String, String?>): ApiResult<UgcGameInfo.Games?>

    /**
     * 获取UG承接的超级推荐位
     */
    @POST("/ug/monitor/location/selectSuperLocationCode")
    suspend fun getSuperGameCode(@Body map: Map<String, String>): ApiResult<UGGameSuggestionInfo>

    /**
     * 【已废弃】playza 小屋相关接口
     * 批量用uid获取用户的主小屋信息
     *     @POST("/ugc/content/v2/ts/small/room/friend/home/<USER>")
     * 获取热门小屋列表
     *     @POST("/ugc/content/v2/ts/small/room/home/<USER>")
     * 获取邻居信息
     *     @POST("/ugc/content/v2/ts/small/room/neighborInfo/get")
     * 设置为邻居 contentType为5时
     *     @POST("/love/v2/game/do")
     * 解除邻居关系 contentType为5时
     *     @POST("/love/v2/game/calloff")
     * 获取自己的已发布小屋游戏列表
     *     @POST("/ugc/content/v2/ts/small/room/home/<USER>/list")
     */

    /**
     *  获取小屋模板列表
     */
    @GET("gameServer/scene/getHomeTempSceneList")
    suspend fun getHomeTemplateList(
        @Query("pageSize") pageSize: Int,
        @Query("currentPage") currentPage: Int
    ): ApiResult<ArrayList<EditorTemplate>?>

    /**
     * 公共接口：查询分享参数
     */
    @POST("/share/v1/create")
    suspend fun createShare(@Body info: CommonShareRequest): ApiResult<CommonShareResult>

    /**
     * 公共接口：用shareId查询分享参数
     */
    @POST("/share/info/v2/get")
    suspend fun getInviteInfoByShareId(@Body map: HashMap<String, String>): ApiResult<CommonShareResult?>

    /**
     * 获取游戏详情页运营位公告列表
     */
    @POST("supper/operation/v2/position/query")
    suspend fun getNoticeList(@Body data: Map<String, String>): ApiResult<List<UniJumpConfig>>

    /**
     * 保存点赞状态
     */
    @POST("forum/v1/comment/save")
    suspend fun saveCommunityLike(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResult<Boolean>


    @POST("forum/v1/inform/save")
    suspend fun report(@Body mapOf: Map<String, @JvmSuppressWildcards Any>): ApiResult<Boolean>

    /**
     * 资源类型 1帖子 2游戏 3评论 4回复
     * @return
     *  const val IS_NONE = 0// 无感
     *  const val IS_LIKE = 1// 赞
     *  const val IS_UNLIKE = 2// 踩
     */
    @GET("forum/v1/comment/query")
    suspend fun getOpinion(@Query("resId") resId: String, @Query("resType") resType: Int): ApiResult<Int>

    /**
     * 检查文本是否合法，增加透传字段
     * [mock] https://mock.metaapp.cn/project/619/interface/api/28555
     */
    @POST("data/detection/v2/text/check")
    suspend fun commonReviewTextRisk(@Body request: IMCheckRequest): ApiResult<ReviewTextRiskResult?>

    /**
     * 判断用户是否大于13岁
     */
    @POST("/authorize/v2/adult/check")
    suspend fun isUserAbove13(@Body map: Map<String, String> = HashMap()): ApiResult<Boolean>

    /**
     * 批量使用ugcid获取游戏列表
     */
    @POST("/ugc/content/v2/ts/game/query/byIds")
    suspend fun getUgcInfoByIdLIst(@Body map: HashMap<String, List<String>>): ApiResult<UgcGameInfo?>

    /**
     * 获取某个模板游戏
     * [mock](https://mock.metaapp.cn/project/775/interface/api/40657)
     */
    @GET("/gameServer/scene/tempGame")
    suspend fun getGameTemplate(@Query("crGameId") crGameId: String): ApiResult<EditorTemplate>

    /**
     * ugc游戏详情页
     * [mock](https://mock.metaapp.cn/project/264/interface/api/34151)
     */
    @POST("/ugc/content/v2/ts/detailPage/query")
    suspend fun getUgeDetailPage(@Body body: Map<String, String>): ApiResult<UgcDetailInfo>

    /**
     * TS-建造模板展示页面
     * [mock](https://mock.metaapp.cn/project/264/interface/api/40063)
     */
    @POST("/ugc/content/v2/ts/formwork/hot/query")
    suspend fun getFormworkList(@Body map: Map<String, String>): ApiResult<FormworkList>

    /**
     * TS-建造模板云存档列表
     */
    @POST("/ugc/content/v2/ts/formwork/query/v2")
    suspend fun getFormWorkV4List(@Body body: ReqFormWorkV4Body): ApiResult<UgcFormWorkV4Data>

    /**
     * TS- check建造页模板是否有存档权限
     */
    @POST("/ugc/content/v2/ts/formwork/archive/check")
    suspend fun checkFormWorkArchive(@Body body: ReqFormWorkArchiveBody): ApiResult<UgcFormWorkArchiveData>

    /**
     * 上报拉起游戏事件
     * [mock](https://mock.metaapp.cn/project/264/interface/api/34159)
     */
    @POST("/ugc/content/v2/user/visit")
    suspend fun reportLaunchUgcGame(@Body body: Map<String, String>): ApiResult<String>


    /**
     * 获取一个随机用户名
     */
    @GET("/user/v2/nickname/random")
    suspend fun getRandomNickname(): ApiResult<String?>

    @GET("/user/v2/nickname/random")
    suspend fun getRandomNicknameV2(): ApiResult<String>

    @POST("/user/v2/nickname/check")
    suspend fun checkNickname(@Body body: Map<String, String>): ApiResult<Boolean>

    @POST("/mwRoom2C/getHomePageRooms")
    suspend fun getHomeRoomList(@Body getRoomListBody: GetRoomListBody): ApiResult<GetRoomListResult>

    @GET("/mwRoom2C/getHomePageRoom")
    suspend fun getHomeRoomDetail(@Query("roomId") roomId: String): ApiResult<ChatRoomInfo>

    @POST("/mwRoom2C/createHomePageRoom")
    suspend fun createHomeRoom(@Body createRoomBody: CreateRoomBody): ApiResult<ChatRoomInfo>

    @POST("mwRoom2C/getRoomStatus")
    suspend fun canJoinRoom(@Body body: Map<String, String>): ApiResult<CanJoinRoom>

    @POST("/msg/center/internal/group/list")
    suspend fun getSysHeaderInfo(): ApiResult<List<SysHeaderInfo>>

    @POST("/msg/center/internal/group/msg/list")
    suspend fun getSysActivitiesInfo(@Body map: HashMap<String, String>): ApiResult<List<SysActivitiesInfo>>

    @POST("msg/center/internal/unread/count/total")
    suspend fun getSysUnreadCount(): ApiResult<Int>

    @POST("/user/gsom/appearance/modify")
    suspend fun modifyUserFullBodyImg(@Body body: Map<String, String>): ApiResult<Boolean>

    @GET("/community/tag/v1/random/query")
    suspend fun getRandomPostTags(): ApiResult<List<PostTag>>

    @POST("/community/tag/v1/query")
    suspend fun getRecommendPostTags(@Body body: Map<String, String>): ApiResult<List<PostTag>>

    @POST("/community/post/v1/save")
    @Deprecated(message = "use savePostV3 instead")
    suspend fun savePostV2(@Body body: PublishPostRequest): ApiResult<String>

    /**
     * 社区发帖，返回已审核通过的话题列表
     * [mock] https://mock.metaapp.cn/project/595/interface/api/58227
     */
    @POST("/community/post/v1/save/return")
    suspend fun savePostV3(@Body body: PublishPostRequest): ApiResult<PostPublishResult>

    /**
     * 编辑帖子
     * [mock] https://mock.metaapp.cn/project/595/interface/api/71261
     */
    @POST("/community/post/v1/update/return")
    suspend fun editPost(@Body body: PublishPostRequest): ApiResult<PostPublishResult>

    @GET("/community/post/v1/detail/query")
    suspend fun getPostDetailV2(@Query("postId") postId: String): ApiResult<PostDetail>

    @POST("/community/post/v1/delete")
    suspend fun deletePostV2(@Body body: Map<String, String>): ApiResult<Boolean>

    @POST("/community/comment/v1/query")
    suspend fun getPostCommentListV2(@Body body: PostCommentListRequestBody): ApiResult<PagingApiResult<PostComment>>

    @POST("/community/comment/v1/save")
    suspend fun addPostComment(@Body body: PostCommentRequestBody): ApiResult<String>

    @POST("/community/comment/v1/delete")
    suspend fun deletePostComment(@Body body: Map<String, String>): ApiResult<Boolean>

    @POST("/community/comment/v1/ugc/delete")
    suspend fun deleteUgcComment(@Body body: Map<String, String>): ApiResult<Boolean>

    @POST("/community/reply/v1/query")
    suspend fun getPostReplyListV2(@Body body: PostReplyListRequestBody): ApiResult<PagingApiResult<PostReply>>

    @POST("/community/reply/v1/save")
    suspend fun addPostReply(@Body body: PostReplyRequestBody): ApiResult<String>

    @POST("/community/reply/v1/delete")
    suspend fun deletePostReply(@Body body: Map<String, String>): ApiResult<Boolean>

    @POST("/community/opinion/v1/save")
    suspend fun saveOpinion(@Body body: OpinionRequestBody): ApiResult<Boolean>


    /**
     * 查询feed流帖子列表
     * [mock] https://mock.metaapp.cn/project/595/interface/api/43909
     */
    @POST("/community/post/v1/query")
    suspend fun getCommunityTagFeed(@Body request: PostTagFeedRequest): ApiResult<CommunityFeedWrapper>

    /**
     * 查询用户帖子列表
     */
    @POST("/community/post/v1/user/query")
    suspend fun getCommunityProfileFeed(@Body request: PostProfileFeedRequest): ApiResult<CommunityFeedWrapper>

    /**
     * 查询用户封禁状态
     */
    @POST("/community/user/mute/query")
    suspend fun queryUserMuteStatus(@Body map: Map<String, String?>, @Query("uid") uid: String?): ApiResult<UserMuteStatus>

    /**
     * 关系列表
     */
    @POST("/relation/v2/info/list")
    suspend fun getRelationList(@Body request: RelationListRequest): ApiResult<RelationListResult>

    /**
     * 关系数量
     */
    @POST("/relation/v2/count")
    suspend fun getRelationCount(@Body request: RelationCountRequest): ApiResult<RelationCountResult>

    /**
     * 获取我开发的pgc游戏
     */
    @POST("/community/game/card/v1/query")
    suspend fun getIDevelopedPgcList(@Body request: IDevelopedPostCardRequest): ApiResult<PostCardResult>

    /**
     * 搜索游戏卡片
     */
    @POST("/community/game/card/v1/query")
    suspend fun searchPostCardList(@Body request: SearchPostCardRequest): ApiResult<PostCardResult>

    /**
     * 获取全部tag列表
     */
    @POST("/community/tag/v1/query")
    suspend fun getAllPostTagList(): ApiResult<List<PostTag>>

    @GET("/miniHouse/list")
    suspend fun getCottageRoomList(@Query("currentPage") currentPage: Int): ApiResult<CottageRoomList?>

    @GET("/miniHouse/detail")
    suspend fun getCottageRoomInfo(@Query("roomId") roomId: String): ApiResult<CottageRoomInfo?>

    @GET("/miniHouse/recentVisitors")
    suspend fun getHomeVisitorCount(@Query("roomId") roomId: String): ApiResult<List<CottageVisitor>?>

    /**
     * 当前用户的小屋信息
     */
    @GET("/miniHouse/userHouseInfo")
    suspend fun queryUserHouseInfo(@Query("uuid") uuid: String): ApiResult<House>


    @POST("device/v1/report/push")
    suspend fun uploadPushToken(@Body body: Map<String, String>): ApiResult<JsonElement>

    @POST("msg/common/v1/switch/query")
    suspend fun getNotificationSwitch(): ApiResult<Map<String, Boolean>>

    @POST("msg/common/v1/switch/change")
    suspend fun setNotificationSwitch(@Body body: Map<String, Boolean>): ApiResult<Boolean>

    /**
     * 【已废弃】剧情创作模板
     * @see com.socialplay.gpark.data.api.MetaApi.plotTemplateV2
     */
    @Deprecated("see plotTemplateV2")
    @POST("plot/template/v1/bath/query")
    suspend fun plotTemplate(@Body body: Map<String, String>): ApiResult<PlotTemplateList>

    /**
     * 剧情创作模板-后台可配
     * [mock] https://mock.metaapp.cn/project/811/interface/api/58374
     */
    @POST("plot/template/v1/bath/queryV2")
    suspend fun plotTemplateV2(@Body body: Map<String, String>): ApiResult<PlotTemplateList>

    /*剧情创作模板增加点击数*/
    @POST("love/v2/do")
    suspend fun plotTemplateLoveDo(@Body body: Map<String, String>): ApiResult<String?>

    /*剧情创作模板*/
    @GET("plot/template/v1/index/page")
    suspend fun plotMainList(): ApiResult<PlotMainList>

    @POST("plot/template/v1/bath/query")
    suspend fun plotAllList(@Body body: PlotListBody): ApiResult<com.socialplay.gpark.data.model.moments.PlotTemplateList>

    /*剧情创作模板增加点击数*/
    @POST("like/v1/add")
    suspend fun plotTemplateLoveDoV2(@Body body: Map<String, String>): ApiResult<String?>

    /*查询ugc类型拍剧模板*/
    @POST("/plot/template/ugc/v1/query")
    suspend fun momentTemplateList(@Body body: MomentsTemplateBody): ApiResult<MomentsTemplate>

    /*用户删除ugc类型拍剧模板*/
    @POST("/plot/template/ugc/v1/delete")
    suspend fun momentTemplateDelete(@Body body: Map<String, String>): ApiResult<Boolean>

    /**
     * 获取已发布UGC语音房列表
     */
    @POST("/ugc/content/v2/ts/self/voice/room/query")
    suspend fun fetchMyChatRoomPublishedList(@Body map: Map<String, String?>): ApiResult<UgcGameInfo>

    /**
     * 获取UGC语音房模板列表
     */
    @GET("/ugc/content/v2/ts/voice/room/gameCode/query")
    suspend fun fetchChatRoomTemplateList(): ApiResult<RoomTemplate>

    // 仅登出
    @POST("/authorize/v2/login/out/simple")
    suspend fun logout(@Body body: Map<String, String?>, @Header("isAuthorizable") isAuthorizable: String = "false"): ApiResult<Boolean>

    // 获取设备最后登录账号的信息
    @POST("/user/device/v1/login/last")
    suspend fun getContinueAccount(): ApiResult<ContinueAccountInfo>

    //根据三方订单号查三方订单摘要信息
    @POST("/pay/v2/pay/tripartite/order/query")
    suspend fun getTripartite(@Body body: Map<String, String?>): ApiResult<TripartiteInfo>

    /**
     * https://mock.metaapp.cn/project/160/interface/api/51276
     * 商品类型:
     * 1 普通会员
     * 2 免广告大会员
     * 4 乐币充值商品
     * 8 普通会员首充商品（当前需求端上不使用，服务端根据用户属性自动匹配）
     * 16 钻石（送会员）
     * 32 口袋方舟乐币
     * 512 钥匙大会员
     */
    @GET("/member/v1/product/store")
    suspend fun getSubsProduct(@Query("type") type: String): ApiResult<List<SubscribeProductInfo>?>

    @POST("/community/video/query")
    suspend fun getVideoFeedList(@Body body: Map<String, String>): ApiResult<VideoFeedApiResult>

    @POST("/data/pv/increment")
    suspend fun visitPostOutfitCard(@Body body: PostOutfitVisitRequest): ApiResult<Any?>

    /**
     * 查询各业务红点信息
     */
    @POST("/badge/v1/unread")
    suspend fun queryUnreadRedBadge(@Body types: RedBadgeRequest): ApiResult<RedBadgeData>

    /**
     * 清除红点信息
     */
    @POST("/badge/v1/unread/clear")
    suspend fun clearRedBadge(@Body types: RedBadgeRequest): ApiResult<Boolean>

    /**
     * 【加密】创建图片审核任务
     * [mock] https://mock.metaapp.cn/project/619/interface/api/59662
     */
    @POST("/encrypt/data/check/create/job")
    suspend fun commonCreateImageRiskCheck(
        @Body body: CreateImgRiskCheckTaskRequest,
        @HeaderMap headers: Map<String, String> = DeEncryptCommonParams.getEncryptParams()
    ): ApiResult<RiskTaskQueryRequest>

    /**
     * 【加密】查询图片审核结果
     * [mock] https://mock.metaapp.cn/project/619/interface/api/59669
     */
    @POST("/encrypt/data/check/result/query")
    suspend fun queryImageRiskTask(
        @Body body: RiskTaskQueryRequest,
        @HeaderMap headers: Map<String, String> = DeEncryptCommonParams.getEncryptParams()
    ): ApiResult<RiskQueryResult>

    @POST("/rec/gpark/video/list")
    suspend fun getRecommendVideoFeed(
        @Body body: RecommendVideoFeedListRequest,
        @HeaderMap headers: Map<String, String?>,
    ): ApiResult<VideoFeedApiResult>

    @POST("/user/v2/privacySwitch/query")
    suspend fun getPrivacySwitch(): ApiResult<PrivacySwitch>

    @POST("/user/v2/privacySwitch/edit")
    suspend fun setPrivacySwitch(@Body switch: PrivacySwitch): ApiResult<Boolean>

    // 关注话题
    @POST("/community/tag/v1/follow")
    suspend fun followTopic(@Body body: FetchTopicRequest): ApiResult<Any?>

    // 取消关注话题
    @POST("/community/tag/v1/unfollow")
    suspend fun unfollowTopic(@Body body: FetchTopicRequest): ApiResult<Any?>

    // 我关注的话题列表
    @POST("/community/tag/v1/my/follow")
    suspend fun myFollowTopics(@Body body: QueryTopicsRequest): ApiResult<CommunityTopicBlockWrap>

    // 热门话题
    @GET("/community/tag/v1/recommend")
    suspend fun hotTopics(
        @Query("pageSize") pageSize: Int,
        @Query("pageNum") pageNum: Int
    ): ApiResult<List<PostTag>?>

    // 话题详情
    @POST("/community/tag/v1/detail")
    suspend fun topicDetail(@Body body: FetchTopicRequest): ApiResult<TopicDetailInfo>

    /**
     * 批量增加各种pv数据
     * [mock] https://mock.metaapp.cn/project/595/interface/api/57996
     */
    @POST("/data/pv/batch/increment")
    suspend fun commonAddPvCountBatch(@Body body: AddPvBatchRequest): ApiResult<Any?>

    /**
     * 增加各种pv数据
     * [mock] https://mock.metaapp.cn/project/595/interface/api/51535
     */
    @POST("/data/pv/increment")
    suspend fun commonAddPvCount(@Body body: AddPvRequest): ApiResult<Boolean>


    /**
     * 查询社区版块
     * [mock] https://mock.metaapp.cn/project/595/interface/api/33487
     */
    @POST("/community/block/query")
    suspend fun fetchBlockList(@Body body: FetchBlockRequest): ApiResult<List<CommunityBlock>?>

    @GET("/metaverse/user/resource/userStyle/getStyleList")
    suspend fun getRoleStyleList(
        @Query("beginIndex") beginIndex: Int,
        @Query("length") length: Int
    ): ApiResult<RoleStyleListResponse>

    @POST("/metaverse/user/resource/userStyle/getOtherStyleList")
    suspend fun getOtherRoleStyleList(@Body body: RoleOtherStyleListRequest): ApiResult<RoleStyleListResponse>

    @POST("/metaverse/user/resource/userStyle/likeStyle")
    suspend fun likeRoleStyle(@Body body: LikeRoleStyleRequest): ApiResult<LikeRoleStyleResponse>

    @POST("/metaverse/user/resource/userStyle/deleteStyle")
    suspend fun deleteRoleStyle(@Body body: DeleteRoleStyleRequest): ApiResult<DeleteRoleStyleResponse>

    @POST("/LRpHGk1sGXHe14h6/v2/ouNYZzbBO/mDB31B")
    suspend fun userAgentUpload(
        @Body params: DeviceParamsRequest, @HeaderMap headers: Map<String, String> = DeEncryptCommonParams.getEncryptParams()
    ): ApiResult<String?>

    @POST("/aibot/info/simple/query")
    suspend fun getAiBotInfoList(@Body map: Map<String, String?>): ApiResult<AiBotListResult?>

    @POST("/aibot/info/detail/query")
    suspend fun getAiBotInfo(@Body map: Map<String, String>): ApiResult<BotInfo?>

    @POST("/aibot/info/used/record/query")
    suspend fun getAiBotConversionList(@Body map: Map<String, String>): ApiResult<AiBotConversationResult?>

    @POST("/aibot/info/used/record/delete")
    suspend fun deleteAiBotConversion(@Body map: Map<String, String>): ApiResult<Boolean?>

    @POST("/aibot/info/used/record/modify")
    suspend fun updateAiBotConversion(@Body map: Map<String, String>): ApiResult<Boolean?>

    @POST("/aibot/info/follow/query")
    suspend fun getFollowAiBotList(@Body map: Map<String, String?>): ApiResult<AiBotFollowResult?>

    @POST("/hotfix-service/api/v1/android/getAssetDataListFiles")
    suspend fun getMWGameResource(@Body params: MWGameResourceRequest): ApiResult<List<MWGameResourceResponse>>

    @POST("rec/gpark/v2/feed/list")
    suspend fun postRecommend3(
        @Body rec: RecommendRequestBody? = RecommendRequestBody(offset = null),
        @HeaderMap(allowUnsafeNonAsciiValues = true) headers: Map<String, String>,
    ): ApiResult<HomeRecommend>

    /**
     * 替换推荐接口的后端接口，模拟推荐逻辑
     */
    @POST("/game/home/<USER>/rec/v1/query")
    suspend fun postCustomRecommend(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResult<HomeCustomRecommend>

    /**
     * 首页 新游tab
     * https://mock.metaapp.cn/project/264/interface/api/72197
     */
//    @POST("https://mock.metaapp.cn/mock/264/ugc/new/game/v1/query")
    @POST("/ugc/new/game/v1/query")
    suspend fun postMapsNewest(@Body map: Map<String, Long?>): ApiResult<HomeMapsNewest>

    @GET("/externalRouter/aigc/character/bot/ugc/imageStyle")
    suspend fun getStyleList(): ApiResult<List<AIBotStyle>>

    @POST("/externalRouter/aigc/character/bot/ugc/image/generate")
    suspend fun generateAIBotImage(@Body body: AIBotCreateRequest): ApiResult<AIBotCreateResult?>


    /**
     * 新版mgs/联运 创建预支付订单
     */
    @POST("order/v1/pay/prepay")
    suspend fun createPrepaidOrder(@Body info: PayOrderInfo): ApiResult<JsonElement>

    /**
     * 获取kol首页列表部分数据
     */
    @POST("/ugc/content/v2/ts/creator/homePage")
    suspend fun getKolFrameList(@Body map: Map<String, String> = HashMap()): ApiResult<CreatorFrameResult>

    /**
     * 按标签查询创作者列表
     */
    @POST("/ugc/content/v2/ts/creator/user/star/list")
    suspend fun getKolCreatorListByTag(@Body params: RecommendCreatorRequest): ApiResult<RecommendCreatorResult>

    /**
     * 查询关注创作者的ugc游戏
     */
    @POST("/ugc/content/v2/ts/creator/followGame/list")
    suspend fun getFollowedCreatorUgcList(@Body params: FollowedCreatorUgcRequest): ApiResult<FollowedCreatorUgcWrapper>

    /**
     * 查询已关注、更多创作者列表
     */
    @POST("/ugc/content/v2/ts/creator/user/list")
    suspend fun getTypeCreatorList(@Body params: TypeCreatorRequest): ApiResult<TypeCreatorResult>

    /**
     * 查询标签创作者列表
     */
    @POST("/ugc/content/v2/ts/creator/user/star/list")
    suspend fun getLabelCreatorList(@Body params: LabelCreatorRequest): ApiResult<LabelCreatorResult>

    /**
     * 【推荐】查询所有ugc发布标签
     */
    @GET("/rec/gpark/tag/all/query")
    suspend fun getAllUgcPublishedTag(): ApiResult<List<UgcPublishLabel>>

    /**
     * 【推荐】获取kol推荐ugc游戏-分页列表
     */
    @POST("/rec/gpark/feed/ugc/list")
    suspend fun getKolRecommendUgcGameListByPage(@Body rec: RecommendRequestBody? = RecommendRequestBody()): ApiResult<RecommendKolUgcResult>

    /**
     * 【推荐】按标签查询ugc游戏列表
     */
    @POST("/rec/gpark/feed/ugc/list/tag")
    suspend fun getUgcGameListByTag(@Body rec: RecommendRequestBody? = RecommendRequestBody()): ApiResult<RecommendUgcResult>

    /**
     * 用户是否已经在应用商店评分
     */
    @POST("/user/v2/query/app/store/score/record")
    suspend fun queryPositiveComment(): ApiResult<QueryPositiveComment>

    @POST("/user/v2/submit/app/store/score/record")
    suspend fun submitPositiveComment(): ApiResult<Boolean>

    @POST("/user/additional/v1/external/link/del")
    suspend fun deleteLink(@Body map: Map<String, String?>): ApiResult<Boolean>

    @POST("/user/additional/v1/external/link/add")
    suspend fun addLink(@Body map: Map<String, String?>): ApiResult<ProfileLinkInfo>

    @GET("/ugc/content/v2/ts/creator/status/query")
    suspend fun getUserCreate(): ApiResult<UserCreateInfo?>

    @GET("/externalRouter/aigc/character/bot/ugc/image/generationResults")
    suspend fun generateAIBotImageResult(@Query("requestId") requestId: String): ApiResult<AIBotCreateImageResult?>

    @POST("/externalRouter/aigc/character/bot/ugc/info/generate")
    suspend fun generateInfo(@Body map: Map<String, String?>): ApiResult<AIBotGenerateInfo?>

    @POST("/aibot/info/create")
    suspend fun saveBotInfo(@Body botInfo: BotInfoCreate): ApiResult<BotInfo?>

    /**
     * 0无 1有
     */
    @GET("/activity/template/daily/task/v1/has/reward")
    suspend fun queryDailyTaskRewardStatus(): ApiResult<Int>

    @POST("/activity/template/daily/task/v1/finish")
    suspend fun finishDailyTask(@Body body: FinishDailyTaskBody): ApiResult<Boolean>

    @GET("/activity/template/daily/task/v1/query")
    suspend fun queryDailyTaskInfo(): ApiResult<DailyTaskInfo?>

    @POST("/activity/template/daily/task/v1/sign")
    suspend fun finishDailySign(@Body body: FinishDailyTaskBody): ApiResult<Boolean>

    /**
     * 查询分享中转数据
     */
    @GET("/data/relay/v2/query")
    suspend fun shareDataRelayQuery(
        @Header("User-Agent") ua: String,
        @Query("uniqId") uniqId: String?
    ): ApiResult<RelayData>

    @Deprecated("当前方法与 getNoticeList 方法重复, 使用 getNoticeList 方法替换")
    @POST("/supper/operation/v2/position/query")
    suspend fun getHomeRecommendOperations(@Body body: HomeRecommendOperationBody): ApiResult<List<HomeRecommendOperation>>

    @POST("/externalRouter/aigc/character/bot/reset")
    suspend fun resetAIBOTHistory(@Body map: Map<String, String?>): ApiResult<Any?>

    @POST("/msg/center/internal/clear/all/unread/msg")
    suspend fun postClearRed(): ApiResult<Boolean>

    @GET("/cloud/archive/v1/list/query")
    suspend fun getUgcBackup(@Query("projectId") projectId: String, @Query("templateId") templateId: String): ApiResult<List<UgcBackupInfo>>

    @GET("/cloud/archive/v1/allProject/query")
    suspend fun getUgcCloudProject(
        @Query("pageSize") pageSize: Long,
        @Query("sinceId") sinceId: Long?
    ): ApiResult<List<UgcCloudProject>>

    @GET("/gameServer/scene/client/platformMapping/query")
    suspend fun getGidPkg(@Query("sceneId") sceneId: String): ApiResult<GidPkg>

    @GET("/cloud/archive/v1/projectLimit/query")
    suspend fun getMaxCloud(): ApiResult<ProjectLimit>

    @GET("/cloud/archive/v1/allInProjects/delete")
    suspend fun deleteAllBackup(@Query("projectId") fileId: String): ApiResult<Boolean>

    @GET("/cloud/module/v1/list/query")
    suspend fun getUgcModuleProjectBackup(@Query("projectId") projectId: String, @Query("templateId") templateId: String): ApiResult<List<UgcBackupInfo>>

    @GET("/cloud/module/v1/allProject/query")
    suspend fun getUgcModuleProjectList(@Query("pageSize") pageSize: Long, @Query("sinceId") sinceId: Long?): ApiResult<List<UgcCloudProject>>

    @GET("/cloud/module/v1/projectLimit/query")
    suspend fun getUgcModuleProjectLimit(): ApiResult<ProjectLimit>

    @GET("/cloud/module/v1/allInProjects/delete")
    suspend fun deleteUgcModuleProjectBackups(@Query("projectId") fileId: String): ApiResult<Boolean>

    /**
     * 裂变
     */
    @GET("/activity/template/robux/v1/query")
    suspend fun recordRoBux(): ApiResult<RoBuxRecordInfo>

    @GET("/metaverse/item/ugc/favorite")
    suspend fun likeUgcDesign(
        @Query("feedId") feedId: String,
        @Query("positive") positive: Boolean
    ): ApiResult<Long>

    @GET("/metaverse/item/ugc/purchase")
    suspend fun getUgcDesign(
        @Query("feedIds") feedIds: String
    ): ApiResult<String>

    @GET("/metaverse/item/ugc/edit")
    suspend fun editUgcDesign(
        @Query("feedId") feedId: String,
        @Query("title") title: String?,
        @Query("comment") comment: String?
    ): ApiResult<Boolean>

    @GET("/metaverse/item/ugc/delete")
    suspend fun deleteUgcDesign(
        @Query("feedId") feedId: String
    ): ApiResult<String>

    @GET("/metaverse/item/ugc/feed/getFeedList")
    suspend fun getUgcDesignFeed(
        @Query("sortType") sortType: Int,
        @Query("pageSize") pageSize: Int,
        @Query("pageNumber") pageNumber: Int
    ): ApiResult<UgcDesignFeedResponse>

    @GET("/metaverse/item/ugc/feed/getFeedListV2")
    suspend fun getUgcDesignFeedV2(
        @Query("sortType") sortType: Int,
        @Query("pageSize") pageSize: Int,
        @Query("pageNumber") pageNumber: Int,
        @Query("tagTypes") tagTypes: String?,
    ): ApiResult<UgcDesignFeedResponse>

    @GET("/metaverse/item/ugc/feed/getNewbornZoneList")
    suspend fun getUgcRookieFeed(
        @Query("pageNumber") pageNumber: Int,
        @Query("pageSize") pageSize: Int
    ): ApiResult<UgcDesignFeedResponse>

    @GET("/metaverse/item/ugc/feed/getFeedInfo")
    suspend fun getUgcDesignDetail(
        @Query("feedId") feedId: String
    ): ApiResult<UgcDesignDetail>

    @GET("/metaverse/item/ugc/userProfile/getUserCurrentDressing")
    suspend fun getProfileCurrentClothes(
        @Query("targetUuid") targetUuid: String
    ): ApiResult<List<ProfileCurrentCloth>>

    @GET("/metaverse/item/ugc/userProfile/getUserProfileTagTypes")
    suspend fun getUgcDesignProfileTags(
        @Query("entrance") entrance: Int
    ): ApiResult<List<UgcDesignProfileTag>>

    @GET("/metaverse/item/ugc/userProfile/getUserProfileUgcItems")
    suspend fun getUgcDesignProfile(
        @Query("targetUuid") targetUuid: String,
        @Query("tag") tag: Int,
        @Query("pageSize") pageSize: Int,
        @Query("pageNumber") pageNumber: Int,
    ): ApiResult<UgcDesignProfileResponse>

    @GET("/metaverse/item/ugc/userProfile/getUserProfileUgcItemsV2")
    suspend fun getUgcDesignProfileV2(
        @Query("targetUuid") targetUuid: String,
        @Query("tag") tag: Int,
        @Query("pageSize") pageSize: Int,
        @Query("pageNumber") pageNumber: Int,
    ): ApiResult<UgcDesignProfileResponse>

    @GET("/metaverse/item/ugc/userProfile/ugcBrief")
    suspend fun getUgcAssetProfile(
        @Query("targetUuid") targetUuid: String,
        @Query("pageSize") pageSize: Int
    ): ApiResult<List<UgcAssetProfileEntrance>>

    @GET("/metaverse/item/ugc/batchPublish")
    suspend fun publicUgcAssetBatch(
        @Query("feedIds") feedIds: String
    ): ApiResult<Boolean>

    @GET("/metaverse/item/ugc/batchAbolish")
    suspend fun privateUgcAssetBatch(
        @Query("feedIds") feedIds: String
    ): ApiResult<Boolean>

    @GET("/metaverse/item/ugc/pin")
    suspend fun pinUgcAsset(
        @Query("feedId") feedId: String
    ): ApiResult<Boolean>

    @GET("/metaverse/item/ugc/unpin")
    suspend fun unpinUgcAsset(
        @Query("feedId") feedId: String
    ): ApiResult<Boolean>

    @GET("/metaverse/item/ugc/feed/getTagTree")
    suspend fun getUgcAssetTagTree(): ApiResult<List<UgcAssetEntrance>>

    @GET("/metaverse/item/ugc/userProfile/getMyModList")
    suspend fun getMyModules(
        @Query("sortType") sortType: Int,
        @Query("pageSize") pageSize: Int,
        @Query("pageNumber") pageNumber: Int,
        @Query("tagTypes") tagType: String?,
        @Query("published") published: Boolean?,
    ): ApiResult<MyUgcModulesResponse>

    @GET("/metaverse/item/ugc/publish")
    suspend fun publishUgcAsset(
        @Query("feedId") feedId: String
    ): ApiResult<String>

    @GET("/metaverse/item/ugc/abolish")
    suspend fun abolishUgcAsset(
        @Query("feedId") feedId: String
    ): ApiResult<String>

    @GET("/metaverse/item/ugc/erase")
    suspend fun eraseUgcAsset(
        @Query("feedId") feedId: String
    ): ApiResult<String>

    @GET("/metaverse/item/ugc/pin")
    suspend fun pinUgcAsset(
        @Query("feedId") feedId: String,
        @Query("pin") pin: Boolean,
    ): ApiResult<Boolean>

    /**
     * 查询运营位列表
     * 目前可用于查询详情页游戏圈活动配置：biz = "game_detail", bizId = gameId
     */
    @GET("/position/v1/query")
    suspend fun getGameDetailOperationInfo(
        @Query("biz") biz: String,
        @Query("bizId") bizId: String,
        @Query("pageNum") pageNum: Int,
        @Query("pageSize") pageSize: Int
    ): ApiResult<OperationList>

    @POST("/qr/v1/resolve")
    suspend fun resolveQrCode(@Body body: QrCodeResolveApiRequest): ApiResult<QrCodeResolveApiResponse>

    @POST("/game/light/up/v1/light")
    suspend fun lightUp(@Body body: LightUpBody): ApiResult<SparkLightUpData>

    @POST("/game/light/up/v1/account/balance")
    suspend fun queryUserBalance(@Body body: UserBalanceRequestBody): ApiResult<List<Balance>>

    /**
     * 获取交易详情
     * 没有mock, 但有个接口一样的 mock:
     * https://mock.metaapp.cn/project/190/interface/api/4556
     */
    @POST("/coin/v2/platform/coin/record/list")
    suspend fun queryCoinRecordList(@Body body: CoinsRecordRequestBody): ApiResult<CoinsRecords>

    @GET("/feed/tutorial/getTutorialState")
    suspend fun getModuleGuideStatus(): ApiResult<Int>

    @POST("/qr/code/create")
    suspend fun qrCodeCreate(@Body body: QrCodeCreateRequest): ApiResult<QrCodeCreateResponse>

    @GET
    suspend fun qrCodeResolve(@Url url: String): ApiResult<QrCodeResolveResponse>

    /**
     * 检查创建群组权限
     * https://mock.metaapp.cn/project/811/interface/api/72593
     */
    @POST("/chat/group/v1/manage/create/power/check")
    suspend fun checkCreateGroupPower(): ApiResult<Boolean>

    /**
     * 加载群聊列表
     * https://mock.metaapp.cn/project/811/interface/api/72397
     */
    @POST("/chat/group/v1/query/info/list")
    suspend fun getGroupChatInfoList(@Body request: MgsGroupChatListRequest): ApiResult<GroupChatInfoPage>

    /**
     * 用户加入的群数量
     * https://mock.metaapp.cn/project/811/interface/api/72401
     */
    @POST("/chat/group/v1/query/info/statistics")
    suspend fun getGroupChatCount(): ApiResult<GroupChatCount>

    /**
     * 搜索群聊
     * https://mock.metaapp.cn/project/811/interface/api/72405
     */
    @POST("/chat/group/v1/query/search")
    suspend fun searchGroupChat(@Body request: MgsGroupChatSearchRequest): ApiResult<GroupChatInfo>

    /**
     * 获取游戏点赞用户列表
     * 使用moduleType和moduleContentId替代gameId
     * 使用offset替代pageNum
     */
    @POST("/opinion/user/like/query")
    suspend fun getLikePlayerList(@Body request: LikeAndPlayerListRequest): ApiResult<LikeAndPlayerListData>

    /**
     * 获取游戏游玩用户列表
     * 使用offset替代pageNum
     */
    @POST("/flower/v1/player/page/query")
    suspend fun getFlowerPlayerList(@Body request: LikeAndPlayerListRequest): ApiResult<LikeAndPlayerListData>

    /**
     * 获取送花排行榜
     */
    @POST("/flower/v1/ranking/query")
    suspend fun getFlowerLeaderboard(@Body request: FlowerLeaderboardRequest): ApiResult<FlowerLeaderboardData>

    /**
     * 查询群详情
     * https://mock.metaapp.cn/project/811/interface/api/72489
     */
    @POST("/chat/group/v1/query/info/detail")
    suspend fun getGroupChatDetailInfo(@Body request: MgsGroupChatIdRequest): ApiResult<GroupChatDetailInfo>

    /**
     * 群聊未处理的申请数量
     * https://mock.metaapp.cn/project/811/interface/api/72529
     */
    @POST("/chat/group/v1/query/ask/unexamine/count")
    suspend fun getGroupChatPendingRequestCount(): ApiResult<Int>

    /**
     * 用户管理的群聊的申请列表
     * https://mock.metaapp.cn/project/811/interface/api/72533
     */
    @POST("/chat/group/v1/query/ask/list")
    suspend fun getGroupChatPendingRequestList(@Body request: MgsGroupApplyPageRequest): ApiResult<GroupChatApplyInfoList>

    /**
     * 群聊申请加入
     * https://mock.metaapp.cn/project/811/interface/api/72521
     */
    @POST("/chat/group/v1/manage/ask/apply")
    suspend fun applyJoinGroupChat(@Body request: MgsGroupChatApplyJoinRequest): ApiResult<Boolean>

    /**
     * 群聊申请审核
     * https://mock.metaapp.cn/project/811/interface/api/72525
     */
    @POST("/chat/group/v1/manage/ask/examine")
    suspend fun processApplyJoinGroupChat(@Body request: MgsGroupChatApplyJoinProcessRequest): ApiResult<Boolean>

    /**
     * 创建群聊
     * https://mock.metaapp.cn/project/811/interface/api/72357
     */
    @POST("/chat/group/v1/manage/create")
    suspend fun createGroupChat(@Body request: MgsGroupChatCreateRequest): ApiResult<GroupChatDetailInfo>

    /**
     * 解散群聊
     * https://mock.metaapp.cn/project/811/interface/api/72361
     */
    @POST("/chat/group/v1/manage/disband")
    suspend fun disbandGroupChat(@Body request: MgsGroupChatIdRequest): ApiResult<Boolean>

    /**
     * 编辑群聊信息
     * https://mock.metaapp.cn/project/811/interface/api/72365
     */
    @POST("/chat/group/v1/manage/info/edit")
    suspend fun editGroupChat(@Body request: MgsGroupChatEditRequest): ApiResult<GroupChatDetailInfo>

    /**
     * 邀请群成员
     * https://mock.metaapp.cn/project/811/interface/api/72369
     */
    @POST("/chat/group/v1/manage/member/invite")
    suspend fun inviteGroupChatMembers(@Body request: MgsGroupChatInviteMembersRequest): ApiResult<GroupChatAddMembers>

    /**
     * 主动加入群组, 需要根据返回的结果判断加入成功, 以及失败的原因
     * https://mock.metaapp.cn/project/811/interface/api/72373
     */
    @POST("/chat/group/v1/manage/member/join")
    suspend fun joinGroupChat(@Body request: MgsGroupChatIdRequest): ApiResult<GroupChatAddMembers>

    /**
     * 主动退出群组
     * https://mock.metaapp.cn/project/811/interface/api/72377
     */
    @POST("/chat/group/v1/manage/member/leave")
    suspend fun leaveGroupChat(@Body request: MgsGroupChatIdRequest): ApiResult<Boolean>

    /**
     * 踢出群组成员
     * https://mock.metaapp.cn/project/811/interface/api/72381
     */
    @POST("/chat/group/v1/manage/member/remove")
    suspend fun removeGroupChatMember(@Body request: MgsGroupChatRemoveMemberRequest): ApiResult<Boolean>

    /**
     * 编辑群组成员通知开关
     * https://mock.metaapp.cn/project/811/interface/api/72385
     */
    @POST("/chat/group/v1/manage/motification/edit")
    suspend fun editGroupChatNotification(@Body request: MgsGroupChatEditNotificationRequest): ApiResult<Boolean>

    /**
     * 编辑群成员权限
     * https://mock.metaapp.cn/project/811/interface/api/72389
     */
    @POST("/chat/group/v1/manage/power/edit")
    suspend fun editGroupChatMemberPower(@Body request: MgsGroupChatEditMemberPowerRequest): ApiResult<Boolean>

    /**
     * 加载群聊成员
     * https://mock.metaapp.cn/project/811/interface/api/72393
     */
    @POST("/chat/group/v1/query/member/loading")
    suspend fun getGroupChatMembers(@Body request: MgsGroupChatMembersRequest): ApiResult<GroupChatMembersPage>

    /**
     * 根据 imIds 获取群信息
     * https://mock.metaapp.cn/project/811/interface/api/72929
     */
    @POST("/chat/group/v1/query/info/imid")
    suspend fun getGroupSimpleInfoByImIds(@Body request: MgsGetSimpleGroupInfoByImIdsRequest): ApiResult<Map<String, GroupSimpleInfo>>

    /**
     * 获取礼物商品列表
     * https://mock.metaapp.cn/project/160/interface/api/73413
     */
    @GET("/member/v1/product/gifts")
    suspend fun getProductGifts(@Query("type") type: Int): ApiResult<GiftFlowers>

    /**
     * 获取赠礼物条件
     * https://mock.metaapp.cn/project/1078/interface/api/73525
     */
    @POST("/developer-web/developer/game/config/query")
    suspend fun getSendGiftConditions(@Body request: SendGiftConditionsRequestBody): ApiResult<SendGiftConditionsInfoResult>

    /**
     * 更新当前游戏的赠礼开关
     * https://mock.metaapp.cn/project/1078/interface/api/73529
     */
    @POST("/developer-web/developer/game/config/update")
    suspend fun switchSendGift(@Body request: SwitchSendGiftRequestBody): ApiResult<Int>

    /**
     * ugc游戏详情页
     * [mock](https://mock.metaapp.cn/project/132/interface/api/73421)
     */
    @GET("/ugc/game/park/gpark/query/detail/v1")
    suspend fun getUgeGameDetail(@Query("gameId") gameId: String): ApiResult<UgcGameDetailResponse>

    /**
     * pgc游戏详情页
     * [mock](https://mock.metaapp.cn/project/132/interface/api/73437)
     */
    @GET("/pgc/game/park/gpark/query/detail/v1")
    suspend fun getPgcGameDetail(@Query("gameId") gameId: String): ApiResult<PgcGameDetailResponse>

    /**
     * 游戏作品最近送花人
     * [mock](https://mock.metaapp.cn/project/132/interface/api/74073)
     */
    @POST("/flower/v1/list/query")
    suspend fun getSendGiftUserList(@Body request: GetSendGiftUserRequestBody): ApiResult<List<SendGiftData>>
}