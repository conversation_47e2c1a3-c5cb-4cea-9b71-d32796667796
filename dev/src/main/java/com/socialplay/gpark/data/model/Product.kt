package com.socialplay.gpark.data.model

data class Product(
        val productId: String,
        val title: String,
        val price: String,
        val priceCurrencyCode: String,
        val realPrice: Long,
        val subscriptionOfferDetails: SubscriptionOfferDetails?=null,
) {
        companion object {
                var INAPP = "inapp"
                var SUBS = "subs"
                val SUCCESS = 200
                val FAILD = 404
                val VERSION_UNSUPPORT = "VERSION_UNSUPPORT"
                val SDK_NOT_INIT = "SDK_NOT_INIT"
                //拉起支付弹窗前失败
                val FAIL_LACH_CODE = 0
                //拉起支付弹窗后失败-已经购买当前商品并且还是未消耗状态
                val ITEM_ALREADY_OWNED: Int = 7

        }
}
data class SubscriptionOfferDetails(
        //支付类型（价格阶段的重复模式，
        //值为 1 表示将在无限的计费周期内重复进行，除非用户主动取消
        //值为 2 就表示将在 payType指定的周期内重复扣费
        //值为 3 表示是一次性收费，不会重复
        val payType: Int,
        //周期 P1W 表示一周，P1M 表示一月，P1Y 表示一年
        val period: String,
        //优惠价格的周期（假如原价==优惠卷）这个参数为空
        val salePeriod: String?,
        //原价格
        val originalPrice: String,
        val baseid: String,
        val offerid: String?
)

data class BodyRequestOrder(
        val amount: Long,
        val payAmount: Long,
        val payUnit: String,
        var productCode: String,
        val productName: String,
        val count: Int,
        val payTunnel: Int,
        val payChannel: Int,
        val nonce: Long,
        val productPrice: Long,
        val sceneCode: Int,
)