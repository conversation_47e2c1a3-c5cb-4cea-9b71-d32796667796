package com.socialplay.gpark.data.kv

import com.socialplay.gpark.util.property.MMKVScope
import com.socialplay.gpark.util.property.kvProperty
import com.tencent.mmkv.MMKV

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/09/27
 *     desc   :
 * </pre>
 */
class CommunityKV(override val mmkv: MMKV) : MMKVScope {
    companion object {
        private const val KEY_HAS_READ_COMMUNITY_RULE = "key_has_read_community_rule"
        private const val KEY_PUBLISH_POST_DRAFT = "key_publish_post_draft"
        private const val KEY_POST_SHOW_EDIT_RED_DOT = "key_post_show_edit_red_dot"
    }

    var hasReadCommunityRule by kvProperty(defValue = false, KEY_HAS_READ_COMMUNITY_RULE)
    var publishPostDraft: String? by kvProperty(defValue = null, KEY_PUBLISH_POST_DRAFT)
    var showEditRedDot by kvProperty(defValue = true, KEY_POST_SHOW_EDIT_RED_DOT)
}