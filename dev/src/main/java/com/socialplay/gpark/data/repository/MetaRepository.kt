package com.socialplay.gpark.data.repository

import androidx.paging.ExperimentalPagingApi
import androidx.paging.PagingData
import com.ly123.tes.mgs.metacloud.message.MetaConversation
import com.ly123.tes.mgs.metacloud.model.Conversation
import com.ly123.tes.mgs.metacloud.model.Conversation.ConversationType
import com.ly123.tes.mgs.metacloud.model.PagingResult
import com.meta.biz.mgs.data.model.MgsPlayerInfo
import com.meta.biz.mgs.data.model.MgsUserInfo
import com.meta.biz.ugc.model.EditorTemplate
import com.meta.box.biz.friend.model.FriendInfo
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.api.MetaDTokenApi
import com.socialplay.gpark.data.base.ApiDataException
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.invokeAndRethrow
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.successOr
import com.socialplay.gpark.data.base.suspendApiNotNull
import com.socialplay.gpark.data.interactor.DeviceInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.local.AppDatabase
import com.socialplay.gpark.data.local.SimpleDiskLruCache
import com.socialplay.gpark.data.mapper.MetaMapper
import com.socialplay.gpark.data.model.BodyRequestOrder
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.GameItem
import com.socialplay.gpark.data.model.GameRoomList
import com.socialplay.gpark.data.model.GameRoomStatus
import com.socialplay.gpark.data.model.GameSuggestionInfo
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.MyPlayedGame
import com.socialplay.gpark.data.model.QrResult
import com.socialplay.gpark.data.model.ReviewGameInfo
import com.socialplay.gpark.data.model.SearchGameItem
import com.socialplay.gpark.data.model.SnsInfo
import com.socialplay.gpark.data.model.SysActivitiesInfo
import com.socialplay.gpark.data.model.SysHeaderInfo
import com.socialplay.gpark.data.model.TTaiConfig
import com.socialplay.gpark.data.model.UGGameSuggestionInfo
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.UniJumpConfig.Companion.POSITION_GAME_DETAIL
import com.socialplay.gpark.data.model.UniJumpConfig.Companion.POSITION_KOL_BANNER
import com.socialplay.gpark.data.model.UniJumpConfig.Companion.POSITION_KOL_FLY_WHEEL
import com.socialplay.gpark.data.model.UniJumpConfig.Companion.POSITION_PROFILE
import com.socialplay.gpark.data.model.UniJumpConfig.Companion.POSITION_ROLE_FLY_WHEEL
import com.socialplay.gpark.data.model.UniJumpConfig.Companion.POSITION_UGC_BANNER_NEW
import com.socialplay.gpark.data.model.UniJumpConfig.Companion.POSITION_UGC_MODULE
import com.socialplay.gpark.data.model.account.PrivacySwitch
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.data.model.account.UserCreateInfo
import com.socialplay.gpark.data.model.aibot.AIBotCreateImageResult
import com.socialplay.gpark.data.model.aibot.AIBotCreateRequest
import com.socialplay.gpark.data.model.aibot.AIBotGenerateInfo
import com.socialplay.gpark.data.model.aibot.AiBotConversationResult
import com.socialplay.gpark.data.model.aibot.AiBotFollowResult
import com.socialplay.gpark.data.model.aibot.BotInfo
import com.socialplay.gpark.data.model.aibot.BotInfoCreate
import com.socialplay.gpark.data.model.auth.QRCodeAuthScanResult
import com.socialplay.gpark.data.model.choice.CardAll
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceCardListApiResult
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.data.model.choice.Recommend
import com.socialplay.gpark.data.model.choice.RecommendRequestBody
import com.socialplay.gpark.data.model.community.UserMuteStatus
import com.socialplay.gpark.data.model.creator.CreatorFrameResult
import com.socialplay.gpark.data.model.creator.RecommendCreatorRequest
import com.socialplay.gpark.data.model.creator.RecommendCreatorResult
import com.socialplay.gpark.data.model.creator.RecommendKolUgcResult
import com.socialplay.gpark.data.model.creator.RecommendUgcResult
import com.socialplay.gpark.data.model.creator.label.UgcPublishLabel
import com.socialplay.gpark.data.model.creator.morecreator.LabelCreatorRequest
import com.socialplay.gpark.data.model.creator.morecreator.LabelCreatorResult
import com.socialplay.gpark.data.model.creator.morecreator.TypeCreatorRequest
import com.socialplay.gpark.data.model.creator.morecreator.TypeCreatorResult
import com.socialplay.gpark.data.model.creator.moreugc.FollowedCreatorUgcRequest
import com.socialplay.gpark.data.model.creator.moreugc.FollowedCreatorUgcWrapper
import com.socialplay.gpark.data.model.editor.EditorCreationShowInfo
import com.socialplay.gpark.data.model.editor.EditorHomeMomentData
import com.socialplay.gpark.data.model.editor.EditorHomePopularData
import com.socialplay.gpark.data.model.editor.EditorLocalStatusInfo
import com.socialplay.gpark.data.model.editor.EditorNotice
import com.socialplay.gpark.data.model.editor.FormworkList
import com.socialplay.gpark.data.model.editor.GroupedData
import com.socialplay.gpark.data.model.editor.MultiTsGameResult
import com.socialplay.gpark.data.model.editor.MyCreationsV3Request
import com.socialplay.gpark.data.model.editor.MyCreationsV4Request
import com.socialplay.gpark.data.model.editor.NoticeWrapper
import com.socialplay.gpark.data.model.editor.QueryPositiveComment
import com.socialplay.gpark.data.model.editor.ReqFormWorkArchiveBody
import com.socialplay.gpark.data.model.editor.ReqFormWorkV4Body
import com.socialplay.gpark.data.model.editor.TSTypeInfo
import com.socialplay.gpark.data.model.editor.UgcBannerInfo
import com.socialplay.gpark.data.model.editor.UgcFormWorkArchiveData
import com.socialplay.gpark.data.model.editor.UgcFormWorkV4Data
import com.socialplay.gpark.data.model.editor.UgcGameConfig
import com.socialplay.gpark.data.model.editor.UgcGameInfo
import com.socialplay.gpark.data.model.editor.cloud.ProjectLimit
import com.socialplay.gpark.data.model.editor.cloud.UgcBackupInfo
import com.socialplay.gpark.data.model.editor.cloud.UgcCloudProject
import com.socialplay.gpark.data.model.editor.share.AvatarShareCompositeBackground
import com.socialplay.gpark.data.model.entity.AIConversationEntity
import com.socialplay.gpark.data.model.entity.AIMessageEntity
import com.socialplay.gpark.data.model.entity.GameDetailEntity
import com.socialplay.gpark.data.model.entity.RecentUgcGameEntity
import com.socialplay.gpark.data.model.feedback.FeedbackConfigItem
import com.socialplay.gpark.data.model.feedback.SubmitNewFeedbackRequest
import com.socialplay.gpark.data.model.flower.FlowerLeaderboardData
import com.socialplay.gpark.data.model.friend.CommonShareRequest
import com.socialplay.gpark.data.model.friend.FriendRequestInfoWrapper
import com.socialplay.gpark.data.model.gamedetail.LikeAndPlayerListData
import com.socialplay.gpark.data.model.gamereview.AddAppraiseReplyRequest
import com.socialplay.gpark.data.model.gamereview.AppraiseReplyListRequest
import com.socialplay.gpark.data.model.gamereview.AppraiseReplyListResult
import com.socialplay.gpark.data.model.gamereview.AttentionRequest
import com.socialplay.gpark.data.model.gamereview.GameAppraiseData
import com.socialplay.gpark.data.model.gamereview.GameReviewResult
import com.socialplay.gpark.data.model.gamereview.GameScoreResult
import com.socialplay.gpark.data.model.gift.GiftFlower
import com.socialplay.gpark.data.model.gift.SendGiftConditionsInfo
import com.socialplay.gpark.data.model.gift.SendGiftData
import com.socialplay.gpark.data.model.groupchat.GroupChatAddMembers
import com.socialplay.gpark.data.model.groupchat.GroupChatApplyInfoList
import com.socialplay.gpark.data.model.groupchat.GroupChatCount
import com.socialplay.gpark.data.model.groupchat.GroupChatDetailInfo
import com.socialplay.gpark.data.model.groupchat.GroupChatInfoPage
import com.socialplay.gpark.data.model.groupchat.GroupChatMemberInfo
import com.socialplay.gpark.data.model.groupchat.MgsGroupApplyPageRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatApplyJoinProcessRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatApplyJoinRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatCreateRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatEditNotificationRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatEditRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatIdRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatInviteMembersRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatListRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatRemoveMemberRequest
import com.socialplay.gpark.data.model.im.HomeOperationNotification
import com.socialplay.gpark.data.model.im.ImInfo
import com.socialplay.gpark.data.model.im.ImUpdate
import com.socialplay.gpark.data.model.im.MetaSimpleUserEntity
import com.socialplay.gpark.data.model.im.ReviewTextRiskResult
import com.socialplay.gpark.data.model.im.RiskQueryResult
import com.socialplay.gpark.data.model.im.SystemNotification
import com.socialplay.gpark.data.model.im.request.CreateImgRiskCheckTaskRequest
import com.socialplay.gpark.data.model.im.request.IMCheckRequest
import com.socialplay.gpark.data.model.im.request.RiskTaskQueryRequest
import com.socialplay.gpark.data.model.member.MemberInfo
import com.socialplay.gpark.data.model.member.MemberRequest
import com.socialplay.gpark.data.model.mgs.MgsSceneConfig
import com.socialplay.gpark.data.model.moments.MomentsTemplateBody
import com.socialplay.gpark.data.model.moments.PlotListBody
import com.socialplay.gpark.data.model.pay.CoinType
import com.socialplay.gpark.data.model.pay.CoinsRecordRequestBody
import com.socialplay.gpark.data.model.pay.CoinsRecords
import com.socialplay.gpark.data.model.pay.SubmitResult
import com.socialplay.gpark.data.model.pay.SubscribeProductInfo
import com.socialplay.gpark.data.model.pay.TakeOderResult
import com.socialplay.gpark.data.model.pay.UserBalance
import com.socialplay.gpark.data.model.plot.PlotTemplate
import com.socialplay.gpark.data.model.post.CommunityBlock
import com.socialplay.gpark.data.model.post.CommunityFeedWrapper
import com.socialplay.gpark.data.model.post.CommunityTopicBlockWrap
import com.socialplay.gpark.data.model.post.OpinionRequestBody
import com.socialplay.gpark.data.model.post.PostCardResult
import com.socialplay.gpark.data.model.post.PostCommentListRequestBody
import com.socialplay.gpark.data.model.post.PostCommentRequestBody
import com.socialplay.gpark.data.model.post.PostPublishResult
import com.socialplay.gpark.data.model.post.PostReplyListRequestBody
import com.socialplay.gpark.data.model.post.PostReplyRequestBody
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.PublishPostRequest
import com.socialplay.gpark.data.model.post.SearchPostCardRequest
import com.socialplay.gpark.data.model.post.topic.TopicDetailInfo
import com.socialplay.gpark.data.model.profile.RelationCountResult
import com.socialplay.gpark.data.model.profile.RelationListResult
import com.socialplay.gpark.data.model.profile.UserProfileInfo
import com.socialplay.gpark.data.model.profile.friend.OthersFriendList
import com.socialplay.gpark.data.model.profile.recent.GameEntity
import com.socialplay.gpark.data.model.profile.recent.RecentPlayListV2Response
import com.socialplay.gpark.data.model.profile.request.RelationCountRequest
import com.socialplay.gpark.data.model.profile.request.RelationListRequest
import com.socialplay.gpark.data.model.qrcode.QrCodeCreateRequest
import com.socialplay.gpark.data.model.qrcode.QrCodeCreateResponse
import com.socialplay.gpark.data.model.qrcode.QrCodeResolveApiRequest
import com.socialplay.gpark.data.model.qrcode.QrCodeResolveApiResponse
import com.socialplay.gpark.data.model.qrcode.QrCodeResolveResponse
import com.socialplay.gpark.data.model.room.CanJoinRoom
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.data.model.room.CottageRoomInfo
import com.socialplay.gpark.data.model.room.CottageRoomList
import com.socialplay.gpark.data.model.room.CottageVisitor
import com.socialplay.gpark.data.model.room.GetRoomListResult
import com.socialplay.gpark.data.model.room.House
import com.socialplay.gpark.data.model.room.RoomStyle
import com.socialplay.gpark.data.model.sdk.AppAccessToken
import com.socialplay.gpark.data.model.sdk.AuthAppInfo
import com.socialplay.gpark.data.model.share.RelayData
import com.socialplay.gpark.data.model.share.RoBuxRecordInfo
import com.socialplay.gpark.data.model.task.DailyTaskInfo
import com.socialplay.gpark.data.model.task.FinishDailyTaskBody
import com.socialplay.gpark.data.model.user.AccessTokenRefreshResult
import com.socialplay.gpark.data.model.user.AuthInfoApiResult
import com.socialplay.gpark.data.model.user.ContinueAccountInfo
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.RedBadgeData
import com.socialplay.gpark.data.model.user.RedBadgeRequest
import com.socialplay.gpark.data.model.user.RelationType
import com.socialplay.gpark.data.model.user.UserRelation
import com.socialplay.gpark.data.model.user.VisitorInfoApiResult
import com.socialplay.gpark.data.model.videofeed.VideoFeedApiResult
import com.socialplay.gpark.function.lock.AppStoreReviewLock
import com.socialplay.gpark.function.mw.bean.MWGameResourceRequest
import com.socialplay.gpark.function.mw.bean.MWGameResourceResponse
import com.socialplay.gpark.function.mw.bean.MWLaunchGameExpand
import com.socialplay.gpark.function.mw.bean.MWLaunchMgsInfo
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.insertAt
import com.socialplay.gpark.util.fromJSON
import com.socialplay.gpark.util.ifNullOrEmpty
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.lastOrNull
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.withContext

/**
 * Created by yaqi.liu on 2021/6/22
 */
@ExperimentalPagingApi
open class MetaRepository(
    private val metaApi: MetaApi,
    private val dtokenMetaApi: MetaDTokenApi,
    private val metaKV: MetaKV,
    private val db: AppDatabase,
    private val cache: SimpleDiskLruCache,
    private val deviceInteractor: DeviceInteractor,
    private val metaMapper: MetaMapper

) : IMetaRepository {

    private var gameRepository: GameRepository = GameRepository(
        metaApi, db.gameDetailDao(), db.myPlayedGameDao(), db.recentUgcGameDao(), db, metaKV, metaMapper, deviceInteractor
    )

    private var authRepository = AuthRepository(metaApi)
    private var userRepository = UserRepository(metaApi, dtokenMetaApi, metaKV, authRepository, db.shareRecordDao)
    private var imRepository: ImRepository = ImRepository(metaApi, db, db.metaSimpleUserDao(), db.aiMessageDao, db.aiConversationDao())
    private var friendRepository = FriendRepository(metaApi, metaKV)
    private var mgsRepository = MgsRepository(metaApi, metaKV, db, metaMapper, gameRepository)
    private var accountRepository = AccountRepository(metaApi, metaKV, metaMapper)
    private var gameReviewRepository = GameReviewRepository(metaApi, cache)
    private var editorRepository: EditorRepository = EditorRepository(this, metaApi, metaKV, cache, metaMapper)
    private var miscRepository = MiscRepository(metaApi, db, db.metaSimpleUserDao(), metaKV)
    private var tsGameRepository: TSGameRepository = TSGameRepository(metaApi, db.metaCacheDao())
    private var thirdAppAuthRepository: ThirdAppAuthRepository = ThirdAppAuthRepository(metaApi)
    private var postRepository: PostRepository = PostRepository(miscRepository, metaApi, metaKV)
    private var videoFeedRepository: VideoFeedRepository = VideoFeedRepository(metaApi, miscRepository, metaKV)

    private var userMemberRepository: UserMemberRepository = UserMemberRepository(metaApi)

    private var homeRoomRepository: HomeRoomRepository = HomeRoomRepository(metaApi, metaKV.tTaiKV)
    private val plotRepository: PlotRepository = PlotRepository(metaApi)

    private val avatarRepository: AvatarRepository = AvatarRepository(metaApi, cache, metaKV, miscRepository)
    private val ugcRepository: UgcRepository = UgcRepository(metaApi)
    private val groupChatRepository: GroupChatRepository = GroupChatRepository(metaApi, metaKV, cache)
    private val giftsRepository: GiftsRepository = GiftsRepository(metaApi, cache)

    /**
     * 游戏相关
     */
    override suspend fun getGameInfoCache(gameId: String): DataResult<GameDetailInfo> = gameRepository.getGameInfoCache(gameId)

    // 是否是我玩过的
    override suspend fun isMyPlayedGame(id: String) = gameRepository.isMyPlayedGame(id)

    override suspend fun updateMyGameInfo(info: GameDetailInfo, percent: Float) = gameRepository.updateMyGameInfo(info, percent)

    // 删除我玩过的游戏
    override suspend fun deleteMyPlayedGame(id: String) = gameRepository.deleteMyPlayedGame(id)

    override suspend fun deleteMyPlayedGameList(ids: List<String>) {
        gameRepository.deleteMyPlayedGameList(ids)
    }


    override fun fetchPlayedGame(size: Int): Flow<PagingData<MyPlayedGame>> = gameRepository.fetchPlayedGame(size)

    override fun fetchPlayedGameV2(index: Int, size: Int): Flow<List<MyPlayedGame>> = gameRepository.fetchPlayedGameV2(index, size)

    override fun fetchRecentPlayedPgcCardList(
        pageSize: Int, pageNum: Int, uuid: String
    ): Flow<PostCardResult> = gameRepository.fetchRecentPlayedPgcCardList(pageSize, pageNum, uuid)

    override fun fetchRecentPlayedPgcCardListV2(
        pageSize: Int, pageNum: Int, uuid: String
    ): Flow<PostCardResult> = gameRepository.fetchRecentPlayedPgcCardListV2(pageSize, pageNum, uuid)

    override fun fetchFriendRequests(size: Int): Flow<PagingData<FriendRequestInfoWrapper>> = friendRepository.fetchFriendRequests(size)

    override fun fetchRecommendGames(size: Int): Flow<PagingData<GameItem>> {
        return gameRepository.fetchRecommendGames(size)
    }

    override fun fetchSearchGame(
        searchKey: String, isRelate: Boolean, size: Int, keywordId: String?
    ): Flow<PagingData<SearchGameItem>> {
        return gameRepository.fetchSearchGame(searchKey, isRelate, size, keywordId)
    }

    override fun postSearchReport(contentId: String, contentType: Int): Flow<DataResult<Any?>> = gameRepository.postSearchReport(contentId, contentType)

    override suspend fun fetchGameInfoById(id: String): Flow<DataResult<GameDetailInfo>> = gameRepository.getGameInfoByGameId(id)

    override fun getGameDetailEnteredTimes(gameId: String): Long {
        return gameRepository.getGameDetailEnteredTimes(gameId)
    }

    override suspend fun onLaunchGame(gameId: String, packageName: String) {
        gameRepository.onLaunchGame(gameId, packageName)
    }

    override suspend fun visitorLogin(loginType: LoginType): Flow<DataResult<VisitorInfoApiResult>> = userRepository.visitorLogin(loginType)


    override fun saveMetaUserInfo(metaUserInfo: MetaUserInfo) = userRepository.saveMetaUserInfo(metaUserInfo)


    override suspend fun refreshAccessToken(): AccessTokenRefreshResult? = userRepository.refreshAccessToken()

    override fun logout(notifyBackend: Boolean): Flow<DataResult<Boolean>> = userRepository.logout(notifyBackend)

    override suspend fun insertGameDetailEntityIfNoExists(gameInfo: GameDetailEntity) {
        gameRepository.insertGameDetailEntityIfNoExists(gameInfo)
    }

    override fun isNeedShowGuide(): Boolean {
        return metaKV.appKV.isNeedShowGuide
    }

    override fun showGuideDone() {
        metaKV.appKV.isNeedShowGuide = false
    }

    override suspend fun getMetaUserInfoFromNet(): Flow<DataResult<MetaUserInfo>> = userRepository.getMetaUserInfoFromNet()

    override suspend fun bindByEmail(email: String, code: String): Flow<DataResult<Boolean>> = userRepository.bindByEmail(email, code)

    override suspend fun bindByParentEmail(
        parentEmail: String, code: String
    ): Flow<DataResult<Boolean>> = userRepository.bindByParentEmail(parentEmail, code)

    override suspend fun bindEmailChange(
        newEmail: String, newEmailCode: String, oldEmailCode: String
    ): Flow<DataResult<Boolean>> = userRepository.bindEmailChange(newEmail, newEmailCode, oldEmailCode)

    override suspend fun bindParentEmailChange(
        newEmail: String, newEmailCode: String, oldEmailCode: String
    ): Flow<DataResult<Boolean>> = userRepository.bindParentEmailChange(newEmail, newEmailCode, oldEmailCode)

    override suspend fun bindAccountAndPassword(bindKey: String, password: String): Flow<DataResult<Boolean>> = userRepository.bindAccountAndPassword(bindKey, password)
    override suspend fun bindPasswordByGparkId(bindKey: String, password: String): Flow<DataResult<Boolean>> = userRepository.bindPasswordByGparkId(bindKey, password)
    override suspend fun getImInfo(): DataResult<ImInfo> = imRepository.getImInfo()
    override suspend fun refreshUserInfoForNet() = imRepository.refreshUserInfoForNet()

    override suspend fun getSimpleUserInfo(uuid: String): MetaSimpleUserEntity? = imRepository.getSimpleUserInfo(uuid)

    override suspend fun getUnReadCount(
        conversationType: Conversation.ConversationType, targetId: String, callback: (ImUpdate) -> Unit?
    ) = imRepository.getUnReadCount(conversationType, targetId, callback)

    override suspend fun saveNewestFriendWithStateToLocal(friendMap: List<FriendInfo>) = friendRepository.saveNewestFriendWithStateToLocal(friendMap)

    override suspend fun getNewestFriendWithStateFromLocal(): DataResult<List<FriendInfo>> = friendRepository.getNewestFriendWithStateFromLocal()

    override suspend fun getQrCode(): Flow<DataResult<String>> = friendRepository.getQrCode()
    override suspend fun clearMessageUnReadStatus(
        conversationType: Conversation.ConversationType?, targetId: String?, callback: (ImUpdate) -> Unit?
    ) = imRepository.clearMessageUnReadStatus(conversationType, targetId, callback)

    override suspend fun removeConversation(
        conversationType: Conversation.ConversationType, targetId: String, callback: (ImUpdate) -> Unit?
    ) = imRepository.removeConversation(conversationType, targetId, callback)

    override suspend fun deleteMessages(
        conversationType: Conversation.ConversationType, targetId: String, callback: (ImUpdate) -> Unit?
    ) = imRepository.deleteMessages(conversationType, targetId, callback)

    override fun syncConversationReadStatus(
        type: Conversation.ConversationType, targetId: String, timestamp: Long, callback: (Boolean) -> Unit?
    ) = imRepository.syncConversationReadStatus(type, targetId, timestamp)

    override suspend fun setConversationToTop(
        conversationType: Conversation.ConversationType, targetId: String, isTop: Boolean, callback: (ImUpdate) -> Unit?
    ) = imRepository.setConversationToTop(conversationType, targetId, isTop, callback)

    override suspend fun putUserInfoCache(userinfo: MetaSimpleUserEntity) = imRepository.putUserInfoCache(userinfo)

    override suspend fun queryFriendInfo(uuid: String): DataResult<FriendInfo> = imRepository.queryFriendInfo(uuid)

    override suspend fun updateUserPlayGame(gameId: String): Flow<DataResult<String?>> = userRepository.updateUserPlayGame(gameId)

    override suspend fun updateUserStartGame(gameId: String) = userRepository.updateUserStartGame(gameId)

    override suspend fun updateUserStopGame(gameId: String) = userRepository.updateUserStopGame(gameId)

    override suspend fun searchFriends(clear: Boolean, keyword: String, pageSize: Int) = friendRepository.searchFriends(clear, keyword, pageSize)

    override suspend fun fetchConversationListV2(
        seq: String?, count: Int, conversationTypes: Set<ConversationType>
    ): PagingResult<List<MetaConversation>>? = imRepository.fetchConversationListV2(seq, count, conversationTypes)

    override suspend fun fetchAllConversationList(): List<MetaConversation> = imRepository.fetchAllConversationList()

    override suspend fun getPlayerInfoByUuId(
        packageName: String, uuid: String
    ): Flow<DataResult<MgsPlayerInfo>> = mgsRepository.getPlayerInfoByUuId(packageName, uuid)

    override suspend fun getMgsGameInfoFromLocal(packageName: String): GameDetailInfo? = mgsRepository.getMgsGameInfoFromLocal(packageName)

    override fun getMgsGameIdByPackageName(packageName: String): String? = mgsRepository.getMgsGameIdByPackageName(packageName)

    override suspend fun getGameInfoByGameIdWithoutFlow(gameId: String): DataResult<GameDetailInfo> {
        return gameRepository.getGameInfoByGameIdWithoutFlow(gameId)
    }


    override suspend fun requestScanQRCode(url: String): DataResult<QRCodeAuthScanResult> = authRepository.requestScanQRCode(url)

    override suspend fun confirmLogin(code: String): DataResult<Any> = authRepository.confirmLogin(code)

    override fun fetchGameInfoByIdFromRemoteWithCache(id: String): Flow<DataResult<GameDetailInfo>> = gameRepository.getGameInfoByGameIdFromRemoteWithCache(id)

    override fun searchReviewGameById(gameId: String): Flow<DataResult<ReviewGameInfo>> {
        return gameRepository.searchReviewGameById(gameId)
    }

    override fun getTTaiConfigById(resourceId: Int): Flow<DataResult<TTaiConfig>> = miscRepository.getTTaiConfigById(resourceId)

    override fun getTTaiConfigByIdV2(resourceId: Int) = suspendApiNotNull {
        metaApi.getTTaiConfig(resourceId).also {
            if (it.isSuccessful && it.data != null) {
                metaKV.tTaiKV.saveConfig(it.data)
            }
        }
    }

    override fun getTTaiConfigByIds(ids: List<Int>): Flow<DataResult<List<TTaiConfig>>> = flow {
        val result = DataSource.getDataResultForApi { metaApi.getTTaiConfigs(ids.joinToString(",")) }

        //如果请求成功，则先移除掉旧的值
        if (result is DataResult.Success) {
            ids.forEach { id -> metaKV.tTaiKV.removeConfig(id) }
        }

        result.successOr(null)?.let {
            metaKV.tTaiKV.saveConfigs(it)
        }

        emit(result)
    }

    override fun getQrResultByUrl(url: String): Flow<DataResult<QrResult>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.getQrResultByUrl(url) })
    }

    override suspend fun submitNewFeedback(feedbackInfo: SubmitNewFeedbackRequest): Boolean = miscRepository.submitNewFeedback(feedbackInfo)

    override fun sendEmail(email: String?, scene: String): Flow<DataResult<Boolean>> {
        return accountRepository.sendEmail(email, scene)
    }

    override fun checkEmail(email: String, code: String, scene: String): Flow<DataResult<Boolean>> {
        return accountRepository.checkEmail(email, code, scene)
    }

    override fun passwordReset(
        email: String, code: String, newPassword: String
    ): Flow<DataResult<Boolean>> {
        return userRepository.passwordReset(email, code, newPassword)
    }

    override fun passwordChange(
        oldPassword: String, newPassword: String
    ): Flow<DataResult<Boolean>> {
        return userRepository.passwordChange(oldPassword, newPassword)
    }

    override suspend fun accountSignup(
        account: String, password: String, loginType: LoginType
    ): Flow<LoginState<MetaUserInfo>> {
        return userRepository.accountSignup(account, password, loginType)
    }

    override suspend fun accountLogin(
        account: String, password: String, loginType: LoginType
    ): Flow<LoginState<MetaUserInfo>> = userRepository.accountLogin(account, password, loginType)

    override suspend fun gparkIdLogin(
        gparkId: String,
        password: String,
        loginType: LoginType
    ): Flow<LoginState<MetaUserInfo>> = userRepository.gparkIdLogin(gparkId, password, loginType)

    override suspend fun updateUserInfo(
        birthday: Long?, gender: Int, nickname: String?, portrait: String?, city: String?, signature: String?, reviewBirth: Boolean
    ): Flow<DataResult<Boolean>> {
        return accountRepository.updateUserInfo(
            birthday, gender, nickname, portrait, city, signature, reviewBirth
        )
    }

    override suspend fun ditout(code: String): Flow<DataResult<Boolean>> = userRepository.ditout(code)

    override suspend fun queryGameScore(
        resType: String, resId: String
    ): Flow<DataResult<GameScoreResult?>> = gameReviewRepository.queryGameScore(resType, resId)

    override suspend fun publishGameReview(
        gameId: String, content: String, score: Int, moduleTypeCode: Int?
    ): Flow<DataResult<String?>> = gameReviewRepository.publishGameAppraise(gameId, content, score, moduleTypeCode)

    override suspend fun updateGameReview(
        commentId: String, content: String, score: Int
    ): Flow<DataResult<Boolean>> = gameReviewRepository.updateGameReview(commentId, content, score)

    override suspend fun attitudeGameReview(request: AttentionRequest): Flow<DataResult<Boolean>> = gameReviewRepository.attitudeGameReview(request)

    override suspend fun fetchAppraiseByUid(
        otherUid: String, gameId: String, moduleTypeCode: Int?
    ): Flow<DataResult<GameReviewResult?>> = gameReviewRepository.queryAppraiseByUid(otherUid, gameId, moduleTypeCode)

    override suspend fun fetchAllReviewList(
        gameId: String, queryType: Int?, pageSize: Int, needLoadMore: Boolean, targetCommentId: String?, moduleTypeCode: Int?
    ): Flow<PagingData<GameAppraiseData>> = if (needLoadMore) gameReviewRepository.fetchAllReviewList(
        gameId, queryType, pageSize, moduleTypeCode
    ) else gameReviewRepository.fetchSizeReviewList(
        gameId, queryType, pageSize, targetCommentId, moduleTypeCode
    )

    override suspend fun getAppraiseReplyList(request: AppraiseReplyListRequest): Flow<DataResult<AppraiseReplyListResult?>> = gameReviewRepository.getAppraiseReplyList(request)

    override suspend fun addAppraiseReply(request: AddAppraiseReplyRequest): Flow<DataResult<String?>> = gameReviewRepository.addAppraiseReplay(request)

    override suspend fun deleteGameAppraise(commentId: String): Flow<DataResult<Boolean>> = gameReviewRepository.deleteGameAppraise(commentId)

    override suspend fun deleteReply(replyId: String): Flow<DataResult<Boolean>> = gameReviewRepository.deleteReply(replyId)

    override suspend fun topGameAppraise(commentId: String): Flow<DataResult<String>> = gameReviewRepository.topGameAppraise(commentId)

    override fun topGameAppraiseV2(commentId: String, isTop: Boolean) = gameReviewRepository.topGameAppraiseV2(commentId, isTop)

    override fun checkIfHaveComment(moduleType: String, moduleContentId: String) = gameReviewRepository.checkIfHaveComment(moduleType, moduleContentId)

    override suspend fun fetchLikeByGameId(gameId: String): Flow<DataResult<SnsInfo>> = gameReviewRepository.fetchLikeByGameId(gameId)

    override suspend fun getOperationNoticeList(
        pageNum: Int, pageSize: Int
    ): Flow<DataResult<List<SystemNotification>>> = miscRepository.getOperationNoticeList(pageNum, pageSize)

    override suspend fun getOperationNoticeListV2(): Flow<DataResult<List<UniJumpConfig>>> = miscRepository.getNoticeList(null, UniJumpConfig.POSITION_IM)

    override suspend fun getLatestOperationNotice(): Flow<DataResult<SystemNotification?>> = miscRepository.getLatestOperationNotice()

    override suspend fun getLatestOperationNoticeV2(): Flow<DataResult<UniJumpConfig>> = miscRepository.getLatestOperationNoticeV2()

    override suspend fun markOperationNoticeRead(systemNotification: UniJumpConfig): Flow<DataResult<Any>> = miscRepository.markOperationNoticeRead(systemNotification)

    override suspend fun getSuggestGameIdList(): Flow<DataResult<List<String>>> = gameRepository.getSuggestGameIdList()

    override suspend fun getGameListByIds(gameIdList: List<String>): Flow<DataResult<List<GameSuggestionInfo>>> = gameRepository.getGameListByIds(gameIdList)

    override suspend fun getUgSuggestGameId(): Flow<DataResult<UGGameSuggestionInfo>> = gameRepository.getUgSuggestGameId()

    override suspend fun isSuggestGameAlreadyShow(): Boolean = gameRepository.isSuggestGameAlreadyShow()

    override suspend fun updateSuggestGameShowStatus(isAlreadyShow: Boolean) = gameRepository.updateSuggestGameShowStatus(isAlreadyShow)

    override fun payResultSubmit(map: Map<String, Any>): Flow<DataResult<SubmitResult>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.payResultSubmit(map) })
    }

    override fun privilegePlaceOrder(body: BodyRequestOrder): Flow<DataResult<TakeOderResult>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.privilegePlaceOrder(body) })

    }

    override suspend fun rechargingLoop(orderId: String): Boolean {
        val data = DataSource.getDataResultForApi { metaApi.rechargingLoop(orderId) }
        return data.data ?: false
    }

    override suspend fun getBalance(coinType: CoinType): Flow<DataResult<UserBalance>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.getBalance(HashMap<String, Int>().apply { this["coinType"] = coinType.value }) })
    }

    override suspend fun getPoint(): Flow<DataResult<UserBalance>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.getPoint() })
    }

    override suspend fun mwPay(hashMap: Map<String, Any?>): Flow<DataResult<String?>> = userRepository.mwPay(hashMap)

    override fun getAppLaunchTimes(): Long = gameRepository.getAppLaunchTimes()

    override suspend fun getChoiceCardList(): Flow<DataResult<ChoiceCardListApiResult>> = gameRepository.getChoiceCardList()

    override fun getChoiceCardListForAvatar(): Flow<DataResult<ChoiceCardListApiResult?>> = gameRepository.getChoiceCardListForAvatar()

    override fun getChoiceAiBotCardList(
        tagId: Int?, gender: Int?, pageIndex: Int, pageCount: Int
    ): Flow<DataResult<ChoiceCardListApiResult?>> = gameRepository.getChoiceAiBotCardList(tagId, gender, pageIndex, pageCount)

    override fun isInformationCollectDialogShown(): Boolean {
        return metaKV.appKV.isInformationCollectDialogShown
    }

    override fun updateInformationCollectDialogStatus(isShown: Boolean) {
        metaKV.appKV.isInformationCollectDialogShown = isShown
    }

    // 获取ugc游戏id配置
    override suspend fun getUgcGameConfig(): Flow<DataResult<UgcGameConfig>> = editorRepository.getUgcGameConfig()

    // 获取首页ugc游戏列表
    override fun getUgcGameList(pageSize: Int): Flow<PagingData<MultiTsGameResult>> = editorRepository.getUgcGameList(pageSize)

    // 获取我喜欢的ugc游戏列表
    override fun getUgcGameLikeList(pageSize: Int): Flow<PagingData<UgcGameInfo.Games>> = editorRepository.getUgcGameLikeList(pageSize)

    override suspend fun getEditorPublished(pageNum: String): Flow<DataResult<UgcGameInfo>> = editorRepository.getEditorPublished(pageNum)

    // 删除已发布游戏
    override fun deleteEditorPublished(ugid: String): Flow<DataResult<Boolean>> = editorRepository.deleteEditorPublished(ugid)

    override fun deleteEditorPublishedV2(ugid: String): suspend () -> Boolean = editorRepository.deleteEditorPublishedV2(ugid)

    override suspend fun getUgcTemplateList(
        pageSize: Int, sinceId: String?, currentPage: Int?
    ): Flow<DataResult<ArrayList<EditorTemplate>?>> = editorRepository.getUgcTemplateList(pageSize, sinceId, currentPage)

    // 判断ts游戏的id对应的ugc游戏、mgs功能
    override suspend fun getTsTypeInfo(id: String): DataResult<TSTypeInfo> = editorRepository.getTsTypeInfo(id)

    // 获取未读的通知数量
    override fun getUnreadNoticeCount(type: Int): Flow<DataResult<Int>> = editorRepository.getUnreadNoticeCount(type)

    // 标记所有的通知为已读
    override suspend fun markAllNoticeAsRead(type: Int): DataResult<Boolean> = editorRepository.markAllNoticeAsRead(type)

    override fun getEditorNotice(pageSize: Int): Flow<PagingData<NoticeWrapper>> = editorRepository.getEditorNotice(pageSize)

    override suspend fun getNewestEditorNotice(): Flow<DataResult<EditorNotice.OuterShowNotice?>> = editorRepository.getNewestEditorNotice()

    override suspend fun getMgsUserInfo(gameId: String): Flow<DataResult<MgsUserInfo?>> {
        return mgsRepository.getMgsUserInfo(gameId)
    }

    override suspend fun insertGameDetailCache(info: GameDetailEntity) = gameRepository.insertGameDetailCache(info)

    override suspend fun getGameRoomList(
        gameId: String,
        maxId: String,
        pageSize: Int,
        sortType: Int,
        version: String,
    ): DataResult<GameRoomList> = tsGameRepository.getGameRoomList(gameId, maxId, pageSize, sortType, version)

    override suspend fun getMgsSceneConfig(gameId: String): Flow<DataResult<MgsSceneConfig>> = mgsRepository.getMgsSceneConfig(gameId)

    override fun mgsSceneLike(gameId: String): Flow<DataResult<Boolean>> = mgsRepository.mgsSceneLike(gameId)

    override fun mgsSceneUnLike(gameId: String): Flow<DataResult<Boolean>> = mgsRepository.mgsSceneUnLike(gameId)

    override suspend fun getMgsGameInfoByGameId(gameId: String): GameDetailInfo? = mgsRepository.getMgsGameInfoByGameId(gameId)

    override fun getRecentPlayGameList(uid: String, pageSize: Int): Flow<PagingData<GameEntity>> = gameRepository.getRecentGameListByUuid(uid, pageSize)

    override fun getRecentPlayGameListV2(
        uid: String, page: Int, pageSize: Int
    ) = gameRepository.getRecentGameListByUuidV2(uid, page, pageSize)

    override fun getRecentPlayGameListV3(
        uid: String, pageSize: Int
    ): Flow<PagingData<RecentPlayListV2Response.Game>> = gameRepository.getRecentGameListByUuidV3(uid, pageSize)

    override fun getRecentPlayGameListV4(
        uid: String, page: Int, pageSize: Int
    ): Flow<DataResult<RecentPlayListV2Response>> = gameRepository.getRecentGameListByUuidV4(uid, page, pageSize)

    override fun getRecentPlayGameListMaverick(
        uid: String,
        page: Int,
        pageSize: Int
    ) = gameRepository.getRecentGameListByUuidMaverick(uid, page, pageSize)

    override fun queryUserProfile(uid: String): Flow<DataResult<UserProfileInfo>> = userRepository.queryUserProfile(uid)

    override suspend fun getUserCreate(): Flow<DataResult<UserCreateInfo?>> = userRepository.getUserCreate()

    override suspend fun receiveBadge(badgeCode: String): Flow<DataResult<Boolean>> = userRepository.receiveBadge(badgeCode)

    override fun getOthersFriendList(
        uid: String, pageNum: Int, pageSize: Int
    ): Flow<DataResult<OthersFriendList?>> = friendRepository.getOthersFriendList(uid, pageNum, pageSize)

    override suspend fun authAppCheck(appKey: String): Flow<DataResult<AuthAppInfo>> = thirdAppAuthRepository.authAppCheck(appKey)

    override suspend fun getAppAccessToken(appKey: String): Flow<DataResult<AppAccessToken>> = thirdAppAuthRepository.getAppAccessToken(appKey)

    override fun getLaunchMWMgsInfo(gameId: String): Flow<DataResult<MWLaunchMgsInfo>> = tsGameRepository.getLaunchMWMgsInfo(gameId)

    override fun getLaunchMWGameExpand(
        gameCode: String, params: Map<String, String>
    ): Flow<DataResult<MWLaunchGameExpand>> = tsGameRepository.getLaunchMWGameExpand(gameCode, params)

    override suspend fun reportAdd(
        reportType: String,
        reportedId: String,
        reportContent: String?,
        reportedUid: String?,
        reportReason: String?,
        attachmentUrlList: List<String>?,
        reportSource: String?,
        reportAdditional: String?,
    ): Flow<DataResult<Boolean>> = accountRepository.reportAdd(
        reportType, reportedId, reportContent, reportedUid, reportReason, attachmentUrlList, reportSource, reportAdditional
    )

    override fun relationAdd(targetUid: String, relationType: String): Flow<DataResult<Boolean>> = accountRepository.relationAdd(targetUid, relationType)

    override fun relationDel(targetUid: String, relationType: String): Flow<DataResult<Boolean>> = accountRepository.relationDel(targetUid, relationType)

    // 添加用户关系
    override fun relationAddV2(targetUid: String, relationType: String): suspend () -> Boolean = accountRepository.relationAddV2(targetUid, relationType)

    // 删除用户关系
    override fun relationDelV2(targetUid: String, relationType: String): suspend () -> Boolean = accountRepository.relationDelV2(targetUid, relationType)

    override fun followUser(targetUid: String): suspend () -> Boolean = accountRepository.relationAddV2(targetUid, RelationType.Follow.value)

    override fun unfollowUser(targetUid: String): suspend () -> Boolean = accountRepository.relationDelV2(targetUid, RelationType.Follow.value)

    override suspend fun getBlockRelation(otherUuid: String): Flow<DataResult<UserRelation?>> = accountRepository.getUserRelation(relationType = RelationType.Block, otherUuid)


    override suspend fun getAvailableHomeNotice(): Flow<DataResult<HomeOperationNotification?>> = miscRepository.getAvailableHomeNotice()

    override fun setHomeNoticeClose(noticeId: String, time: Long) = miscRepository.setHomeNoticeClose(noticeId, time)

    override fun chooseDefaultRole(id: String): Flow<DataResult<Boolean>> = editorRepository.chooseDefaultRole(id)

    override suspend fun getTsGameRoomInfo(
        id: String, roomId: String
    ): Flow<DataResult<GameRoomStatus>> = tsGameRepository.getTsGameRoomInfo(id, roomId)

    override suspend fun fetchUgcBannerList(): Flow<DataResult<List<UgcBannerInfo>?>> = editorRepository.fetchUgcBannerList()


    override fun getUserMemberInfoList(request: MemberRequest): Flow<DataResult<List<MemberInfo>>> = userMemberRepository.getUserMemberInfoList(request)

    override suspend fun fetchTemplateInfoByCode(gameCode: String): Flow<DataResult<EditorTemplate?>> = editorRepository.fetchTemplateInfoByCode(gameCode)

    override fun saveCommunityLike(
        resId: String, option: Int, resType: Int
    ): Flow<DataResult<Boolean>> {
        return postRepository.saveCommunityLike(resId, option, resType)
    }

    override fun report(postId: String, reportType: Int): Flow<DataResult<Boolean>> {
        return postRepository.report(postId, reportType)
    }

    override fun getPostOpinion(
        resId: String,
        resType: Int,
    ): Flow<DataResult<Int>> {
        return postRepository.getPostOpinion(resId, resType)
    }

    override fun getLaunchMWMgsInfoWithCache(
        userUnique: String, gameId: String
    ): Flow<DataResult<MWLaunchMgsInfo>> = tsGameRepository.getLaunchMWMgsInfoWithCache(userUnique, gameId)

    override fun getLaunchMWGameExpandWithCache(
        gameCode: String, params: Map<String, String>
    ): Flow<DataResult<MWLaunchGameExpand>> = tsGameRepository.getLaunchMWGameExpandWithCache(gameCode, params)

    override suspend fun getGameDetailNotice(gId: String): Flow<DataResult<List<UniJumpConfig>?>> = miscRepository.getNoticeList(gId, POSITION_GAME_DETAIL)

    override fun getRoleFlyWheel(): Flow<DataResult<List<UniJumpConfig>?>> = miscRepository.getNoticeList(null, POSITION_ROLE_FLY_WHEEL)

    override fun getKolFlyWheel(): Flow<DataResult<List<UniJumpConfig>?>> = miscRepository.getNoticeList(null, POSITION_KOL_FLY_WHEEL)

    override fun getKolBanner(): Flow<DataResult<List<UniJumpConfig>?>> = miscRepository.getNoticeList(null, POSITION_KOL_BANNER)

    override fun getUgcBannerList(): Flow<DataResult<List<UniJumpConfig>?>> = miscRepository.getNoticeList(null, POSITION_UGC_BANNER_NEW)

    override fun getModuleBannerList() = miscRepository.getNoticeList(null, POSITION_UGC_MODULE)

    override fun getProfileBannerList() = miscRepository.getNoticeList(null, POSITION_PROFILE)

    override suspend fun reviewPrivateMessageRisk(
        content: String, gameId: String?
    ): Flow<DataResult<ReviewTextRiskResult?>> = imRepository.reviewMessageRisk(
        IMCheckRequest.AUTH_CODE_PRIVATE, content, gameId
    )

    override suspend fun reviewRoomMessageRisk(
        content: String, gameId: String?
    ): Flow<DataResult<ReviewTextRiskResult?>> = imRepository.reviewMessageRisk(
        IMCheckRequest.AUTH_CODE_ROOM, content, gameId
    )

    override suspend fun isUserAbove13(): Flow<DataResult<Boolean>> = accountRepository.isUserAbove13()

    override fun getPublishedCreationList(
        uuid: String?, orderId: String?
    ): Flow<DataResult<UgcGameInfo>> = editorRepository.getPublishedCreationList(uuid, orderId)

    override fun getPublishedCreationListV2(
        uuid: String?, orderId: String?
    ): suspend () -> UgcGameInfo = editorRepository.getPublishedCreationListV2(uuid, orderId)

    override fun getMyCreations(body: MyCreationsV3Request) = editorRepository.getMyCreations(body)

    override fun pinMyCreations(gameId: String, gameType: Int, pinOrNot: Boolean) = editorRepository.pinMyCreation(gameId, gameType, pinOrNot)

    override suspend fun getUgcIdByPackageName(packageName: String): Flow<DataResult<String?>> = editorRepository.getUgcIdByPackageName(packageName)

    override suspend fun getEditorLocalStatus(bizIdList: List<String>): DataResult<EditorLocalStatusInfo> = editorRepository.getEditorLocalStatus(bizIdList)

    override suspend fun getUgcInfoByIdLIst(ugids: List<String>): DataResult<UgcGameInfo?> = editorRepository.getUgcInfoByIdLIst(ugids)

    override fun getGameTemplate(crGameId: String, type: Long): Flow<DataResult<EditorTemplate>> = gameRepository.getGameTemplate(crGameId, type)

    override fun getGameTemplateV2(crGameId: String, type: Long) = gameRepository.getGameTemplateV2(crGameId, type)

    override fun getUgcDetailPage(ugid: String) = gameRepository.getUgcDetailPage(ugid)

    override fun getUgcGameDetail(ugid: String) = gameRepository.getUgcGameDetail(ugid)

    override fun getPgcGameDetail(gameId: String) = gameRepository.getPgcGameDetail(gameId)

    override suspend fun getFormworkList(
        page: Int, formworkCode: String?
    ): Flow<DataResult<FormworkList>> = editorRepository.getFormworkList(page, formworkCode)

    override suspend fun getFormWorkV4List(body: ReqFormWorkV4Body): Flow<DataResult<UgcFormWorkV4Data>> = editorRepository.getFormWorkV4List(body)

    override fun getFormWorkV4ListMvrk(body: ReqFormWorkV4Body) = editorRepository.getFormWorkV4ListMvrk(body)

    override suspend fun checkFormWorkArchive(body: ReqFormWorkArchiveBody): Flow<DataResult<UgcFormWorkArchiveData>> = editorRepository.checkFormWorkArchive(body)

    override fun checkFormWorkArchiveMvrk(body: ReqFormWorkArchiveBody) = editorRepository.checkFormWorkArchiveMvrk(body)

    override suspend fun reportLaunchUgcGame(ugid: String): DataResult<String> = gameRepository.reportLaunchUgcGame(ugid)


    override suspend fun getRandomNickname(): Flow<DataResult<String?>> = accountRepository.getRandomNickname()

    override fun getRandomNicknameV2() = accountRepository.getRandomNicknameV2()

    override fun checkNickname(nickname: String, uid: String) = accountRepository.checkNickname(nickname, uid)

    override suspend fun getHomeRoomList(
        gameIds: List<String>?, pageNum: Int, pageSize: Int
    ): DataResult<GetRoomListResult> {
        return homeRoomRepository.getHomeRoomList(gameIds, pageNum, pageSize)
    }

    override suspend fun getHomeRoomListV2(
        pageNum: Int, pageSize: Int
    ): suspend () -> GetRoomListResult {
        val rawJson: String = metaKV.tTaiKV.homeRoomGame.ifNullOrEmpty {
            getTTaiConfigById(TTaiKV.ID_HOME_ROOM_GAME).singleOrNull()?.data?.value ?: ""
        }.ifNullOrEmpty {
            throw ApiDataException(GetRoomListResult::class)
        }
        val gameList = GsonUtil.gsonSafeParseCollection<List<RoomStyle>>(rawJson)?.map { it.gameId }
        if (gameList.isNullOrEmpty()) {
            throw ApiDataException(GetRoomListResult::class)
        }
        return homeRoomRepository.getHomeRoomListV2(gameList, pageNum, pageSize)
    }

    override suspend fun getRoomInfo(
        roomId: String,
    ): Flow<DataResult<ChatRoomInfo>> {
        return homeRoomRepository.getHomeRoomDetail(roomId)
    }

    override suspend fun createRoom(
        roomStyle: RoomStyle, roomName: String, roomTag: String
    ): Flow<DataResult<ChatRoomInfo>> {
        return homeRoomRepository.createHomeRoom(roomStyle, roomName, roomTag)
    }

    override fun canJoinRoom(roomId: String, version: String): Flow<DataResult<CanJoinRoom>> {
        return homeRoomRepository.canJoinRoom(roomId, version)
    }

    override suspend fun getSysHeaderInfo(): Flow<DataResult<List<SysHeaderInfo>>> {
        return imRepository.getSysHeaderInfo()
    }

    override fun getSysActivitiesInfo(
        groupId: Long,
        pageSize: Int,
        subGroupKey: String?,
        lastRecordTime: Long?,
    ): Flow<PagingData<SysActivitiesInfo>> {
        return imRepository.getSysActivitiesInfo(groupId, pageSize, subGroupKey, lastRecordTime)
    }

    override suspend fun getSysUnReadCount(): Flow<DataResult<Int>> = imRepository.getSysUnReadCount()

    override fun getFeedbackOptionList(): Flow<List<FeedbackConfigItem>?> {
        return miscRepository.getFeedbackOptionList()
    }

    override fun getNotificationSwitch(): Flow<DataResult<Map<String, Boolean>>> = userRepository.getNotificationSwitch()

    override fun setNotificationSwitch(checked: Boolean): Flow<DataResult<Boolean>> = userRepository.setNotificationSwitch(checked)

    override fun getFeedbackDiscordLink(): suspend () -> String? {
        return miscRepository.getFeedbackDiscordLink()
    }

    override fun modifyUserFullBodyImg(fullBodyImUrl: String): Flow<DataResult<Any?>> {
        return userRepository.modifyUserFullBodyImg(fullBodyImUrl)
    }

    override fun insertUgcPlayedGame(entity: RecentUgcGameEntity, replaceIfExist: Boolean) = gameRepository.insertUgcPlayedGame(entity, replaceIfExist)

    override fun insertUgcPlayedGame(ugid: String, replaceIfExist: Boolean) = gameRepository.insertUgcPlayedGame(ugid, replaceIfExist)

    override fun getRandomPostTags() = postRepository.getRandomPostTags()
    override fun getRecommendPostTags(text: String) = postRepository.getRecommendPostTags(text)

    @Deprecated(message = "use savePostV3 instead")
    override suspend fun savePostV2(body: PublishPostRequest) = postRepository.savePostV2(body)

    override suspend fun savePostV3(body: PublishPostRequest): DataResult<PostPublishResult> = postRepository.savePostV3(body)

    override suspend fun editPost(body: PublishPostRequest): DataResult<PostPublishResult> = postRepository.editPost(body)

    override fun getPostDetailV2(postId: String) = postRepository.getPostDetailV2(postId)
    override fun deletePostV2(postId: String) = postRepository.deletePostV2(postId)

    override fun getPostCommentListV2(body: PostCommentListRequestBody) = postRepository.getPostCommentListV2(body)

    override fun addPostComment(body: PostCommentRequestBody) = postRepository.addPostComment(body)
    override suspend fun addAIBotComment(body: PostCommentRequestBody): DataResult<String?> = postRepository.addAIBotComment(body)

    override fun deletePostComment(commentId: String) = postRepository.deletePostComment(commentId)
    override fun deleteUgcComment(gameId: String, commentId: String) = postRepository.deleteUgcComment(gameId, commentId)

    override fun getPostReplyListV2(body: PostReplyListRequestBody) = postRepository.getPostReplyListV2(body)

    override fun addPostReply(body: PostReplyRequestBody) = postRepository.addPostReply(body)
    override fun deletePostReply(replyId: String) = postRepository.deletePostReply(replyId)

    override fun saveOpinion(body: OpinionRequestBody): suspend () -> Boolean = postRepository.saveOpinion(body)

    override fun getCommunityFeed(
        orderType: Int, postTagType: Int?, tagId: Long?, blockId: Long?, pageSize: Int, pageNum: Int
    ): suspend () -> CommunityFeedWrapper = postRepository.getCommunityTagFeed(
        orderType, postTagType, tagId, blockId, pageSize, pageNum
    )

    override fun getCommunityProfileFeed(
        otherUid: String, pageSize: Int, pageNum: Int
    ): suspend () -> CommunityFeedWrapper = postRepository.getCommunityProfileFeed(otherUid, pageSize, pageNum)

    override fun getRelationList(request: RelationListRequest): Flow<RelationListResult> = userRepository.getRelationList(request)

    override fun getRelationCount(request: RelationCountRequest): Flow<RelationCountResult> = userRepository.getRelationCount(request)

    override fun getFriendCount(request: RelationCountRequest): Flow<DataResult<RelationCountResult>> = userRepository.getFriendCount(request)

    override fun getIDevelopedPgcList(request: MyCreationsV4Request): suspend () -> PostCardResult = postRepository.getIDevelopedPgcList(request)

    override fun searchPostCardList(request: SearchPostCardRequest): suspend () -> PostCardResult = postRepository.searchPostCardList(request)

    override fun getRecentUgcCardList(pageSize: Int): Flow<PostCardResult> = gameRepository.getRecentUgcCardList(pageSize)

    override fun queryUserMuteStatus(uid: String?): suspend () -> UserMuteStatus = userRepository.queryUserMuteStatus(uid)

    override suspend fun getAllPostTagList(): List<PostTag> = postRepository.getAllPostTagList()

    override suspend fun getGlobalCircleId() = postRepository.getGlobalCircleId()

    override fun getCottageRoomList(currentPage: Int): Flow<CottageRoomList?> {
        return homeRoomRepository.getCottageRoomList(currentPage)
    }


    override fun getCottageRoomInfo(roomId: String): Flow<CottageRoomInfo?> {
        return homeRoomRepository.getCottageRoomInfo(roomId)
    }

    override fun getHomeVisitorCount(roomId: String): Flow<List<CottageVisitor>?> {
        return homeRoomRepository.getHomeVisitorCount(roomId)
    }

    override suspend fun getUserHouse(uuid: String): Flow<DataResult<House>> {
        return homeRoomRepository.getUserHouse(uuid)
    }

    override fun getRoleList() = editorRepository.getRoleList()

    override fun getRoleListV2() = editorRepository.getRoleListV2()

    override fun getCardAll(cardId: String, offset: Int, pageSize: Int): suspend () -> CardAll = gameRepository.getCardAll(cardId, offset, pageSize)

    override fun plotTemplateLoveDo(templateId: String): Flow<Boolean> = plotRepository.plotTemplateLoveDo(templateId)

    override fun featPlotMainList() = plotRepository.featPlotMainList()

    override fun featPlotAllList(body: PlotListBody) = plotRepository.featPlotAllList(body)

    override fun plotTemplateLoveDoV2(templateId: String, contentType: String): Flow<Boolean> = plotRepository.plotTemplateLoveDoV2(templateId, contentType)

    override fun momentTemplateList(body: MomentsTemplateBody) = plotRepository.momentTemplateList(body)

    override fun momentTemplateDelete(id: Long) = plotRepository.momentTemplateDelete(id)

    override fun fetchMyChatRoomPublishedList(lastId: String?) = homeRoomRepository.fetchMyChatRoomPublishedList(lastId)

    override fun fetchChatRoomTemplateList() = homeRoomRepository.fetchChatRoomTemplateList()

    override suspend fun getRecommend(offset: Int?): Flow<DataResult<Recommend>> = gameRepository.getRecommend(offset)

    override fun getContinueAccount(): Flow<DataResult<ContinueAccountInfo?>> = userRepository.getContinueAccount()

    override fun getVideoFeedList(
        version: String?, offset: Int, size: Int, pinPostId: String?
    ): Flow<VideoFeedApiResult> = postRepository.getVideoFeedList(version, offset, size, pinPostId)


    override fun saveTrendingInsertGameInfo(gameInfo: ChoiceGameInfo) {
        gameRepository.saveTrendingInsertGameInfo(gameInfo)
    }

    override fun removeTrendingInsertGameInfo(gameId: String) {
        gameRepository.removeTrendingInsertGameInfo(gameId)
    }

    override suspend fun addTrendingInsertList(dataResultList: MutableList<ChoiceCardInfo>): MutableList<ChoiceCardInfo> {
        return gameRepository.addTrendingInsertList(dataResultList)
    }

    override fun visitPostOutfitCard(postId: String, roleId: String) = postRepository.visitOutfitCard(postId, roleId)


    override fun getEditorHomeDataList(): Flow<List<GroupedData<*>>> = flow {
        val isOpenAvatarGameList = PandoraToggle.isOpenAvatarGameList
        val isOpenAvatarMomentList = PandoraToggle.isOpenAvatarMomentList

        val appStoreReviewLocked = AppStoreReviewLock.isLocked()

        val tTaiKeyList = mutableListOf<Int>()

        if (isOpenAvatarMomentList) {
            tTaiKeyList.add(TTaiKV.ID_EDITOR_HOME_MOMENTS)
        }

        if (isOpenAvatarGameList) {
            tTaiKeyList.add(if (appStoreReviewLocked) TTaiKV.ID_EDITOR_HOME_POPULAR_LOCKED else TTaiKV.ID_EDITOR_HOME_POPULAR)
        }

        if (tTaiKeyList.isEmpty()) {
            emit(emptyList())
            return@flow
        }

        val tTaiConfigs = invokeAndRethrow {
            metaApi.getTTaiConfigs(tTaiKeyList.joinToString(","))
        }?.associateBy { it.id } ?: emptyMap()

        // 这给操作相当于按照指定的顺序排序了
        // 因为后端的TTai列表的顺序不是固定的，而产品逻辑上是需要固定的顺序的额
        val sortedKeys = arrayOf(
            TTaiKV.ID_EDITOR_HOME_MOMENTS, TTaiKV.ID_EDITOR_HOME_POPULAR, TTaiKV.ID_EDITOR_HOME_POPULAR_LOCKED
        )
        val dataList: List<GroupedData<*>> = sortedKeys.mapNotNull { tTaiConfigs[it] }.mapNotNull { config ->
                when (config.id) {
                    TTaiKV.ID_EDITOR_HOME_MOMENTS -> {
                        val editorHomeMomentData = config.value.fromJSON<EditorHomeMomentData>()

                        val templateIdList = editorHomeMomentData.templateIdList

                        if ((templateIdList.isNullOrEmpty())) {
                            return@mapNotNull null
                        }

                        val allTemplateList = invokeAndRethrow { metaApi.plotTemplateV2(mapOf("size" to "all")) }?.dataList ?: emptyList()

                        val filteredPlotTemplateList = mutableListOf<PlotTemplate>()
                        allTemplateList.forEach {
                            it.socialPlotTemplateDTOList?.forEach { template ->
                                if (templateIdList.contains(template.templateId.toString())) {
                                    filteredPlotTemplateList.add(template)
                                }
                            }
                        }

                        if (filteredPlotTemplateList.isEmpty()) {
                            return@mapNotNull null
                        }

                        return@mapNotNull GroupedData(
                            "moment_data", editorHomeMomentData.title, filteredPlotTemplateList, GroupedData.CHOICE_CARD_TYPE_TTAI_MOMENT
                        )
                    }

                    TTaiKV.ID_EDITOR_HOME_POPULAR, TTaiKV.ID_EDITOR_HOME_POPULAR_LOCKED -> {
                        val editorHomePopularData = config.value.fromJSON<EditorHomePopularData>()
                        val gameCodeList = editorHomePopularData.gameCodeList
                        if ((gameCodeList.isNullOrEmpty())) {
                            return@mapNotNull null
                        }
                        val gameList = if (PandoraToggle.isOpenAiBot) {
                            DataSource.getDataResultForApi {
                                metaApi.getGameListByIds(gameCodeList)
                            }.data

                        } else {
                            invokeAndRethrow { metaApi.getGameListByIds(gameCodeList) } ?: emptyList()
                        }
                        return@mapNotNull GroupedData(
                            "popular_data", editorHomePopularData.title, gameList, GroupedData.CHOICE_CARD_TYPE_TTAI_POPULAR
                        )

                    }

                    else -> {
                        null
                    }
                }
            }

        emit(dataList)
    }

    override fun getRecommendVideoList(
        pageIndex: Int,
        pageCount: Int,
        categoryId: Int,
        targetVideoId: String?,
        pinVideoId: String?,
    ) = videoFeedRepository.getRecommendVideoFeedList(
        pageIndex, pageCount, categoryId, targetVideoId, pinVideoId
    )

    override fun getVideoPublishGuideStatus(): Flow<Boolean> = videoFeedRepository.getVideoPublishGuideStatus()

    override fun setVideoPublishGuideShown(): Flow<Boolean> = videoFeedRepository.setVideoPublishGuideShown()

    override fun getVideoPublishCircleId(): Flow<String?> = videoFeedRepository.getVideoPublishCircleId()

    override suspend fun getTripartiteInfo(orderId: String) = flow {
        emit(DataSource.getDataResultForApi { metaApi.getTripartite(mapOf("orderId" to orderId)) })
    }

    override fun getSubsProduct(type: String): Flow<DataResult<List<SubscribeProductInfo>?>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.getSubsProduct(type) })
    }

    override suspend fun queryUnreadRedBadge(list: List<String>): Flow<DataResult<RedBadgeData>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.queryUnreadRedBadge(RedBadgeRequest(list)) })
    }

    override suspend fun clearRedBadge(list: List<String>): Flow<DataResult<Boolean>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.clearRedBadge(RedBadgeRequest(list)) })
    }

    @Throws
    override fun commonImageRiskCheck(
        url: String, authCode: String
    ): suspend () -> RiskTaskQueryRequest = suspendApiNotNull {
        metaApi.commonCreateImageRiskCheck(
            CreateImgRiskCheckTaskRequest(
                authCode, System.currentTimeMillis().toString(), listOf(
                    CreateImgRiskCheckTaskRequest.RiskImgCheckParam(
                        authCode, CreateImgRiskCheckTaskRequest.RiskImgCheckParam.TYPE_IMAGE, url, null
                    )
                )
            )
        )
    }

    @Throws
    override fun queryImageRiskTask(jobId: String): suspend () -> RiskQueryResult = suspendApiNotNull {
        metaApi.queryImageRiskTask(RiskTaskQueryRequest(jobId))
    }

    override suspend fun setPrivacySwitch(switch: PrivacySwitch): DataResult<Boolean> = userRepository.setPrivacySwitch(switch)

    override fun getPrivacySwitch() = userRepository.getPrivacySwitch()

    override fun changeTopicFollow(follow: Boolean, tagId: Long): suspend () -> Any? {
        return if (follow) {
            postRepository.followTopic(tagId)
        } else {
            postRepository.unfollowTopic(tagId)
        }
    }

    override fun fetchMyFollowTopics(
        pageSize: Int, pageNum: Int
    ): suspend () -> CommunityTopicBlockWrap = postRepository.fetchMyFollowTopics(pageSize, pageNum)

    override fun fetchHotTopics(pageSize: Int, pageNum: Int): suspend () -> List<PostTag>? = postRepository.fetchHotTopics(pageSize, pageNum)

    override fun fetchTopicDetail(tagId: Long): suspend () -> TopicDetailInfo = postRepository.fetchTopicDetail(tagId)

    override suspend fun addTopicViewCount(tagIds: List<String>): DataResult<Any?> {
        return if (tagIds.isEmpty()) {
            DataResult.Success(null)
        } else {
            DataSource.getDataResultForApi {
                postRepository.addTopicViewCountBatch(
                    tagIds
                )
            }
        }
    }

    override fun commonAddPvCount(resType: String, resId: String): Flow<DataResult<Boolean?>> {
        return postRepository.commonAddPvCount(resType, resId)
    }

    override fun fetchBlockList(vis: Int?): suspend () -> List<CommunityBlock>? = postRepository.fetchBlockList(vis)

    override fun getRoleStyleList(
        isMe: Boolean, otherUuid: String, beginIndex: Int, length: Int
    ) = editorRepository.getRoleStyleList(isMe, otherUuid, beginIndex, length)

    override fun likeRoleStyle(styleId: String, isLike: Boolean) = editorRepository.likeRoleStyle(styleId, isLike)

    override fun deleteRoleStyle(styleId: String) = editorRepository.deleteRoleStyle(styleId)

    override fun getRoleBannerList(): Flow<List<UniJumpConfig>> = flow {
        val list = invokeAndRethrow {
            metaApi.getNoticeList(
                mapOf(
                    "scopeCode" to ("all"), "operatingPosition" to UniJumpConfig.POS_AVATAR_BANNER
                )
            )
        } ?: emptyList()
        emit(list)
    }


    override fun getAvatarPopupOperationConfig(): Flow<DataResult<UniJumpConfig>> = flow {
        val dataResult = miscRepository.getNoticeList(null, UniJumpConfig.POS_AVATAR_POPUP).lastOrNull()
        val data = dataResult?.data
        if (dataResult == null || data.isNullOrEmpty()) {
            emit(DataResult.Error(-1, "No operation config available"))
            return@flow
        }

        val uniJumpConfig = data.first()

        val isShowed = withContext(Dispatchers.Default) {
            metaKV.account.getAvatarPopupTodayShowCount(uniJumpConfig.uniqueCode) > 0
        }

        if (isShowed) {
            emit(
                DataResult.Error(
                    -1, "Operation config ${uniJumpConfig.uniqueCode} is showed today"
                )
            )
            return@flow
        }

        emit(DataResult.Success(uniJumpConfig))
    }

    override fun setAvatarPopupOperationShowed(uniqueCode: String): Flow<DataResult<Unit>> = flow {
        withContext(Dispatchers.Default) {
            val showedCount = metaKV.account.getAvatarPopupTodayShowCount(uniqueCode)
            metaKV.account.setAvatarPopupTodayShowCount(uniqueCode, showedCount + 1)
        }
        emit(DataResult.Success(Unit))
    }

    override suspend fun userAgentUpload(): Flow<DataResult<String?>> = userRepository.userAgentUpload()

    override fun addAiTextMessage(
        sendUserId: String, receiverUserID: String, content: String, botInfo: BotInfo?, messageType: Int?
    ): Flow<AIMessageEntity> = imRepository.addAiTextMessage(sendUserId, receiverUserID, content, botInfo, messageType)

    override fun addAiMessageList(list: java.util.ArrayList<AIMessageEntity>): Flow<List<AIMessageEntity>> = imRepository.addAiMessageList(list)

    override fun getAiBotHistoryMessageList(
        targetId: String, uuid: String, pageNum: Int?, pageSize: Int
    ): Flow<ArrayList<AIMessageEntity>> = imRepository.getAiBotHistoryMessageList(targetId, uuid, pageNum, pageSize)

    override fun cleanAllAiBotMessageHistory(targetId: String, uuid: String): Flow<Boolean> = imRepository.cleanAllAiBotMessageHistory(targetId, uuid)

    override fun updateAiBotConversation(
        targetId: String, uuid: String, messageId: Long?, aiMessageEntity: AIMessageEntity?
    ): Flow<Boolean> = imRepository.updateAiBotConversation(targetId, uuid, messageId, aiMessageEntity)

    override fun getAllAiBotConversationList(
        uuid: String
    ): Flow<List<AIConversationEntity>> = imRepository.getAllAiBotConversationList(uuid)

    override suspend fun getAiBotConversation(
        uuid: String, targetId: String
    ): Flow<AIConversationEntity?> = imRepository.getAiBotConversation(uuid, targetId)

    override fun getConversationList(lastId: Int, uuid: String): Flow<List<AIConversationEntity>> = imRepository.getConversationList(lastId, uuid)

    override fun getAiBotInfo(targetId: String): Flow<BotInfo?> = flow {
        val data = DataSource.getDataResultForApi { metaApi.getAiBotInfo(mapOf("id" to targetId)) }
        emit(data.data)
    }

    override fun followBot(targetId: String, follow: Boolean): Flow<DataResult<Boolean?>> = flow {
        val map = mapOf(
            "targetUid" to targetId, "relationType" to "AiBot"
        )
        if (follow) {
            emit(DataSource.getDataResultForApi { metaApi.relationAdd(map) })
        } else {
            emit(DataSource.getDataResultForApi { metaApi.relationDel(map) })
        }
    }

    override fun deleteAiBotConversion(botId: String, uuid: String): Flow<DataResult<Boolean?>> = flow {
        imRepository.deleteLocalAiBotConversation(botId, uuid).collect()
        imRepository.cleanAllAiBotMessageHistory(botId, uuid).collect()
        val map = mapOf("botId" to botId)
        emit(DataSource.getDataResultForApi { metaApi.deleteAiBotConversion(map) })
    }

    override fun getAiBotConversionList(map: Map<String, String>): Flow<DataResult<AiBotConversationResult?>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.getAiBotConversionList(map) })
    }

    override fun getFollowAiBotList(
        scrollId: String?, size: Int, otherUuid: String?
    ): Flow<DataResult<AiBotFollowResult?>> = flow {
        val map = HashMap<String, String>()
        map["size"] = size.toString()
        if (!scrollId.isNullOrEmpty()) {
            map["scrollId"] = scrollId.toString()
        }
        if (!otherUuid.isNullOrEmpty()) {
            map["otherUuid"] = otherUuid.toString()
        }
        emit(DataSource.getDataResultForApi { metaApi.getFollowAiBotList(map) })
    }


    override fun updateAiBotConversion(map: Map<String, String>): Flow<DataResult<Boolean?>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.updateAiBotConversion(map) })
    }


    override fun getMWGameResource(request: MWGameResourceRequest): Flow<DataResult<List<MWGameResourceResponse>>> = tsGameRepository.getMWGameResource(request)

    override fun getKolFrameList(): Flow<DataResult<CreatorFrameResult>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.getKolFrameList() })
    }

    override fun getUgcGameListByTag(
        tagId: Int?, offset: Int?, pageSize: Int
    ): Flow<DataResult<RecommendUgcResult>> = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.getUgcGameListByTag(
                RecommendRequestBody(
                    offset = offset, tag = tagId?.toString() // 为null就发null
                )
            )
        })
    }

    override fun getAllUgcPublishedTag(): Flow<DataResult<List<UgcPublishLabel>?>> = flow {
        val result = DataSource.getDataResultForApi { metaApi.getAllUgcPublishedTag() }
        if (result is DataResult.Success && result.data.isNotEmpty()) {
            val newList = result.data.insertAt(0, UgcPublishLabel.buildAllLabel()) ?: emptyList()
            emit(result.copy(data = newList))
        } else {
            emit(result)
        }
    }

    override fun getKolCreatorListByTag(
        tagId: Int, sinceId: String?
    ): Flow<DataResult<RecommendCreatorResult>> = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.getKolCreatorListByTag(
                RecommendCreatorRequest(
                    tagId, sinceId
                )
            )
        })
    }

    override fun getFollowedCreatorUgcList(pageNum: Int): suspend () -> FollowedCreatorUgcWrapper = suspendApiNotNull {
        metaApi.getFollowedCreatorUgcList(FollowedCreatorUgcRequest(pageNum))
    }


    override fun deleteLink(id: String) = flow {
        emit(DataSource.getDataResultForApi { metaApi.deleteLink(mapOf("id" to id)) })
    }

    override fun addLink(
        title: String, url: String, type: String, icon: String
    ): Flow<DataResult<ProfileLinkInfo>> = flow {
        val map = hashMapOf("title" to title, "url" to url, "type" to type)
        val result =
            DataSource.getDataResultForApi { metaApi.addLink(map) }.map { it.copy(icon = icon) }
        emit(result)
    }


    override suspend fun finishDailySign(body: FinishDailyTaskBody): Flow<DataResult<Boolean>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.finishDailySign(body) })
    }

    override suspend fun queryDailyTaskInfo(): Flow<DataResult<DailyTaskInfo?>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.queryDailyTaskInfo() })
    }

    override fun getKolRecommendUgcGameListByPage(offset: Int?): suspend () -> RecommendKolUgcResult = suspendApiNotNull {
        metaApi.getKolRecommendUgcGameListByPage(RecommendRequestBody(offset = offset))
    }

    override fun getKolRecommendUgcGameListByPageV2(offset: Int?): Flow<DataResult<RecommendKolUgcResult>> = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.getKolRecommendUgcGameListByPage(
                RecommendRequestBody(offset = null)
            )
        })
    }

    override fun getTypeCreatorList(type: Int, sinceId: String?): suspend () -> TypeCreatorResult = suspendApiNotNull {
        metaApi.getTypeCreatorList(TypeCreatorRequest(type, sinceId))
    }

    override fun getLabelCreatorList(
        tagId: Int, sinceId: String?
    ): suspend () -> LabelCreatorResult = suspendApiNotNull {
        metaApi.getLabelCreatorList(LabelCreatorRequest(tagId, sinceId))
    }


    override suspend fun queryPositiveComment(): DataResult<QueryPositiveComment> {
        val data = DataSource.getDataResultForApi { metaApi.queryPositiveComment() }
        return data
    }

    override suspend fun getStyleList() = flow {
        emit(DataSource.getDataResultForApi { metaApi.getStyleList() })
    }

    override suspend fun generateAIBotImage(body: AIBotCreateRequest) = flow {
        emit(DataSource.getDataResultForApi { metaApi.generateAIBotImage(body) })
    }

    override suspend fun generateAIBotImageResult(resId: String): DataResult<AIBotCreateImageResult?> {
        return DataSource.getDataResultForApi { metaApi.generateAIBotImageResult(resId) }
    }

    override suspend fun generateInfo(map: Map<String, String>): Flow<DataResult<AIBotGenerateInfo?>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.generateInfo(map) })
    }

    override suspend fun saveAIBotInfo(botInfo: BotInfoCreate): Flow<DataResult<BotInfo?>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.saveBotInfo(botInfo) })
    }

    override suspend fun deleteAiMessage(messageId: Long): Flow<DataResult<Boolean?>> = imRepository.deleteAiMessage(messageId)

    override fun resetAIBOTHistory(map: Map<String, String>): Flow<DataResult<Any?>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.resetAIBOTHistory(map) })
    }

    override fun recordRoBux(): Flow<DataResult<RoBuxRecordInfo>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.recordRoBux() })
    }


    override fun submitPositiveComment(): Flow<DataResult<Boolean>> = flow {
        val data = DataSource.getDataResultForApi { metaApi.submitPositiveComment() }
        emit(data)
    }

    override suspend fun queryShareRelayData(ua: String, uniqueId: String?): DataResult<RelayData> {
        return DataSource.getDataResultForApi { metaApi.shareDataRelayQuery(ua, uniqueId) }
    }

    override fun createShare(request: CommonShareRequest) = suspendApiNotNull {
        metaApi.createShare(request)
    }

    override fun insertShareRecord(shareRecordId: String, platform: String) = userRepository.insertShareRecord(shareRecordId, platform)

    override fun existShareRecord(shareRecordId: String) = userRepository.existsShareRecord(shareRecordId)

    override fun getAvatarSaveShareBackgroundList(): Flow<List<AvatarShareCompositeBackground>> = avatarRepository.getAvatarSaveShareBackgroundList()

    override fun getUgcBackup(
        type: Int, fileId: String, gameIdentity: String
    ): suspend () -> List<UgcBackupInfo>? = editorRepository.getUgcBackup(
        type, fileId, gameIdentity
    )

    override suspend fun fetchAllCloudGames(type: Int): List<UgcCloudProject> = editorRepository.fetchAllCloudGames(type)

    override suspend fun mergeCloudList(localList: MutableList<EditorCreationShowInfo>, type: Int) = editorRepository.mergeCloudList(localList, type)

    override fun mergeCloudListFlow(localList: MutableList<EditorCreationShowInfo>, type: Int) = editorRepository.mergeCloudListFlow(localList, type)

    override suspend fun checkMaxCloud(type: Int): DataResult<Boolean> = editorRepository.checkMaxCloud(type)

    override fun checkMaxCloudV2(type: Int) = editorRepository.checkMaxCloudV2(type)

    override suspend fun deleteAllBackup(type: Int, fileId: String): DataResult<Boolean> = editorRepository.deleteAllBackup(type, fileId)

    override fun deleteAllBackupV2(type: Int, fileId: String) = editorRepository.deleteAllBackupV2(type, fileId)

    override suspend fun getMaxCloud(type: Int): DataResult<ProjectLimit> = editorRepository.getMaxCloud(type)

    override fun getMaxCloudV2(type: Int) = editorRepository.getMaxCloudV2(type)
    override fun getGameDetailOperationInfo(
        biz: String, bizId: String, pageNum: Int, pageSize: Int
    ) = miscRepository.getGameDetailOperationInfo(biz, bizId, pageNum, pageSize)

    override fun resolveQrCode(body: QrCodeResolveApiRequest): Flow<DataResult<QrCodeResolveApiResponse>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.resolveQrCode(body) })
    }

    override fun processAuthLogin(
        dataResult: DataResult<AuthInfoApiResult>, loginType: LoginType, loginWay: LoginWay, oAuthToken: String
    ) = userRepository.processAuthLogin(dataResult, loginType, loginWay, oAuthToken)

    override suspend fun getCoinsRecord(
        lastCoinsRecordId: Long?, pageSize: Int
    ): DataResult<CoinsRecords> {
        return DataSource.getDataResultForApi {
            metaApi.queryCoinRecordList(
                CoinsRecordRequestBody(
                    offset = lastCoinsRecordId,
                    pageSize = pageSize,
                    coinType = if (EnvConfig.isParty()) CoinType.partyCoin.value else CoinType.gParkCoin.value,
                )
            )
        }
    }

    override fun getModuleGuideStatus() = ugcRepository.getModuleGuideStatus()

    override fun qrCodeCreateSuspend(body: QrCodeCreateRequest) = suspendApiNotNull {
        metaApi.qrCodeCreate(body)
    }

    override fun qrCodeCreateFlow(body: QrCodeCreateRequest): Flow<DataResult<QrCodeCreateResponse>> = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.qrCodeCreate(body)
        })
    }

    override fun qrCodeResolveSuspend(url: String) = suspendApiNotNull {
        metaApi.qrCodeResolve(url)
    }

    override fun qrCodeResolveFlow(url: String): Flow<DataResult<QrCodeResolveResponse>> = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.qrCodeResolve(url)
        })
    }

    override fun checkCreateGroupPower(): Flow<Boolean> = groupChatRepository.checkCreateGroupPower()

    override suspend fun createGroupChat(members: List<String>): DataResult<GroupChatDetailInfo> {
        return groupChatRepository.createGroupChat(
            MgsGroupChatCreateRequest(
                name = null, icon = null, describe = null, announcement = null, members = members
            )
        )
    }

    override fun getGroupChatDetailInfo(chatGroupId: Long): Flow<DataResult<GroupChatDetailInfo>> = groupChatRepository.getGroupChatDetailInfo(chatGroupId)

    override suspend fun getGroupChatInfoList(request: MgsGroupChatListRequest): DataResult<GroupChatInfoPage> {
        return groupChatRepository.getGroupChatInfoList(request)
    }

    override suspend fun applyJoinGroupChat(request: MgsGroupChatApplyJoinRequest): DataResult<Boolean> {
        return groupChatRepository.applyJoinGroupChat(request)
    }

    override suspend fun joinGroupChat(groupId: Long): DataResult<GroupChatAddMembers> {
        return groupChatRepository.joinGroupChat(
            MgsGroupChatIdRequest(
                chatGroupId = groupId
            )
        )
    }

    override suspend fun getGroupChatPendingRequestList(request: MgsGroupApplyPageRequest): DataResult<GroupChatApplyInfoList> {
        return groupChatRepository.getGroupChatPendingRequestList(request)
    }

    override suspend fun editGroupChat(request: MgsGroupChatEditRequest): DataResult<GroupChatDetailInfo> {
        return groupChatRepository.editGroupChat(request)
    }

    override suspend fun leaveGroupChat(chatGroupId: Long): DataResult<Boolean> {
        return groupChatRepository.leaveGroupChat(MgsGroupChatIdRequest(chatGroupId = chatGroupId))
    }

    override suspend fun disbandGroupChat(chatGroupId: Long): DataResult<Boolean> {
        return groupChatRepository.disbandGroupChat(MgsGroupChatIdRequest(chatGroupId = chatGroupId))
    }

    override suspend fun editGroupChatNotification(
        chatGroupId: Long, enableNotifications: Boolean
    ): DataResult<Boolean> {
        return groupChatRepository.editGroupChatNotification(
            MgsGroupChatEditNotificationRequest(
                chatGroupId = chatGroupId, enableNotifications = enableNotifications
            )
        )
    }

    override suspend fun processApplyJoinGroupChat(
        askId: Long, accept: Boolean
    ): DataResult<Boolean> {
        return groupChatRepository.processApplyJoinGroupChat(
            MgsGroupChatApplyJoinProcessRequest(
                askId = askId, status = if (accept) {
                    MgsGroupChatApplyJoinProcessRequest.STATUS_AGREE
                } else {
                    MgsGroupChatApplyJoinProcessRequest.STATUS_REFUSE
                }
            )
        )
    }

    override suspend fun getAllGroupChatMembers(
        chatGroupId: Long,
        forceLoad: Boolean?,
    ): DataResult<List<GroupChatMemberInfo>> {
        return groupChatRepository.getAllGroupChatMembersWithCache(chatGroupId, forceLoad)
    }

    override suspend fun removeGroupChatMember(
        chatGroupId: Long, memberId: String
    ): DataResult<Boolean> {
        return groupChatRepository.removeGroupChatMember(
            MgsGroupChatRemoveMemberRequest(
                chatGroupId = chatGroupId, member = memberId
            )
        )
    }

    override suspend fun inviteGroupChatMembers(
        chatGroupId: Long, memberIdList: List<String>
    ): DataResult<GroupChatAddMembers> {
        return groupChatRepository.inviteGroupChatMembers(
            MgsGroupChatInviteMembersRequest(
                chatGroupId = chatGroupId,
                members = memberIdList,
            )
        )
    }

    override suspend fun getGroupIdByImIds(imIds: List<String>): Map<String, Long> {
        return groupChatRepository.getGroupIdByImIds(imIds)
    }

    override fun getGroupChatCount(): Flow<DataResult<GroupChatCount>> = groupChatRepository.getGroupChatCount()

    override suspend fun getGroupChatPendingRequestCount(): DataResult<Int> = groupChatRepository.getGroupChatPendingRequestCount()

    /**
     * 获取游戏点赞用户列表
     * @param moduleType 模块类型，固定为"game"
     * @param moduleContentId 模块内容ID，即gameId
     * @param offset 偏移量，第一次请求为null，后续请求使用上一页最后一个元素的offset
     * @param pageSize 每页数量
     */
    override fun getLikePlayerList(moduleType: String, moduleContentId: String, offset: Int?, pageSize: Int): Flow<DataResult<LikeAndPlayerListData>> = gameRepository.getLikePlayerList(moduleType, moduleContentId, offset, pageSize)

    /**
     * 获取游戏游玩用户列表
     * @param gameId 游戏ID
     * @param offset 偏移量，第一次请求为null，后续请求使用上一页最后一个元素的offset
     * @param pageSize 每页数量
     */
    override fun getFlowerPlayerList(gameId: String, offset: Int?, pageSize: Int): Flow<DataResult<LikeAndPlayerListData>> = gameRepository.getFlowerPlayerList(gameId, offset, pageSize)

    /**
     * 获取送花排行榜
     */
    override fun queryFlowerLeaderboard(gameId: String): Flow<DataResult<FlowerLeaderboardData>> = gameRepository.queryFlowerLeaderboard(gameId)

    override fun getFlowerGifts(): Flow<DataResult<List<GiftFlower>>> = giftsRepository.getFlowerGifts()

    override suspend fun getSendGiftConditions(gameId: String): DataResult<SendGiftConditionsInfo> = giftsRepository.getSendGiftConditions(gameId)

    override suspend fun switchSendGift(gameId: String, giveaway: Boolean): DataResult<Int> = giftsRepository.switchSendGift(gameId,giveaway)
    override suspend fun getSendGiftUserList(gameId: String, count: Int): DataResult<List<SendGiftData>> = giftsRepository.getSendGiftUserList(gameId,count)

    override fun getGroupChatCountByUuid(uuid: String) =
        groupChatRepository.getGroupChatCountByUuid(uuid)
}