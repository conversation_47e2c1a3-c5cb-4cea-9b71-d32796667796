package com.socialplay.gpark.data.kv

import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.data.model.editor.DefaultRoleInfo
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.GsonUtil
import com.tencent.mmkv.MMKV
import com.socialplay.gpark.util.property.MMKVScope
import com.socialplay.gpark.util.property.kvProperty
import timber.log.Timber

class AccountKV(override val mmkv: MMKV, val metaAppMmkv: MMKV) : MMKVScope {
    companion object {

        // 就是accessToken，兼容老版本就没换key
        const val API_ACCESS_TOKEN = "api_token"
        const val API_ACCESS_TOKEN_REFRESH_TIME = "api_token_refresh_time"
        const val API_REFRESH_TOKEN = "api_refresh_token"
        const val UUID = "uuid"
        const val USER_INFO = "meta_user_info"
        const val NEW_USER = "meta_new_user"
        private const val KEY_SESSION_ID = "sessionId"
        private const val KEY_DOMAIN_NAME = "domain_name"

        // 保存本人的二维码内容
        private const val KEY_MY_QR_CODE = "key_my_qr_code"

        //保存未读好友申请数量
        private const val KEY_UNREAD_FRIEND_REQUEST_COUNT_PREFIX = "key_unread_friend_request_count_"

        // 玩过的游戏
        private const val KEY_IS_PLAYED_GAME_PREFIX = "key_user_is_played_game_prefix_"


        // NeedsLoginFirst
        private const val KEY_NEEDS_LOGIN_FIRST = "key_needs_login_first"

        private const val KEY_BACKUP_CHOOSE_ROLE = "key_backup_choose_role"

        private const val KEY_ROLE_DATA = "key_role_data"
        private const val KEY_FIRST_POST_SHARE_OUTFIT = "key_first_post_share_outfit"
        private const val KEY_AVATAR_POPUP_SHOW_COUNT = "key_avatar_popup_show_count"
        private const val KEY_IM_SHOW_NOTIFICATION_RECORD = "key_im_show_notification_record"
        private const val KEY_POST_SHOW_NOTIFICATION_RECORD = "key_post_show_notification_record"
        private const val KEY_AI_CREATE_COUNT = "key_ai_create_today_count"
        private const val KEY_AI_CREATE_COUNT_TIME = "key_ai_create_save_time"
        private const val KEY_AI_CREATE_FIRST_TIPS= "key_ai_create_first_tips"
        private const val KEY_AI_CREATE_FIRST_ENTRANCE_TIPS= "key_ai_create__entrance_first_tips"
        //用户提示入口控制
        private const val KEY_IM_TIPS_LAST_TIME= "key_im_tip_last_time"
        private const val KEY_IM_TIPS_TOAST_COUNT= "key_im_tip_toast_count"
        //裂变入口
        private const val KEY_SHARE_ENTRANCE="key_robux_share_entrance"

        private const val KEY_UGC_DESIGN_DETAIL_FIRST_TIPS = "key_ugc_design_detail_first_tips"
        private const val KEY_UGC_MODEL_DETAIL_FIRST_TIPS = "key_ugc_model_detail_first_tips"
        //用户选择的性别avatar
        private const val KEY_USER_SELECT_AVATAR_SEX = "key_user_select_avatar_sex"
        private const val KEY_ENABLE_FRIEND_REQUESTS_NOTICE = "key_enable_friend_requests_notice"
        private const val KEY_SHOW_COMMENT_PIN_RED_DOT = "key_show_comment_pin_red_dot"
        private const val KEY_UGC_BUILD_MODE_GUIDE = "key_ugc_build_mode_guide"
        private const val KEY_MODULE_GUIDE_STATUS = "key_module_guide_status"
        private const val KEY_ASSET_FIRST_INTERACT = "key_asset_first_interact"
        private const val KEY_ASSET_ROOKIE_TAB = "key_asset_rookie_tab"
        private const val KEY_ASSET_ROOKIE_TAB_GUIDE = "key_asset_rookie_tab_guide"
        private const val KEY_MODULE_GUIDE_SHOW_TIME = "key_module_guide_show_time"
        private const val KEY_SHOW_CREATE_GROUP_RED_DOT = "key_show_create_group_red_dot"

        /**
         * 打开群聊申请页面的时间
         */
        private const val KEY_OPEN_GROUP_CHAT_JOIN_REQUEST_PAGE_TIMESTAMP = "key_open_group_chat_join_request_page_timestamp"
        private const val KEY_LAST_LOGIN_TYPE = "key_last_login_type"
    }

    /**
     * 注意：委托形式key中不要包含uuid，否则切换账户后key不会变
     * 错误栗子：var xxx by kvProperty(defValue = "", key = "xxx${uuid}")
     */
    var uuid by kvProperty<String>(key = UUID, defValue = "")
    var accessToken: String?
        set(value) {
            mmkv.putString(API_ACCESS_TOKEN, value)
        }
        get() {
            return mmkv.getString(API_ACCESS_TOKEN, null)
        }

    var refreshToken: String?
        set(value) {
            mmkv.putString(API_REFRESH_TOKEN, value)
        }
        get() {
            return mmkv.getString(API_REFRESH_TOKEN, null)
        }

    // -1已过期, 0未初始化, >0过期时间 + System.currentTimeMills()
    var accessTokenExpireTime by kvProperty<Long>(defValue = 0, key = API_ACCESS_TOKEN_REFRESH_TIME)

    var userInfo by kvProperty(defValue = "",key = USER_INFO)
    var newUser by kvProperty<Int>(key = NEW_USER)
    var sessionId by kvProperty<String>(key = KEY_SESSION_ID)
    var doMainName by kvProperty<String>(key = KEY_DOMAIN_NAME)
    // 角色数据
    var roleData by kvProperty<String?>(key = KEY_ROLE_DATA, defValue = null)
    var isFirstPostShareOutfit by kvProperty<Boolean>(key = KEY_FIRST_POST_SHARE_OUTFIT, defValue = true)
    var ugcDesignDetailGuide by kvProperty<Boolean>(
        key = KEY_UGC_DESIGN_DETAIL_FIRST_TIPS,
        defValue = false
    )
    var ugcModelDetailGuide by kvProperty<Boolean>(
        key = KEY_UGC_MODEL_DETAIL_FIRST_TIPS,
        defValue = false
    )
    var showCommentPinRedDot by kvProperty<Boolean>(
        key = KEY_SHOW_COMMENT_PIN_RED_DOT,
        defValue = true
    )
    var ugcModuleHomeGuide by kvProperty<Boolean>(
        key = KEY_UGC_DESIGN_DETAIL_FIRST_TIPS,
        defValue = false
    )

    var moduleGuideStatus by kvProperty<Int>(
        key = KEY_MODULE_GUIDE_STATUS,
        defValue = BaseAccountInteractor.MODULE_GUIDE_STATUS_INIT
    )

    var assetFirstInteract by kvProperty<Boolean>(
        key = KEY_ASSET_FIRST_INTERACT,
        defValue = true
    )

    var showRedDotOnAssetRookieTab by kvProperty<Boolean>(
        key = KEY_ASSET_ROOKIE_TAB,
        defValue = true
    )

    var showAssetRookieTabGuide by kvProperty<Boolean>(
        key = KEY_ASSET_ROOKIE_TAB_GUIDE,
        defValue = true
    )

    // 没人用
    var unreadFriendRequestsCount by kvProperty<Int>(key = "$KEY_UNREAD_FRIEND_REQUEST_COUNT_PREFIX${uuid}")

    var lastLoginType by kvProperty<String?>(key = KEY_LAST_LOGIN_TYPE)

    // male: 男 female: 女
    var userSelectAvatarSex by kvProperty<String>("", key = KEY_USER_SELECT_AVATAR_SEX)

    var needUgcBuildModeGuide by kvProperty<Boolean>(defValue = true, key = KEY_UGC_BUILD_MODE_GUIDE)

    var moduleGuideShowTime by kvProperty<Long>(defValue = 0L, key = KEY_MODULE_GUIDE_SHOW_TIME)

    fun isGamePlayed(gameId: Long): Boolean {
        return mmkv.getBoolean("$KEY_IS_PLAYED_GAME_PREFIX${uuid}_$gameId", false)
    }

    fun setGamePlayed(gameId: String) {
        mmkv.putBoolean("$KEY_IS_PLAYED_GAME_PREFIX${uuid}_$gameId", true)
    }

    fun setMyQrCode(codeString: String?) {
        mmkv.putString("$KEY_MY_QR_CODE${uuid}", codeString)
    }

    fun getMyQrCode(): String? {
        return mmkv.getString("$KEY_MY_QR_CODE${uuid}", null)
    }

    /**
     * 创角兜底
     */
    @Deprecated("backend have handled error if role game not login")
    fun getBackupChooseRole(): DefaultRoleInfo? {
        return GsonUtil.gsonSafeParse(mmkv.getString("${KEY_BACKUP_CHOOSE_ROLE}${uuid}", ""))
    }

    /**
     * 创角兜底
     */
    @Deprecated("backend have handled error if role game not login")
    fun setBackupChooseRole(defaultRoleInfo: DefaultRoleInfo?) {
        mmkv.putString("${KEY_BACKUP_CHOOSE_ROLE}${uuid}", GsonUtil.gson.toJson(defaultRoleInfo))
    }

    fun getAvatarPopupTodayShowCount(
        uniqueCode: String,
        today: String = DateUtil.getTodayString()
    ): Int {
        return mmkv.getInt("${KEY_AVATAR_POPUP_SHOW_COUNT}_${uuid}_${today}_${uniqueCode}", 0)
    }

    fun setAvatarPopupTodayShowCount(
        uniqueCode: String,
        showCount: Int,
        today: String = DateUtil.getTodayString()
    ) {
        mmkv.putInt("${KEY_AVATAR_POPUP_SHOW_COUNT}_${uuid}_${today}_${uniqueCode}", showCount)
    }


    fun setImShowNotificationRecord(
        showCount: String,
    ) {
        mmkv.putString("${KEY_IM_SHOW_NOTIFICATION_RECORD}_${uuid}", showCount)
    }

    fun getImShowNotificationRecord(): String? {
        return mmkv.getString("${KEY_IM_SHOW_NOTIFICATION_RECORD}_${uuid}", "")
    }

    fun setPostShowNotificationRecord(
        showCount: String,
    ) {
        mmkv.putString("${KEY_POST_SHOW_NOTIFICATION_RECORD}_${uuid}", showCount)
    }

    fun getPostShowNotificationRecord(): String? {
        return mmkv.getString("${KEY_POST_SHOW_NOTIFICATION_RECORD}_${uuid}", "")
    }
    fun setAICreateTodayCount( showCount: Int) {
        mmkv.putInt("${KEY_AI_CREATE_COUNT}_${uuid}", showCount)
    }

    fun getAiCreateTodayCount():Int{
       return mmkv.getInt("${KEY_AI_CREATE_COUNT}_${uuid}",0)
    }

    fun saveAiCreateTime() {
        mmkv.putLong("${KEY_AI_CREATE_COUNT_TIME}_${uuid}", DateUtil.getToday())
    }

    fun getAiCreateTime(): Long {
        return mmkv.getLong("${KEY_AI_CREATE_COUNT_TIME}_${uuid}", DateUtil.getToday())
    }

    fun isFirstAICreateTips():Boolean{
        return mmkv.getBoolean("${KEY_AI_CREATE_FIRST_TIPS}_${uuid}", true)
    }
    fun setAICreateTips(){
        mmkv.putBoolean("${KEY_AI_CREATE_FIRST_TIPS}_${uuid}", false)
    }
    fun isFirstAiCreateEntrance():Boolean{
        return mmkv.getBoolean("${KEY_AI_CREATE_FIRST_ENTRANCE_TIPS}_${uuid}", true)
    }
    fun setAICreateEntranceTips(){
        mmkv.putBoolean("${KEY_AI_CREATE_FIRST_ENTRANCE_TIPS}_${uuid}", false)
    }

    fun getIMTipLastTime(): Long {
        return mmkv.getLong("${KEY_IM_TIPS_LAST_TIME}_${uuid}", 0L)
    }

    fun saveIMTipLastTime(day: Long) {
        mmkv.putLong("${KEY_IM_TIPS_LAST_TIME}_${uuid}", day)
    }

    fun getIMTipCount(): Int {
        return mmkv.getInt("${KEY_IM_TIPS_TOAST_COUNT}_${uuid}", 0)
    }

    fun saveIMTipCount(count: Int) {
        mmkv.putInt("${KEY_IM_TIPS_TOAST_COUNT}_${uuid}", count)
    }
    fun getTodayMineUnreadVisible():Boolean{
        return mmkv.getBoolean("TODAY_MINE_UNREAD_VISIBLE${DateUtil.getTodayString()}$uuid", true)
    }

    fun setTodayMineUnreadVisible(visible:Boolean){
        mmkv.putBoolean("TODAY_MINE_UNREAD_VISIBLE${DateUtil.getTodayString()}$uuid", visible)
    }
    fun setTodayMineRobuxVisible() {
        mmkv.putLong("${KEY_SHARE_ENTRANCE}_${uuid}", DateUtil.getToday())
    }

    fun isTodayMineRobuxVisible(): Boolean {
        val last = mmkv.getLong("${KEY_SHARE_ENTRANCE}_${uuid}", 0)
        return DateUtil.getToday() > last
    }

    fun isFriendRequestsNoticeEnabled(uuid: String?): Boolean {
        return mmkv.getBoolean("${KEY_ENABLE_FRIEND_REQUESTS_NOTICE}_${uuid ?: this.uuid}", true)
    }

    fun setFriendRequestsNoticeEnabled(enabled: Boolean) {
        mmkv.putBoolean("${KEY_ENABLE_FRIEND_REQUESTS_NOTICE}_${uuid}", enabled)
    }

    var showCreateGroupRedDot by kvProperty<Boolean>(
        key = KEY_SHOW_CREATE_GROUP_RED_DOT,
        defValue = true
    )

    var openGroupJoinRequestPageTime by kvProperty<Long>(
        key = KEY_OPEN_GROUP_CHAT_JOIN_REQUEST_PAGE_TIMESTAMP,
        defValue = 0L
    )

    fun clear() {
        uuid = ""
        refreshToken = ""
        accessToken = null
        accessTokenExpireTime = 0
        userInfo = ""
        newUser = 1
        sessionId = ""
        doMainName = ""
        setMyQrCode(null)
        val allKeys = mmkv.allKeys()
        if (!allKeys.isNullOrEmpty()) {
            allKeys.forEach {
                if (it.startsWith(KEY_IS_PLAYED_GAME_PREFIX)) {
                    mmkv.removeValueForKey(it)
                }
            }
        }
    }
}