package com.socialplay.gpark.data.interactor
import android.content.Intent
import android.os.Bundle
import com.socialplay.gpark.data.model.sdk.SdkCommandType
import com.socialplay.gpark.data.model.sdk.SdkRequestParams
import com.socialplay.gpark.data.model.sdk.resp.AuthReq

class SdkInteractor {
    companion object {
        const val SDK_ENTRY_ACTIVITY = "com.socialplay.gpark.ui.entry.GparkEntryActivity"
    }
    val sdkRequestParams: SdkRequestParams by lazy { SdkRequestParams() }
    val authReq: AuthReq by lazy { AuthReq() }

    fun handleIntent(intent: Intent): Boolean {

        val bundle = intent.extras ?: return false
        sdkRequestParams.fromBundle(bundle)

        val result = sdkRequestParams.checkArgs()
        if (result) {
            handleCommandType(bundle)
        }

        return result
    }


    private fun handleCommandType(bundle: Bundle) {
        when(sdkRequestParams.commandType) {
            SdkCommandType.TYPE_AUTHORIZE ->  {
                authReq.fromBundle(bundle)
            }
            else -> Unit
        }
    }

    fun currentCommandType(): Int = sdkRequestParams.commandType


    fun getAppKey(): String {
        return sdkRequestParams.appKey ?: ""
    }

    fun buildResultToCallerIntent(): Intent {
        return Intent().apply {
            val bundle = Bundle()
            sdkRequestParams.toBundle(bundle)
            putExtras(bundle)
            setClassName(sdkRequestParams.callerPackage ?: "", SDK_ENTRY_ACTIVITY)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK).addFlags(Intent.FLAG_ACTIVITY_MULTIPLE_TASK)
        }
    }
}
