package com.socialplay.gpark.data.model.pay

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/01/18
 *     desc   :
 *
 */
data class CommonPayParams(
    val ourProductId:String? = "",
    val parentProductId: String? = "", //google play订阅的商品id
    val source: String? = null,
    val gameId: String? = null,
    val scene: String,
    val productId: String? = "",//google play订阅的计划id
    val price: Int = 0,
    val sceneCode: Int,
    val currencyCode: String,
)

data class PayExtraInfo(val productIdList: ArrayList<String>? = null)
data class SubsData(
    val parentProductId: String = "",//google play订阅的商品id
    val productId: String? = null,//google play订阅的计划id
)