package com.socialplay.gpark.data.model.editor

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.data.model.entity.RecentUgcGameEntity
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.UserTagInfo
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/02/07
 *     desc   :
 * </pre>
 */
@Parcelize
data class UgcDetailInfo(
    val id: String = "0",
    val metaNum: String? = null,
    val packageName: String? = null,
    val ugcGameName: String? = null,
    val gameCode: String? = null,
    val banner: String? = null,
    val ugcGameDesc: String? = null,
    val userName: String? = null,
    val userUuid: String? = null,
    val userIcon: String? = null,
    val userReleaseCount: Long = 0,
    var followUser: Boolean = false,
    var loveQuantity: Long = 0,
    var likeIt: Boolean = false,
    val disLoveQuantity: Long = 0,
    val disLikeIt: Boolean = false,
    val pageView: Long = 0,
    val parentIcon: String? = null,
    val updateTime: Long = 0,
    val gameCirclePostCount: Long = 0,
    val hasGameCircle: Boolean = false,
    val backdropImg: String? = null,
    val portraits: List<String>? = null,
    // 用户标签
    val userTags: List<UserTagInfo>? = null,
    val mwTip: String? = null, //MW引擎不兼容提示
    val shareCount: Long = 0,
    // 创建时间
    val createTime: Long = 0,
    val realGameCode: String? = null,
    @SerializedName(value = "labelInfo", alternate = ["userBadge"])
    val labelInfo: LabelInfo? = null,
    val totalTippedCoins: Long = 0,
    val myTippableCoins: Long = 0,
    val myTippedCoins: Long = 0,
    val myTippedTimes: Long = 0,
) : Parcelable, RecentUgcGameEntity.Convertor {

    companion object {
        // ugc游戏的ugid都会>=这个值，
        private const val UGC_MIN_UGID = (1L shl 62) + 1

        /**
         * 根据ugid辨别是否是ugc游戏
         * @return true:是ugc游戏 false:不是ugc游戏
         */
        fun identifyUgcGameByUgId(ugid: Long): Boolean {
            return ugid > UGC_MIN_UGID
        }

        /**
         * 根据ugid辨别是否是ugc游戏
         * @return true:是ugc游戏 false:不是ugc游戏
         */
        fun identifyUgcGameByUgId(ugid: String): Boolean {
            ugid.toLongOrNull()?.let {
                return identifyUgcGameByUgId(it)
            } ?: return false
        }
    }

    val availableGameCode: String? get() = realGameCode ?: gameCode

    val tagIds get() = userTags?.map { it.id }

    @Parcelize
    data class UserBadge(val name: String? = null, val icon: String? = null) : Parcelable

    fun isOfficial(): Boolean {
        return MetaUserInfo.isOfficial(userTags)
    }

    override fun toMetaRecentUgcGameEntity() = RecentUgcGameEntity(
        id = id,
        packageName = packageName ?:"",
        gameName = ugcGameName,
        gameIcon = banner ?: parentIcon,
        gameCode = availableGameCode.orEmpty(),
        username = userName,
        userAvatar = userIcon,
        likeCount = loveQuantity,
        likeIt = likeIt,
        popularity = pageView,
        updateTime = updateTime,
        releaseTime = createTime,
    )
}