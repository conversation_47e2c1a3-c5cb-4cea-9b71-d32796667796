package com.socialplay.gpark.data.kv

import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.model.DevEnvType
import com.socialplay.gpark.util.property.MMKVScope
import com.socialplay.gpark.util.property.kvProperty
import com.tencent.mmkv.MMKV

/**
 * xingxiu.hou
 * 2021/6/6
 */
class DeveloperKV(override val mmkv: MMKV): MMKVScope {

    companion object {
        private const val KEY_OPEN_SHOE_EVENT_TOGGLE = "key_open_shoe_event_toggle"

        const val KEY_CUR_DEV_ENV_TYPE = "KEY_CUR_DEV_ENV_TYPE"
        const val KEY_PANDORA_ENV_TYPE = "PD_ENV_TYPE"

    }

    var isFirstOpen by kvProperty(true)

    var envType: DevEnvType
        get() {
            return when (mmkv.getString(KEY_CUR_DEV_ENV_TYPE, null)) {
                DevEnvType.Dev.name    -> {
                    DevEnvType.Dev
                }
                DevEnvType.Test.name   -> {
                    DevEnvType.Test
                }
                DevEnvType.Pre.name    -> {
                    DevEnvType.Pre
                }
                DevEnvType.Online.name -> {
                    DevEnvType.Online
                }
                else                   -> {
                    DevEnvType.values().firstOrNull {
                        // 此处没获得到可能是覆盖安装问题
                        it.name == BuildConfig.ENV_TYPE
                    } ?: DevEnvType.values().first {
                        // 此处不应当崩，崩了就是配置问题
                        it.name in BuildConfig.ENV_SCOPE
                    }
                }
            }
        }
        set(value) {
            mmkv.putString(KEY_CUR_DEV_ENV_TYPE, value.name)
        }

    var isShowEvent: Boolean
        set(value) {
            mmkv.putBoolean(KEY_OPEN_SHOE_EVENT_TOGGLE, value)
        }
        get() {
            return mmkv.getBoolean(KEY_OPEN_SHOE_EVENT_TOGGLE, false)
        }

    var isNeedPassWordOpenDeveloper: Boolean by kvProperty(!BuildConfig.DEBUG)

    fun putString(key: String, value: String) {
        mmkv.putString(key, value)
    }

    fun putInt(key: String, value: Int) {
        mmkv.putInt(key, value)
    }

    fun putBoolean(key: String, value: Boolean) {
        mmkv.putBoolean(key, value)
    }

    fun putFloat(key: String, value: Float) {
        mmkv.putFloat(key, value)
    }

    fun putLong(key: String, value: Long) {
        mmkv.putLong(key, value)
    }

    fun hasKey(key: String): Boolean {
        return mmkv.containsKey(key)
    }

    fun removeKey(key: String) {
        mmkv.remove(key)
    }

    fun getInt(key: String, defValue: Int): Int {
        return mmkv.getInt(key, defValue)
    }

    fun getLong(key: String, defValue: Long): Long {
        return mmkv.getLong(key, defValue)
    }

    fun getString(key: String, defValue: String): String? {
        return mmkv.getString(key, defValue)
    }

    fun getFloat(key: String, defValue: Float): Float {
        return mmkv.getFloat(key, defValue)
    }

    fun getBoolean(key: String, defValue: Boolean): Boolean {
        return mmkv.getBoolean(key, defValue)
    }

}