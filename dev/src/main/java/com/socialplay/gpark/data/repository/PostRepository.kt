package com.socialplay.gpark.data.repository

import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.invokeAndRethrow
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.suspendApi
import com.socialplay.gpark.data.base.suspendApiNotNull
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.editor.MyCreationsV4Request
import com.socialplay.gpark.data.model.post.CommentResultWrapper
import com.socialplay.gpark.data.model.post.CommunityFeedWrapper
import com.socialplay.gpark.data.model.post.OpinionRequestBody
import com.socialplay.gpark.data.model.post.PostCardResult
import com.socialplay.gpark.data.model.post.PostCommentListRequestBody
import com.socialplay.gpark.data.model.post.PostCommentRequestBody
import com.socialplay.gpark.data.model.post.PostOutfitVisitRequest
import com.socialplay.gpark.data.model.post.PostPublish
import com.socialplay.gpark.data.model.post.PostPublish.Companion.filterEmptyUrl
import com.socialplay.gpark.data.model.post.PostReplyListRequestBody
import com.socialplay.gpark.data.model.post.PostReplyRequestBody
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.PublishPostRequest
import com.socialplay.gpark.data.model.post.SearchPostCardRequest
import com.socialplay.gpark.data.model.post.request.AddPvBatchRequest
import com.socialplay.gpark.data.model.post.request.AddPvRequest
import com.socialplay.gpark.data.model.post.request.FetchBlockRequest
import com.socialplay.gpark.data.model.post.request.FetchTopicRequest
import com.socialplay.gpark.data.model.post.request.PostProfileFeedRequest
import com.socialplay.gpark.data.model.post.request.PostTagFeedRequest
import com.socialplay.gpark.data.model.post.request.QueryTopicsRequest
import com.socialplay.gpark.data.model.videofeed.VideoFeedApiResult
import com.socialplay.gpark.function.search.SearchHelper
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.singleOrNull

class PostRepository(
    private val miscRepository: MiscRepository,
    private val metaApi: MetaApi,
    private val metaKV: MetaKV
) {

    /**
     * option 看法 0无感 1赞 2踩
     * resType 资源类型 1帖子 2游戏 3评论 4回复
     */
    fun saveCommunityLike(resId: String, option: Int, resType: Int) = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.saveCommunityLike(
                mapOf(
                    "resId" to resId,
                    "opinion" to option,
                    "resType" to resType,
                )
            )
        })
    }

    /**
     * reportType 举报类型 1举报用户 2评论举报 3帖子举报
     */
    fun report(postId: String, reportType: Int): Flow<DataResult<Boolean>> = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.report(
                mapOf(
                    "reportId" to postId,
                    "reportType" to reportType
                )
            )
        })
    }

    /**
     * 发送回复
     */
    fun getPostOpinion(
        resId: String,
        resType: Int
    ): Flow<DataResult<Int>> = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.getOpinion(resId, resType)
        })
    }

    fun getRandomPostTags() = suspendApiNotNull {
        metaApi.getRandomPostTags()
    }

    /**
     * 获取推荐标签
     */
    fun getRecommendPostTags(text: String) = suspendApiNotNull {
        metaApi.getRecommendPostTags(mapOf("keyword" to text, "pageSize" to "10", "pageNum" to "1"))
    }

    @Deprecated(message = "use savePostV3 instead")
    suspend fun savePostV2(body: PublishPostRequest) = DataSource.getDataResultForApi {
        metaApi.savePostV2(body)
    }

    /**
     * 社区发帖，返回已审核通过的话题列表
     * [mock] https://mock.metaapp.cn/project/595/interface/api/58227
     */
    suspend fun savePostV3(body: PublishPostRequest) = DataSource.getDataResultForApi {
        metaApi.savePostV3(body)
    }

    suspend fun editPost(body: PublishPostRequest) = DataSource.getDataResultForApi {
        metaApi.editPost(body)
    }

    fun getPostDetailV2(postId: String) = suspendApiNotNull {
        metaApi.getPostDetailV2(postId)
    }

    fun deletePostV2(postId: String) = suspendApiNotNull {
        metaApi.deletePostV2(mapOf("postId" to postId))
    }

    fun getPostCommentListV2(body: PostCommentListRequestBody) = suspendApiNotNull {
        metaApi.getPostCommentListV2(body)
    }

    fun addPostComment(body: PostCommentRequestBody) = suspendApiNotNull {
        val result = metaApi.addPostComment(body)
        result.map {
            CommentResultWrapper(result.data, result.message)
        }
    }

    fun deletePostComment(commentId: String) = suspendApiNotNull {
        metaApi.deletePostComment(mapOf("commentId" to commentId))
    }

    fun deleteUgcComment(gameId: String, commentId: String) = suspendApiNotNull {
        metaApi.deleteUgcComment(mapOf("gameId" to gameId, "commentId" to commentId))
    }

    fun getPostReplyListV2(body: PostReplyListRequestBody) = suspendApiNotNull {
        metaApi.getPostReplyListV2(body)
    }

    fun addPostReply(body: PostReplyRequestBody) = suspendApiNotNull {
        metaApi.addPostReply(body)
    }

    fun deletePostReply(replyId: String) = suspendApiNotNull {
        metaApi.deletePostReply(mapOf("replyId" to replyId))
    }

    fun saveOpinion(body: OpinionRequestBody) = suspendApiNotNull {
        metaApi.saveOpinion(body)
    }

    suspend fun getGlobalCircleId(): String? {
        val cache = metaKV.tTaiKV.communityCircleId
        if (cache.isEmpty()) {
            return miscRepository.getTTaiConfigById(TTaiKV.ID_COMMUNITY_CIRCLE_ID)
                .singleOrNull()?.data?.value
        }
        return cache
    }

    @Throws
    suspend fun getAllPostTagList(): List<PostTag> {
        return suspendApiNotNull { metaApi.getAllPostTagList() }.invoke()
    }

    fun getCommunityTagFeed(
        orderType: Int,
        postTagType: Int?,
        tagId: Long?,
        blockId: Long?,
        pageSize: Int,
        pageNum: Int
    ): suspend () -> CommunityFeedWrapper =
        suspendApiNotNull {
            metaApi.getCommunityTagFeed(
                PostTagFeedRequest(
                    orderType,
                    tagId,
                    blockId,
                    pageSize,
                    pageNum,
                    postTagType = postTagType,
                    null,
                    circleId = getGlobalCircleId() ?: ""
                )
            )
        }.map {
            it.validate
        }

    fun getCommunityProfileFeed(otherUid: String, pageSize: Int, pageNum: Int): suspend () -> CommunityFeedWrapper = suspendApiNotNull {
        metaApi.getCommunityProfileFeed(PostProfileFeedRequest(otherUid, pageSize, pageNum))
    }.map {
        it.validate
    }

    fun getIDevelopedPgcList(request: MyCreationsV4Request): suspend () -> PostCardResult = suspendApiNotNull {
        metaApi.getMyCreationsV4(request)
    }.map {
        PostCardResult(
            it.end,
            it.validPostCardList.orEmpty(),
            orderId = it.games?.lastOrNull()?.orderId
        )
    }

    fun searchPostCardList(request: SearchPostCardRequest): suspend () -> PostCardResult =
        suspendApi {
            metaApi.getSearchGameInfo(
                mapOf(
                    "keyword" to request.keyword,
                    "page" to request.pageNum,
                    "lastOrderNum" to request.lastOrderNum,
                    "typeList" to SearchHelper.typeList
                )
            )
        }.map {
            PostCardResult(
                it?.end ?: true,
                it?.validPostCardList.orEmpty(),
                lastOrderNum = it?.lastOrderNum
            )
        }

    fun getVideoFeedList(version: String?, offset: Int, size: Int, pinPostId: String?): Flow<VideoFeedApiResult> = flow {
        val result = invokeAndRethrow {
            metaApi.getVideoFeedList(
                mutableMapOf(
                    "version" to version.toString(),
                    "offset" to offset.toString(),
                    "size" to size.toString()
                ).apply {
                    pinPostId?.let { put("targetVideoId", it) }
                }
            )
        }
        emit(result!!)
    }

    fun visitOutfitCard(postId: String, roleId: String) = suspendApi {
        metaApi.visitPostOutfitCard(PostOutfitVisitRequest.createOutfitVisit(postId, roleId))
    }

    fun followTopic(tagId: Long) = suspendApi {
        metaApi.followTopic(FetchTopicRequest(tagId))
    }

    fun unfollowTopic(tagId: Long) = suspendApi {
        metaApi.unfollowTopic(FetchTopicRequest(tagId))
    }

    fun fetchMyFollowTopics(pageSize: Int, pageNum: Int) = suspendApiNotNull {
        metaApi.myFollowTopics(QueryTopicsRequest(pageSize, pageNum))
    }

    fun fetchHotTopics(pageSize: Int, pageNum: Int) = suspendApiNotNull {
        metaApi.hotTopics(pageSize, pageNum)
    }

    fun fetchTopicDetail(tagId: Long) = suspendApiNotNull {
        metaApi.topicDetail(FetchTopicRequest(tagId))
    }

    suspend fun addTopicViewCountBatch(tagIds: List<String>) = metaApi.commonAddPvCountBatch(AddPvBatchRequest(AddPvRequest.RES_TYPE_TOPIC_VIEW, tagIds))

    fun commonAddPvCount(resType: String, resId: String): Flow<DataResult<Boolean?>> {
        return flow {
            emit(DataSource.getDataResultForApi {
                metaApi.commonAddPvCount(AddPvRequest(resType, resId))
            })
        }
    }

    fun fetchBlockList(vis: Int?) = suspendApi {
        metaApi.fetchBlockList(FetchBlockRequest(getGlobalCircleId() ?:"", vis))
    }

    suspend fun addAIBotComment(body: PostCommentRequestBody): DataResult<String?> {
        return DataSource.getDataResultForApi {
            metaApi.addPostComment(body)
        }
    }
}