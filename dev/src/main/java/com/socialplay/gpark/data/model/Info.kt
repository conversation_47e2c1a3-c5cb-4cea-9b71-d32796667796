package com.socialplay.gpark.data.model

import android.graphics.drawable.Drawable
import android.os.Parcelable
import com.socialplay.gpark.util.TsEngineVersionCheckUtil
import com.meta.lib.mwbiz.MWBiz
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.UserTagInfo
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/16
 * desc   :
 * </pre>
 */


data class ImageInfo(
    val url: String? = null, // 图片地址
    val width: Int = 0, // 宽
    val height: Int = 0, // 高
) {
    /**
     * 是否为横向图片
     */
    fun isHor(): Boolean {
        return width >= height
    }
}

data class VideoInfo(
    val image: String? = null, // 图片地址
    val video: String? = null, // 图片地址
    val width: Int = 0, // 宽
    val height: Int = 0, // 高
) {
    /**
     * 是否为横向图片
     */
    fun isHor(): <PERSON><PERSON><PERSON> {
        return width >= height
    }
}

data class ArchiveInfo(
    val id: String, // 内容库定义的存档游戏id【如果是存档游戏的话才有值】
)

@Parcelize
data class AuthorInfo(
    val id: String,
    val number: String?, // 国外: gpark号, 国内: 233派对号
    val name: String?, // 作者名称
    val avatar: String?, // 作者头像URL地址
    val introduction: String?, // 作者游戏介绍
    // 用户标签
    var tags: List<UserTagInfo>?,
    var follow: Boolean
): Parcelable {

    val tagIds get() = tags?.map { it.id }

    fun isOfficial(): Boolean {
        return MetaUserInfo.isOfficial(tags)
    }
}

data class SnsInfo(
    val playerCount: Long, // 玩家数量
    val likeCount: Long, // 点赞数量
    val rate: Double?, // 点赞百分比
)

data class ResourceInfo(
    val url: String?, // 资源下载URL地址
    val resourceType: Int?, // 资源类型; 1: apk;
    val size: Long?, // 长整型（单位：字节数）
    val fingerprint: String?, // 资源指纹【安装包md5 or sha】
    val packageName: String?, // 安装包包名
    val upgradeStrategy: Int?, // 升级策略（枚举值）；【1：可选择性更新；2：强制更新】
    val upgradeInstallType: Int?, // 更新安装方式（枚举值）；【1：覆盖安装，2：删除重装】
    val editorVersion: String? // ts游戏支持的版本号，当前引擎版本比这个小不能玩
) {
    companion object {
        const val RESOURCE_TYPE_APK = 1
    }

    fun isApkResource(): Boolean = resourceType == RESOURCE_TYPE_APK

    /**
     * 检查当前引擎 是否可玩ts游戏
     */
    fun isValidMwEngineVersion(): Boolean {
        // editorVersion为null 直接返回true就好， 正常进游戏
        if (editorVersion.isNullOrEmpty()) return true
        return TsEngineVersionCheckUtil.checkVersion(editorVersion, MWBiz.engineVersion())
    }
}

data class ApkInfo(
    val packageName: String,
    val appName: String,
    val versionName: String,
    val versionCode: Long,
    val icon: Drawable,
    val fileSize: Long,
    var localPath: String
)

data class StartupInfo(
    val id: String,
    val packageName: String,
    val params: String?
)