package com.socialplay.gpark.data.model.share

import android.net.Uri
import android.os.Parcelable
import androidx.core.net.toUri
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.function.share.platform.ShareHelper
import kotlinx.parcelize.Parcelize
import org.koin.core.context.GlobalContext
import java.io.File

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/05/28
 *     desc   :
 * </pre>
 */
@Parcelize
data class UserShareInfo(
    val userUuid: String?,
    val userNumber: String?,
    val username: String?,
    val userAvatar: String?,
    val userQrCode: String?,
) : Parcelable {

    companion object {

        fun new(): UserShareInfo? {
            val account = GlobalContext.get().get<AccountInteractor>().accountLiveData.value ?: return null
            val url = ShareHelper.downloadPage
            return UserShareInfo(
                account.uuid,
                account.userNumber,
                account.nickname,
                account.portrait,
                "${url}?my_uniq_id=${account.uuid.orEmpty()}&source=avatar"
            )
        }
    }
}

@Parcelize
data class RoleScreenshot(
    val path: String,
    val uri: Uri = File(path).toUri(),
    val isChecked: Boolean = true
) : Parcelable {
    fun switch() = copy(isChecked = !isChecked)
}

@Parcelize
data class RoleMyInfo(
    val info: UserShareInfo,
    val isChecked: Boolean = true
) : Parcelable {
    fun switch() = copy(isChecked = !isChecked)
}

@Parcelize
data class ShareRoleScreenshotEvent(
    val gameId: String,
    val screenshots: List<String>,
    val roleId: String?,
    val isShowUuid: Boolean,
    val publishPostNeedFinish: Boolean= false
): Parcelable

@Parcelize
data class ShareRoleScreenshotData(
    val roleId: String?,
    val isShowUuid: Boolean,
    val screenshots: List<String>?
): Parcelable