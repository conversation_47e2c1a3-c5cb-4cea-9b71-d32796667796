package com.socialplay.gpark.data.model.share

import com.socialplay.gpark.data.model.editor.UgcDetailInfo
import com.socialplay.gpark.data.model.outfit.UgcDesignDetail
import com.socialplay.gpark.data.model.post.PostDetail
import com.socialplay.gpark.data.model.post.PostShareDetail
import com.socialplay.gpark.data.model.profile.UserProfileInfo
import com.socialplay.gpark.data.model.videofeed.VideoFeedItem
import com.socialplay.gpark.util.GsonUtil

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/04/11
 *     desc   :
 *
 */
data class ShareContent(
    val type: String = "", //分享的类型
    val content: String = "" //分享的内容
) {
    companion object {
        const val TYPE_TOPIC = "topic"
        const val TYPE_POST = "post"
        const val TYPE_PROFILE = "profile"
        const val TYPE_PGC_DETAIL = "pgcDetail"
        const val TYPE_UGC_DETAIL = "ugcDetail"
        const val TYPE_VIDEO_FEED = "videoFeed"
        const val TYPE_IMAGE = "image"
        const val TYPE_UGC_DESIGN_DETAIL = "ugcDesignDetail"

        fun userProfileString(userProfile: UserProfileInfo) =
            GsonUtil.safeToJson(ShareContent(TYPE_PROFILE, GsonUtil.safeToJson(userProfile)))

        fun pgcDetailString(pgcDetail: ShareRawData.Game) =
            GsonUtil.safeToJson(ShareContent(TYPE_PGC_DETAIL, GsonUtil.safeToJson(pgcDetail)))

        fun ugcDetailString(ugcDetail: UgcDetailInfo) =
            GsonUtil.safeToJson(ShareContent(TYPE_UGC_DETAIL, GsonUtil.safeToJson(ugcDetail)))

        fun postDetailString(postShareDetail: PostShareDetail) =
            GsonUtil.safeToJson(ShareContent(TYPE_POST, GsonUtil.safeToJson(postShareDetail)))

        fun videoFeedString(videoFeed: VideoFeedItem) =
            GsonUtil.safeToJson(ShareContent(TYPE_VIDEO_FEED, GsonUtil.safeToJson(videoFeed)))

        fun imageString(imagePath: String) =
            GsonUtil.safeToJson(ShareContent(TYPE_IMAGE, imagePath))

        fun ugcDesignDetailString(ugcDesignDetail: UgcDesignDetail) =
            GsonUtil.safeToJson(
                ShareContent(
                    TYPE_UGC_DESIGN_DETAIL,
                    GsonUtil.safeToJson(ugcDesignDetail)
                )
            )
    }

}