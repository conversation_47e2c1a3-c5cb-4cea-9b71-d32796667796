package com.socialplay.gpark.data.repository

import androidx.paging.ExperimentalPagingApi
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.map
import androidx.room.withTransaction
import com.meta.biz.ugc.model.EditorTemplate
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.ApiResult
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.base.suspendApi
import com.socialplay.gpark.data.base.suspendApiNotNull
import com.socialplay.gpark.data.interactor.DeviceInteractor
import com.socialplay.gpark.data.interactor.RecommendApiHeaderWrapper
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.local.AppDatabase
import com.socialplay.gpark.data.local.DeleteItem
import com.socialplay.gpark.data.local.GameDetailDao
import com.socialplay.gpark.data.local.MyPlayedGameDao
import com.socialplay.gpark.data.local.RecentUgcGameDao
import com.socialplay.gpark.data.local.exists
import com.socialplay.gpark.data.mapper.MetaMapper
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.GameItem
import com.socialplay.gpark.data.model.GameSuggestionInfo
import com.socialplay.gpark.data.model.MyPlayedGame
import com.socialplay.gpark.data.model.ReviewGameInfo
import com.socialplay.gpark.data.model.SearchGameItem
import com.socialplay.gpark.data.model.UGGameSuggestionInfo
import com.socialplay.gpark.data.model.aibot.AiBotApiResult
import com.socialplay.gpark.data.model.aibot.AiBotTitle
import com.socialplay.gpark.data.model.aibot.BotLabelInfo
import com.socialplay.gpark.data.model.aibot.BotLabelInfo.Companion.TAG_ALL
import com.socialplay.gpark.data.model.choice.CardAll
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceCardListApiResult
import com.socialplay.gpark.data.model.choice.ChoiceCardType
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.data.model.choice.Recommend
import com.socialplay.gpark.data.model.choice.RecommendRequestBody
import com.socialplay.gpark.data.model.editor.GroupedData.Companion.CHOICE_CARD_TYPE_AI_BOT
import com.socialplay.gpark.data.model.entity.GameDetailEntity
import com.socialplay.gpark.data.model.entity.RecentUgcGameEntity
import com.socialplay.gpark.data.model.entity.UpdateLastPlayTime
import com.socialplay.gpark.data.model.entity.UpdateMyPlayedGameDownloadPercent
import com.socialplay.gpark.data.model.entity.UpdateMyPlayedGameGameInfo
import com.socialplay.gpark.data.model.post.PostCardResult
import com.socialplay.gpark.data.model.profile.recent.GameEntity
import com.socialplay.gpark.data.model.profile.recent.RecentPlayList
import com.socialplay.gpark.data.model.profile.recent.RecentPlayListV2Request
import com.socialplay.gpark.data.model.profile.recent.RecentPlayListV2Response
import com.socialplay.gpark.data.repository.api.RecentPlayPagingSource
import com.socialplay.gpark.data.repository.api.RecentPlayPagingSourceV2
import com.socialplay.gpark.data.repository.api.SearchGamePagingSource
import com.socialplay.gpark.data.repository.db.HomePageRemoteMediator
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.editor.home.datalist.EditorHomeDataViewModel.Companion.GENDER_ALL
import com.socialplay.gpark.ui.suggestion.GameSuggestionViewModel
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.SpanUtil
import com.socialplay.gpark.util.getStringByGlobal
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

/**
 * Created by yaqi.liu on 2021/6/22
 */
@ExperimentalPagingApi
class GameRepository(
    private val metaApi: com.socialplay.gpark.data.api.MetaApi,
    private val gameDetailDao: GameDetailDao,
    private val myPlayedGameDao: MyPlayedGameDao,
    private val recentUgcDao: RecentUgcGameDao,
    private val db: AppDatabase,
    private val metaKV: MetaKV,
    private val metaMapper: MetaMapper,
    private val deviceInteractor: DeviceInteractor
) {

    suspend fun getGameInfoCache(id: String): DataResult<GameDetailInfo> {
        return DataSource.getDataResult(true) { gameDetailDao.getById(id) }.map { metaMapper.map(it) }
    }

    /**
     * 根据gameId获取游戏信息
     */
    suspend fun getGameInfoByGameId(id: String): Flow<DataResult<GameDetailInfo>> = flow {
        emit(getGameInfoByGameIdWithoutFlow(id))
    }

    suspend fun updateMyGameInfo(info: GameDetailInfo, percent: Float) {
        runCatching {
            db.withTransaction {
                myPlayedGameDao.run {
                    if (!exists(info.id)) {
                        insertInfo(metaMapper.map(info, System.currentTimeMillis(), percent, 0, System.currentTimeMillis()))
                    } else {
                        updateDownloadPercent(UpdateMyPlayedGameDownloadPercent(info.id, percent))
                    }
                }
                gameDetailDao.run {
                    if (!exists(info.id)) {
                        insertInfo(metaMapper.map(info))
                    }
                }
            }
        }
    }

    /**
     * 更新游戏详情页缓存游戏信息
     */
    private suspend fun updateMetaAppInfo(entity: GameDetailEntity) {
        runCatching {
            db.withTransaction {
                if (gameDetailDao.exists(entity.id)) {
                    gameDetailDao.updateInfo(entity)
                } else {
                    gameDetailDao.insertInfo(entity)
                }
                if (myPlayedGameDao.exists(entity.id)) {
                    myPlayedGameDao.updateGameInfo(UpdateMyPlayedGameGameInfo(entity.id, entity.name, entity.gameIcon, entity.type, entity.startupExtension, entity.resource?.packageName, entity.gameTags))
                }
            }
        }
    }

    suspend fun deleteMyPlayedGame(id: String) {
        myPlayedGameDao.delete(DeleteItem(id))
    }

    suspend fun deleteMyPlayedGameList(ids: List<String>) {
        kotlin.runCatching {
            myPlayedGameDao.deleteList(ids.map { DeleteItem(it) })
        }
    }

    suspend fun isMyPlayedGame(id: String): Boolean {
        return myPlayedGameDao.exists(id)
    }

    fun fetchPlayedGame(size: Int): Flow<PagingData<MyPlayedGame>> {
        return Pager(
            PagingConfig(
                pageSize = size,
                enablePlaceholders = true,
                initialLoadSize = size
            )
        ) {
            myPlayedGameDao.playedGames(0, size)
        }.flow
            .catch {
                it.printStackTrace()
                emit(PagingData.empty())
            }
            .map { pagingData ->
                pagingData.map { metaMapper.map(it) }
            }
    }

    fun fetchPlayedGameV2(index: Int = 0, size: Int): Flow<List<MyPlayedGame>> = flow {
        emit(myPlayedGameDao.playedGamesV2(index, size).map { metaMapper.map(it) })
    }

    fun fetchRecentPlayedPgcCardList(
        pageSize: Int,
        pageNum: Int,
        uuid: String
    ): Flow<PostCardResult> = flow {
        emit(
            suspendApi {
                metaApi.getRecentPlayGameList(pageNum, pageSize, uuid)
            }.invoke()
        )
    }.map {
        PostCardResult(
            end = it?.end ?: true,
            dataList = it?.dataList?.map { metaMapper.mapPostCard(it) } ?: emptyList()
        )
    }

    fun fetchRecentPlayedPgcCardListV2(
        pageSize: Int,
        pageNum: Int,
        uuid: String
    ): Flow<PostCardResult> = flow {
        emit(
            suspendApi {
                metaApi.getRecentPlayGameListV2(
                    RecentPlayListV2Request(
                        pageNum,
                        pageSize,
                        uuid,
                        true
                    )
                )
            }.invoke()
        )
    }.map {
        PostCardResult(
            end = it?.end ?: true,
            dataList = it?.validPostCardList.orEmpty()
        )
    }

    fun fetchRecommendGames(size: Int): Flow<PagingData<GameItem>> {
        return Pager(
            config = PagingConfig(
                pageSize = size,
                enablePlaceholders = false,
            ),
            initialKey = 0,
            remoteMediator = HomePageRemoteMediator(db, metaApi, metaKV)
        ) {
            db.homePageDao().all()
        }.flow
            .catch {
                it.printStackTrace()
                emit(PagingData.empty())
            }
            .map { pagingData ->
                pagingData.map { metaMapper.map(it) }
            }
    }

    fun fetchSearchGame(searchKey: String, isRelate: Boolean, size: Int, keywordId: String?): Flow<PagingData<SearchGameItem>> {
        return Pager(
            config = PagingConfig(
                pageSize = size,
                initialLoadSize = size,
                prefetchDistance = 10,
            ),
            initialKey = 1 to null
        ) {
            SearchGamePagingSource(metaApi, searchKey, isRelate, keywordId)
        }.flow.map { pagingData ->
            pagingData.map {
                metaMapper.map(it).apply {
                    displayName = if (displayName.isNullOrEmpty()) null else SpanUtil.getHighlightSpannable(displayName.toString(), searchKey)
                }
            }
        }
    }

    /**
     * 获取游戏详情进入次数
     */
    fun getGameDetailEnteredTimes(gameId: String): Long = metaKV.gameDetail.getEnteredGames(gameId)

    suspend fun onLaunchGame(gameId: String, packageName: String) {
        runCatching {
            db.withTransaction {
                if (myPlayedGameDao.exists(gameId)) {
                    myPlayedGameDao.updateLastPlayTime(UpdateLastPlayTime(gameId))
                } else {
                    gameDetailDao.getById(gameId)?.let {
                        myPlayedGameDao.insertInfo(metaMapper.map(it, System.currentTimeMillis(), 1F, System.currentTimeMillis(), System.currentTimeMillis()))
                    }
                }
            }
        }
    }

    suspend fun insertGameDetailEntityIfNoExists(gameInfo: GameDetailEntity) {
        runCatching {
            db.withTransaction {
                if (!gameDetailDao.exists(gameInfo.id)) {
                    gameDetailDao.insertInfo(gameInfo)
                }
            }
        }
    }

    /**
     * 无缓存
     */
    suspend fun getGameInfoByGameIdWithoutFlow(id: String): DataResult<GameDetailInfo> {
        val result = DataSource.getDataResultForApi { metaApi.getGameInfoByGameId(id) }
        val dataResult = result.map { metaMapper.map(it) }
        if (result is DataResult.Success) {
            updateMetaAppInfo(result.data)
        }
        return dataResult
    }

    /**
     * 根据gameId获取游戏信息
     */
    fun getGameInfoByGameIdFromRemoteWithCache(id: String): Flow<DataResult<GameDetailInfo>> = flow {
        val cache = getGameInfoCache(id)
        if (cache.data != null) {
            cache.data?.isCache = true
            emit(cache)
        }
        val result = DataSource.getDataResultForApi { metaApi.getGameInfoByGameId(id) }
        val dataResult = result.map { metaMapper.map(it) }
        if (result is DataResult.Success) {
            updateMetaAppInfo(result.data)
        }
        emit(dataResult)
    }

    fun searchReviewGameById(gameId: String): Flow<DataResult<ReviewGameInfo>> = flow {
        emit(DataResult.Loading)
        emit(DataSource.getDataResultForApi { metaApi.searchReviewGameById(gameId) })
    }

    fun postSearchReport(contentId: String, contentType: Int): Flow<DataResult<Any?>> = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.postSearchReport(
                hashMapOf(
                    "contentId" to contentId,
                    "contentType" to contentType
                )
            )
        })
    }

    suspend fun getGameListByIds(gameIdList: List<String>): Flow<DataResult<List<GameSuggestionInfo>>> = flow {
        Timber.d("getGameListByIds game id list : $gameIdList")
        emit(DataSource.getDataResultForApi { metaApi.getGameListByIds(gameIdList) })
    }

    suspend fun getSuggestGameIdList(): Flow<DataResult<List<String>>> = flow {
        var suggestGameIdList = metaKV.tTaiKV.suggestGameIdList
        Timber.d("Local TTAIKV Suggest game id list : $suggestGameIdList")

        if (suggestGameIdList.isNullOrBlank()) {
            val result = DataSource.getDataResultForApi { metaApi.getTTaiConfig(TTaiKV.ID_SUGGEST_GAME_ID) }
            if (result.succeeded && result.data!!.value.isNotBlank()) {
                suggestGameIdList = result.data!!.value
                Timber.d("Remote TTAI Suggest game id list : $suggestGameIdList")
            }
        }


        if (suggestGameIdList.isNullOrBlank()) {
            emit(DataResult.Error(-1, "Suggested game id list is empty"))
            return@flow
        }

        val gameIdList = kotlin.runCatching { suggestGameIdList.split(",") }.getOrElse { emptyList() }
        if (gameIdList.isEmpty()) {
            emit(DataResult.Error(-1, "Suggested game id list is empty"))
            return@flow
        }

        emit(DataResult.Success(gameIdList))
    }

    suspend fun isSuggestGameAlreadyShow(): Boolean {
        return withContext(Dispatchers.IO) {
            metaKV.appKV.isSuggestGameAlreadyShow
        }
    }

    suspend fun updateSuggestGameShowStatus(isAlreadyShow: Boolean) {
        metaKV.appKV.isSuggestGameAlreadyShow = isAlreadyShow
    }

    fun getAppLaunchTimes(): Long {
        return metaKV.appKV.appOpenTimes
    }

    suspend fun insertGameDetailCache(entity: GameDetailEntity) {
        runCatching {
            db.withTransaction {
                if (gameDetailDao.exists(entity.id)) {
                    gameDetailDao.updateInfo(entity)
                } else {
                    gameDetailDao.insertInfo(entity)
                }
            }
        }
    }

    val sceneCode = "g-party-homepage-android" // 安卓写死 ，后端对接 @程有松
    /**
     * 精选-首页
     */
    suspend fun getChoiceCardList() = flow {
        val result: DataResult<ChoiceCardListApiResult> = DataSource.getDataResultForApi { metaApi.getChoiceCardList(sceneCode) }
        val dataList = ArrayList(result.data?.dataList ?: emptyList())
        // 添加小屋卡片
        addCottageRoomList(dataList)
        // 做Trending卡片处理
        addTrendingInsertList(dataList)
        // 根据接口值过滤item
        choiceFilterCard(dataList)
        result.data?.dataList = dataList
        emit(result)
    }

    /**
     * 精选-角色页面
     */
    fun getChoiceCardListForAvatar() = flow {
        if (PandoraToggle.openAvatarUgcMode) {
            val result: DataResult<ChoiceCardListApiResult> = DataSource.getDataResultForApi { metaApi.getChoiceCardList(sceneCode) }
            emit(result)
        } else {
            emit(DataResult.Success(null))
        }
    }

    fun getChoiceAiBotCardList(tagId: Int?, gender: Int?, pageIndex: Int, pageCount: Int): Flow<DataResult<ChoiceCardListApiResult?>> = flow {
        if (!PandoraToggle.isOpenAiBot) {
            emit(DataResult.Success(null))
        } else {
            val result = ChoiceCardListApiResult()
            val choiceGameInfo = ChoiceCardInfo(CHOICE_CARD_TYPE_AI_BOT.toLong(), "AiBot", CHOICE_CARD_TYPE_AI_BOT, null)
            //标签列表
            val data = try {
                if (metaKV.tTaiKV.aiBotList.isNullOrEmpty()) {
                    DataSource.getDataResultForApi { metaApi.getTTaiConfig(TTaiKV.ID_KEY_AI_BOT_LABEL) }.data?.value
                } else {
                    metaKV.tTaiKV.aiBotList
                }
            } catch (e: Exception) {
                null
            }
            val labelList = GsonUtil.gsonSafeParseCollection<ArrayList<BotLabelInfo>>(data) ?: java.util.ArrayList()
            val label = if (!labelList.isNullOrEmpty() && tagId != null && tagId != TAG_ALL) {
                labelList.firstOrNull { (it.tagId == tagId) }
            } else {
                null
            }
            if (label != null) {
                label.selected = true
                labelList.add(0, BotLabelInfo(TAG_ALL, getStringByGlobal(R.string.ai_bot_tag_all_name), null, false))
            } else {
                labelList.add(
                    0,
                    BotLabelInfo(
                        TAG_ALL,
                        getStringByGlobal(R.string.ai_bot_tag_all_name),
                        null,
                        true
                    )
                )
            }
            val list = labelList
            val title = AiBotTitle(label = list.toList(), gender)
            // bot列表
            val map = HashMap<String, String>()
            map["pageNum"] = pageIndex.toString()
            map["pageSize"] = pageCount.toString()
            if (gender != null && (gender != GENDER_ALL)) {
                map["gender"] = gender.toString()
            }
            if (tagId != null && tagId != TAG_ALL) {
                map["tagId"] = tagId.toString()
            }
            val dataList = DataSource.getDataResultForApi { metaApi.getAiBotInfoList(map) }
            choiceGameInfo.aiBotList = AiBotApiResult(title, dataList.data?.dataList ?: emptyList())
            result.dataList = arrayListOf(choiceGameInfo)
            emit(DataResult.Success(result))
        }

    }

    /**
     * 精选-推荐
     */
    suspend fun getRecommend(offset: Int?): Flow<DataResult<Recommend>> = flow {
        val result = DataSource.getDataResultForApi {
            val headers = RecommendApiHeaderWrapper.getHeaders()
            if (PandoraToggle.isRecommendOpen()) metaApi.postRecommend2(RecommendRequestBody(offset = offset), headers) else metaApi.getRecommend(
                offset ?: -1
            )
        }
        if (PandoraToggle.isRecommendOpen()) {
            val reqid = result.data?.requestId
            result.data?.dataList?.forEach {
                it.reqid = reqid
            }
        }
        emit(result)
    }

    /**
     * 所有卡片
     */
    fun getCardAll(cardId: String, offset: Int, pageSize: Int): suspend () -> CardAll = suspendApiNotNull {
        metaApi.getCardAll(cardId, offset, pageSize)
    }

    /**
     * 精选过滤不支持的卡片/item
     */
    private fun choiceFilterCard(dataResultList: MutableList<ChoiceCardInfo>): MutableList<ChoiceCardInfo> {
        dataResultList.forEach {
            it.gameList?.removeAll {
                val ignore = it.ignoreThis
                val operationNotShow = it.operatingPosition != null && !it.operatingPosition.canShow()
                ignore || operationNotShow
            }
        }
        dataResultList.removeAll { it.cardType == ChoiceCardType.BANNER && it.gameList.isNullOrEmpty() }
        return dataResultList
    }

    /**
     * trending强插游戏
     */
    suspend fun addTrendingInsertList(dataResultList: MutableList<ChoiceCardInfo>): MutableList<ChoiceCardInfo> = withContext(Dispatchers.IO) {
        val insertGame = getTrendingInsertGameInfo(GameSuggestionViewModel.TRENDING_LIMIT_TIME)
            ?: return@withContext dataResultList
        dataResultList.firstOrNull { cardInfo ->
            ChoiceCardType.isTrending(
                cardInfo.cardName,
                cardInfo.cardType
            )
        }?.gameList?.apply {
            val oldItem = find { it.code == insertGame.code }
            if (oldItem == null) {
                add(0, insertGame)
            } else {
                remove(oldItem)
                // 这里决定一下是用自己的数据还是列表的数据
                add(0, insertGame)
            }
        }
        return@withContext dataResultList
    }

    /**
     * 小屋房间信息
     */
    private suspend fun addCottageRoomList(dataResultList: MutableList<ChoiceCardInfo>): MutableList<ChoiceCardInfo> {
        if (!PandoraToggle.openUgcHomeEntrance) return dataResultList
        val indexRoomCard =
            dataResultList.indexOfFirst { cardInfo -> ChoiceCardType.isHomeRoomCardType(cardInfo.cardType) }
        if (indexRoomCard >= 0) {
            val roomList = DataSource.getDataResultForApi {
                metaApi.getCottageRoomList(1)
            }.data?.houses
            if (roomList.isNullOrEmpty()) {
                dataResultList.removeAt(indexRoomCard)
            } else {
                if (metaKV.tTaiKV.dsItem.isNullOrEmpty()) {
                    DataSource.getDataResultForApi {
                        metaApi.getTTaiConfig(TTaiKV.ID_DS_ROOM_ITEM)
                    }.let {
                        it.data?.let { it1 -> metaKV.tTaiKV.saveConfig(it1) }
                    }
                }
                val dsItemImage = metaKV.tTaiKV.dsItem
                roomList.map { it.image = if (dsItemImage.isNullOrEmpty()) it.image else dsItemImage }
                dataResultList[indexRoomCard].homeRoomList = roomList
            }
        }
        return dataResultList
    }


    fun getRecentGameListByUuid(uid: String, pageSize: Int = 20): Flow<PagingData<GameEntity>> {
        return Pager(
            config = PagingConfig(
                pageSize = pageSize,
                enablePlaceholders = true,
                initialLoadSize = pageSize,
                prefetchDistance = 5
            ),
            initialKey = 1
        ) {
            RecentPlayPagingSource(uid, metaApi, pageSize)
        }.flow
    }

    fun getRecentGameListByUuidV2(
        uid: String,
        page: Int,
        pageSize: Int
    ): Flow<DataResult<RecentPlayList>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.getRecentPlayGameList(page, pageSize, uid) })
    }

    fun getRecentGameListByUuidV3(
        uid: String,
        pageSize: Int = 20
    ): Flow<PagingData<RecentPlayListV2Response.Game>> {
        return Pager(
            config = PagingConfig(
                pageSize = pageSize,
                enablePlaceholders = true,
                initialLoadSize = pageSize,
                prefetchDistance = 5
            ),
            initialKey = 1
        ) {
            RecentPlayPagingSourceV2(uid, metaApi, pageSize)
        }.flow
    }

    fun getRecentGameListByUuidV4(
        uid: String,
        page: Int,
        pageSize: Int
    ): Flow<DataResult<RecentPlayListV2Response>> = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.getRecentPlayGameListV2(
                RecentPlayListV2Request(page, pageSize, uid, false)
            )
        })
    }

    suspend fun getUgSuggestGameId(): Flow<DataResult<UGGameSuggestionInfo>> = flow {
        val userAgent: String = deviceInteractor.userAgent
        val androidId: String = deviceInteractor.androidId
        emit(DataSource.getDataResultForApi { metaApi.getSuperGameCode(mapOf("userAgent" to userAgent, "androidId" to androidId)) })
    }

    /**
     * 获取某个模板游戏
     */
    fun getGameTemplate(crGameId: String, type: Long): Flow<DataResult<EditorTemplate>> = flow {
        val result = DataSource.getDataResultForApi { metaApi.getGameTemplate(crGameId) }
        if (!result.succeeded) {
            val code = if (result.code == null || result.code == 0) 6100 else result.code
            val message = "code: ${code}\n${result.message}"
            emit(DataResult.Error(code ?: 6100, message, result.exception, result.duration))
            Analytics.track(
                EventConstants.EVENT_UGC_TEMPLATE_FAIL,
                "error" to message,
                "gameid" to crGameId,
                "type" to type
            )
        } else {
            emit(result)
        }
    }

    fun getGameTemplateV2(crGameId: String, type: Long) = suspendApiNotNull {
        val rawResult = metaApi.getGameTemplate(crGameId)
        if (rawResult.isSuccessful && rawResult.data != null) {
            rawResult
        } else {
            val code = if (rawResult.code == 0) 6100 else rawResult.code
            val message = "code: ${code}\n${rawResult.message}"
            Analytics.track(
                EventConstants.EVENT_UGC_TEMPLATE_FAIL,
                "error" to message,
                "gameid" to crGameId,
                "type" to type
            )
            ApiResult(code, message, rawResult.data)
        }
    }

    /**
     * 上报拉起游戏事件
     */
    suspend fun reportLaunchUgcGame(ugid: String): DataResult<String> =
        DataSource.getDataResultForApi { metaApi.reportLaunchUgcGame(mapOf("ugid" to ugid)) }

    fun getUgcDetailPage(ugid: String) = suspendApiNotNull {
        metaApi.getUgeDetailPage(mapOf("ugid" to ugid))
    }

    /**
     * 插入ugc数据库
     * @param entity 插入数据
     * @param replaceIfExist 数据库已存在时，是否完全替换，false：只替换访问时间
     */
    fun insertUgcPlayedGame(entity: RecentUgcGameEntity, replaceIfExist: Boolean) {
        GlobalScope.launch(Dispatchers.IO) {
            runCatching {
                db.withTransaction {
                    myPlayedGameDao.run {
                        if (!exists(entity.id)) {
                            insertInfo(entity.toPlayedEntity())
                        } else {
                            updateInfo(entity.toPlayedEntity())
                        }
                    }

                    if (recentUgcDao.exists(entity.id) && !replaceIfExist) {
                        recentUgcDao.getInfo(entity.id)?.apply {
                            visitTime = entity.visitTime
                            recentUgcDao.updateInfo(this)
                        }
                    } else {
                        recentUgcDao.insertInfo(entity)
                    }
                }
            }
        }
    }

    /**
     * 插入ugc数据库，会调用接口
     * @param ugid 插入ugc游戏的id
     * @param replaceIfExist 数据库已存在时，是否完全替换，false：只替换访问时间
     */
    fun insertUgcPlayedGame(ugid: String, replaceIfExist: Boolean) {
        GlobalScope.launch(Dispatchers.IO) {
            runCatching {
                db.withTransaction {
                    val existInfo = recentUgcDao.getInfo(ugid)
                    if (existInfo == null || replaceIfExist || !existInfo.verifyIntegrity()) {
                        recentUgcDao.insertInfo(
                            getUgcDetailPage(ugid).invoke().toMetaRecentUgcGameEntity()
                        )
                    }
                }
            }
        }
    }

    fun getRecentUgcCardList(pageSize: Int): Flow<PostCardResult> = flow {
        emit(recentUgcDao.getRecentVisitUgcGames(0, pageSize) ?: emptyList())
    }.map {
        PostCardResult(
            end = true,
            it.map { metaMapper.mapPostCard(it) }
        )
    }

    /**
     * 保存首页trending强插游戏
     */
    fun saveTrendingInsertGameInfo(gameInfo: ChoiceGameInfo) {
        metaKV.gameDetail.saveTrendingInsertGame(gameInfo)
    }

    /**
     * 比对清除首页trending强插游戏
     */
    fun removeTrendingInsertGameInfo(gameId: String) {
        val game = metaKV.gameDetail.getTrendingInsertGame()
        if (game?.code == gameId) {
            clearTrendingInsertGameInfo()
        }
    }

    /**
     * 获取首页trending强插游戏
     * @param durationLimit 有效时间
     */
    private fun getTrendingInsertGameInfo(durationLimit: Long): ChoiceGameInfo? {
        val time = metaKV.gameDetail.getTrendingInsertTime()
        return if (time + durationLimit <= System.currentTimeMillis()) {
            clearTrendingInsertGameInfo()
            null
        } else {
            metaKV.gameDetail.getTrendingInsertGame()
        }
    }

    /**
     * 清除首页trending强插游戏
     */
    private fun clearTrendingInsertGameInfo() {
        metaKV.gameDetail.clearTrendingInsertGame()
    }


}