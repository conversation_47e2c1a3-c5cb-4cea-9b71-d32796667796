package com.socialplay.gpark.data.model.editor

import com.google.gson.annotations.SerializedName
import com.meta.biz.ugc.model.EditorConfigJsonEntity
import com.socialplay.gpark.data.model.GameSuggestionInfo
import com.socialplay.gpark.data.model.creator.label.UgcPublishLabel
import com.socialplay.gpark.data.model.entity.RecentUgcGameEntity
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.room.RoomStyle

/**
 * Created by bo.li
 * Date: 2022/3/7
 * Desc: UGC游戏信息
 */
data class UgcGameInfo(
    val games: MutableList<Games>?,
    val end: Boolean,
    // 已发布作品总数
    val releaseCount: Int
) {

    val validList get() = games?.filter { it.isSupported }
    val validPostCardList get() = validList?.map { it.toPostCardInfo() }

    data class Games(
        // ugcId
        val id: String,
        // 每个ugc游戏不同
        val packageName: String,
        @SerializedName(value = "ugcGameName", alternate = ["gameName"])
        val ugcGameName: String?,
        val banner: String?,
        val gameIcon: String?,
        val userName: String?,
        val userIcon: String?,
        val loveQuantity: Long = 0,
        var likeIt: Boolean = false,
        val gameCode: String,
        var gameSpTags: List<Int>? = null,
        // 发布时的标签
        var gameTagList: List<UgcPublishLabel>? = null,
        // 里面塞了主小屋资源
        var extra: String?,
        // 已发布v2接口才有的字段，用于请求下一页
        val orderId: String?,

        // 本地用，标记是主小屋
        var isMainHome: Boolean = false,
        // 本地用，选中
        var isChecked: Boolean = false,
        // 游戏发布时间
        val releaseTime: Long = 0,
        val pvCount: Long = 0,
        val gameType: String? = "1",
        val topOn: Boolean = false,
        val realGameCode: String? = null,
    ) : RecentUgcGameEntity.Convertor {

        companion object {
            private const val TAG_HOME = 1
        }

        val isPgc get() = gameType == "2"
        val isUgc get() = gameType == null || gameType == "1"
        val availableGameCode: String get() = realGameCode ?: gameCode
        val isSupported get() = isPgc || isUgc

        override fun toMetaRecentUgcGameEntity() = RecentUgcGameEntity(
            id = id,
            packageName = packageName,
            gameName = ugcGameName,
            gameIcon = banner,
            gameCode = availableGameCode,
            username = userName,
            userAvatar = userIcon,
            likeCount = loveQuantity,
            likeIt = likeIt,
            popularity = pvCount,
            updateTime = releaseTime ?: 0L
        )

        fun toRoomStyle() = RoomStyle(
            image = banner.orEmpty(),
            gameId = id,
            style = "Custom",
            pkg = packageName
        )

        fun toPostCardInfo() = PostCardInfo(
            if (isPgc) PostCardInfo.TYPE_PGC else PostCardInfo.TYPE_UGC,
            id,
            packageName,
            loveQuantity,
            pvCount,
            userName,
            if (gameIcon.isNullOrEmpty()) banner else gameIcon,
            null,
            ugcGameName,
            0.0f,
            orderId
        )
    }
}

/**
 * 提供给游戏的小屋类
 */
data class GameHomeInfo(
    // ugcId
    val id: String,
    // 每个ugc游戏不同
    val packageName: String?,
    val ugcGameName: String?,
    val banner: String?,
    val loveQuantity: Long = 0,
    var likeIt: Boolean = false,
    val gameCode: String?,
)

data class MultiTsGameResult(
    val localGameType: String?,
    val ugcInfo: UgcGameInfo.Games?,
    val pgcInfo: GameSuggestionInfo?
) {
    companion object {
        const val TYPE_UGC = "ugc"
        const val TYPE_PGC = "pgc"
    }

    fun isUgcGame() = localGameType == TYPE_UGC
    fun isPgcGame() = localGameType == TYPE_PGC
}

class FormworkList(val list: List<Formwork>? = null) {
    class Formwork(
        val formworkCode: String? = null,
        val title: String? = null,
        val gameCode: String? = null,
        var gameList: List<FormworkGame>? = null,
        val titleIcon: String? = null, // 标题图标
        val makeSameConfig: String? = null, // 做同款字体颜色 √
        val formworkImg: String? = null, // 模板封面图 √
        val startBuildConfig: String? = null, // 开始建造按钮背景颜色
        val backColorConfig: String? = null, // 卡片背景颜色 √
        var end: Boolean = false, // 是否有下一页
        val realGameCode: String? = null,
        var ugids: List<String>? = null // ugc列表
    ) {
        var loading = false // 加载中
        var fail = false // 加载失败
        var curPage = 1 // 当前页码
        var pos = 0 // 首个元素位置
        var offset = 0 // 首个元素偏移
        var needTrackCraftSame = false // 恢复滚动位置后需要触发做同款的曝光
        var needRestorePos = false // 是否需要恢复滚动位置
        var newList: List<FormworkGame>? = null // 待添加的新数据
        var tag = "" // 独有标签
        val availableGameCode: String? get() = realGameCode ?: gameCode
    }

    class FormworkGame(
        val id: Long = 0,
        val packageName: String? = null,
        val ugcGameName: String? = null,
        val gameCode: String? = null,
        val banner: String? = null,
        val userName: String? = null,
        val userIcon: String? = null,
        val loveQuantity: Long = 0,
        var likeIt: Boolean = false,
        val gameIcon: String? = null,
        val releaseTime: Long = 0,
        val pvCount: Long = 0,
        val realGameCode: String? = null
    ) {
        val availableGameCode: String? get() = realGameCode ?: gameCode
    }
}

data class ProfileMapTab(val titleRes: Int, val type: Int)

data class MyCreationsV3Request(
    val orderId: String?,
    val userUuid: String?,
    val gameType: Int?,
    val orderType: Int,
    val mixedQuery: Boolean,
    val pgcSinceId: String?,
    val ugcSinceId: String?,
) {

    companion object {
        const val ORDER_TYPE_TIME = 1
        const val ORDER_TYPE_PV = 2
    }
}

data class MyCreationsV4Request(
    val orderId: String?,
    val userUuid: String?,
    val gameType: Int?
) {
}

data class PinUgcRequest(
    val ugid: String,
    val topOn: Boolean
)

data class PinPgcRequest(
    val contentGameId: String,
    val topOn: Boolean
)

data class UGCV3Data(
    val formwork: FormworkList.Formwork? = null,
    val ugids: List<String>? = null,
    val games: List<FormworkList.FormworkGame>
)

data class ReqFormWorkV4Body(val page: Int, val tagId: Int?)

data class UgcFormWorkV4Data(
    val end: Boolean = false,
    val formworkGameList: MutableList<FormWorkV4Info>?,
    val tagList: List<TagInfo>?
){
}

data class TagInfo(val tagId: Int?, val name: String?)
data class FormWorkV4Info(
    val title: String?,
    val gameCode: String?,
    val tagConfig: String?,
    val formworkImg: String?, // 模板封面图
    val archiveId: String?, //存档id
    val level: String?, // 星级
    val tagModel: TagInfo?, // 做同款字体颜色
    val visibleType: List<Int>?,
) {
    val supported get() = visibleType.isNullOrEmpty() || visibleType.contains(1)
}

data class ReqFormWorkArchiveBody(val gameCode: String, val archiveId :String)

data class UgcFormWorkArchiveData(
    val checkRes: Boolean?,
    val archiveUrl: String?,
    val sha1: String?,
    val gameCode: String?,
    val archiveId: String?,
) {

    // 模板类型
    var type: Int = EditorConfigJsonEntity.TYPE_NORMAL
}
