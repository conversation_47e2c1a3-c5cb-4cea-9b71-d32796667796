package com.socialplay.gpark.data.model.feedback

/**
 * Created by bo.li
 * Date: 2022/7/7
 * Desc: 游戏反馈接口入参
 * [埋点] https://meta.feishu.cn/wiki/SYitwytbkiQT6uk8A4Oc8ngAnBb
 */
data class SubmitNewFeedbackRequest(
    val gameId: String?, // 反馈的游戏id，可空
    val feedbackType: List<String>?, // 反馈类型
    val content: String?, // 反馈描述，gameId为空时不可空，gameId有值时可空
    val feedbackImg: List<String>?, // 反馈图片，可空
    val feedbackSourceEnum: String, // 反馈来源
) {
    companion object {
        // 后端type id
        const val NET_SOURCE_APP = "SETTING"
        const val NET_SOURCE_GAME = "DETAILS"
        const val NET_SOURCE_AVATAR = "AVATAR"
        const val NET_SOURCE_SUGGESTION = "SUGGESTION"
        const val NET_SOURCE_BUY = "BUY"
        const val NET_EVALUATE = "EVALUATE" // 引导商店评分弹框

        // 埋点用
        const val SOURCE_POSITIVE_COMMENT_NUMBER = "0"
        const val SOURCE_GAME_NUMBER = "1"
        const val SOURCE_APP_NUMBER = "2"
        const val SOURCE_HOME_NUMBER = "3"
        const val SOURCE_AVATAR_NUMBER = "4"
        const val SOURCE_EVALUATE = "10"
        // 5、6、7、8：web端
    }
}