package com.socialplay.gpark.data.model.pay

import com.socialplay.gpark.data.model.BodyRequestOrder
import com.socialplay.gpark.data.model.GooglePayResultData

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/03/18
 *     desc   :
 *
 */
class PayParams {
    /**
     * 当前内购场景
     */
    var currentIapScene: String? = null

    /**
     * 来源
     */
    var currentSource: String? = null

    //当前支付游戏id
    var currentGameId: String? = null

    //当前下单的订单号
    var currentOrderId: String? = null
    //当前支付渠道
    var payChannel: Int = -1
    //当前支付场景
    var sceneCode : Int = -1
    //当前商品类型 inapp  subs
    var productType: String = "inapp"


    //下单请求参数
    var currentRequestBody: BodyRequestOrder? = null

    //下单返回结果
    var takeOderResult: TakeOderResult? = null

    //google支付成功返回参数
    var googleResultData : GooglePayResultData? = null

    var uuid: String = ""
    var tripartiteInfo: TripartiteInfo? = null
    //订阅商品的计划id
    var planId: String? = null

    //google商品id
    var productId: String = ""
    var startTime: Long? = 0

}