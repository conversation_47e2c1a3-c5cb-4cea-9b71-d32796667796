package com.socialplay.gpark.data.model.profile

data class RelationListResult(
    val nextRollId: Long,
    var infos: List<RelationUserInfo>? = null,
) {
    data class RelationUserInfo(
        var uuid: String,
        var avatar: String?,
        var nickname: String?,
        val userNumber: String?,
        val type: Int? = null
    ){
        companion object {
            const val TYPE_FANS = 0
            const val TYPE_FOLLOW = 1
            const val TYPE_AI_BOT = 2
        }
    }
}