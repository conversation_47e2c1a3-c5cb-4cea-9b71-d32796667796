package com.socialplay.gpark.data.interactor

import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.ApiResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.base.successOr
import com.socialplay.gpark.data.base.suspendApiNotNull
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.TTaiConfig
import com.socialplay.gpark.data.model.share.ShareConfig
import com.socialplay.gpark.function.locale.MetaLanguageListener
import com.socialplay.gpark.function.locale.MetaLanguages
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.ifNullOrBlank
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.last
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.Locale

class TTaiInteractor(
    val metaRepository: IMetaRepository,
    val metaKV: MetaKV,
) : MetaLanguageListener {

    private val _configs = MutableStateFlow<Map<Int, TTaiConfig>>(emptyMap())
    val configs: StateFlow<Map<Int, TTaiConfig>> get() = _configs

    private val scope = MainScope()

    /**
     * http://mock.metaapp.cn/project/206/interface/api/6164
     * @param matchingFromCache true表示可以从内存中获取，false表示不可以从内存中获取要实时请求网络
     */
    suspend fun getTTaiConfigV2(resourceId: Int, matchingFromCache: Boolean = false): TTaiConfig? {
        if (matchingFromCache) {
            val config = _configs.value[resourceId]
            if (config != null) {
                return config
            }
        }

        val dataResult = metaRepository.getTTaiConfigById(resourceId).last()
        if (dataResult.succeeded) {
            return dataResult.data
        }
        return null
    }

    /**
     * kv → cache → 接口
     */
    fun getTTaiV3(resId: Int) = flow {
        emit(getTTaiValueHelper(resId))
    }

    inline fun <reified T : Any> getTTaiWithTypeV3(resId: Int) = flow {
        emit(GsonUtil.gsonSafeParseCollection<T>(getTTaiValueHelper(resId)))
    }

    suspend fun getTTaiValueHelper(resId: Int) = metaKV.tTaiKV.getConfig(resId).ifNullOrBlank {
        getTTaiConfigV2(resId, matchingFromCache = true)?.value.orEmpty()
    }

    fun getShareConfig(resId: Int, key: String) =
        getTTaiWithTypeV3<Map<String, ShareConfig>>(resId).map {
            it?.get(key) ?: ShareConfig.default()
        }

    fun getSceneShareConfig(key: String) = getShareConfig(TTaiKV.ID_KEY_ID_SCENE_SHARE_CONFIG, key)
    fun getGameShareConfig(key: String) = getShareConfig(TTaiKV.ID_KEY_ID_GAME_SHARE_CONFIG, key)

    suspend fun init() = withContext(Dispatchers.IO) {
        withContext(Dispatchers.Main) {
            MetaLanguages.addCallback(this@TTaiInteractor)
        }
        val ids = ArrayList<Int>()
        ids.add(TTaiKV.ID_QR_TRUST_DOMAIN_NAME)
//        ids.add(TTaiKV.ID_GAME_REVIEW_FAST_TAG)
//        ids.add(TTaiKV.ID_SYSTEM_NOTIFICATION)
        ids.add(TTaiKV.ID_SUGGEST_GAME_ID)
        ids.add(TTaiKV.ID_SUGGEST_MALE_FEMALE_GAME_ID)
//        ids.add(TTaiKV.ID_EDITOR_PLAZA_INFO)
        ids.add(TTaiKV.ID_REPORT_USER_REASON)
        ids.add(TTaiKV.ID_REPORT_REVIEW_REASON)
        ids.add(TTaiKV.ID_REPORT_GROUP_CHAT_REASON)
        ids.add(TTaiKV.ID_REPORT_LIBRARY_REASON)
//        ids.add(TTaiKV.ID_DEFAULT_ROLE_LIST)
        ids.add(TTaiKV.ID_DEFAULT_ROLE_LIST_V2)
        ids.add(TTaiKV.ID_MIGRATE_EDITOR_TEMPLATE_MAP)
//        ids.add(TTaiKV.ID_GAME_REVIEW_CONFIG)
        ids.add(TTaiKV.ID_HOME_ROOM_GAME)
        ids.add(TTaiKV.ID_ROOM_TAG)
        ids.add(TTaiKV.ID_AGE_LIMIT)
        ids.add(TTaiKV.ID_FEEDBACK_DISCORD)
        ids.add(TTaiKV.ID_EDITOR_ROLE_GAME_LOAD_TIPS)
        ids.add(TTaiKV.ID_EDITOR_ROLE_GAME_LOAD_ART_IMGS)
        ids.add(TTaiKV.ID_COMMUNITY_CIRCLE_ID)
        ids.add(TTaiKV.ID_RECORD_SHARE_PLATFORM_CONFIG)

//        ids.add(TTaiKV.ID_DS_ROOM_DETAIL)
//        ids.add(TTaiKV.ID_DS_ROOM_ITEM)
        ids.add(TTaiKV.ID_TS_GAME_PRELOAD_AD_CONFIG)
//        ids.add(TTaiKV.ID_AD_CONFIG)
//        ids.add(TTaiKV.ID_SELECT_MODE_GAME_INFO)
//        ids.add(TTaiKV.ID_KEY_AI_BOT_LABEL)
        ids.add(TTaiKV.ID_KEY_MW_GAME_PRE_DOWNLOAD)
        ids.add(TTaiKV.ID_LINK_PROFILE)
//        ids.add(TTaiKV.ID_KEY_AI_BOT_CREATE_COUNT)
//        ids.add(TTaiKV.ID_KEY_VIDEO_PUBLISH_CIRCLE_ID)
        ids.add(TTaiKV.ID_IM_TIPS_TIME)
        ids.add(TTaiKV.ID_SEARCH_FILTER)
        ids.add(TTaiKV.ID_AVATAR_SAVE_SHARE_BACKGROUND_LIST)
        ids.add(TTaiKV.ID_UGC_SINGLE_MODE)
        ids.add(TTaiKV.ID_ADD_TEMPLATE)
        ids.add(TTaiKV.ID_OAID_CERT_ID)
        ids.add(TTaiKV.ID_GUIDE_CREATE_THEME_LIST_CONFIG)
        ids.add(TTaiKV.ID_UGC_ASSET_FEED_NOTICE)
        ids.add(TTaiKV.ID_QUICK_INPUT_CONFIG)
        ids.add(TTaiKV.ID_REALNAME_APP_LOCK)
        ids.add(TTaiKV.ID_MODULE_TEMPLATE_TEMPLATE_ID)
        ids.add(TTaiKV.ID_MODULE_GUIDE_ID)
        ids.add(TTaiKV.ID_GUIDE_COMMENT_DIALOG_ID)

        metaRepository.getTTaiConfigByIds(ids).collect { result ->
            val data = result.successOr(null)

            if (!data.isNullOrEmpty()) {
                _configs.value = data.associateBy { it.id }
            }

            Timber.d("TTaiInit: getConfig from remote : succeed:${data != null}, data:$data")
        }
    }

    override fun onAppLocaleChange(oldLocale: Locale?, newLocale: Locale?) {
        scope.launch {
            init()
        }
    }

    override fun onSystemLocaleChange(oldLocale: Locale?, newLocale: Locale?) {

    }

    suspend fun getConfig(id: Int): TTaiConfig? {
        var config = _configs.value[id]
        if (config == null) {
            config = kotlin.runCatching {
                metaRepository.getTTaiConfigByIdV2(id).invoke().also {
                    _configs.value += (id to it)
                }
            }.getOrNull()
        }
        return config
    }

    suspend fun getConfigStringTopdown(id: Int): String? {
        val dataResult = metaRepository.getTTaiConfigById(id).last()
        if (dataResult.succeeded) {
            return dataResult.data?.value
        }

        val mem = _configs.value[id]
        if (mem != null) {
            return mem.value
        }

        val kv = metaKV.tTaiKV.getConfig(id)
        if (!kv.isNullOrBlank()) {
            return kv
        }

        return null
    }

    fun getConfigStringTopdownFlow(id: Int): Flow<String?> = flow {
        emit(getConfigStringTopdown(id))
    }

    fun getConfigStringTopdownV2(id: Int) = suspendApiNotNull {
        ApiResult(code = ApiResult.CODE_OK, data = getConfigStringTopdown(id))
    }

    inline fun <reified T : Any> getConfigTopdownWithType(resId: Int) = flow {
        emit(GsonUtil.gsonSafeParseCollection<T>(getConfigStringTopdown(resId)))
    }
}