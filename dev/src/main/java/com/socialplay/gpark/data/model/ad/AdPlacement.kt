package com.socialplay.gpark.data.model.ad

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2022/04/22
 *     desc   :
 */
private const val IN_GAME_MASK = 0x80000000

enum class AdPlacement(val value: String, private val bitValue: Long) {

    InGameDefault("in_game_default", IN_GAME_MASK or 1L),
    Default("default", 1L);

    fun isInGame(): Bo<PERSON>an {
        return bitValue and IN_GAME_MASK == IN_GAME_MASK
    }
}