package com.socialplay.gpark.data.interactor

import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.meta.biz.mgs.data.model.MgsBriefRoomInfo
import com.meta.biz.ugc.local.EditorLocalHelper
import com.meta.biz.ugc.local.LocalGameFilter
import com.meta.biz.ugc.model.EditorConfigJsonEntity
import com.meta.biz.ugc.model.EditorTemplate
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.cached
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.editor.EditorCreationCombineResult
import com.socialplay.gpark.data.model.editor.EditorCreationShowInfo
import com.socialplay.gpark.data.model.editor.EditorLocalStatusInfo
import com.socialplay.gpark.data.model.editor.EditorNotice
import com.socialplay.gpark.data.model.editor.EditorNotice.Notice.Companion.TYPE_METAVERSE
import com.socialplay.gpark.data.model.editor.UgcGameConfig
import com.socialplay.gpark.data.model.editor.UgcGameInfo
import com.socialplay.gpark.data.model.room.CottageVisitor
import com.socialplay.gpark.data.model.room.House
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.mgs.MgsGameRoomLauncher
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.util.ToastUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.single
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Created by bo.li
 * Date: 2022/7/21
 * Desc:
 */
class EditorInteractor(val metaRepository: com.socialplay.gpark.data.IMetaRepository, private val metaKV: MetaKV, private val accountInteractor: AccountInteractor) {

    val scope = MainScope()

    //是否正在启动小屋游戏
    private val isLaunchCottageGame = AtomicBoolean(false)
    private var launchCottageGameJob: Job? = null

    // 通知未读数
    private val _noticeNumLiveData: MutableLiveData<Int> by lazy { MutableLiveData(0) }
    val noticeNumLiveData: LiveData<Int> = _noticeNumLiveData

    // 帖子未读通知
    private val _postNoticeLiveData: MutableLiveData<Int> by lazy { MutableLiveData(0) }
    val postNoticeLiveData: LiveData<Int> = _postNoticeLiveData

    // 最新一条通知
    private val _newestNoticeLiveData: MutableLiveData<EditorNotice.OuterShowNotice> by lazy { MutableLiveData() }
    val newestNoticeLiveData: LiveData<EditorNotice.OuterShowNotice> = _newestNoticeLiveData

    // 自用非缓存两个游戏的id
    private val _selfGameConfigLiveData: MutableLiveData<DataResult<UgcGameConfig?>?> by lazy { MutableLiveData() }

    // 两个游戏的id
    private val _gameConfigLiveData: MutableLiveData<DataResult<UgcGameConfig?>?> by lazy { MutableLiveData() }
    val gameConfigLiveData: LiveData<DataResult<UgcGameConfig?>?> = _gameConfigLiveData

    // 是否等待横屏角色游戏activity OnResume
    private val _waitingHorRoleGameResumeLiveData: MutableLiveData<Boolean?> by lazy { MutableLiveData() }
    val waitingHorRoleGameResumeLiveData: MutableLiveData<Boolean?> = _waitingHorRoleGameResumeLiveData

    // 预置小屋模板信息
    private var templateHomeInfo: EditorTemplate? = null

    // 邻居好友
    var neighborFriendList: List<String>? = null

    private val testViewGameId by lazy { metaKV.tsKV.testViewGameId }
    private val testPlazaGameId by lazy { metaKV.tsKV.testPlazaGameId }

    // 开放广场游戏信息
    private var plazaGameInfo: GameDetailInfo? = null

    // 启动是否锚定移动编辑器
    val openWithEditor by lazy { PandoraToggle.openWithEditor }

    // 开发者模式：是否去掉角色加载遮罩
    val isDebugHideRoleMask = metaKV.tsKV.debugHideRoleMask && com.socialplay.gpark.BuildConfig.DEBUG

    // 是否有移动编辑器探索页-正式版底栏
    var hasEditorExploreFormalTab: Boolean = false

    // 用户当前主小屋信息
    val userMainHome = MutableStateFlow<DataResult<UgcGameInfo.Games?>?>(null)
    //小屋访客数
    private val _homeVisitorCountLiveData = MutableLiveData<List<CottageVisitor>>()
    val homeVisitorCountLiveData: LiveData<List<CottageVisitor>> = _homeVisitorCountLiveData

    val userMainHouse = MutableStateFlow<DataResult<House?>?>(null)
    private var lastUser: MetaUserInfo? = null

    private val accountChangeListener = Observer<MetaUserInfo?> {
        if (lastUser?.uuid != it?.uuid) {
            getUserHouse()
            lastUser = it
        }
    }
    fun init() {
        accountInteractor.accountLiveData.observeForever(accountChangeListener)
    }



    private fun isTestView(): Boolean {
        return testViewGameId.isNotEmpty()
    }

    private fun isTestPlazaView(): Boolean {
        return testPlazaGameId.isNotEmpty()
    }

    /**
     * 获取通知未读数
     */
    fun getUnreadNoticeCount() = GlobalScope.launch {
        metaRepository.getUnreadNoticeCount(TYPE_METAVERSE).collect {
            _noticeNumLiveData.postValue(if (!it.succeeded) 0 else (it.data) ?: 0)
        }
    }
    private fun getHomeVisitorCount(roomId: String) {
        scope.launch {
            metaRepository.getHomeVisitorCount(roomId).collect {
                _homeVisitorCountLiveData.postValue(it?: emptyList())
            }
        }

    }

    /**
     * 标记通知已读
     */
    fun markAllNoticeAsRead() = GlobalScope.launch {
        metaRepository.markAllNoticeAsRead(TYPE_METAVERSE)
    }

    /**
     * 获取移动编辑器最新一条通知
     */
    fun fetchNewestNotice() = GlobalScope.launch {
        metaRepository.getNewestEditorNotice().collect {
            _newestNoticeLiveData.postValue(it.data ?: EditorNotice.OuterShowNotice(type = EditorNotice.OuterShowNotice.TYPE_EDITOR))
        }
    }

    suspend fun fetchGameConfigSingle(): DataResult<UgcGameConfig?> {
        if (!accountInteractor.checkAccountInit()) {
            return DataResult.Error(-401, "")
        }
        return if (_selfGameConfigLiveData.value?.succeeded != true) {
            val result = metaRepository.getUgcGameConfig().single()
            if (isTestView()) {
                result.data?.roleViewGameId = testViewGameId
            }
            if (isTestPlazaView()) {
                result.data?.plazaGameId = testPlazaGameId
            }
            if (!result.cached) {
                _selfGameConfigLiveData.postValue(result)
            }
            updateGameConfig(result)
            result
        } else {
            updateGameConfig(_selfGameConfigLiveData.value)
            _selfGameConfigLiveData.value ?: DataResult.Error(-1, "")
        }
    }

    /**
     * 拉游戏配置
     */
    fun fetchGameConfig(): Job {
        return GlobalScope.launch {
            if (_selfGameConfigLiveData.value?.succeeded != true) {
                metaRepository.getUgcGameConfig().collect {
                    Analytics.track(EventConstants.EVENT_ROLE_GAME_ID_API_RESULT) {
                        if (!it.succeeded) {
                            put("api_info", it.message ?:"")
                        }
                        put("result", if (it.succeeded) "success" else "failed")
                        put("gameid", (it.data?.roleViewGameId ?: ""))
                    }
                    if (isTestView()) {
                        it.data?.roleViewGameId = testViewGameId
                    }
                    if (isTestPlazaView()) {
                        it.data?.plazaGameId = testPlazaGameId
                    }
                    if (!it.cached) {
                        _selfGameConfigLiveData.postValue(it)
                    }
                    updateGameConfig(it)
                }
            } else {
                updateGameConfig(_selfGameConfigLiveData.value)
            }
        }
    }

    suspend fun fetchGameInfo(gameId: String?): GameDetailInfo? {
        gameId ?: return null
        val gameInfoCache = metaRepository.getGameInfoCache(gameId)
        return if (gameInfoCache.succeeded && gameInfoCache.data != null) {
            gameInfoCache.data
        } else {
            metaRepository.getGameInfoByGameIdWithoutFlow(gameId.toString()).data
        }
    }

    suspend fun getPlazaInfo(): GameDetailInfo? {
        if (plazaGameInfo != null) {
            return plazaGameInfo
        }
        return fetchGameInfo(_gameConfigLiveData.value?.data?.plazaGameId).also { plazaGameInfo = it }
    }

    private fun updateGameConfig(newData: DataResult<UgcGameConfig?>?) {
        if (_gameConfigLiveData.value?.data != newData?.data || (_gameConfigLiveData.value?.data == null && newData != null)) {
            Analytics.track(EventConstants.EVENT_ROLE_GAME_ID_SYNC)
            _gameConfigLiveData.postValue(newData)
        }
    }

    fun startWaitingHorRoleGameResume() {
        _waitingHorRoleGameResumeLiveData.value = true
    }

    fun stopWaitingHorRoleGameResume() {
        _waitingHorRoleGameResumeLiveData.value = null
    }

    fun horRoleGameResumed() {
        if (_waitingHorRoleGameResumeLiveData.value == true) {
            _waitingHorRoleGameResumeLiveData.value = false
        }
    }

    fun getPostUnread() = scope.launch {
        metaRepository.getUnreadNoticeCount(EditorNotice.Notice.TYPE_POST).collect {
            _postNoticeLiveData.postValue(if (!it.succeeded) 0 else (it.data) ?: 0)
        }
    }

    fun setHomeVisitorCount(count: Int) {
        _homeVisitorCountLiveData.value = emptyList()
    }

    /**
     * 获取用户
     */
    private fun getUserHouse() {
        if(PandoraToggle.openUgcHomeEntrance){
            val uuid = accountInteractor.curUuid
            scope.launch {
                metaRepository.getUserHouse(uuid).collect {
                    userMainHouse.emit(it)
                    it.data?.roomId?.let { it1 -> getHomeVisitorCount(it1) }
                }
            }
        } else {
            _homeVisitorCountLiveData.postValue(emptyList())
        }
    }


    fun launchCottageGame(fragment: Fragment, uuid: String, source: String?, categoryId: Int, from: String) {
        if (!isLaunchCottageGame.compareAndSet(false, true)) return
        val context = fragment.requireContext()
        val launchListenerKey = "LaunchCottageGame"
        MgsGameRoomLauncher.registerLaunchEndListener(launchListenerKey) {
            if (it == launchListenerKey) {
                MgsGameRoomLauncher.removeLaunchEndListener(launchListenerKey)
                isLaunchCottageGame.set(false)
            }
        }
        launchCottageGameJob?.cancel()
        launchCottageGameJob = scope.launch {
            val house = metaRepository.getUserHouse(uuid).singleOrNull()?.data
            if (house == null) {
                withContext(Dispatchers.Main) {
                    ToastUtil.showLong(context, context.getString(R.string.launch_failed_click_to_retry))
                }
                return@launch
            }
            val info = metaRepository.fetchGameInfoByIdFromRemoteWithCache(house.gameId).map { it.data }.firstOrNull()

            if (source != null) {
                Analytics.track(
                    EventConstants.EVENT_DSHOME_ENTRY_CLICK,
                    mapOf(
                        "show_categoryid" to source,
                        "userid" to (house.ownerUuid?:""),
                        "homename" to( house.description?:""),
                        "onlinenumber" to (house.number?:""),
                        "roomid" to( house.roomId?:"")
                    )
                )
            }
            withContext(Dispatchers.Main) {
                if (info != null) {
                    val resIdBean = ResIdBean.newInstance().setCategoryID(categoryId)
                    MgsGameRoomLauncher.suspendEnterMgsGame(fragment, info.packageName, house.gameId,
                        MgsBriefRoomInfo(
                            roomIdFromCp = house.roomId,
                            roomName = null,
                            roomShowNum = null,
                            roomTags = null
                        ),
                        from,
                        0,
                        metaKV.account.uuid,
                        true,
                        resIdBean,
                        null
                    )
                } else {
                    ToastUtil.showLong(context, context.getString(R.string.launch_failed_click_to_retry))
                }
            }
        }
    }

    /**
     * 获取创作列表flow
     */
    fun getLocalCreationListFlow(
        gameSet: HashSet<String>,
        type: Int = EditorConfigJsonEntity.TYPE_NORMAL,
        filter: LocalGameFilter = { true },
        skipNet: Boolean = false,
        errorMsgBlock: ErrorMessageBlock? = null,
    ): Flow<DataResult<EditorCreationCombineResult?>> = flow {
        val allLocalGames = EditorLocalHelper.getAllLocalGamesWithoutPreload(type = type, filter = filter)
            .map { EditorCreationShowInfo(draftInfo = it, ugcInfo = null) }.toMutableList()
        allLocalGames.firstOrNull {
            val jsonConfig = it.draftInfo?.jsonConfig
            val checkTime = jsonConfig?.checkTime ?: 0
            val lastTime = jsonConfig?.lastPublishTimestamp ?: 0
            checkTime < lastTime && lastTime != 0L && jsonConfig?.id.isNullOrEmpty() && jsonConfig?.packageName != null && it.draftInfo?.path != null
        }?.apply {
            kotlin.runCatching {
                val id = metaRepository.getUgcIdByPackageName(draftInfo?.jsonConfig?.packageName!!)
                    .singleOrNull()?.data
                if (id != null) {
                    val result = EditorLocalHelper.rewriteId(File(draftInfo?.path!!), id)
                    Timber.d("checkcheck, result:${result}")
                    this.draftInfo?.jsonConfig?.id = id
                }
            }.getOrElse {
                Timber.e("checkcheck, getOrElse:${it}")
            }
        }
        val idList: ArrayList<String> = ArrayList()
        allLocalGames.forEach {
            it.draftInfo?.jsonConfig?.id?.let { it1 -> idList.add(it1) }
        }
        if (idList.isEmpty()) {
            emit(DataResult.Success(EditorCreationCombineResult(allLocalGames, false)))
            return@flow
        }
        val itemStatusList = if (skipNet) emptyList() else loadStatusList(idList, errorMsgBlock)
        val ugcInfoList = if (skipNet) emptyList() else
            loadUgcInfoList(itemStatusList.filter { !it.ugid.isNullOrEmpty() && it.auditStatus == 3 }
                .map { it.ugid!! }.toMutableList(), errorMsgBlock)
        val statusMap = HashMap<String, EditorLocalStatusInfo.Item>().apply {
            itemStatusList.forEach {
                put(it.bizId, it)
            }
        }
        val ugcInfoMap = HashMap<String, UgcGameInfo.Games>().apply {
            ugcInfoList.forEach {
                put(it.id, it)
            }
        }
        allLocalGames.forEach {
            val statusItem = statusMap[it.draftInfo?.jsonConfig?.id]
            it.draftInfo?.auditStatus = statusItem?.auditStatus
            it.draftInfo?.auditStatusDesc = statusItem?.auditStatusDesc
            it.draftInfo?.ugid = statusItem?.ugid
            ugcInfoMap[it.draftInfo?.ugid]?.let { ugcItem ->
                it.ugcInfo = ugcItem
                gameSet.add(ugcItem.id)
            }
        }
        emit(DataResult.Success(EditorCreationCombineResult(allLocalGames, false)))
    }

    private suspend fun loadUgcInfoList(
        ugidList: MutableList<String>,
        errorMsgBlock: ErrorMessageBlock?
    ): List<UgcGameInfo.Games> {
        val ugcInfoList = ArrayList<UgcGameInfo.Games>()
        val t = ugidList.size / 20

        for (index in 0..t) {
            val start = index * 20
            var end = (index + 1) * 20
            if (ugidList.size < end) {
                end = ugidList.size
            }
            val subList = ugidList.subList(start, end)
            if (subList.isNotEmpty()) {
                val result = metaRepository.getUgcInfoByIdLIst(subList)
                if (!result.succeeded) {
                    errorMsgBlock?.invoke(result.message)
                }
                result.data?.let {
                    ugcInfoList.addAll(it.games ?: arrayListOf())
                }
            }
        }
        return ugcInfoList

    }

    private suspend fun loadStatusList(
        idList: ArrayList<String>,
        errorMsgBlock: ErrorMessageBlock?
    ): List<EditorLocalStatusInfo.Item> {
        val itemStatusList = ArrayList<EditorLocalStatusInfo.Item>()
        val t = idList.size / 20

        for (index in 0..t) {
            val start = index * 20
            var end = (index + 1) * 20
            if (idList.size < end) {
                end = idList.size
            }
            val subList = idList.subList(start, end)
            if (subList.isNotEmpty()) {
                val result = metaRepository.getEditorLocalStatus(subList)
                if (!result.succeeded) {
                    errorMsgBlock?.invoke(result.message)
                }
                result.data?.list?.let {
                    itemStatusList.addAll(it)
                }
            }
        }
        return itemStatusList
    }
}

typealias ErrorMessageBlock = (String?) -> Unit