package com.socialplay.gpark.data.interactor

import android.util.ArrayMap
import androidx.core.net.toUri
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.meta.pandora.data.entity.Event
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.lastOrNull
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.TimeUnit
import kotlin.random.Random


class PingInteractor(private val repository: IMetaRepository) {

    private companion object {
        private const val TAG = "PingService"
    }

    private var apiReqTotalTime: Long = 0
    private var apiReqTotalCnt: Long = 0
    private var apiReqTimeOpLock = Any()


    private suspend fun ping(host: String, cnt: Int = 4): Float? {
        return withContext(Dispatchers.IO) {

            // rtt min/avg/max/mdev = 80.117/0.249/0.338/185 ms
            val statisticsRegex =
                """rtt\s+min/avg/max/mdev\s+=\s+(\d+\.?\d*)/(\d+\.?\d*)/(\d+\.\d*)/(\d+\.?\d*)\s+ms""".toRegex()

            val process = ProcessBuilder()
                .command(listOf("ping", "-c $cnt", host))
                .start()
                .also { it.waitFor() }

            return@withContext process.inputStream.reader()
                .readLines()
                .mapNotNull { statisticsRegex.matchEntire(it) }
                .onEach { Timber.tag(TAG).d(it.value) }
                .mapNotNull { it.groups[2/*avg*/]?.value?.toFloatOrNull() }
                .singleOrNull()
        }
    }

    private suspend fun echo(cnt: Int = 4): Float? {

        val values = (0 until cnt).mapNotNull {
            val result = repository.getTTaiConfigById(-1).singleOrNull()
            if (result == null || result.code == 0/*Http status code error*/) return@mapNotNull null
            return@mapNotNull result.duration
        }

        return if (values.isEmpty()) {
            null
        } else {
            values.average().toFloat()
        }
    }

    suspend fun init() {
        withContext(Dispatchers.Main) { bindApiReqTimeObserver() }

        //等15~30秒，错过应用启动网络请求高峰期
        val delayInMs = TimeUnit.SECONDS.toMillis(Random.nextLong(16) + 15)
        Timber.tag(TAG).d("Ping task delayed ${delayInMs}ms")
        delay(delayInMs)

        val pingAvgTimeList = coroutineScope {
            val domainList = getPingDomainList()
            domainList.map { async { it to (ping(it) ?: -1F) } }.awaitAll()
        }

        pingAvgTimeList.forEach {
            Analytics.track(EventConstants.EVENT_PING_TIME) {
                put("host", it.first)
                put("duration", it.second)
            }
            Timber.tag(TAG).d("Average ping time host: ${it.first} ${it.second}ms")
        }

        val echoAvgTime = echo() ?: -1F
        Analytics.track(EventConstants.EVENT_ECHO_TIME) {
            put("duration", echoAvgTime)
            put("host", BuildConfig.BASE_URL.toUri().host ?: "")
        }

        Timber.tag(TAG).d("Echo time ${echoAvgTime}ms")
    }

    private suspend fun getPingDomainList(): List<String> {
        var ttaiConfiguredDomainList = emptyList<String>()

        val dataResult = repository.getTTaiConfigById(TTaiKV.ID_PING_DOMAIN_LIST).lastOrNull()

        Timber.tag(TAG).d("TTai configured domain result:$dataResult")

        if (dataResult != null && dataResult.succeeded) {
            val data = dataResult.data
            ttaiConfiguredDomainList = data?.value?.split(",")?.toList() ?: emptyList()
        }
        if (EnvConfig.isParty()) {
            return ttaiConfiguredDomainList.ifEmpty {
                listOf(
                    "api.233party.com",
                    "api.meta-verse.co",
                    "res-content-flow-qn.meta-verse.co"
                )
            }
        }

        // 如果T台没有配置 则使用默认的数据
        return ttaiConfiguredDomainList.ifEmpty {
            listOf(
                "us-east-ping.gpark.io",// 腾讯云美东
                "us-west-ping.gpark.io",// 腾讯云美西
                "aws-us-west-ping.gpark.io"//aws美西
            )
        }
    }


    private fun bindApiReqTimeObserver() {
        ProcessLifecycleOwner.get().lifecycle.addObserver(object : LifecycleEventObserver {
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                if (event == Lifecycle.Event.ON_PAUSE) {
                    val avgTime = synchronized(apiReqTimeOpLock) {
                        val time = apiReqTotalTime
                        val cnt = apiReqTotalCnt

                        Timber.d("AverageApiRequestTime: ${if (cnt == 0L) 0 else time / cnt}ms (total time: $time, total count: $cnt)")

                        apiReqTotalTime = 0
                        apiReqTotalCnt = 0

                        if (cnt == 0L) 0L else time / cnt
                    }

                    if (avgTime > 0) {
                        val analyticsMap = ArrayMap<Long, Event>().apply {
                            put(80, EventConstants.C_SE_API_REQUEST_TIME_LE80)
                            put(100, EventConstants.C_SE_API_REQUEST_TIME_LE100)
                            put(120, EventConstants.C_SE_API_REQUEST_TIME_LE120)
                            put(140, EventConstants.C_SE_API_REQUEST_TIME_LE140)
                            put(160, EventConstants.C_SE_API_REQUEST_TIME_LE160)
                            put(180, EventConstants.C_SE_API_REQUEST_TIME_LE180)
                        }

                        analyticsMap.forEach { (key, value) ->
                            if (key >= avgTime) {
                                Analytics.track(value)
                            }
                        }
                    }
                }
            }
        })
    }

    fun addApiReqTime(duration: Long) {
        synchronized(apiReqTimeOpLock) {
            apiReqTotalTime += duration
            apiReqTotalCnt++
        }
    }
}