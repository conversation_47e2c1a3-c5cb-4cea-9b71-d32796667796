package com.socialplay.gpark.data.model.pay

data class CoinsRecordRequestBody(
    /**
     * 首页为null，其它为分页数据最后数据项ID
     */
    val offset: Long?,
    /**
     * 分页大小 最大给20(建议给10)
     */
    val pageSize: Int,
    /**
     * 1:乐币 2:ARK币  3:G-coin币 5:钥匙， 6-乐积分
     */
    val coinType: Int?,
    /**
     * 开始时间
     */
    val startTime: String? = null,
    /**
     * 结束时间
     */
    val endTime: String? = null,
    /**
     * ALL 不传
     * 充值 201
     * 道具兑换 203
     * 钥匙消耗403
     */
    val flowType: Int? = null,
){
    companion object{
        /**
         * 乐币
         */
        const val COIN_TYPE_LE_COIN = 1

        /**
         * ARK币
         */
        const val COIN_TYPE_ARK = 2

        /**
         * G-coin币
         */
        const val COIN_TYPE_G_COIN = 3

        /**
         * 派对币
         */
        const val COIN_TYPE_PARTY_COIN = 4

        /**
         * 钥匙
         */
        const val COIN_TYPE_KEY = 5

        /**
         * 乐积分
         */
        const val COIN_TYPE_LE_POINT = 6
    }
}

data class CoinsRecords(
    val records: List<CoinsRecord>?
)

data class CoinsRecord(
    /**
     * 类型
     * eg: 实名送会员
     */
    val typeName: String?,
    /**
     * 乐币数
     * eg: +100
     *     -50
     */
    val leCoinNum: String?,
    /**
     * 记录时间，yyyy-MM-dd
     */
    val recordTime: String?,
    /**
     * 主键，分页offset要用此值
     */
    val id: Long?,
    /**
     * 流水描述
     * eg: 樱花校园购买皮肤
     */
    val describe: String?,
    /**
     * 游戏扩展信息
     */
    val gameExtendInfo: GameExtendInfo?,
    /**
     * 记录时间时间戳
     */
    val recordTimestamp: Long?,
    /**
     * 基础乐币数
     */
    val baseCoinNum: Long?,
    /**
     * 赠送乐币数
     */
    val awardCoinNum: Long?,
)

data class GameExtendInfo(
    /**
     * 游戏ID
     */
    val gameId: String?,
    /**
     * 游戏名
     */
    val gameName: String?,
    /**
     * 游戏包名
     */
    val packageName: String?,
    /**
     * 游戏icon
     */
    val gameIcon: String?,
)