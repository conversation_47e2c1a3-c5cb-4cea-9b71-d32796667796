package com.socialplay.gpark.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2022/07/29
 *     desc   :
 */
@Parcelize
data class GameSuggestionInfo(
    val code: String, // 游戏ID 或者 模板ID(UGC游戏)
    val gameId: String?,//UGC 游戏ID,只有这个游戏是UGC时才有值
    val icon: String?,
    val name: String?,
    val packageName: String,
    val cover: String?,
    val gameAuthor: AuthorInfo?,
    val playingCount: Long?, // 游玩人数
    val score: Double?,//游戏评分
    val gameType: Int, // 游戏来源 UGC(1),PGC(2)
    // T台配置的游戏描述
    var desc: String? = null
) : Parcelable{
    companion object{
        const val GAME_TYPE_UGC = 1
        const val GAME_TYPE_PGC = 2
    }

    fun getMatchGameId(): String? {
        if (gameType == GAME_TYPE_PGC) {
            return code
        }
        if (gameType == GAME_TYPE_UGC) {
            return gameId
        }
        return gameId
    }

}


@Parcelize
data class UGGameSuggestionInfo(
    val gameCode: String?
) : Parcelable

@Parcelize
data class SuggestionSelectResult(
    val gameInfo: GameSuggestionInfo,
    val position: Int
) : Parcelable


@Parcelize
data class SuggestionTTaiInfo(
    val toggleState: Boolean,
    val male: MutableList<GameSuggestTTaiDetailInfo>,
    val female: MutableList<GameSuggestTTaiDetailInfo>
) : Parcelable

@Parcelize
data class GameSuggestTTaiDetailInfo(
    val gameid: String,
    val desc: String?
) : Parcelable


