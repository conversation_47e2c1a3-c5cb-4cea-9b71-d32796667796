package com.socialplay.gpark.data.kv

import com.tencent.mmkv.MMKV
import org.koin.core.context.GlobalContext
import timber.log.Timber

class DailyTaskKV(private val mmkv: MMKV, private val timeKV: TimeKV) {
    companion object {
        private const val KEY_DAILY_TASK_SHARE_OC_VIDEO = "daily_task_share_oc_video"
        private const val KEY_DAILY_TASK_SHARE_OC_VIDEO_SUCCESS =
            "daily_task_share_oc_video_success"
        private const val KEY_DAILY_SIGN_TASK_IS_SHOW_DIALOG = "key_daily_sign_task_is_show_dialog"

        private const val KEY_DAILY_SIGN_TASK_IS_FINISHED = "key_daily_sign_task_is_finished"

    }

    private val accountKV: AccountKV = GlobalContext.get().get<MetaKV>().account

    fun getShareOCVideoInfo(): Pair<String, String>? {
        return mmkv.getString(KEY_DAILY_TASK_SHARE_OC_VIDEO, "").runCatching {
            if (isNullOrEmpty()) {
                Timber.d("dailyTask: getShareOCVideoInfo: isNullOrEmpty")
                return@runCatching null
            }
            val split = split(",")
            if (split.size == 4) {
                val uuid = split[0]
                if (uuid != accountKV.uuid) {
                    Timber.d("dailyTask: getShareOCVideoInfo: uuid error")
                    return@runCatching null
                }
                val time = split[1].toLong()
                val now = System.currentTimeMillis()
                // 是否是同一天
                if (time / 1000 / 60 / 60 / 24 != now / 1000 / 60 / 60 / 24) {
                    Timber.d("dailyTask: getShareOCVideoInfo: time error, $time, $now")
                    return@runCatching null
                }

                val actId = split[2]
                if (actId.isEmpty()) {
                    Timber.d("dailyTask: getShareOCVideoInfo: actId error")
                    return@runCatching null
                }
                val logicId = split[3]
                if (logicId.isEmpty()) {
                    Timber.d("dailyTask: getShareOCVideoInfo: logicId error")
                    return@runCatching null
                }
                Pair(actId, logicId)
            } else {
                Timber.d("dailyTask: getShareOCVideoInfo: size error $this")
                null
            }
        }.getOrElse {
            Timber.d("dailyTask: getShareOCVideoInfo: $it")
            null
        }
    }

    fun setShareOCVideoInfo(actId: String, logicId: String) {
        val uuid = accountKV.uuid
        val time = System.currentTimeMillis()
        mmkv.encode(KEY_DAILY_TASK_SHARE_OC_VIDEO, "$uuid,$time,$actId,$logicId")
    }

    fun resetDailyTasKShareOcVideo() {
        mmkv.remove(KEY_DAILY_TASK_SHARE_OC_VIDEO)
        mmkv.encode(KEY_DAILY_TASK_SHARE_OC_VIDEO_SUCCESS, true)
    }

    fun getFinishShareOcVideoSuccess(): Boolean {
        return mmkv.getBoolean(KEY_DAILY_TASK_SHARE_OC_VIDEO_SUCCESS, false)
    }

    fun resetFinishShareOcVideoSuccess() {
        mmkv.remove(KEY_DAILY_TASK_SHARE_OC_VIDEO_SUCCESS)
    }

    private fun getUserUuidSignTaskKey(key: String): String {
        val uuid = accountKV.uuid
        return "${key}_${uuid}"
    }

    fun getDailySignIsShown(): Boolean {
        if (timeKV.dayOnce(getUserUuidSignTaskKey(KEY_DAILY_SIGN_TASK_IS_SHOW_DIALOG))) {
            setDailySignShown(false)
        }
        return mmkv.getBoolean(getUserUuidSignTaskKey(KEY_DAILY_SIGN_TASK_IS_SHOW_DIALOG), false)
    }

    fun setDailySignShown(shown: Boolean) {
        mmkv.putBoolean(getUserUuidSignTaskKey(KEY_DAILY_SIGN_TASK_IS_SHOW_DIALOG), shown)
    }

    fun getDailySignIsFinished(): Boolean {
        if (timeKV.dayOnce(getUserUuidSignTaskKey(KEY_DAILY_SIGN_TASK_IS_FINISHED))) {
            setDailySignFinished(false)
        }
        return mmkv.getBoolean(getUserUuidSignTaskKey(KEY_DAILY_SIGN_TASK_IS_FINISHED), false)
    }

    fun setDailySignFinished(shown: Boolean) {
        mmkv.putBoolean(getUserUuidSignTaskKey(KEY_DAILY_SIGN_TASK_IS_FINISHED), shown)
    }

}