package com.socialplay.gpark.data.model

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/12
 * desc   :
 * </pre>
 */


data class MyPlayedGame(
    val id: String, // 和详情页id一致
    val updateTimestamp: Long,
    val name: String,
    val icon: String,
    val type: Int, // 游戏类型；1：普通安装包游戏；     3：普通存档类游戏；
    val startupExtension: String?, // 游戏启动扩展参数
    val downloadPercent: Float, // 下载安装进度
    val packageName: String?,
    val downloadTimestamp: Long, // 第一次点击开始按钮时间
    val lastPlayedTime: Long, // 上次玩游戏时间
    val gameTags: List<Int>?, //游戏能力 1:MGS游戏
    val isPlaceHolder: Boolean = false,
    val isUgcGame:Boolean = false
) {

    /**
     * 是否MGS游戏
     */
    fun isMgsGame(): Boolean {
        return gameTags?.contains(GameDetailInfo.GAME_TYPE_MGS) ?: false
    }

    fun isTsGame(): Boolean {
        //直接使用 GameDetailInfo 类中的类型,避免后面直接修改一份就可以全部生效
        return type and GameDetailInfo.GAME_TYPE_TS == GameDetailInfo.GAME_TYPE_TS
    }

    /**
     * 游戏类型转换成String类型（用于发送埋点）
     */
    fun typeToString(): String {
        return when {
            isTsGame() -> "ts"
            else -> "apk"
        }
    }
}