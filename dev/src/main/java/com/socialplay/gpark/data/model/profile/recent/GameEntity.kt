package com.socialplay.gpark.data.model.profile.recent

import com.socialplay.gpark.data.model.GameDetailInfo


/**
 * created by liyanfeng on 2022/7/27 3:31 下午
 * @describe:
 */
data class GameEntity(
    val gameId: String = "",               //游戏id
    var name: String? = null,
    var packageName: String? = null,
    var appDownCount: Long = 0,          //游戏下载量
    var briefIntro: String? = null,     //游戏简要介绍
    var deleteTime: Long = 0, //删除游戏时间（毫秒）
    var duration: Long = 0,           // 玩过持续时间（秒）
    var fileSize: Long = 0,              //游戏文件大小
    var iconUrl: String? = null,
    var lastPlayTime: Long = 0,      //最后一次游玩时间（毫秒）
    var likeCount: Int = 0, //点赞数
    var serverTime: Long = 0, // 当前系统时间（毫秒）
    val type: Int, //游戏类型 按二进制位区分 0b1：普通安装包游戏； 0b10：普通存档类游戏； 0b100：MetaVerse游戏home_page
    val gameScore: Float = 0.0F,
    val developerNickname: String? = null, // 作者昵称
) {
    override fun equals(other: Any?): Boolean {
        if (other is GameEntity) {
            return this.gameId == other.gameId
        }
        return super.equals(other)
    }

    fun isTsGame(): Boolean {
        //直接使用 GameDetailInfo 类中的类型,避免后面直接修改一份就可以全部生效
        return type and GameDetailInfo.GAME_TYPE_TS == GameDetailInfo.GAME_TYPE_TS
    }

    /**
     * 游戏类型转换成String类型（用于发送埋点）
     */
    fun typeToString(): String {
        return "ts"
    }
}
