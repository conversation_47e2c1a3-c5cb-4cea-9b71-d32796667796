package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.data.model.LoginPageSource
import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.router.MetaRouter

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/01/31
 *     desc   :
 *
 */
class BindAccountAndPasswordHandler : LinkHandler {

    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        val dataString = data.uri.getQueryParameter("data") ?: return LinkHandleResult.Failed("no data")
        val arguments = BasicNavigateLinkHandler.decodeBundle(dataString)
        val fromGameId = arguments.getString(MetaDeepLink.PARAM_FROM_GAME_ID)
        val source = arguments.getString(MetaDeepLink.PARAM_SOURCE_FROM)
        MetaRouter.Account.bindAccountAndPassword(
            data.navHost,
            if (source.isNullOrEmpty()) LoginPageSource.SchemeUnknown.source else source,
            fromGameId
        )
        return LinkHandleResult.Success
    }
}