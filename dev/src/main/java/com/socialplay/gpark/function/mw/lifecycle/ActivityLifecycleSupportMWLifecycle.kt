package com.socialplay.gpark.function.mw.lifecycle

import android.app.Activity
import androidx.annotation.CallSuper
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import java.util.*


abstract class ActivityLifecycleSupportMWLifecycle : MWLifecycle() {

    private val lifecycles = WeakHashMap<Activity, ActivityLifecycleWrapper>()

    @CallSuper
    override fun onActivityCreated(activity: Activity) {
        super.onActivityCreated(activity)
        getLifecycleInternal(activity).handleLifecycleEvent(Lifecycle.Event.ON_CREATE)
    }

    @CallSuper
    override fun onActivityStarted(activity: Activity) {
        super.onActivityStarted(activity)
        getLifecycleInternal(activity).handleLifecycleEvent(Lifecycle.Event.ON_START)
    }

    @CallSuper
    override fun onActivityResumed(activity: Activity) {
        super.onActivityResumed(activity)
        getLifecycleInternal(activity).handleLifecycleEvent(Lifecycle.Event.ON_RESUME)
    }

    @CallSuper
    override fun onActivityPaused(activity: Activity) {
        super.onActivityPaused(activity)
        getLifecycleInternal(activity).handleLifecycleEvent(Lifecycle.Event.ON_PAUSE)
    }

    @CallSuper
    override fun onActivityStopped(activity: Activity) {
        super.onActivityStopped(activity)
        getLifecycleInternal(activity).handleLifecycleEvent(Lifecycle.Event.ON_STOP)
    }

    @CallSuper
    override fun onActivityDestroyed(activity: Activity) {
        super.onActivityDestroyed(activity)
        getLifecycleInternal(activity).handleLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    }


    private fun getLifecycleInternal(activity: Activity): ActivityLifecycleWrapper {
        var wrapper = lifecycles[activity]
        if (wrapper == null) {
            wrapper = ActivityLifecycleWrapper(activity)
            lifecycles[activity] = wrapper
        }
        return wrapper
    }

    fun getLifecycle(activity: Activity): Lifecycle {
        return getLifecycleInternal(activity).lifecycle
    }

    fun getLifecycleOwner(activity: Activity): LifecycleOwner {
        return getLifecycleInternal(activity)
    }

    inner class ActivityLifecycleWrapper(activity: Activity) : LifecycleOwner {

        private val lifecycleRegistry =
            LifecycleRegistry(this)

        override val lifecycle: Lifecycle
            get() = lifecycleRegistry

        fun handleLifecycleEvent(event: Lifecycle.Event) {
            lifecycleRegistry.handleLifecycleEvent(event)
        }
    }
}