package com.socialplay.gpark.function.startup

import com.socialplay.gpark.function.startup.core.ProcessType
import com.socialplay.gpark.function.startup.core.plus
import com.socialplay.gpark.function.startup.dsl.ProcessTypeFactory

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/06/11
 * desc   :
 * </pre>
 */

object StartupProcessType {
    val H = ProcessType("HOST")
    val O = ProcessType("Other")
    val M = ProcessType("MW")
    val R = ProcessType("MW-Render")
    val B = ProcessType("Bin")
    val AUTO = ProcessType("AutoTest")
    val AWO = H + M + R + AUTO + B
}


class StartupProcessTypeFactory(val packageName: String) : ProcessTypeFactory {
    override fun invoke(processName: String): ProcessType {
        return when {
            processName == packageName                       -> {
                StartupProcessType.H
            }
            processName == "$packageName:m" -> {
                StartupProcessType.M
            }
            processName == "$packageName:r"                  -> {
                StartupProcessType.R
            }
            processName == "$packageName:bin"                  -> {
                StartupProcessType.B
            }
            processName == "$packageName:auto_test" -> {
                StartupProcessType.AUTO
            }
            else                                             -> {
                StartupProcessType.O
            }
        }
    }
}