package com.socialplay.gpark.function.mw

import com.meta.biz.ugc.model.ReceiveMsg
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * xingxiu.hou
 * 2023/3/23
 */
class MWGameExitCaller {

    private val mainScope by lazy { MainScope() }

    private var onBefore: (suspend () -> Unit)? = null
    private var onAfter: (suspend () -> Unit)? = null
    private var floatExitJob: Job? = null

    fun setOnBefore(call: suspend () -> Unit) {
        this.onBefore = call
    }

    fun setOnAfter(call: suspend () -> Unit) {
        this.onAfter = call
    }

    private fun getMWExitResponse(): ReceiveMsg {
        //发送消息给MW,这里格式有出入，status变成了string，直接用原始方法调用
        return UGCProtocolSender.sendProtocol(ProtocolSendConstant.PROTOCOL_EXIT_GAME, 0, mapOf()) ?: ReceiveMsg()
    }

    //点击退出按钮,执行操作
    fun onFloatExitClick(): () -> Unit = {
        floatExitJob = mainScope.launch {
            onBefore?.invoke()
            val exitResponse = getMWExitResponse()
            val timeout = exitResponse.get("timeout", -1L) { it.toLong() }
            if (exitResponse.isSuccess() && timeout > 0) delay(timeout)
            onAfter?.invoke()
        }
    }

    //接收MW的消息，退出游戏
    fun onReceiverMWExit() {
        mainScope.launch {
            floatExitJob?.cancelAndJoin()
            onAfter?.invoke()
            onAfter = null
        }
    }

    //直接运行所有回调，完成退出游戏操作
    fun callExit(): () -> Unit = {
        mainScope.launch {
            onBefore?.invoke()
            onAfter?.invoke()
        }
    }

}