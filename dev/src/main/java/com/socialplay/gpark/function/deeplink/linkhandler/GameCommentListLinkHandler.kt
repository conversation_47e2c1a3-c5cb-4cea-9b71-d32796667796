package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.router.MetaRouter
import timber.log.Timber

/**
 * 处理跳转到游戏详情页
 */
class GameCommentListLinkHandler : LinkHandler {

    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        Timber.d("metadeeplink GameDetailLinkHandler handle uri:%s", data.uri)
        val gameId = data.uri.getQueryParameter(MetaDeepLink.PARAM_GAME_ID) ?: return LinkHandleResult.Failed("no game id")
        val categoryId = data.uri.getQueryParameter(MetaDeepLink.PARAM_CATEGORY_ID)?.toIntOrNull() ?: -1
        val gamePackageName = data.uri.getQueryParameter(MetaDeepLink.PARAM_GAME_PACKAGE_NAME).orEmpty()
        val targetCommentId = data.uri.getQueryParameter(MetaDeepLink.PARAM_TARGET_COMMENT_ID)
        val targetReplyId = data.uri.getQueryParameter(MetaDeepLink.PARAM_TARGET_REPLY_ID)

        MetaRouter.GameDetail.navigate(
            data.navHost,
            gameId,
            ResIdBean().setCategoryID(categoryId),
            gamePackageName,
            targetCommentId = targetCommentId,
            targetReplyId = targetReplyId
        )
        return LinkHandleResult.Success
    }
}