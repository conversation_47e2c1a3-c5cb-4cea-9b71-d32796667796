package com.socialplay.gpark.function.router

import android.content.Context
import androidx.fragment.app.Fragment
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.function.MetaRouterWrapper
import com.socialplay.gpark.util.ToastUtil

object LoginCompleteRouter {

    /**
     * 登录完成，跳转到主页之前，进行判断
     * 如果，是派对，用户登录完成，已经实名的，直接跳转到主页
     * 如果，是派对，用户登录完成，未实名的，跳转到实名界面
     */
    fun router(context: Context, fragment: Fragment, userInfo: MetaUserInfo? = null) {
        if ((userInfo?.bindIdCard == true && !userInfo.leyuanUserFirstLogin) || !EnvConfig.isParty()) {
            // 实名了，并且不是今天的新用户，就可以直接去主页
            // Gpark可以直接进主页
            ToastUtil.showShort(R.string.login_succeed)
            if (userInfo?.firstBind == true) {
                MetaRouter.Startup.createAvatar(fragment, true)
            } else {
                MetaRouter.Main.tabHome(context)
            }
        } else if (userInfo?.bindIdCard == true && userInfo.leyuanUserFirstLogin) {
            // 实名了，并且是今天首次绑定，去创建角色
            ToastUtil.showShort(R.string.login_succeed)
            MetaRouter.Startup.createAvatar(fragment, false, false)
        } else {
            // 未实名，去实名
            MetaRouterWrapper.RealName.openRealName(fragment)
        }
    }
}