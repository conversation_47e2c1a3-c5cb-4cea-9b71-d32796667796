package com.socialplay.gpark.function.mw.lifecycle

import android.app.Application
import android.os.SystemClock
import androidx.fragment.app.FragmentManager
import com.meta.pandora.utils.AppLifecycleCallback
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.RoleGameIdConfig
import com.socialplay.gpark.data.model.TTaiPositiveComment
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.positivecomment.PositiveCommentDialog
import com.socialplay.gpark.util.fromJSON
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

object AvatarGameTime {
    //    private const val AVATAR_GAME_ID = "h6ZjaE8nZrQWaJ9RDNJy"//测试
    private const val AVATAR_GAME_ID = RoleGameIdConfig.AVATAR_GAME_ID//线上
    //    private const val AVATAR_GAME_ID = "    zT93xRn2zf9ypVddqyIX"//预发

    private val AVATAR_GAME_IDS = RoleGameIdConfig.AVATAR_GAME_IDS

    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }
    private val application: Application by lazy { GlobalContext.get().get() }
    private val repo: IMetaRepository by lazy { GlobalContext.get().get() }
    private val scope by lazy { CoroutineScope(Dispatchers.IO) }
    val showPositiveCommentDialogFlow = MutableStateFlow(0L)
    private var isPositiveCommentDialogShown = false
    private var gameIdStr = ""
    private val applicationCallback = object : AppLifecycleCallback() {
        override fun onAppEnterBackground() {
            stop()
        }

        override fun onAppEnterForeground() {
            start()
        }

    }

    var startTime = 0L
    fun start(registerAppLifecycle: Boolean = false) {
        startTime = SystemClock.uptimeMillis()
        if (registerAppLifecycle) {
            application.registerActivityLifecycleCallbacks(applicationCallback)
        }
    }

    fun stop(unregisterAppLifecycle: Boolean = false) {
        if (startTime == 0L) return
        val additionTime = SystemClock.uptimeMillis() - startTime
        // 更新当天单个游戏时长（this game, today time）
        addCurDayGameTime(AVATAR_GAME_ID, additionTime)
        if (unregisterAppLifecycle) {
            application.unregisterActivityLifecycleCallbacks(applicationCallback)
        }
        check(AVATAR_GAME_ID)
        startTime = 0L
    }

    fun check(gameId: String) {
        Timber.i("check:$gameId--${PandoraToggle.positiveCommentConfig}")

        gameIdStr = gameId
        if (isPositiveCommentDialogShown) return
        if (!PandoraToggle.positiveCommentConfig.contains("1") && !PandoraToggle.positiveCommentConfig.contains("2")) {
            return
        }
        if (PandoraToggle.positiveCommentConfig == "1" && AVATAR_GAME_IDS.contains(gameIdStr)) {
            return
        }
        if (PandoraToggle.positiveCommentConfig == "2" && !AVATAR_GAME_IDS.contains(gameIdStr)) {
            return
        }

        val positiveCommentFlag = metaKV.analytic.getPositiveCommentFlag(metaKV.account.uuid)
        if (positiveCommentFlag) {
            return
        }

        Timber.i("positiveComment_result:")
        scope.launch {
            if (!timeInterval(metaKV.analytic.getPopUpDialog(metaKV.account.uuid), System.currentTimeMillis(), 0)) {
                return@launch
            }

            val positiveComment = queryPositiveComment()
            Timber.i("positiveComment_result:$positiveComment")
            if (positiveComment) {
                metaKV.analytic.setPositiveCommentFlag(metaKV.account.uuid, true)
                return@launch
            }

            val positiveCommentConfig =
                GlobalContext.get().get<TTaiInteractor>().getConfig(TTaiKV.ID_KEY_POSITIVE_COMMENT)?.value?.fromJSON<TTaiPositiveComment>()
                    ?: TTaiPositiveComment()
            val feedBackTime = metaKV.analytic.getFeedBackTime(metaKV.account.uuid)
            if (!timeInterval(feedBackTime, System.currentTimeMillis(), positiveCommentConfig.intervalTime)) {
                return@launch
            }

            val curDayGameTime = getCurDayGameTime(gameId)
            Timber.i("curDayGameTime:$curDayGameTime  gameId:$gameId")
            val time = if (gameId == AVATAR_GAME_ID) positiveCommentConfig.editorTime else positiveCommentConfig.gameTime

            if (!timeCompare(curDayGameTime, time)) {
                return@launch
            }
            Timber.i("showPositiveCommentDialogFlow")
            showPositiveCommentDialogFlow.value = System.currentTimeMillis()
        }

    }

    fun deleteCurDayGameTime() {
        scope.launch {
            val calendar: Calendar = Calendar.getInstance()
            // 将今天的日期减去一天
            calendar.add(Calendar.DAY_OF_MONTH, -1)
            // 获取前一天的年、月、日
            val year: Int = calendar.get(Calendar.YEAR)
            val month: Int = calendar.get(Calendar.MONTH) + 1 // Calendar.MONTH从0开始，所以需要加1
            val day: Int = calendar.get(Calendar.DAY_OF_MONTH)
            // 格式化日期为 yyyy-MM-dd 的形式
            val dateString = String.format("%d-%02d-%02d", year, month, day)
            val allKeys = metaKV.analytic.getAllKeys()
            allKeys?.forEach {
                if (it.contains(dateString)) {
                    metaKV.analytic.deleteDayGameIdPlayedGameTime(it)
                }
            }
        }
    }

    private fun convertToDayStartMillis(milliseconds: Long): Long {
        // 将毫秒数转换为Date对象
        val date = Date(milliseconds)
        // 创建Calendar实例并设置为Date对象的时间
        val calendar = Calendar.getInstance()
        calendar.time = date
        // 将时间部分设置为0
        calendar[Calendar.HOUR_OF_DAY] = 0
        calendar[Calendar.MINUTE] = 0
        calendar[Calendar.SECOND] = 0
        calendar[Calendar.MILLISECOND] = 0
        // 返回调整后的毫秒时间戳
        return calendar.timeInMillis
    }

    private fun timeInterval(startTime: Long, endTime: Long, intervalValue: Int): Boolean {
        if (startTime == 0L) return true
        val interval = convertToDayStartMillis(endTime) - convertToDayStartMillis(startTime) > intervalValue * 86400000
        return interval
    }

    private fun timeCompare(sumTime: Long, timeValue: Int): Boolean {
        return sumTime - timeValue * 60000 > 0
    }

    fun showPositiveCommentDialog(fragmentManager: FragmentManager) {
        if (isPositiveCommentDialogShown) return
        PositiveCommentDialog().show(fragmentManager, "PositiveCommentDialog")
        isPositiveCommentDialogShown = true
        Analytics.track(EventConstants.EVENT_ACCLAIM_UPS_SHOW) {
            put("source", if (gameIdStr == AVATAR_GAME_ID) "2" else "1")
        }
        metaKV.analytic.setPopUpDialog(metaKV.account.uuid, System.currentTimeMillis())
    }

    private fun getCurDayGameTime(gameId: String): Long {
        val curDateStr = SimpleDateFormat("yyyy-MM-dd", Locale.ROOT).run {
            format(Date(System.currentTimeMillis()))
        }
        return metaKV.analytic.getDayPackageNamePlayedGameTime("$curDateStr$gameId")
    }

    private fun addCurDayGameTime(gameId: String, additionTime: Long) {
        val curDateStr = SimpleDateFormat("yyyy-MM-dd", Locale.ROOT).run {
            format(Date(System.currentTimeMillis()))
        }
        metaKV.analytic.addDayPackageNamePlayedGameTime("$curDateStr$gameId", additionTime)

    }

    private suspend fun queryPositiveComment(): Boolean {
        return repo.queryPositiveComment().data?.hasScore == true
    }

    fun submitPositiveComment() {
        scope.launch {
            repo.submitPositiveComment().collect {
                it.data
            }
        }
    }
}