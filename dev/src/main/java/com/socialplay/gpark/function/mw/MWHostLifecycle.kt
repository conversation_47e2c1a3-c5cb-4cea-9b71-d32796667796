package com.socialplay.gpark.function.mw

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import androidx.core.net.toUri
import androidx.fragment.app.FragmentActivity
import com.bin.cpbus.CpEventBus
import com.meta.biz.ugc.UGCProtocolBiz
import com.meta.biz.ugc.listener.IMWFunction
import com.meta.biz.ugc.model.ActionOnlyMsg
import com.meta.biz.ugc.model.DuplicateImageCallbackMsg
import com.meta.biz.ugc.model.DuplicateImageMsg
import com.meta.biz.ugc.model.FeatureSupportMsg
import com.meta.biz.ugc.model.GameCommonFeature
import com.meta.biz.ugc.model.GameCommonFeatureResult
import com.meta.biz.ugc.model.GameTSLoading
import com.meta.biz.ugc.model.GameTransform
import com.meta.biz.ugc.model.IMWMsg
import com.meta.biz.ugc.model.IPlatformMsg
import com.meta.biz.ugc.model.MWProtocol
import com.meta.biz.ugc.model.NewStartGame
import com.meta.biz.ugc.model.RoleGameUploadFullBodyImgMsg
import com.meta.biz.ugc.model.SaveImage2Gallery
import com.meta.biz.ugc.model.TSUserDataLoad
import com.meta.biz.ugc.protocol.UGCProtocolReceiver
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolReceiveConstants
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.meta.biz.ugc.protocol.listener.SingleProtocolListener
import com.socialplay.gpark.ui.mgs.mw.GameImLifecycle
import com.meta.lib.mwbiz.MWBiz
import com.meta.lib.mwbiz.MWBizBridge
import com.meta.lib.mwbiz.MWBizProxy
import com.meta.lib.mwbiz.MWLifeCallback
import com.meta.lib.mwbiz.bean.bridge.ReceiveMsg
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.interactor.LifecycleInteractor
import com.socialplay.gpark.data.interactor.MVCoreProxyInteractor
import com.socialplay.gpark.data.interactor.MgsInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.event.UpdateFollowEvent
import com.socialplay.gpark.data.model.user.RelationType
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.editor.EditorGameInteractHelper
import com.socialplay.gpark.function.ipc.IPCFunctionRegister
import com.socialplay.gpark.function.ipc.ReceivedActionRegistry
import com.socialplay.gpark.function.ipc.provider.host.IPlatformFunctionEntry
import com.socialplay.gpark.function.mw.GameCommonFeatureResolver.duplicateImage
import com.socialplay.gpark.function.mw.GameCommonFeatureResolver.saveImage2Gallery
import com.socialplay.gpark.function.mw.lifecycle.GameDialogLifecycle
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.pay.PayProvider
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.startup.StartupProcessType
import com.socialplay.gpark.ui.gamepay.GamePayLifecycle
import com.socialplay.gpark.ui.gamepay.GamePayLifecycle.Companion.IAP_TS_PAY_RECHARGE
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.ui.main.MainBottomNavigationItem
import com.socialplay.gpark.util.ActivityLifecycleCallbacksAdapter
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.get
import com.socialplay.gpark.util.simJsonObj
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean

/**
 * <AUTHOR>
 * @date 2022/5/29
 */
@Deprecated("Use UMW instead")
object MWHostLifecycle {

    private const val TAG = "MetaVerseHostLifecycle:: %s"

    private val mvCoreProxyInteractor: MVCoreProxyInteractor by lazy { GlobalContext.get().get() }
    private val mgsInteractor: MgsInteractor by lazy { GlobalContext.get().get() }
    private val app: Application by lazy { GlobalContext.get().get() }
    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }
    private val scope by lazy { MainScope() }
    private val payInteractor: IPayInteractor by lazy { GlobalContext.get().get() }
    private val mgsIsInit = AtomicBoolean(false)

    fun register(application: Application) { //接收MW发送的消息
        CpEventBus.register(this)

        defineBasicFunc()

        // 目前 UGCProtocolReceiver.registerProtocol 注册太慢, 这里提前初始化
        // 以解决游戏引擎报这个问题: BroadCasting: ue.action.feature.support without receiver
        if (PandoraToggle.getTabIdList().contains(MainBottomNavigationItem.EDITOR_HOME_ITEM_ID.toString())) {
            val mvCoreProxyInteractor: MVCoreProxyInteractor = GlobalContext.get().get()
            mvCoreProxyInteractor.startup(app)
        }

        registerMWMsgActions()

        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<NewStartGame>(
            ProtocolReceiveConstants.PROTOCOL_OPEN_GAME
        ) {
            override fun handleProtocol(message: NewStartGame?, messageId: Int) {
                message?.apply {
                    EditorGameInteractHelper.newStartGameFromHostProcess(
                        application, currentGameId, gameId, gamePkg, roomIdFromCp, inviteOpenId,
                        gameType, false
                    )
                }
            }
        })

        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<GameTransform>(
            ProtocolReceiveConstants.PROTOCOL_ROLE_TRANSFORM
        ) {
            override fun handleProtocol(message: GameTransform?, messageId: Int) {
                message?.apply {
                    EditorGameInteractHelper.gameTransform(gameId, status)
                }
            }
        })

        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<GameTransform>(
            ProtocolReceiveConstants.PROTOCOL_ROLE_TRANSFORM_CHANGED
        ) {
            override fun handleProtocol(message: GameTransform?, messageId: Int) {
                message?.apply {
                    EditorGameInteractHelper.syncTransformStatus(status, message.transformVersion)
                }
            }
        })

        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<TSUserDataLoad>(
            ProtocolReceiveConstants.PROTOCOL_ROLE_GAME_USER_READY
        ) {
            override fun handleProtocol(message: TSUserDataLoad?, messageId: Int) {

                // 目前只发这一个，需要再加，避免全部发送导致事件过多
                Analytics.track(
                    EventConstants.EVENT_MW_CALL_CLIENT,
                    mapOf(
                        "action" to ProtocolReceiveConstants.PROTOCOL_ROLE_GAME_USER_READY.action,
                        "content" to (message?.status ?: ""),
                    )
                )

                message?.apply {
                    EditorGameInteractHelper.useDataLoad(this)
                }
            }
        })

        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<GameTSLoading>(
            ProtocolReceiveConstants.PROTOCOL_TS_GAME_LOADING
        ) {
            override fun handleProtocol(message: GameTSLoading?, messageId: Int) {
                message?.apply {
                    EditorGameInteractHelper.gameTsLoading()
                }
            }
        })


        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<FeatureSupportMsg>(
            ProtocolReceiveConstants.PROTOCOL_MEMBER_DETECT
        ) {
            override fun handleProtocol(message: FeatureSupportMsg?, messageId: Int) {
                message?.apply {
                    MWFeatureSupport.isSupportFeature(this.feature, messageId, true)
                }
            }
        })

        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<GameCommonFeature>(
            ProtocolReceiveConstants.PROTOCOL_TS_GAME_FEATURE
        ) {
            override fun handleProtocol(message: GameCommonFeature?, messageId: Int) {
                message?.apply {
                    GameCommonFeatureResolver.addHostGameCommonReceiver(
                        application,
                        this,
                        messageId,
                        MWBizProxy.currentGameId(),
                        MWBizProxy.currentGamePkg(),
                    )
                }
            }
        })

        UGCProtocolReceiver.addProtocolObserver(object :
            SingleProtocolListener<GameCommonFeatureResult>(
                ProtocolReceiveConstants.PROTOCOL_TS_GAME_FEATURE_RESULT
            ) {

            override fun handleProtocol(message: GameCommonFeatureResult?, messageId: Int) {
                message?.apply {
                    GameCommonFeatureResultResolver.dispatchCommonFeatureResult(this)
                }
            }
        })

        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<RoleGameUploadFullBodyImgMsg>(ProtocolReceiveConstants.PROTOCOL_ROLE_GAME_UPLOAD_FULL_BODY_IMG) {
            override fun handleProtocol(message: RoleGameUploadFullBodyImgMsg?, messageId: Int) {
                message?.apply {
                    mgsInteractor.modifyUserFullBodyImg(this, messageId)
                }
            }
        })

        // 打开充值
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<ActionOnlyMsg>(IAP_TS_PAY_RECHARGE) {
                override fun handleProtocol(message: ActionOnlyMsg?, messageId: Int) {
                    if (PayProvider.ENABLE_RECHARGE) {
                        val curActivity = LifecycleInteractor.activityRef?.get() ?: return
                        (curActivity as? FragmentActivity)?.apply {
                            val navHostFragment = if (curActivity is MainActivity) {
                                curActivity.findNavHostFragment()
                            } else {
                                null
                            }
                            scope.launch {
                                MetaRouter.Pay.goBuyCoinsPage(
                                    application,
                                    navHostFragment,
                                    "game_dire",
                                )
                            }
                        }
                    } else {
                        ToastUtil.showShort(R.string.under_development)
                    }
                }
            }
        )
        // 是否关注玩家
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<ActionOnlyMsg>(ProtocolReceiveConstants.UE_ACTION_IS_FOLLOW_PLAYER) {
            override fun handleProtocol(message: ActionOnlyMsg?, messageId: Int) {
                callbackUeIsFollow(message?.rawData?.get("uuid").toString(), messageId)
            }
        })
        // 关注|取消关注玩家协议
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<ActionOnlyMsg>(ProtocolReceiveConstants.UE_ACTION_FOLLOW_PLAYER) {
            override fun handleProtocol(message: ActionOnlyMsg?, messageId: Int) {
                message ?: return
                callbackUeFollowStatus(message, messageId)
            }
        })
        // 打开用户首页
        UGCProtocolReceiver.addProtocolObserver(
            object : SingleProtocolListener<ActionOnlyMsg>(ProtocolReceiveConstants.UE_ACTION_OPEN_PLAYER_HOME) {
                override fun handleProtocol(message: ActionOnlyMsg?, messageId: Int) {
                    message?.apply {
                        val uuid = message.rawData.get("uuid") ?: return
                        MetaRouter.Main.dispatchUrl(GlobalContext.get().get<Context>(), MetaRouter.Main.buildJumpUri(MetaDeepLink.ACTION_USER_HOME, mapOf(MetaDeepLink.PARAM_USER_ID to uuid)), LinkData.SOURCE_HOST_MW)
                    }
                }
            }
        )
        UGCProtocolReceiver.addProtocolObserver(
            object :
                SingleProtocolListener<IMWMsg>(ProtocolReceiveConstants.PROTOCOL_INVOKE_COMMON_PARAMS) {
                override fun handleProtocol(message: IMWMsg?, messageId: Int) {
                    MWFeatureCommonParams.invokeCommonParams(messageId)
                }
            }
        )
        UGCProtocolReceiver.addProtocolObserver(
            object :
                SingleProtocolListener<IMWMsg>(ProtocolReceiveConstants.PROTOCOL_REFRESH_ACCESS_TOKEN) {
                override fun handleProtocol(message: IMWMsg?, messageId: Int) {
                    MWFeatureCommonParams.handleRefreshAccessToken(messageId)
                }
            }
        )


        // 选择单张图片复制到指定路径
        UGCProtocolReceiver.addProtocolObserver(object :
            SingleProtocolListener<DuplicateImageMsg>(ProtocolReceiveConstants.UE_ACTION_DUPLICATE_IMAGE) {
            override fun handleProtocol(message: DuplicateImageMsg?, messageId: Int) {
                if (message != null) {
                    message.duplicateImage(
                        LifecycleInteractor.activityRef?.get() as? FragmentActivity,
                        messageId
                    )
                } else {
                    UGCProtocolSender.sendProtocol(
                        ProtocolSendConstant.BRIDGE_ACTION_DUPLICATE_IMAGE,
                        messageId,
                        DuplicateImageCallbackMsg(
                            message = "protocol parsing result is null",
                            code = 1001,
                            imagePath = null,
                        )
                    )
                }
            }
        })

        // 保存单张图片到图库
        UGCProtocolReceiver.addProtocolObserver(object :
            SingleProtocolListener<SaveImage2Gallery>(ProtocolReceiveConstants.UE_ACTION_SAVE_IMAGE_TO_GALLERY) {
            override fun handleProtocol(message: SaveImage2Gallery?, messageId: Int) {
                if (message != null) {
                    message.saveImage2Gallery(
                        LifecycleInteractor.activityRef?.get() as? FragmentActivity,
                        messageId
                    )
                } else {
                    UGCProtocolSender.sendProtocol(
                        ProtocolSendConstant.BRIDGE_ACTION_SAVE_IMAGE_TO_GALLERY,
                        messageId,
                        DuplicateImageCallbackMsg(
                            message = "protocol parsing result is null",
                            code = 1001,
                            imagePath = null,
                        )
                    )
                }
            }
        })

        MWBizProxy.addOnMWMsgCallback {
            runCatching {
                JSONObject().put("action", it.action)
                    .put("data", it.data.simJsonObj())
                    .put("channel", it.channel)
                    .put("message", it.message)
                    .put("status", it.status)
                    .put("messageId", it.messageId)
                    .toString().let {
                        UGCProtocolReceiver.dispatchUECall(it)
                    }
            }.getOrElse {
                Timber.e(TAG, "addOnMWMsgCallback error! ${it}")
            }
            Timber.d(TAG, "addOnMWMsgCallback Received! ${it.action}")
            addAdOnMWMsgCallback(it)
        }

        MWLifeCallback.startGameUseView.addCallback("MWHostLifecycleStartViewGame") {
            if (it.isEmpty()) {
                if (mgsIsInit.compareAndSet(false, true)) {
                    //防止多次初始化
                    Timber.d(TAG, "Host Game Pkg: ${MWBizBridge.currentGamePkg()}")
                    mgsInteractor.init(app, MWBizBridge.currentGamePkg(), true)
                }
            }
        }
        val lifecycles = listOf(
            GamePayLifecycle(true),
            GameDialogLifecycle(true),
            GameImLifecycle.init(true)
        )
        application.registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacksAdapter() {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                lifecycles.forEach { it.onActivityCreated(activity) }
            }

            override fun onActivityStarted(activity: Activity) {
                super.onActivityStarted(activity)
                lifecycles.forEach { it.onActivityStarted(activity) }
            }

            override fun onActivityResumed(activity: Activity) {
                lifecycles.forEach { it.onActivityResumed(activity) }
            }

            override fun onActivityPaused(activity: Activity) {
                lifecycles.forEach { it.onActivityPaused(activity) }
            }

            override fun onActivityStopped(activity: Activity) {
                lifecycles.forEach { it.onActivityStopped(activity) }
            }

            override fun onActivityDestroyed(activity: Activity) {
                lifecycles.forEach { it.onActivityDestroyed(activity) }
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
                lifecycles.forEach { it.onActivitySaveInstanceState(activity, outState) }
            }
        })

    }

    private fun callbackUeFollowStatus(message: ActionOnlyMsg, messageId: Int) {
        val uuid = message.rawData.get("uuid") ?: return
        val isFollow = message.rawData.get("isFollow").toString().toBooleanStrictOrNull() ?: return
        scope.launch {
            runCatching {
                val repository = GlobalContext.get().get<IMetaRepository>()
                if (isFollow) {
                    repository.relationAdd(uuid.toString(), RelationType.Follow.value)
                } else {
                    repository.relationDel(uuid.toString(), RelationType.Follow.value)
                }.collect {
                    UGCProtocolSender.sendProtocol(
                        ProtocolSendConstant.BRIDGE_ACTION_FOLLOW_CALLBACK, messageId, object : IPlatformMsg() {
                            override fun addJsonData(data: MutableMap<String, Any>) {
                                data["isFollow"] = isFollow
                                data["uuid"] = uuid
                                data["code"] = if (it.data == true) 200 else it.code ?: -1
                                data["message"] = it.message ?: ""
                            }
                        })
                }
            }
        }
    }

    private fun callbackUeIsFollow(uuid: String, messageId: Int) {
        scope.launch {
            runCatching {
                GlobalContext.get().get<IMetaRepository>().queryUserProfile(uuid).collect {
                    UGCProtocolSender.sendProtocol(
                        ProtocolSendConstant.BRIDGE_ACTION_IS_FOLLOW_CALLBACK,
                        messageId,
                        object : IPlatformMsg() {
                            override fun addJsonData(data: MutableMap<String, Any>) {
                                data["code"] = it.code ?: 200
                                data["message"] = it.message ?: ""
                                data["uuid"] = uuid
                                data["isFollow"] = it.data?.isFollow ?: false
                            }
                        })
                }
            }

        }
    }

    private fun defineBasicFunc() {
        UGCProtocolBiz.setFuncListener(object : IMWFunction {
            override fun callUE(json: String): String? {
                if (MWBiz.isAvailable()) {
                    MWBizProxy.callUE(json)
                } else {
                    Timber.w("checkcheck_ugc_protocol, onCallUE MVCore.core.available() == false")
                }
                return null
            }

            override fun registerAction(action: String) {
                MWBizProxy.registerMWMsgAction(action)
            }
        })
    }


    private fun registerMWMsgActions() {
        Timber.i("registerMWMsgActions")
        MainScope().launch {
            mvCoreProxyInteractor.connectionStatusFlow.collect {
                if (it) {
                    withContext(Dispatchers.IO) {
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_MEMBER_DETECT)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_TS_GAME_FEATURE)

                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_ROLE_GAME_LOAD_STATUS_CHANGED)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_ROLE_GAME_UPLOAD_FULL_BODY_IMG)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_SET_ORIENTATION)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_GET_ORIENTATION)

                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_MW_GAME_SCENE_READY)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_PAY_START)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_PAY_GET_ARK)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_PAY_FINISH)
                        UGCProtocolReceiver.registerProtocol(IAP_TS_PAY_RECHARGE)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.UE_ACTION_IS_FOLLOW_PLAYER)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.UE_ACTION_FOLLOW_PLAYER)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.UE_ACTION_OPEN_PLAYER_HOME)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_MEMBER_INFO)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_MEMBER_RECHARGE)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_MW_GAME_LOADING_FAILED)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.UE_ACTION_DUPLICATE_IMAGE)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.UE_ACTION_SAVE_IMAGE_TO_GALLERY)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_ROLE_TRANSFORM_CHANGED)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_ACTION_GAME_IM_SDK)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_INVOKE_COMMON_PARAMS)
                        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_REFRESH_ACCESS_TOKEN)
                        registerAdActions()
                    }
                }
            }
        }
    }

    /**
     * 注册角色编辑器广告相关的消息
     */
    private fun registerAdActions() {
        ReceivedActionRegistry.entries.forEach {
            Timber.d("Register MWMessage. action: ${it.value}")
            UGCProtocolReceiver.registerProtocol(MWProtocol(it.value, it.desc))
        }
    }

    /**
     * 通过IPC通道转发MW的消息 接收角编广告相关的消息
     */
    private fun addAdOnMWMsgCallback(receiveMsg: ReceiveMsg) {
        Timber.d("MWBizProxy Received mw message. action: ${receiveMsg.action}, ${MWBizProxy.currentGameId()}, ${MWBizProxy.currentGamePkg()} ,data: ${receiveMsg.data}")
        kotlin.runCatching {
            IPCFunctionRegister.ipc.get(IPlatformFunctionEntry)
                .call(StartupProcessType.H.desc, receiveMsg.action, MWBizProxy.currentGameId(), MWBizProxy.currentGamePkg(), receiveMsg.data)
        }.getOrElse { ex ->
            Timber.w(ex, "MWBizProxy Failed to dispatch mw message via ipc channel.")
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: UpdateFollowEvent) {
        callbackUeIsFollow(event.uuid, 0)
    }
}