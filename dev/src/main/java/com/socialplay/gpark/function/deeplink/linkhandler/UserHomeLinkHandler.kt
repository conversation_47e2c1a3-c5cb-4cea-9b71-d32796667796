package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.router.MetaRouter
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/10/09
 *     desc   :
 * </pre>
 */
class UserHomeLinkHandler : LinkHandler {
    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        Timber.d("metadeeplink UserHomeLinkHandler handle uri:%s", data.uri)

        val uid = data.uri.getQueryParameter(MetaDeepLink.PARAM_USER_ID) ?: return LinkHandleResult.Failed("no user id")
        val from = data.uri.getQueryParameter(MetaDeepLink.PARAM_FROM)
            ?: if (data.source == LinkData.SOURCE_QR_CODE) {
                "scanQR"
            } else {
                "scheme"
            }

        MetaRouter.Profile.other(data.navHost, uid, from)
        return LinkHandleResult.Success
    }
}