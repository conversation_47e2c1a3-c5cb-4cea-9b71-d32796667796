/*
 * Copyright (C) 2015 Square, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.socialplay.gpark.function.http

import java.io.IOException
import java.nio.charset.Charset
import java.nio.charset.StandardCharsets.UTF_8
import java.util.TreeSet
import java.util.concurrent.TimeUnit
import okhttp3.Headers
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.internal.http.promisesBody
import okio.Buffer
import okio.GzipSource
import timber.log.Timber
import java.io.EOFException

/**
 * An OkHttp interceptor which logs request and response information. Can be applied as an
 * [application interceptor][OkHttpClient.interceptors] or as a [OkHttpClient.networkInterceptors].
 *
 * The format of the logs created by this class should not be considered stable and may
 * change slightly between releases. If you need a stable logging format, use your own interceptor.
 */
class HashCodeHttpLoggingInterceptor @JvmOverloads constructor(
  private val logger: Logger = Logger.DEFAULT
) : Interceptor {

  @Volatile private var headersToRedact = emptySet<String>()

  @set:JvmName("level")
  @Volatile var level = Level.NONE

  enum class Level {
    /** No logs. */
    NONE,

    /**
     * Logs request and response lines.
     *
     * Example:
     * ```
     * --> POST /greeting http/1.1 (3-byte body)
     *
     * <-- 200 OK (22ms, 6-byte body)
     * ```
     */
    BASIC,

    /**
     * Logs request and response lines and their respective headers.
     *
     * Example:
     * ```
     * --> POST /greeting http/1.1
     * Host: example.com
     * Content-Type: plain/text
     * Content-Length: 3
     * --> END POST
     *
     * <-- 200 OK (22ms)
     * Content-Type: plain/text
     * Content-Length: 6
     * <-- END HTTP
     * ```
     */
    HEADERS,

    /**
     * Logs request and response lines and their respective headers and bodies (if present).
     *
     * Example:
     * ```
     * --> POST /greeting http/1.1
     * Host: example.com
     * Content-Type: plain/text
     * Content-Length: 3
     *
     * Hi?
     * --> END POST
     *
     * <-- 200 OK (22ms)
     * Content-Type: plain/text
     * Content-Length: 6
     *
     * Hello!
     * <-- END HTTP
     * ```
     */
    BODY,
    CURL
  }

  fun interface Logger {
    fun log(message: String)

    companion object {
      /** A [Logger] defaults output appropriate for the current platform. */
      @JvmField
      val DEFAULT: Logger = DefaultLogger()
      private class DefaultLogger : Logger {
        override fun log(message: String) {
          Timber.tag("okhttp.OkHttpClient").d(message)
        }
      }
    }
  }

  fun redactHeader(name: String) {
    val newHeadersToRedact = TreeSet(String.CASE_INSENSITIVE_ORDER)
    newHeadersToRedact += headersToRedact
    newHeadersToRedact += name
    headersToRedact = newHeadersToRedact
  }

  /**
   * Sets the level and returns this.
   *
   * This was deprecated in OkHttp 4.0 in favor of the [level] val. In OkHttp 4.3 it is
   * un-deprecated because Java callers can't chain when assigning Kotlin vals. (The getter remains
   * deprecated).
   */
  fun setLevel(level: Level) = apply {
    this.level = level
  }

  @JvmName("-deprecated_level")
  @Deprecated(
      message = "moved to var",
      replaceWith = ReplaceWith(expression = "level"),
      level = DeprecationLevel.ERROR
  )
  fun getLevel(): Level = level
  private fun interceptForCurStyle(chain: Interceptor.Chain): Response {
    val request = chain.request()

    printCurlCommand(request)
    val startNs = System.nanoTime()
    val response: Response
    try {
      response = chain.proceed(request)
    } catch (e: Exception) {
      printCurlError(request, e)
      throw e
    }
    val tookMs = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - startNs)

    printCurlResponse(response, tookMs)

    return response

  }

  @Throws(IOException::class)
  override fun intercept(chain: Interceptor.Chain): Response {
    val level = this.level

    if (level == Level.CURL) {
      return interceptForCurStyle(chain)
    }

    val request = chain.request()
    
    if (level == Level.NONE) {
      return chain.proceed(request)
    }
    val urlHash = runCatching { request.url.toString().hashCode() + System.currentTimeMillis() }.getOrElse { 0 }.let {
      "$it"
    }

    val logBody = level == Level.BODY
    val logHeaders = logBody || level == Level.HEADERS

    val requestBody = request.body

    val connection = chain.connection()
    var requestStartMessage =
        ("--> ${request.method} ${request.url}${if (connection != null) " " + connection.protocol() else ""}")
    if (!logHeaders && requestBody != null) {
      requestStartMessage += " (${requestBody.contentLength()}-byte body)"
    }
    logWithHashCode(urlHash,requestStartMessage)

    if (logHeaders) {
      val headers = request.headers

      if (requestBody != null) {
        // Request body headers are only present when installed as a network interceptor. When not
        // already present, force them to be included (if available) so their values are known.
        requestBody.contentType()?.let {
          if (headers["Content-Type"] == null) {
            logWithHashCode(urlHash,"Content-Type: $it")
          }
        }
        if (requestBody.contentLength() != -1L) {
          if (headers["Content-Length"] == null) {
            logWithHashCode(urlHash,"Content-Length: ${requestBody.contentLength()}")
          }
        }
      }

      for (i in 0 until headers.size) {
        logHeader(urlHash, headers, i)
      }

      if (!logBody || requestBody == null) {
        logWithHashCode(urlHash,"--> END ${request.method}")
      } else if (bodyHasUnknownEncoding(request.headers)) {
        logWithHashCode(urlHash,"--> END ${request.method} (encoded body omitted)")
      } else if (requestBody.isDuplex()) {
        logWithHashCode(urlHash,"--> END ${request.method} (duplex request body omitted)")
      } else if (requestBody.isOneShot()) {
        logWithHashCode(urlHash,"--> END ${request.method} (one-shot body omitted)")
      } else {
        val buffer = Buffer()
        requestBody.writeTo(buffer)

        val contentType = requestBody.contentType()
        val charset: Charset = contentType?.charset(UTF_8) ?: UTF_8

        logWithHashCode(urlHash,"")
        if (buffer.isProbablyUtf8()) {
          logWithHashCode(urlHash,buffer.readString(charset))
          logWithHashCode(urlHash,"--> END ${request.method} (${requestBody.contentLength()}-byte body)")
        } else {
          logWithHashCode(urlHash,
              "--> END ${request.method} (binary ${requestBody.contentLength()}-byte body omitted)")
        }
      }
    }

    val startNs = System.nanoTime()
    val response: Response
    try {
      response = chain.proceed(request)
    } catch (e: Exception) {
      logWithHashCode(urlHash,"<-- HTTP FAILED: $e")
      throw e
    }

    val tookMs = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - startNs)

    val responseBody = response.body!!
    val contentLength = responseBody.contentLength()
    val bodySize = if (contentLength != -1L) "$contentLength-byte" else "unknown-length"

    logWithHashCode(
      urlHash,
      "<-- ${response.code}${if (response.message.isEmpty()) "" else ' ' + response.message} ${response.request.url} (${tookMs}ms${if (!logHeaders) ", $bodySize body" else ""})"
    )

    if (logHeaders) {
      val headers = response.headers
      for (i in 0 until headers.size) {
        logHeader(urlHash, headers, i)
      }

      if (!logBody || !response.promisesBody()) {
        logWithHashCode(urlHash,"<-- END HTTP")
      } else if (bodyHasUnknownEncoding(response.headers)) {
        logWithHashCode(urlHash,"<-- END HTTP (encoded body omitted)")
      } else {
        val source = responseBody.source()
        source.request(Long.MAX_VALUE) // Buffer the entire body.
        var buffer = source.buffer

        var gzippedLength: Long? = null
        if ("gzip".equals(headers["Content-Encoding"], ignoreCase = true)) {
          gzippedLength = buffer.size
          GzipSource(buffer.clone()).use { gzippedResponseBody ->
            buffer = Buffer()
            buffer.writeAll(gzippedResponseBody)
          }
        }

        val contentType = responseBody.contentType()
        val charset: Charset = contentType?.charset(UTF_8) ?: UTF_8

        if (!buffer.isProbablyUtf8()) {
          logWithHashCode(urlHash,"")
          logWithHashCode(urlHash,"<-- END HTTP (binary ${buffer.size}-byte body omitted)")
          return response
        }

        if (contentLength != 0L) {
          logWithHashCode(urlHash,"")
          logWithHashCode(urlHash,buffer.clone().readString(charset))
        }

        if (gzippedLength != null) {
          logWithHashCode(urlHash,"<-- END HTTP (${buffer.size}-byte, $gzippedLength-gzipped-byte body)")
        } else {
          logWithHashCode(urlHash,"<-- END HTTP (${buffer.size}-byte body)")
        }
      }
    }

    return response
  }

  /**

   * curl --compressed --request POST   --url http://test-api.metaworld.fun/abtest/v4/getConfig   --header 'X-MCS-AppKey: cDEwMDQy'   --header 'content-type: application/json'   --data '{
   *     "pd_device_id": "da5cf71bd95c3be2"
   * }'
   */
  private fun printCurlCommand(request: okhttp3.Request) {
    logger.log("\n")
    val sb = StringBuilder("curl --location '${request.url}' \\\n")

    val requestBody = request.body
    val headers = request.headers

    if (requestBody != null) {
      // Request body headers are only present when installed as a network interceptor. When not
      // already present, force them to be included (if available) so their values are known.
      requestBody.contentType()?.let {
        if (headers["Content-Type"] == null) {
          sb.append("-H 'Content-Type: $it'").append(" \\\n")
        }
      }
      if (requestBody.contentLength() != -1L) {
        if (headers["Content-Length"] == null) {
          sb.append("-H 'Content-Length: ${requestBody.contentLength()}'").append(" \\\n")
        }
      }
    }
    for (i in 0 until headers.size) {
      sb.append("-H '${headers.name(i)}: ${headers.value(i)}'")
      if (i < headers.size - 1) {
        sb.append(" \\\n")
      }
    }

    if (requestBody != null) {
      sb.append(" \\\n")
      val buffer = Buffer()
      requestBody.writeTo(buffer)
      val contentType = requestBody.contentType()
      val charset: Charset = contentType?.charset(UTF_8) ?: UTF_8
      if (buffer.isProbablyUtf8()) {
        sb.append("--data '${buffer.readString(charset)}'")
      }
    }

    logger.log(sb.toString())
    logger.log("\n")

  }

  private fun printCurlError(request: Request, e: Exception) {
    val sb = StringBuilder("request ${request.url} error: $e")
    sb.append("\n")

  }

  private fun printCurlResponse(response: Response, tookMs: Long) {
    val responseBody = response.body!!
    val contentLength = responseBody.contentLength()
    val bodySize = if (contentLength != -1L) "$contentLength-byte" else "unknown-length"
    var finalSb = StringBuilder("${response.code}${if (response.message.isEmpty()) "" else ' ' + response.message} ${response.request.url}")
    val sb = StringBuilder()
    val headers = response.headers
    for (i in 0 until headers.size) {
      sb.append(" ${headers.name(i)}: ${headers.value(i)}")
      sb.append("\n")
    }
    sb.append("\n")
    if (!response.promisesBody()) {
      sb.append(" END HTTP")
    } else if (bodyHasUnknownEncoding(response.headers)) {
      sb.append(" END HTTP (encoded body omitted)")
    } else {
      val source = responseBody.source()
      source.request(Long.MAX_VALUE) // Buffer the entire body.
      var buffer = source.buffer

      var gzippedLength: Long? = null
      if ("gzip".equals(headers["Content-Encoding"], ignoreCase = true)) {
        gzippedLength = buffer.size
        GzipSource(buffer.clone()).use { gzippedResponseBody ->
          buffer = Buffer()
          buffer.writeAll(gzippedResponseBody)
        }
      }

      val contentType = responseBody.contentType()
      val charset: Charset = contentType?.charset(UTF_8) ?: UTF_8

      if (!buffer.isProbablyUtf8()) {
        sb.append(" END HTTP (binary ${buffer.size}-byte body omitted)")
        return
      }

      if (contentLength != 0L) {
        sb.append(" ${buffer.clone().readString(charset)}")
      }
      if (gzippedLength != null) {
        finalSb.append("(${tookMs}ms, ${buffer.size}-byte, $gzippedLength-gzipped-byte body)")
      } else {
        finalSb.append("(${tookMs}ms, ${buffer.size}-byte body)")
      }
    }
    finalSb.append("\n")
    finalSb.append(sb.toString())
    logger.log(finalSb.toString())
    logger.log("\n")
  }


  private fun logWithHashCode(hashCode: String, msg: String) {
    logger.log("$hashCode: $msg")
  }

  private fun logHeader(urlHash: String, headers: Headers, i: Int) {
    val value = if (headers.name(i) in headersToRedact) "██" else headers.value(i)
    logWithHashCode(urlHash,headers.name(i) + ": " + value)
  }

  private fun bodyHasUnknownEncoding(headers: Headers): Boolean {
    val contentEncoding = headers["Content-Encoding"] ?: return false
    return !contentEncoding.equals("identity", ignoreCase = true) &&
        !contentEncoding.equals("gzip", ignoreCase = true)
  }

  private fun Buffer.isProbablyUtf8(): Boolean {
    try {
      val prefix = Buffer()
      val byteCount = size.coerceAtMost(64)
      copyTo(prefix, 0, byteCount)
      for (i in 0 until 16) {
        if (prefix.exhausted()) {
          break
        }
        val codePoint = prefix.readUtf8CodePoint()
        if (Character.isISOControl(codePoint) && !Character.isWhitespace(codePoint)) {
          return false
        }
      }
      return true
    } catch (_: EOFException) {
      return false // Truncated UTF-8 sequence.
    }
  }
}
