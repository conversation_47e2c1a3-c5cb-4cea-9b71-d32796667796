package com.socialplay.gpark.function.pay

import android.app.Activity
import androidx.lifecycle.LiveData
import com.meta.biz.ugc.model.RechargeArkMsg
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.model.Product
import com.socialplay.gpark.data.model.event.PayResult
import com.socialplay.gpark.data.model.pay.CommonPayParams
import com.socialplay.gpark.data.model.pay.SubsData
import com.socialplay.gpark.data.model.pay.UserBalance
import com.socialplay.gpark.function.startup.core.ProcessType

interface IPayInteractor {
    companion object {
        //支付失败-其他 web传入的支付方式不支持,或者其他未知错误
        const val FAIL_OTHER = -1

        //拉起google失败
        const val FAIL_LAUNCHER_ERROR = 0

        //用户取消
        const val FAIL_CANCEL = -2

        //轮询订单状态失败
        const val FAIL_LOOPER_ORDER = -3

        const val ERROR_PARAMS_ERROR = ""
        const val ERROR_GOOGLE_NOT_INIT = "this::billingClient.notInitialized"
    }

    /**
     * 是否支持积分支付
     */
    val supportPointsPayment: Boolean

    val products: LiveData<MutableList<Product>>
    val subsProducts: LiveData<MutableList<Product>>
    val userBalance: LiveData<UserBalance?>

    fun preProcessRechargeArkMsg(msg: RechargeArkMsg) {}

    suspend fun init(processType: ProcessType)
    fun checkConnect()
    suspend fun getBalance(messageId: Int? = 0, callback: (suspend (Long, Long) -> Unit)? = null)

    fun getPayChannel(payType: Int?): Int?

    fun startPay(
        activity: Activity,
        commonPayParams: CommonPayParams,
        extra: Any? = null,
        pageSource: String? = null,
        payResultCallback: ((payResult: PayResult) -> Unit)? = null
    )

    /**
     * 取消支付
     */
    fun cancelPay() {}

    /**
     * 实名认证
     */
    fun needRealName(): Boolean = false
    fun gotoRealNamePage() {}
    suspend fun mwPay(hashMap: Map<String, Any?>, callback: suspend (DataResult<*>) -> Unit)

    /**
     * 仅 GPark 会用到这个方法, 所有用到此方法的地方, 都需要改造
     */
    suspend fun loadProducts(
        scene: String,
        list: List<SubsData>?,
        callBack: (Pair<String?, MutableList<Product>>) -> Unit?
    )

    fun getRechargeViewData(
        balance: Long,
        productPrice: Long,
        msg: RechargeArkMsg,
        callback: (DataResult<RechargeViewData>) -> Unit
    )

    fun loadRechargeProducts(callback: (DataResult<List<RechargeProductCompat>>) -> Unit)
}

data class RechargeViewData(
    /**
     * 我们的商品id
     */
    val ourProductId: String = "",
    /**
     * 谷歌/苹果商品id
     */
    val productId: String = "",
    val parentProductId: String = "",
    /**
     * 是否是会员
     */
    val isMember: Boolean,
    val needAdditional: Long,
    /**
     * 基本币数量
     */
    val baseCoinNum: Long,
    /**
     * 赠送币数量
     */
    val awardCoinNum: Long = 0,
    /**
     * 会员赠送币数量
     */
    val memberRewardCoinNum: Long = 0,
    val currencyCode: String,
    val price: Int,
    val currencyCodePrice: String,
    /**
     * 是否显示充值协议
     */
    val showPrivacy: Boolean,
    /**
     * 充值协议
     */
    val rechargePrivacyUrl: String = "",
    /**
     * 附带的数据, party 和 gpark 的专有数据
     */
    val extra: Any = Any(),
)

data class RechargeProductCompat(
    /**
     * 我们的商品id
     */
    val ourProductId: String = "",
    /**
     * 谷歌/苹果商品id
     */
    val productId: String = "",
    val parentProductId: String = "",
    /**
     * 基本币数量
     */
    val baseCoinNum: Long,
    /**
     * 赠送币数量
     */
    val awardCoinNum: Long = 0,
    /**
     * 会员赠送币数量
     */
    val memberRewardCoinNum: Long = 0,
    /**
     * 货币单位
     */
    val currencyCode: String,
    /**
     * 价格
     */
    val price: Int,
    /**
     * 附带货币单位的价格, 界面可直接用于展示
     */
    val currencyCodePrice: String,
    /**
     * 附带的数据, party 和 gpark 的专有数据
     */
    val extra: Any = Any(),
)