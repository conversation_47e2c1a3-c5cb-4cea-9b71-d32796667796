package com.socialplay.gpark.function.http

import com.meta.ipc.IPC
import com.meta.pandora.Pandora
import com.socialplay.gpark.data.api.API_URL_REFRESH_ACCESS_TOKEN
import com.socialplay.gpark.data.base.ApiCode
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.API_HEADER_TOKEN
import com.socialplay.gpark.data.model.RESPONSE_CODE_ACCESS_TOKEN_INVALID
import com.socialplay.gpark.data.model.RESPONSE_CODE_SUCCESS
import com.socialplay.gpark.data.model.event.TokenInvalidEvent
import com.socialplay.gpark.function.ipc.provider.account.IUserAccountProvider
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.get
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import okio.Buffer
import okio.GzipSource
import org.greenrobot.eventbus.EventBus
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import timber.log.Timber
import java.nio.charset.Charset
import java.nio.charset.StandardCharsets

/**
 * H进程专用
 */
class HUnpackApiCodeInterceptor : CheckTokenInterceptor() {
    override fun refreshAccessToken(currentAccessToken: String?): String? {
        return accountInteractor.refreshAccessToken(currentAccessToken)
    }

    override fun onRefreshTokenInValid(currentAccessToken: String?) {
        // 发送refresh token失效事件，需要用户重新登录
        Timber.tag(TAG).d("${javaClass.simpleName} onRefreshTokenInValid token: $currentAccessToken")
        val tokenIsNullOrEmpty = currentAccessToken.isNullOrEmpty()
        EventBus.getDefault().post(TokenInvalidEvent(tokenIsNullOrEmpty))
    }
}

/**
 * M进程专用
 */
class MUnpackApiCodeInterceptor : CheckTokenInterceptor() {
    override fun refreshAccessToken(currentAccessToken: String?): String? {
        return runCatching {
            IPC.getInstance().get<IUserAccountProvider>()
                .refreshAccessToken(currentAccessToken)
        }.getOrElse {
            Timber.tag(TAG).e("intercept ipc error $it")
            null
        }
    }

    override fun onRefreshTokenInValid(currentAccessToken: String?) {
        // 游戏进程暂不处理
    }
}

/**
 * 其他进程
 */
class OtherUnpackApiCodeInterceptor : CheckTokenInterceptor() {
    override fun refreshAccessToken(currentAccessToken: String?): String? {
        return null
    }

    override fun onRefreshTokenInValid(currentAccessToken: String?) {
        // 其他进程暂不处理
    }

    override fun getResponseAndHandlerTokenExpired(
        chain: Interceptor.Chain,
        request: Request,
        response: Response,
        code: Int
    ): Response {
        return response
    }
}

abstract class CheckTokenInterceptor : Interceptor, KoinComponent {

    protected val accountInteractor: AccountInteractor by inject()

    companion object {
        const val TAG = "checkcheck_token"
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response: Response = try {
            chain.proceed(request)
        } catch (e: Exception) {
            Timber.tag(TAG).e("${javaClass.simpleName}-intercept getResponse $e")
            throw e
        }
        val httpCode = response.code
        val headers = response.headers
        if (httpCode == RESPONSE_CODE_SUCCESS) {
            // 判断bizCode
            val apiCode = getApiCode(response, "gzip".equals(headers["Content-Encoding"], ignoreCase = true))
            if (apiCode != null) {
                // 潘多拉记录接口错误
                reportIfRequestException(apiCode, request)
                return getResponseAndHandlerTokenExpired(chain, request, response, apiCode)
            }
        }
        // 判断httpCode
        return getResponseAndHandlerTokenExpired(chain, request, response, httpCode)
    }

    protected open fun getResponseAndHandlerTokenExpired(chain: Interceptor.Chain, request: Request, response: Response, code: Int): Response {
        val isRefreshAccessTokenApi = request.url.toString().contains(
            API_URL_REFRESH_ACCESS_TOKEN
        )
        if (!isRefreshAccessTokenApi && code == RESPONSE_CODE_ACCESS_TOKEN_INVALID) {
            Timber.tag(TAG).d("${javaClass.simpleName} checkApiCode ${request.url}, ${code}")
            // 刷新accessToken
            val currentToken = request.headers[API_HEADER_TOKEN]
            val newAccessToken = refreshAccessToken(currentToken)
            return if (newAccessToken == null) {
                onRefreshTokenInValid(currentToken)
                response
            } else {
                response.close()
                chain.proceed(
                    request.newBuilder().header(API_HEADER_TOKEN, newAccessToken).build()
                )
            }
        }
        return response
    }

    private fun reportIfRequestException(code: Int, request: Request) {
        if (code == 0 || code == 200) {
            return
        }
        val url = request.url.toString().substringBefore("?")
        Pandora.monitor(url).finishWithBizCode(code)
        Timber.e("RequestException, url: $url, code: $code")
    }

    private fun getApiCode(response: Response, useGzip: Boolean): Int? {
        val responseBody = response.body!!
        val source = responseBody.source()
        source.request(Long.MAX_VALUE) // Buffer the entire body.
        var buffer = source.buffer

        if (useGzip) {
            GzipSource(buffer.clone()).use { gzippedResponseBody ->
                buffer = Buffer()
                buffer.writeAll(gzippedResponseBody)
            }
        }

        val contentType = responseBody.contentType()
        val charset: Charset =
            contentType?.charset(StandardCharsets.UTF_8) ?: StandardCharsets.UTF_8

        val bodyString = buffer.clone().readString(charset)
        return GsonUtil.gsonSafeParse<ApiCode>(bodyString)?.code
    }

    abstract fun refreshAccessToken(currentAccessToken: String?): String?
    abstract fun onRefreshTokenInValid(currentAccessToken: String?)
}