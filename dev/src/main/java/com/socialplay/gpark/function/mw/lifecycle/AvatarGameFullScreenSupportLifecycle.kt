package com.socialplay.gpark.function.mw.lifecycle

import android.app.Activity
import android.view.View
import com.socialplay.gpark.function.umw.UMW
import timber.log.Timber

/**
 * 用于支持角色游戏的全屏，通过UMW实现
 */
class AvatarGameFullScreenSupportLifecycle(umw: UMW) : BaseAvatarGameLifecycle(umw){

    private var systemUiVisibilityBackup: Int = 0

    override fun onEnterEditModeView(activity: Activity) {
        super.onEnterEditModeView(activity)
        Timber.d("AvatarGameFullScreenLifecycle onEnterEditModeView activity:%s", activity)
        updateActivityFullScreenStatus(activity, true)
    }

    override fun onExitEditModeView(activity: Activity) {
        super.onExitEditModeView(activity)
        Timber.d("AvatarGameFullScreenLifecycle onExitEditModeView activity:%s", activity)
        updateActivityFullScreenStatus(activity, false)
    }

    private fun updateActivityFullScreenStatus(activity: Activity, fullScreen: Boolean) {
        if (fullScreen) {
            this.systemUiVisibilityBackup = activity.window.decorView.systemUiVisibility

            activity.window.decorView.systemUiVisibility =
                activity.window.decorView.systemUiVisibility or (View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                        // Set the content to appear under the system bars so that the
                        // content doesn't resize when the system bars hide and show.
                        or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        or View.SYSTEM_UI_FLAG_FULLSCREEN)

        }
        else {
            activity.window.decorView.systemUiVisibility = systemUiVisibilityBackup
        }
    }

}