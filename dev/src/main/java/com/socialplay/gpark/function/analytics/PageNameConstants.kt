package com.socialplay.gpark.function.analytics


/**
 *  <AUTHOR> taiyuan
 *  time   : 2021/5/25
 *  desc   :
 */
object PageNameConstants {
    const val FRAGMENT_NAME_REMARK_ALERT = "friend_remark_alert"
    const val FRAGMENT_NAME_CHAT_SETTING = "chat_setting"
    const val FRAGMENT_NAME_MAIN = "name_main"
    const val FRAGMENT_NAME_HOME = "name_home"
    const val FRAGMENT_NAME_HOME_CHOICE = "name_home_choice"
    const val FRAGMENT_NAME_PROFILETAB = "name_profiletab"
    const val FRAGMENT_SETTING = "setting"
    const val FRAGMENT_NAME_PROFILETAB_RECENT = "profiletab_recent"
    const val FRAGMENT_NAME_PROFILE_TAB_UGC = "profile_tab_ugc"
    const val FRAGMENT_NAME_GAME_DETAIL = "game_detail"
    const val FRAGMENT_NAME_DEV_CONFIG = "dev_config"
    const val FRAGMENT_NAME_DEV_CONFIG_PANDORA_TOGGLE = "dev_config_pandora_toggle"
    const val FRAGMENT_NAME_DEV_APP_PARAMS = "dev_app_params"
    const val FRAGMENT_NAME_DEV_APP_SIG_INFO = "dev_app_sig_info"
    const val FRAGMENT_NAME_SEARCH = "search"
    const val FRAGMENT_NAME_WEB = "web"
    const val FRAGMENT_NAME_ABOUT_US = "about_us"
    const val FRAGMENT_NAME_LOGIN = "login"
    const val FRAGMENT_NAME_LOGIN_BY_PHONE = "login_by_phone"
    const val FRAGMENT_NAME_ACCOUNT = "account_security"
    const val FRAGMENT_NAME_FRIEND_TAB = "friend_tab"
    const val FRAGMENT_NAME_FRIEND_LIST = "friend_list"
    const val FRAGMENT_NAME_FRIEND_REQUEST_LIST = "friend_request_list"
    const val FRAGMENT_NAME_ADD_FRIEND = "add_friend"
    const val CONVERSATION_PRIVATE = "conversation_private"
    const val CONVERSATION_GROUP_CHAT = "conversation_group_chat"
    const val FRAGMENT_NAME_FRIEND_APPLY = "friend_apply"
    const val FRAGMENT_NAME_FRIEND_SEARCH = "friend_search"
    const val FRAGMENT_NAME_QR_CODE_SCAN = "qr_code_scan"
    const val FRAGMENT_NAME_DEV_MW = "dev_mw"
    const val FRAGMENT_NAME_LOGIN_CONFIRM = "login_confirm"
    const val FRAGMENT_NAME_SIGN_UP = "sign_up"
    const val FRAGMENT_DEVELOPER = "developer"

    const val FRAGMENT_ACCOUNT_EMAIL_VERIFY = "email_verify"
    const val FRAGMENT_ACCOUNT_PASSWORD_SET = "account_password_set"
    const val FRAGMENT_ACCOUNT_PASSWORD_CHANGE = "account_password_change"
    const val FRAGMENT_ACCOUNT_NICKNAME_RESET = "account_nickname_reset"
    const val FRAGMENT_ACCOUNT_CANCELLATION = "cancel_account"
    const val FRAGMENT_NAME_EDIT_PROFILE = "edit_profile"
    const val FRAGMENT_NAME_CONNECTED_ACCOUNTS = "linked_accounts"
    const val FRAGMENT_NAME_ACCOUNT_AND_PSWD_BIND = "account_and_pswd_bind"
    const val FRAGMENT_EDIT_GAME_REVIEW = "game_review"
    const val FRAGMENT_REVIEW_LIST = "review_list"
    const val FRAGMENT_REVIEW_LIST_IN_GAME_DETAIL = "review_list_in_game_detail"
    const val FRAGMENT_NAME_NOTICE_LIST = "notice_list"
    const val FRAGMENT_NAME_OPERATION_NOTICE_LIST = "operation_notice_list"

    const val FRAGMENT_MOBILE_EDITOR_ROLE = "mobile_editor_role"
    const val FRAGMENT_INFORMATION_RESET = "information_reset"
    const val FRAGMENT_CITY_RESET = "city_reset"

    const val FRAGMENT_THIRD_APP_AUTHORIZE = "third_app_authorize"
    const val FRAGMENT_CHOOSE_ROLE = "avatar_choose"
    const val FRAGMENT_ROLE = "role"

    const val FRAGMENT_NAME_FULL_SCREEN_VIDEO_PLAYER = "full_screen_video_player-"

    const val PROFILE_USER_AVATAR = "user_avatar"
    const val FRAGMENT_NEW_CREATE = "create_tab"
    const val FRAGMENT_NEW_CREATE_TEMPLATE = "create_tab_template"
    const val FRAGMENT_NEW_CREATE_CENTER = "create_tab_center"
    const val FRAGMENT_UGC_DETAIL = "ugc_detail"
    const val FRAGMENT_PARTY = "party_tab"
    const val FRAGMENT_RECOMMEND = "recommend_tab"
    const val FRAGMENT_MAPS = "HomeMapsTab"
    const val FRAGMENT_MAPS_TAB_MAPS = "RecommendFragment"
    const val FRAGMENT_MAPS_TAB_NEWEST = "NewGamesFragment"

    const val FRAGMENT_NAME_EDITOR_HOME = "editor_home_tab"
    const val FRAGMENT_NAME_CONVERSATION_LIST = "conversation_list_tab"

    const val FRAGMENT_NAME_REAL_NAME = "realname"
    const val FRAGMENT_NAME_SHOW_REAL_NAME = "show_realname"
    const val FRAGMENT_NAME_CREATE_TAB = "create"
    const val FRAGMENT_NAME_PROFILE_TAB = "profile"
    const val FRAGMENT_NAME_FEED_TAB = "community_feed"
    const val FRAGMENT_NAME_CREATOR_TAB = "kol_creator"
    const val FRAGMENT_NAME_AIBOT_TAB = "AIBot_create"

    const val FRAGMENT_NAME_HOME_ALL_ROOM = "home_all_room"
    const val FRAGMENT_NAME_HOME_CREATE_ROOM = "home_create_room"
    const val FRAGMENT_FEEDBACK = "feedback"
    const val FRAGMENT_USER_FANS_TAB = "user_fans_tab"
    const val FRAGMENT_USER_FANS_ITEM = "user_fans_item"
    const val FRAGMENT_COMMUNITY_FEED = "community_feed"
    const val FRAGMENT_ADD_GAME_CARD = "add_game_card"
    const val FRAGMENT_ADD_CARD_RELATED = "add_card_related"
    const val FRAGMENT_PUBLISH_POST = "publish_post"
    const val FRAGMENT_POST_DETAIL = "post_detail"
    const val FRAGMENT_COTTAGE_ALL_ROOM = "cottage_all_room"

    const val FRAGMENT_STARTUP_CREATE_AVATAR = "startup_create_avatar"
    const val FRAGMENT_STARTUP_ENTER_NAME = "startup_enter_name"
    const val FRAGMENT_STARTUP_SELECT_BIRTHDAY = "startup_select_birthday"

    const val FRAGMENT_PLOT_LIST = "plot_list"
    const val FRAGMENT_MOMENTS = "moments"
    const val FRAGMENT_MOMENTS_MAIN = "moments_main"
    const val FRAGMENT_MOMENTS_LIST = "moments_list"
    const val FRAGMENT_MOMENTS_TYPE = "moments_type"

    const val FRAGMENT_UGC_COMMENT = "ugc_comment"
    const val FRAGMENT_UGC_COMMENT_LIST = "ugc_comment_list"
    const val FRAGMENT_GUIDE_LOGIN = "guide_login"
    const val FRAGMENT_SPLASH = "splash"
    const val FRAGMENT_VIDEO_FEED_TAB = "video_feed_tab"
    const val FRAGMENT_VIDEO_FEED = "video_feed"
    const val FRAGMENT_VIDEO_FEED_WRAPPER = "video_feed_wrapper"
    const val DIALOG_VIDEO_FEED_COMMENT = "video_feed_comment"
    const val FRAGMENT_LANGUAGE_SETTING = "language_setting"

    const val FRAGMENT_PRIVACY_SETTINGS = "privacy_settings"

    const val FRAGMENT_SELECT_MODE = "select_mode"

    const val DIALOG_GAME_WEB = "game_web_dialog"
    const val FRAGMENT_TOPIC_DETAIL_PARENT = "topic_detail_parent"
    const val FRAGMENT_TOPIC_DETAIL_CHILD = "topic_detail_child"
    const val FRAGMENT_TOPIC_SQUARE = "topic_square"

    const val FRAGMENT_RECOMMEND_VIDEO_LIST = "recommend_video_list"
    const val PLOT_CLIP_IMAGE = "plot_clip_image"

    const val FRAGMENT_PROFILE_TAB_CLOTHES = "profile_tab_clothes"
    const val FRAGMENT_APP_REPORT_USER = "report_user"
    const val FRAGMENT_AI_BOT = "ai_bot_conversation"
    const val FRAGMENT_AI_BOT_DETAIL = "ai_bot_detail"

    const val FRAGMENT_CREATOR_BOTTOM_TAB = "kol_creator"
    const val FRAGMENT_NAME_KOL_MORE_UGC_PREFIX = "kol_more_ugc"
    const val FRAGMENT_NAME_KOL_MORE_CREATOR_TYPE_PREFIX = "kol_more_creator_type_"
    const val FRAGMENT_NAME_KOL_MORE_CREATOR_TYPE_PARENT = "kol_more_creator_type_parent"
    const val FRAGMENT_NAME_KOL_MORE_CREATOR_STAR = "kol_more_creator_star"
    const val FRAGMENT_CONTINUE_MANAGE = "continue_manage"
    const val FRAGMENT_EDIT_LINK = "fragment_edit_link"
    const val FRAGMENT_ADD_LINK = "fragment_add_link"
    const val FRAGMENT_AI_BOT_REVIEW_LIST = "fragment_ai_bot_review_list"
    const val FRAGMENT_AI_BOT_CREATE = "fragment_ai_bot_create"
    const val FRAGMENT_AI_BOT_CREATE_RESULT = "fragment_ai_bot_create_result"
    const val FRAGMENT_AI_BOT_CREATE_SELECT = "fragment_ai_bot_create_select"
    const val FRAGMENT_AI_BOT_IMAGE_CLIP = "fragment_ai_bot_image_clip"
    const val FRAGMENT_AI_BOT_GENERATE = "fragment_ai_bot_generate"
    const val FRAGMENT_AI_BOT_IMAGE = "fragment_ai_bot_ad_image"
    const val FRAGMENT_AI_BOT_TEXT = "fragment_ai_bot_ad_text"

    const val DIALOG_GLOBAL_SHARE = "dialog_global_share"
    const val DIALOG_SHARE_ROLE_SCREENSHOTS = "dialog_share_role_screenshots"
    const val FRAGMENT_BIND_ACC_PWD_V7 = "fragment_bind_acc_pwd_v7"
    const val FRAGMENT_UGC_DESIGN_FEED = "fragment_ugc_design_feed"
    const val FRAGMENT_UGC_ASSET_TAB = "fragment_ugc_asset_tab"
    const val FRAGMENT_UGC_DESIGN_DETAIL = "fragment_ugc_design_detail"
    const val FRAGMENT_UGC_DESIGN_EDIT = "fragment_ugc_design_edit"
    const val FRAGMENT_PROFILE_UGC_DESIGN_TAB = "fragment_profile_ugc_design"
    const val FRAGMENT_BUILD_GUIDE = "fragment_build_guide"
    const val FRAGMENT_UGC_ASSET_LIST = "fragment_ugc_asset_list"

    const val DIALOG_RECHARGE_CONFIRM = "dialog_recharge_confirm"

    const val FRAGMENT_BUY_COINS = "wallet"
    const val FRAGMENT_TRANSACTION_DETAILS = "fragment_transaction_details"

    const val FRAGMENT_UGC_MODULE_TAB = "fragment_ugc_module_tab"
    const val FRAGMENT_UGC_MODULE_HOME = "fragment_ugc_module_home"
    const val FRAGMENT_UGC_MODULE_MY_WORK = "fragment_ugc_module_my_work"

    const val FRAGMENT_UGC_ASSET_ROOKIE = "fragment_ugc_asset_rookie"
    const val FRAGMENT_UGC_ASSET_ROOKIE_TAB = "fragment_ugc_asset_rookie_tab"

    const val FRAGMENT_HE_GROUPS = "fragment_he_groups"
    const val FRAGMENT_MY_GROUPS_PARENT = "fragment_my_groups_parent"
    const val FRAGMENT_MY_GROUPS_TYPE_PREFIX = "fragment_my_groups_type_"
    const val FRAGMENT_GROUP_PROFILE = "fragment_group_profile"
    const val FRAGMENT_GROUP_PROFILE_GUEST = "fragment_group_profile_guest"
    const val FRAGMENT_EDIT_GROUP_DESC = "fragment_edit_group_desc"
    const val FRAGMENT_ADD_FRIEND_JOIN_GROUP_PARENT = "fragment_add_friend_join_group_parent"
    const val FRAGMENT_FRIENDS_GROUPS_REQUEST_PARENT = "fragment_friends_groups_request_parent"
    const val FRAGMENT_GROUPS_REQUEST = "fragment_groups_request"
    const val FRAGMENT_JOIN_GROUP_REQUEST = "fragment_join_group_request"
    const val FRAGMENT_GROUP_CHAT_MEMBER_LIST = "fragment_group_chat_member_list"
}