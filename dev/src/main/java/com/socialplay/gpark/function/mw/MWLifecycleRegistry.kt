package com.socialplay.gpark.function.mw

import android.app.Application
import android.content.Context
import android.text.TextUtils
import androidx.core.net.toUri
import androidx.fragment.app.FragmentActivity
import com.bin.cpbus.CpEventBus
import com.meta.biz.mgs.MgsBiz
import com.meta.biz.ugc.UGCProtocolBiz
import com.meta.biz.ugc.listener.IMWFunction
import com.meta.biz.ugc.local.EditorLocalHelper
import com.meta.biz.ugc.model.ActionOnlyMsg
import com.meta.biz.ugc.model.DuplicateImageCallbackMsg
import com.meta.biz.ugc.model.DuplicateImageMsg
import com.meta.biz.ugc.model.EditorLocalMsg
import com.meta.biz.ugc.model.FeatureSupportMsg
import com.meta.biz.ugc.model.GameCommonFeature
import com.meta.biz.ugc.model.GameExitMsg
import com.meta.biz.ugc.model.GameTransform
import com.meta.biz.ugc.model.IMWMsg
import com.meta.biz.ugc.model.IPlatformMsg
import com.meta.biz.ugc.model.MWJumpGameMsg
import com.meta.biz.ugc.model.MWProtocol
import com.meta.biz.ugc.model.NewStartGame
import com.meta.biz.ugc.model.RebootStartGame
import com.meta.biz.ugc.model.SaveImage2Gallery
import com.meta.biz.ugc.protocol.UGCProtocolReceiver
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolReceiveConstants
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.meta.biz.ugc.protocol.listener.SingleProtocolListener
import com.meta.ipc.IPC
import com.meta.lib.mwbiz.MWBiz
import com.meta.lib.mwbiz.MWBizBridge
import com.meta.lib.mwbiz.MWBizConst
import com.meta.verse.VerseLog
import com.meta.verse.lib.MetaVerseCore
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.interactor.LifecycleInteractor
import com.socialplay.gpark.data.interactor.MgsInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.event.UpdateFollowEvent
import com.socialplay.gpark.data.model.event.ts.DuplicateImageEvent
import com.socialplay.gpark.data.model.realname.MWLifecycleWrapper
import com.socialplay.gpark.data.model.user.RelationType
import com.socialplay.gpark.function.ad.AdProxy
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.observer.GameCrashHostObserve
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.ipc.provider.host.MWIPCFuncProviderImpl
import com.socialplay.gpark.function.ipc.provider.host.MW_FUNC_PROVIDER_NAME
import com.socialplay.gpark.function.ipc.provider.host.OnMWIPCFuncProvider
import com.socialplay.gpark.function.mgs.MgsDialogManager
import com.socialplay.gpark.function.mw.GameCommonFeatureResolver.duplicateImage
import com.socialplay.gpark.function.mw.GameCommonFeatureResolver.saveImage2Gallery
import com.socialplay.gpark.function.mw.MWLifecycleRegistry.handleGameJumpGame
import com.socialplay.gpark.function.mw.feature.GameWebViewFeature
import com.socialplay.gpark.function.mw.lifecycle.GameCrashGameLifeCycle
import com.socialplay.gpark.function.mw.lifecycle.GameDialogLifecycle
import com.socialplay.gpark.function.mw.lifecycle.GameTimeLifecycle
import com.socialplay.gpark.function.mw.lifecycle.InGameLifecycle
import com.socialplay.gpark.function.mw.lifecycle.LaunchResultLifeCycle
import com.socialplay.gpark.function.mw.lifecycle.MWLifecycle
import com.socialplay.gpark.function.mw.lifecycle.MgsFloatNoticeLifecycle
import com.socialplay.gpark.function.mw.lifecycle.PlotChoiceFriendsLifecycle
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.pay.PayProvider
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.startup.StartupProcessType
import com.socialplay.gpark.function.umw.UMWLifecycleRegistry
import com.socialplay.gpark.function.umw.impl.ts.UMWTSImpl
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivityArgs
import com.socialplay.gpark.ui.floatingball.FloatingBallViewLifecycle
import com.socialplay.gpark.ui.gamepay.GamePayLifecycle
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.ui.mgs.MgsFloatViewLifecycle
import com.socialplay.gpark.ui.mgs.listener.OnMgsQuitGameDialogListener
import com.socialplay.gpark.ui.mgs.mw.GameImLifecycle
import com.socialplay.gpark.ui.permission.TSGamePermission
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.simJsonObj
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.HttpUrl.Companion.toHttpUrl
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.io.File


/**
 * xingxiu.hou
 * 2022/2/11
 */

object MWLifecycleRegistry {
    val UE_ACTION_BUILD_GUIDE_UGC =  MWProtocol("ue.action.build.guide.ugc", "UGC获取是否需要展示引导")
    val UE_ACTION_BUILD_GUIDE_UGC_FINISH = MWProtocol("ue.action.build.guide.ugc.finish", "UGC的新手引导进行完成")
    val BRIDGE_ACTION_BUILD_GUIDE_UGC = MWProtocol("bridge.action.build.guide.ugc", "给UGC传递是否开启引导的标识")

    private const val TAG = "MWLifecycleRegistry:: %s"

    private val metaRepository: IMetaRepository by lazy { GlobalContext.get().get() }
    private val mgsInteractor: MgsInteractor by lazy { GlobalContext.get().get() }

    private val mainScope = MainScope()

    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }
    private val payInteractor: IPayInteractor by lazy { GlobalContext.get().get() }

    /**
     * 获取MWLifecycle拦截器
     */
    internal fun getOnMWGameLifecycleInterceptor(
        app: Application,
        onQuit: (() -> Unit)? = null,
    ): OnMWGameLifecycleInterceptor {
        return object : OnMWGameLifecycleInterceptor {
            override fun getPackageName(): String {
                return MWBizBridge.currentGamePkg()
            }

            override fun getAppName(): String {
                return MWBizBridge.currentGameName()
            }

            override fun quitGame(context: Context) {
                onQuit?.invoke()
            }

            override fun isGameProcessName(processName: String): Boolean {
                return processName == "${app.packageName}:m"
            }

            override fun getGameId(): String {
                return MWBizBridge.currentGameId()
            }

            override fun gameAttribute(): Map<String, String> {
                return MWBizBridge.gameAttribute()
            }

        }
    }

    /**
     * 悬浮球生命周期
     */
    private fun getFloatingBallViewLifecycle(
        app: Application,
        onQuit: () -> Unit,
    ): MWLifecycle {
        return FloatingBallViewLifecycle(app, app, getOnMWGameLifecycleInterceptor(app, onQuit))
    }

    /**
     * 悬浮球生命周期
     */
    private fun getMgsFloatingBallViewLifecycle(
        app: Application,
        onQuit: () -> Unit,
    ): MWLifecycle {
        return MgsFloatViewLifecycle(app, app, metaKV, true).apply {
            onMWGameLifecycleInterceptor = getOnMWGameLifecycleInterceptor(app, onQuit)
        }
    }

    /**
     * 注册生命周期
     */
    fun register(app: Application) {
        val umwLifecycleList = initUMW(app)

        //获取当前游戏信息
        val gameId = MWBizBridge.currentGameId()
        //游戏内游戏时长统计
        val gameTimeLifecycle = GameTimeLifecycle(app, getOnMWGameLifecycleInterceptor(app))
        defineBasicFunc()
        registerMWMsgActions()
        CpEventBus.register(this)

        //构建退出游戏回调
        val exitCaller = MWGameExitCaller().apply {
            setOnBefore { gameTimeLifecycle.dealLeaveGame(MWBizBridge.currentGameId(), MWBizBridge.currentGamePkg()) }
            setOnAfter {

                val pkg = MWBizBridge.currentGamePkg()
                val id = MWBizBridge.currentGameId()
                MgsBiz.exitGame(id)
                GameCrashHostObserve.recordGameOnPause(pkg, id, true)
                MetaRouter.Main.floatBallGameBack(app, id)
                delay(500)
                MWBiz.exit()
            }
        }

        val floatingBallLifecycle = getMgsFloatingBallViewLifecycle(app, exitCaller.onFloatExitClick())
        val plotChoiceFriendsLifecycle = PlotChoiceFriendsLifecycle()
        val lifecycles = listOf(
            InGameLifecycle.apply {
                init(app, getOnMWGameLifecycleInterceptor(app))
            }, gameTimeLifecycle, floatingBallLifecycle, LaunchResultLifeCycle(getOnMWGameLifecycleInterceptor(app)),//游戏拉起结果记录
            GameCrashGameLifeCycle(StartupProcessType.M, getOnMWGameLifecycleInterceptor(app)),//检测是否异常退出
            MgsFloatNoticeLifecycle(app, getOnMWGameLifecycleInterceptor(app)), GamePayLifecycle, plotChoiceFriendsLifecycle, GameDialogLifecycle(false), GameImLifecycle.init(false)
        ) + umwLifecycleList + MWLifecycleWrapper.list(app)
        TSGamePermission.initialize()
        applicationLifecycle(app, lifecycles)
        registerLifecycle(lifecycles)
        GameWebViewFeature.init(app)
        //处理 MW Action
        MWBiz.addQuitCallback(exitCaller.callExit())

        //游戏页面点击返回监听
        MWBiz.setOnGameKeyBackListener {
            VerseLog.d("MW BRIDGE_ACTION_UE_KEY_BACK")
            MgsDialogManager.showQuitGame(it, app, object : OnMgsQuitGameDialogListener {
                override fun onLeftClick() {
                    exitCaller.onFloatExitClick().invoke()
                }

                override fun onRightClick() {
                }
            })
            true
        }

        addProtocolObserver(app, exitCaller, gameTimeLifecycle, plotChoiceFriendsLifecycle)
        MWBizBridge.addOnMWMsgCallback {
            runCatching {
                JSONObject().put("action", it.action).put("data", it.data.simJsonObj()).put("channel", it.channel).put("message", it.message).put("status", it.status).put("messageId", it.messageId).toString().let {
                    UGCProtocolReceiver.dispatchUECall(it)
                }
            }.getOrElse {
                Timber.e(TAG, "addOnMWMsgCallback error! ${it}")
            }
            Timber.d(TAG, "addOnMWMsgCallback Received! ${it.action}")
        }
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<IMWMsg>(ProtocolReceiveConstants.PROTOCOL_INVOKE_COMMON_PARAMS) {
            override fun handleProtocol(message: IMWMsg?, messageId: Int) {
                MWFeatureCommonParams.invokeCommonParams(messageId)
            }
        })
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<IMWMsg>(ProtocolReceiveConstants.PROTOCOL_REFRESH_ACCESS_TOKEN) {
            override fun handleProtocol(message: IMWMsg?, messageId: Int) {
                MWFeatureCommonParams.handleRefreshAccessToken(messageId)
            }
        })
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<FeatureSupportMsg>(
            ProtocolReceiveConstants.PROTOCOL_MEMBER_DETECT
        ) {
            override fun handleProtocol(message: FeatureSupportMsg?, messageId: Int) {
                message?.apply {
                    MWFeatureSupport.isSupportFeature(this.feature, messageId, false)
                }
            }
        })
        // 打开充值
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<ActionOnlyMsg>(
            GamePayLifecycle.IAP_TS_PAY_RECHARGE
        ) {
            override fun handleProtocol(message: ActionOnlyMsg?, messageId: Int) {
                if (PayProvider.ENABLE_RECHARGE) {
                    val curActivity = LifecycleInteractor.activityRef?.get() ?: return
                    val navHostFragment = if (curActivity is MainActivity) {
                        curActivity.findNavHostFragment()
                    } else {
                        null
                    }
                    mainScope.launch {
                        MetaRouter.Pay.goBuyCoinsPage(
                            curActivity.application,
                            navHostFragment,
                            "game_dire"
                        )
                    }
                } else {
                    ToastUtil.showShort(R.string.under_development)
                }
            }
        })
        // 是否关注玩家
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<ActionOnlyMsg>(ProtocolReceiveConstants.UE_ACTION_IS_FOLLOW_PLAYER) {
            override fun handleProtocol(message: ActionOnlyMsg?, messageId: Int) {
                val uuid = message?.rawData?.get("uuid") ?: return
                callbackUeIsFollow(uuid.toString(), messageId)
            }
        })
        // 关注|取消关注玩家协议
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<ActionOnlyMsg>(ProtocolReceiveConstants.UE_ACTION_FOLLOW_PLAYER) {
            override fun handleProtocol(message: ActionOnlyMsg?, messageId: Int) {
                message ?: return
                callbackUeFollowStatus(message, messageId)
            }
        })
        // 打开用户首页
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<ActionOnlyMsg>(ProtocolReceiveConstants.UE_ACTION_OPEN_PLAYER_HOME) {
            override fun handleProtocol(message: ActionOnlyMsg?, messageId: Int) {
                message?.apply {
                    val uuid = message.rawData.get("uuid") ?: return
                    MetaRouter.Main.dispatchUrl(GlobalContext.get().get<Context>(), MetaRouter.Main.buildJumpUri(MetaDeepLink.ACTION_USER_HOME, mapOf(MetaDeepLink.PARAM_USER_ID to uuid)), LinkData.SOURCE_HOST_MW)
                }
            }
        })

        // 选择单张图片复制到指定路径
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<DuplicateImageMsg>(ProtocolReceiveConstants.UE_ACTION_DUPLICATE_IMAGE) {
            override fun handleProtocol(message: DuplicateImageMsg?, messageId: Int) {
                if (message != null) {
                    message.duplicateImage(LifecycleInteractor.activityRef?.get() as? FragmentActivity, messageId)
                } else {
                    UGCProtocolSender.sendProtocol(
                        ProtocolSendConstant.BRIDGE_ACTION_DUPLICATE_IMAGE, messageId, DuplicateImageCallbackMsg(
                            message = "protocol parsing result is null",
                            code = 1001,
                            imagePath = null,
                        )
                    )
                }
            }
        })

        // 保存单张图片到图库
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<SaveImage2Gallery>(ProtocolReceiveConstants.UE_ACTION_SAVE_IMAGE_TO_GALLERY) {
            override fun handleProtocol(message: SaveImage2Gallery?, messageId: Int) {
                if (message != null) {
                    message.saveImage2Gallery(
                        LifecycleInteractor.activityRef?.get() as? FragmentActivity, messageId
                    )
                } else {
                    UGCProtocolSender.sendProtocol(
                        ProtocolSendConstant.BRIDGE_ACTION_SAVE_IMAGE_TO_GALLERY, messageId, DuplicateImageCallbackMsg(
                            message = "protocol parsing result is null",
                            code = 1001,
                            imagePath = null,
                        )
                    )
                }
            }
        })
    }

    @Subscribe
    fun onDuplicateImageResult(event: DuplicateImageEvent) {
        UGCProtocolSender.sendProtocol(
            ProtocolSendConstant.BRIDGE_ACTION_DUPLICATE_IMAGE, event.messageId, DuplicateImageCallbackMsg(
                message = event.message,
                code = event.code,
                imagePath = event.path,
            )
        )
    }

    private fun callbackUeFollowStatus(message: ActionOnlyMsg, messageId: Int) {
        val uuid = message.rawData.get("uuid") ?: return
        val isFollow = message.rawData.get("isFollow").toString().toBooleanStrictOrNull() ?: return
        mainScope.launch {
            runCatching {
                val repository = GlobalContext.get().get<IMetaRepository>()
                if (isFollow) {
                    repository.relationAdd(uuid.toString(), RelationType.Follow.value)
                } else {
                    repository.relationDel(uuid.toString(), RelationType.Follow.value)
                }.collect {
                    UGCProtocolSender.sendProtocol(ProtocolSendConstant.BRIDGE_ACTION_FOLLOW_CALLBACK, messageId, object : IPlatformMsg() {
                        override fun addJsonData(data: MutableMap<String, Any>) {
                            data["isFollow"] = isFollow
                            data["uuid"] = uuid
                            data["code"] = if (it.data == true) 200 else it.code ?: -1
                            data["message"] = it.message ?: ""
                        }
                    })
                }
            }
        }
    }

    private fun callbackUeIsFollow(uuid: String, messageId: Int) {
        mainScope.launch {
            runCatching {
                GlobalContext.get().get<IMetaRepository>().queryUserProfile(uuid).collect {
                    UGCProtocolSender.sendProtocol(ProtocolSendConstant.BRIDGE_ACTION_IS_FOLLOW_CALLBACK, messageId, object : IPlatformMsg() {
                        override fun addJsonData(data: MutableMap<String, Any>) {
                            data["code"] = it.code ?: 200
                            data["message"] = it.message ?: ""
                            data["uuid"] = uuid
                            data["isFollow"] = it.data?.isFollow ?: false
                        }
                    })
                }
            }

        }
    }

    private fun initUMW(application: Application): MutableList<MWLifecycle> {
        val lifecycleList: MutableList<MWLifecycle> = mutableListOf()

        UMWLifecycleRegistry(application, object : UMWTSImpl() {
            override fun registerLifecycle(lifecycle: MWLifecycle) {
                lifecycleList.add(lifecycle)
            }
        }).init()

        return lifecycleList
    }

    private fun registerMWMsgActions() {
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_TS_GAME_FEATURE)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_TEMPLATE_GAME)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_EXIT_MW)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_MEMBER_DETECT)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_PAY_START)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_PAY_GET_ARK)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_PAY_FINISH)
        UGCProtocolReceiver.registerProtocol(GamePayLifecycle.IAP_TS_PAY_RECHARGE)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_MEMBER_INFO)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_MEMBER_RECHARGE)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.UE_ACTION_IS_FOLLOW_PLAYER)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.UE_ACTION_FOLLOW_PLAYER)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.UE_ACTION_OPEN_PLAYER_HOME)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.UE_ACTION_DUPLICATE_IMAGE)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.UE_ACTION_SAVE_IMAGE_TO_GALLERY)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_ACTION_GAME_IM_SDK)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_ACTION_GAME_MGS_LOAD_END)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_ACTION_GAME_PERMISSION)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_ACTION_GAME_RECORD)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_ACTION_GAME_SHARE_ROOM)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_ACTION_GAME_CIRCLE)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_INVOKE_COMMON_PARAMS)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_REFRESH_ACCESS_TOKEN)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_REPORT)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_GAME_ROOM_SHARE_FEATURE)
        UGCProtocolReceiver.registerProtocol(ProtocolReceiveConstants.PROTOCOL_MGS_INPUT_OPEN)
        UGCProtocolReceiver.registerProtocol(UE_ACTION_BUILD_GUIDE_UGC)
        UGCProtocolReceiver.registerProtocol(UE_ACTION_BUILD_GUIDE_UGC_FINISH)
    }

    private fun addProtocolObserver(
        context: Context,
        exitCaller: MWGameExitCaller,
        gameTimeLifecycle: GameTimeLifecycle,
        plotLifecycle: PlotChoiceFriendsLifecycle,
    ) {
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<GameExitMsg>(ProtocolReceiveConstants.PROTOCOL_EXIT_GAME_RESPONSE) {
            override fun handleProtocol(message: GameExitMsg?, messageId: Int) {
                exitCaller.onReceiverMWExit()
            }
        })
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<RebootStartGame>(ProtocolReceiveConstants.PROTOCOL_REBOOT_START_GAME) {
            override fun handleProtocol(message: RebootStartGame?, messageId: Int) {
                message?.apply {
                    Timber.d(
                        MWIPCFuncProviderImpl.TAG, "RebootStartGame Received! ${message.gameId}"
                    )
                    runCatching {
                        Timber.d(MWIPCFuncProviderImpl.TAG, "IPC Remote : Call")
                        val provider: OnMWIPCFuncProvider = IPC.getInstance().get(MW_FUNC_PROVIDER_NAME)
                        provider.rebootStartGame(message.gameId, roomIdFromCp, inviteOpenId, gameType, gamePkg)
                        Timber.d(MWIPCFuncProviderImpl.TAG, "IPC Remote : Call End")
                    }.getOrElse {
                        Timber.d(MWIPCFuncProviderImpl.TAG, "IPC Remote Error: $it")
                    }
                    //退出游戏
                    exitCaller.callExit().invoke()
                }
            }
        })
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<NewStartGame>(ProtocolReceiveConstants.PROTOCOL_OPEN_GAME) {
            override fun handleProtocol(message: NewStartGame?, messageId: Int) {
                message?.apply {
                    Timber.d("NewStartGame ${this.gameId}")
                    runCatching {
                        Timber.d("NewStartGame : Call")
                        val currentGamePkg = MWBizBridge.currentGamePkg()
                        val roomId = mgsInteractor.getMgsRoomParams()?.roomIdFromCp ?: roomIdFromCp
                        startRoleGame(
                            context, currentGameId, currentGamePkg, this.gameId, roomId, inviteOpenId, gameType, gameParams, gamePkg
                        )
                        Timber.d("NewStartGame : Call End")
                    }.getOrElse {
                        Timber.d("NewStartGame Error: $it")
                    }
//                    //退出游戏
//                    exitCaller.callExit().invoke()
//
//                    // 推迟游戏退出检查，防止因为主动在MW中切换游戏被误判位闪退
//                    kotlin.runCatching {
//                        MWBizBridge.currentGamePkg()
//                    }.getOrNull()?.let {
//                        GameQuitObserver.delayExitCheckUntilGameResumedAgain(it)
//                    }
                }
            }
        })

        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<MWJumpGameMsg>(ProtocolReceiveConstants.PROTOCOL_GAME_JUMP) {
            override fun handleProtocol(message: MWJumpGameMsg?, messageId: Int) {
                message?.apply {
                    AdProxy.checkIsPreloadAd(getGameId(), getPackageName())
                    handleGameJumpGame(this, gameTimeLifecycle)
                }
            }
        })
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<GameCommonFeature>(ProtocolReceiveConstants.PROTOCOL_TS_GAME_FEATURE) {
            override fun handleProtocol(message: GameCommonFeature?, messageId: Int) {
                message?.apply {
                    GameCommonFeatureResolver.addGameCommonReceiver(
                        context,
                        this,
                        messageId,
                        MWBizBridge.currentGameId(),
                        MWBizBridge.currentGamePkg(),
                        plotLifecycle,
                        getGameResIdBean(MWBizBridge.currentGamePkg()),
                        exitCaller
                    )
                }
            }
        })

        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<EditorLocalMsg>(ProtocolReceiveConstants.PROTOCOL_TEMPLATE_GAME) {
            override fun handleProtocol(message: EditorLocalMsg?, messageId: Int) {
                message?.apply {
                    EditorLocalMVCallback.handlerEditorLocal(this, messageId)
                }
            }
        })

        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<ActionOnlyMsg>(ProtocolReceiveConstants.UE_ACTION_GAME_WEBVIEW) {
            override fun handleProtocol(message: ActionOnlyMsg?, messageId: Int) {
                GameWebViewFeature.featGameWebViewMessage(messageId)
            }
        })

        UGCProtocolReceiver.addProtocolObserver(
            object :
                SingleProtocolListener<ActionOnlyMsg>(UE_ACTION_BUILD_GUIDE_UGC) {
                override fun handleProtocol(message: ActionOnlyMsg?, messageId: Int) {
                    // UGC游戏内来获取是否要开启引导
                    UGCProtocolSender.sendProtocol(
                        BRIDGE_ACTION_BUILD_GUIDE_UGC,
                        messageId,
                        mapOf("isNeedGuide" to metaKV.appKV.guideUgcIsNeed)
                    )
                }
            }
        )

        UGCProtocolReceiver.addProtocolObserver(
            object :
                SingleProtocolListener<ActionOnlyMsg>(UE_ACTION_BUILD_GUIDE_UGC_FINISH) {
                override fun handleProtocol(message: ActionOnlyMsg?, messageId: Int) {
                    // UGC游戏内引导完成，通知我们改变状态
                    metaKV.appKV.guideUgcIsNeed = false
                }
            }
        )
    }

    //启动角色游戏
    private fun startRoleGame(context: Context, currentGameId: String, currentGamePkg: String, gameId: String, roomIdFromCp: String, inviteOpenId: String, gameType: Int, gameParams: String, gamePkg: String) {
        val roleGameId = metaKV.tsKV.roleGameId
        Timber.d("startRoleGame $gameId $roleGameId")
        //判断是否是角色的Id
        if (TextUtils.equals(gameId, metaKV.tsKV.roleGameId)) {
            val status = runCatching { JSONObject(gameParams).optString("status") }.getOrElse { GameTransform.STATUS_ROLE_VIEW }
            if (status == GameTransform.STATUS_ROLE_EDIT) {
                val args = FullScreenEditorActivityArgs(
                    status = status, fromGameId = currentGameId, fromGamePkg = currentGamePkg, targetGameId = gameId, targetGamePkg = gamePkg, categoryId = CategoryId.JUMP_GAME_DIF_PROCESS, roomIdFromCp = roomIdFromCp, inviteOpenId = inviteOpenId
                )
                val resIdBean = saveRoleResIdBean(CategoryId.JUMP_GAME_DIF_PROCESS, gameId).setParamExtra(gameId)
                Analytics.track(EventConstants.EVENT_GAME_AVATAR_LAUNCH) {
                    putAll(ResIdUtils.getAnalyticsMap(resIdBean))
                    put("from", "jump_game_difprocess")
                    put("from_gameid", currentGameId)
                }
                MetaRouter.MobileEditor.fullScreenRole(context, args)
            } else {
                Timber.d("game jump status not right : $status")
            }
        } else {
            Timber.d("not support gameid jump : $gameId")
        }
    }

    private fun saveRoleResIdBean(categoryId: Int, gameId: String): ResIdBean {
        val resIdBean = ResIdBean().setGameId(gameId).setClickGameTime(System.currentTimeMillis()).setCategoryID(categoryId)
        metaKV.analytic.saveLaunchResIdBean(gameId, resIdBean)
        return resIdBean
    }

    private fun defineBasicFunc() {
        UGCProtocolBiz.setFuncListener(object : IMWFunction {
            override fun callUE(json: String): String {
                return MetaVerseCore.bridge().callUE(json)
            }

            override fun registerAction(action: String) {
                MWBizBridge.registerMWMsgAction(action)
            }
        })
    }

    private fun handleGameJumpGame(msg: MWJumpGameMsg, gameTimeLifecycle: GameTimeLifecycle) {
        msg.apply {
            val map = mutableMapOf<String, Any>(
                "jumpout_gameid" to MWBizBridge.currentGameId(), "jumpin_gameid" to getGameId()
            )
            val currentGameId = MWBizBridge.currentGameId()
            val currentPackageName = MWBizBridge.currentGamePkg()
            // 保证在游戏跳游戏前，上个游戏的游戏时长能够发送，放在前面因为如果跳编辑态会有读文件耗时操作，导致gameId与packageName改变
            val resIdBean = ResIdBean().setCategoryID(getShowCategoryId()).setTsType(getUgcType()).setGameCode(getGameCode()).setGameVersionName(msg.getVersion())
            mainScope.launch {
                gameTimeLifecycle.dealLeaveGame(currentGameId, currentPackageName)
                if (msg.getUgcType() == ResIdBean.TS_TYPE_LOCAL && msg.getUgcPath().isNotEmpty()) {
                    resIdBean.setPath(msg.getUgcPath())
                    withContext(Dispatchers.IO) {
                        EditorLocalHelper.getEditorConfigEntity(EditorLocalHelper.getJsonFile(File(msg.getUgcPath())))?.fileId?.let {
                            resIdBean.setFileId(it)
                        }
                    }
                } else if (msg.getUgcType() == ResIdBean.TS_TYPE_UCG) {
                    // 更新ugc数据库
                    GlobalContext.get().get<IMetaRepository>().insertUgcPlayedGame(msg.getGameId(), true)
                }
                map.putAll(ResIdUtils.getAnalyticsMap(resIdBean))
                Analytics.track(EventConstants.JUMP_GAME_IN_GAME_PROCESS) { putAll(map) }
                metaKV.tsKV.setMWServerType(getGameId(), getMWServerType())
                metaKV.analytic.saveLaunchResIdBeanWithId(getGameId(), resIdBean)
                metaKV.analytic.saveLaunchResIdBean(getPackageName(), resIdBean)
                gameTimeLifecycle.dealJumpGame(getGameId())
            }
        }
    }


    private fun getGameResIdBean(pkg: String): ResIdBean {
        return metaKV.analytic.getLaunchResIdBean(pkg) ?: ResIdBean.newInstance()
    }

    private fun isMgsGame(gamePkg: String): Boolean {
        return metaKV.mgsKV.getMgsGameConfigMap()?.get(gamePkg)?.gameId?.isNotEmpty() ?: false
    }

    /**
     * 映射生命周期
     */
    private fun applicationLifecycle(app: Application, life: List<MWLifecycle>) {
        Timber.d("MWLifecycleRegistry::%s", "onApplicationCreated")
        life.forEach {
            it.onBeforeApplicationCreated(app)
        }
        life.forEach { it.onAfterApplicationCreated(app) }
    }

    /**
     * 映射生命周期
     */
    private fun registerLifecycle(lifecycle: List<MWLifecycle>) {
        MWBiz.addActivityLifeCallback(MWBizConst.AL.ON_CREATE) { activity ->
            Timber.d("MWLifecycleRegistry::%s", "ON_CREATE")
            lifecycle.onEach {
                it.onActivityCreated(activity)
            }
        }
        MWBiz.addActivityLifeCallback(MWBizConst.AL.ON_START) { activity ->
            Timber.d("MWLifecycleRegistry::%s", "ON_START")
            lifecycle.onEach {
                it.onActivityStarted(activity)
            }
        }
        MWBiz.addActivityLifeCallback(MWBizConst.AL.ON_RESUME) { activity ->
            Timber.d("MWLifecycleRegistry::%s", "ON_RESUME")
            lifecycle.onEach {
                it.onActivityResumed(activity)
            }
        }
        MWBiz.addActivityLifeCallback(MWBizConst.AL.ON_PAUSE) { activity ->
            Timber.d("MWLifecycleRegistry::%s", "ON_PAUSE")
            lifecycle.onEach {
                it.onActivityPaused(activity)
            }
        }
        MWBiz.addActivityLifeCallback(MWBizConst.AL.ON_STOP) { activity ->
            Timber.d("MWLifecycleRegistry::%s", "ON_STOP")
            lifecycle.onEach {
                it.onActivityStopped(activity)
            }
        }
        MWBiz.addActivityLifeCallback(MWBizConst.AL.ON_DESTROY) { activity ->
            Timber.d("MWLifecycleRegistry::%s", "ON_DESTROY")
            lifecycle.onEach {
                it.onActivityDestroyed(activity)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: UpdateFollowEvent) {
        callbackUeIsFollow(event.uuid, 0)
    }

}