package com.socialplay.gpark.function.analytics

import android.annotation.SuppressLint
import com.meta.pandora.data.entity.Event

// 因为Release版本会去掉这些描述，所以可以安全抑制
@SuppressLint("ChineseStringLiteral")
object EventConstants {
    @EventDesc("App主进程启动")
    val EVENT_MAIN_PROCESS_START = Event("c_event_main_process_start")

    @EventDesc("页面_展示")
    val EVENT_SHOW_PAGE = Event("c_event_show_page")

    @EventDesc("使用233App的时间")
    val EVENT_APP_PAGE_TIME = Event("app_page_time")

    @EventDesc("app壳子进程统计时长(主进程在前台时长, 剔除广告与其他)")
    val EVENT_APP_TIME = Event("c_app_time")

    @EventDesc("玩游戏的时间")
    val EVENT_PLAY_GAME = Event("c_play_game")

    // 逻辑同play_game
    @EventDesc("玩本地工程游戏的时间")
    val EVENT_DRAFT_PLAY_TIME = Event("draft_play_time")

    @EventDesc("悬浮球_点击展开")
    val EVENT_SUSPENSIONBALL_OPEN_CLICK = Event("c_suspensionball_open_click")

    @EventDesc("悬浮球-收起")
    val EVENT_SUSPENSIONBALL_FOLD = Event("c_suspensionball_fold")

    @EventDesc("悬浮球_退出游戏_点击")
    val EVENT_SUSPENSIONBALL_QUIT_GAME_CLICK =
        Event("c_suspensionball_quit_game_click")

    @EventDesc("图标点击")
    val EVENT_ITEM_CLICK = Event("c_item_click")

    @EventDesc("图标展示")
    val EVENT_ITEM_SHOW = Event("c_item_show")

    @EventDesc("进入游戏详情页")
    val EVENT_APP_DETAIL = Event("c_app_detail")

    @EventDesc("点击开始游戏按钮")
    val EVENT_CLICK_DOWNLOAD = Event("c_click_download")

    @EventDesc("资源下载")
    val EVENT_DOWNLOAD = Event("c_download")

    @EventDesc("开始下载游戏")
    val EVENT_DOWNLOAD_START = Event("c_download_start")

    @EventDesc("开始拉起游戏")
    val EVENT_START_LAUNCH_GAME = Event("c_event_start_launch_game")

    @EventDesc("游戏拉起成功")
    val EVENT_LAUNCH_GAME_SUCCESS = Event("c_event_launch_game_success")

    @EventDesc("游戏拉起有效成功")
    val EVENT_LAUNCH_GAME_EFFECTIVE = Event("c_event_play_game_status")

    @EventDesc("游戏疑似闪退")
    val EVENT_GAME_CRASH = Event("c_event_game_crash")

    @EventDesc("233启动时间,bootCostTime启动时长毫秒")
    val EVENT_APP_BOOT_COST_TIME = Event("c_event_app_boot_cost_time")

    @EventDesc("更新游戏点击")
    val UPDATE_GAME_CLICK = Event("c_update_game_click")

    @EventDesc("更新游戏开始")
    val UPDATE_GAME_START = Event("c_update_game_start")

    @EventDesc("更新游戏完成")
    val UPDATE_GAME_FINISH = Event("c_update_game_finish")

    @EventDesc("更新游戏失败")
    val UPDATE_GAME_FAIL = Event("c_update_game_fail")

    @EventDesc("tab点击")
    val EVENT_TAB_CLICK = Event("c_tab_click")

    @EventDesc("站外链接入口曝光次数")
    val EVENT_LINK_SHOW = Event("offsitelink_entrance_show")

    @EventDesc("站外链接点击次数")
    val EVENT_LINK_CLICK = Event("offsitelink_click")

    /**
     * 我的游戏
     */
    @EventDesc("我的游戏_删除弹窗展示")
    val EVENT_MY_GAME_DELETE_SHOW = Event("c_mygame_delete_show")

    @EventDesc("我的游戏_删除弹窗点击")
    val EVENT_MY_GAME_DELETE_CLICK = Event("c_mygame_delete_click")

    @EventDesc("我的游戏_进入批量操作")
    val EVENT_MY_GAME_PI_LIANG_CAO_ZUO = Event("c_mygame_piliangcaozuo")

    @EventDesc("我的游戏_批量操作勾选")
    val EVENT_MY_GAME_PI_LIANG_CAO_ZUO_SELECT = Event("c_mygame_piliangcaozuo_select")

    @EventDesc("我的游戏_批量操作取消勾选")
    val EVENT_MY_GAME_PI_LIANG_CAO_ZUO_SELECT_CANCEL =
        Event("c_mygame_piliangcaozuo_select_cancel")

    @EventDesc("我的游戏_批量操作全选")
    val EVENT_MY_GAME_PI_LIANG_CAO_ZUO_SELECT_ALL =
        Event("c_mygame_piliangcaozuo_select_all")

    @EventDesc("我的游戏_批量操作删除")
    val EVENT_MY_GAME_PI_LIANG_CAO_ZUO_DELETE = Event("c_mygame_piliangcaozuo_delete")

    @EventDesc("批量操作_删除弹框展示")
    val EVENT_MY_GAME_PI_LIANG_CAO_ZUO_POPUP_SHOW =
        Event("c_mygame_piliangcaozuo_popup_show")

    @EventDesc("批量操作_删除弹框取消")
    val EVENT_MY_GAME_PI_LIANG_CAO_ZUO_POPUP_CANCEL =
        Event("c_mygame_piliangcaozuo_popup_cancel")

    @EventDesc("批量操作_删除弹框删除")
    val EVENT_MY_GAME_PI_LIANG_CAO_ZUO_POPUP_DELETE =
        Event("c_mygame_piliangcaozuo_popup_delete")

    @EventDesc("用户协议点击")
    val EVENT_USER_AGREEMENT_CLICK = Event("c_user_agreement_click")

    @EventDesc("隐私协议点击")
    val EVENT_PRIVACY_AGREEMENT_CLICK = Event("c_privacy_agreement_click")

    @EventDesc("儿童保护指引点击")
    val EVENT_CHILDREN_PROTOCOL_CLICK = Event("c_children_protocol_click")

    @EventDesc("设置_实名认证点击")
    val EVENT_REALNAME_NEED_CLICK = Event("c_realname_need_click")

    @EventDesc("设置_第三方SDK目录点击")
    val EVENT_THIRD_SDK_CLICK = Event("c_third_sdk_click")

    /**
     * TODO, 埋点还没定
     */
    @EventDesc("设置_群聊服务条款点击")
    val EVENT_GROUP_CHAT_AGREEMENT_CLICK = Event("c_group_chat_agreement_click")

    @EventDesc("设置_侵权投诉指引点击")
    val EVENT_COMPLAINING_INFRINGEMENT_CLICK = Event("c_complaining_infringement_click")

    @EventDesc("设置_免责声明点击")
    val EVENT_DISCLAIMER_CLICK = Event("c_disclaimer_click")

    @EventDesc("设置_备案号点击")
    val EVENT_FILING_NUMBER_CLICK = Event("c_filing_number_click")

    @EventDesc("关于我们点击")
    val EVENT_ABOUT_US_CLICK = Event("c_about_us_click")

    /**
     * 账号
     */
    @EventDesc("完善游客账号")
    val EVENT_COMPLETE_ACCOUNT = Event("c_event_complete_account")

    @EventDesc("我的_账户_点击")
    val EVENT_MINE_ACCOUNT_CLICK = Event("c_mine_account_click")

    @EventDesc("我的_钱包_点击")
    val EVENT_MINE_WALLET_CLICK = Event("c_mine_wallet_click")

    @EventDesc("绑定_点击")
    val EVENT_BIND_CLICK = Event("c_bind_click")

    @EventDesc("解绑_点击")
    val EVENT_UNBIND_CLICK = Event("c_unbind_click")


    /**
     * 登录
     *  [文档]https://meta.feishu.cn/wiki/L4vFwdeNQiK46DkJ4DRcnE0nnLg
     */
    @EventDesc("退出登录点击")
    val EVENT_LOGOUT_CLICK = Event("settings_click_logout")

    @EventDesc("退出登录_确定_点击")
    val EVENT_LOGOUT_CONFIRM_CLICK = Event("settings_confirm_logout")

    @EventDesc("退出登录_取消_点击")
    val EVENT_LOGOUT_CANCEL_CLICK = Event("c_logout_cancel_click")

    @EventDesc("登录弹层_展示")
    val EVENT_LOGIN_PAGE_SHOW = Event("c_loginpage_show")

    @EventDesc("登录流程各页面展现")
    val EVENT_LOGIN_SHOW_PAGE = Event("event_login_show_page")

    @EventDesc("登录页面_登录_点击")
    val EVENT_LOGIN_PAGE_LOGIN_CLICK = Event("c_loginpage_login_click")

    @EventDesc("登录成功")
    val EVENT_LOGIN_SUCCEED = Event("c_login_succeed")

    @EventDesc("注册页_展示")
    val EVENT_SIGNUP_PAGE_SHOW = Event("c_event_signup_show")

    @EventDesc("注册成功")
    val EVENT_SIGNUP_SUCCEED = Event("c_event_signup_success")

    @EventDesc("注销账号触发")
    val EVENT_ACCOUNT_CANCEL = Event("c_event_cancel_account")

    @EventDesc("登录上次账号选项点击")
    val EVENT_LOGINPAGE_FORMAL_ACCOUNT_BUTTON_CLICK = Event("loginpage_formal_account_button_click")

    @EventDesc("创建新账号选项点击")
    val EVENT_LOGINPAGE_NEW_ACCOUNT_BUTTON_CLICK = Event("loginpage_new_account_button_click")

    @EventDesc("登录已有其他账号选项点击")
    val EVENT_LOGINPAGE_OTHER_ACCOUNT_BUTTON_CLICK = Event("loginpage_other_account_button_click")

    @EventDesc("忘记密码点击")
    val EVENT_LOGIN_CLICK_FORGOT = Event("login_click_forgot")

    @EventDesc("忘记密码修改结果")
    val EVENT_LOGIN_CLICK_RESULT = Event("login_click_result")

    @EventDesc("登录流程各页面确认按钮点击")
    val EVENT_SHOW_PAGE_CONFIRM_CLICK = Event("event_show_page_confirm_click")

    @EventDesc("完善账密结果")
    val EVENT_SECURITY_ACCOUNT_RESULT = Event("security_account_result")

    @EventDesc("绑定邮箱结果")
    val EVENT_SECURITY_BINDEMAIL_RESULT = Event("security_bindemail_result")

    @EventDesc("换绑邮箱结果")
    val EVENT_SECURITY_CHANGEEMAIL_RESULT = Event("security_changeemail_result")

    @EventDesc("修改密码结果")
    val EVENT_SECURITY_CHANGEPASSWORD_RESULT = Event("security_changepassword_result")

    @EventDesc("关联三方结果")
    val EVENT_SECURITY_CONNECTED_RESULT = Event("security_connected_result")

    // 登录页面展现
    const val KEY_LOGIN_PAGE_NAME = "pagename"

    // 游戏来源
    const val KEY_LOGIN_GAME_CODE = "gamecode"

    // 忘记密码修改结果
    const val KEY_LOGIN_RESULT = "result"

    // 登录各页面展示来源
    const val KEY_LOGIN_SOURCE = "source"

    // 登录成功来源
    const val KEY_LOGIN_WAY = "way"

    // 登录成功方式
    const val KEY_LOGIN_TYPE = "type"

    /**
     * SE 获取转化数据成功
     */
    @EventDesc("SE获取转化数据成功")
    val EVENT_SE_CONVERSION_DATA_RECEIVED =
        Event("c_se_conversion_data_received")


    /**
     * SE初始化完成
     */
    @EventDesc("SE初始化完成")
    val SE_START_FINISH = Event("se_start_finish")

    @EventDesc("SE 数据异常埋点")
    val EVENT_SE_CONVERSION_DATA_EXCEPTION = Event("c_se_conversion_data_exception")

    /**
     * 用户画像调查
     */
    @EventDesc("用户调查展示次数")
    val EVENT_INVESTIGATION_NOTICE_SHOW = Event("c_investigation_notice_show")

    @EventDesc("用户调查点击_提交")
    val EVENT_INVESTIGATION_CLICK_SUBMIT = Event("c_investigation_click_submit")

    @EventDesc("用户调查点击_性别女")
    val EVENT_INVESTIGATION_CLICK_FEMALE = Event("c_investigation_click_female")

    @EventDesc("用户调查点击_性别男")
    val EVENT_INVESTIGATION_CLICK_MALE = Event("c_investigation_click_male")

    @EventDesc("用户调查点击_性别其他")
    val EVENT_INVESTIGATION_CLICK_OTHER = Event("c_investigation_click_other")

    @EventDesc("用户调查点击_选择生日")
    val EVENT_INVESTIGATION_CLICK_CHOOSE_BIRTHDAY =
        Event("c_investigation_click_choose_birthday")

    @EventDesc("用户调查点击_关闭")
    val EVENT_INVESTIGATION_CLICK_CLOSE = Event("c_investigation_click_close")

    // 好友
    @EventDesc("加好友_验证信息页面_取消_点击")
    val EVENT_FRIEND_APPLY_CANCEL_CLICK =
        Event("c_event_friend_apply_click_cancel")

    @EventDesc("加好友_验证信息页面_验证信息_点击")
    val EVENT_FRIEND_APPLY_EDIT_TEXT_CLICK =
        Event("c_event_friend_apply_click_edit_text")

    @EventDesc("加好友_验证信息页面_完成_点击")
    val EVENT_FRIEND_APPLY_CONFIRM_CLICK =
        Event("c_event_friend_apply_click_finish")

    // 好友申请页面
    @EventDesc("好友申请页面_同意_点击")
    val EVENT_FRIEND_APPLY_AGREE_CLICK = Event("c_event_friend_apply_click_accept")

    @EventDesc("好友申请页面_拒绝_点击")
    val EVENT_FRIEND_APPLY_DISAGREE_CLICK = Event("c_event_friend_apply_click_refuse")

    // 搜索用户页面
    @EventDesc("搜索用户页面_搜索按钮_点击")
    val EVENT_SEARCH_FRIEND_SEARCH_CLICK =
        Event("c_event_search_friend_search_click")

    @EventDesc("搜索用户页面_搜索结果_点击")
    val EVENT_SEARCH_FRIEND_SEARCH_RESULT_ITEM_CLICK =
        Event("c_event_search_friend_search_result_item_click")

    // 扫一扫
    @EventDesc("扫一扫_相册_点击")
    val EVENT_QR_SCAN_PICTURE_CLICK = Event("c_event_qr_scan_picture_click")

    @EventDesc("扫一扫_有结果")
    val EVENT_QR_SCAN_RESULT = Event("c_event_qr_scan_result")

    @EventDesc("扫一扫_相机权限弹窗_去开启_点击")
    val EVENT_CAMERA_DIALOG_CONFIRM_CLICK =
        Event("c_camera_dialog_confirm_click")

    @EventDesc("扫一扫_相机权限弹窗_取消_点击")
    val EVENT_CAMERA_DIALOG_CANCEL_CLICK = Event("c_camera_dialog_cancel_click")

    // 加好友页面
    @EventDesc("加好友页面_搜索点击")
    val EVENT_ADD_FRIEND_SEARCH_CLICK = Event("c_event_add_friend_search_click")

    @EventDesc("好友页面_好友申请_点击")
    val EVENT_FRIEND_FRIEND_REQUEST_CLICK: Event =
        Event("c_event_friend_click_friend_recommend_add_one")

    @EventDesc("加好友页面_我的二维码_点击")
    val EVENT_ADD_FRIEND_MY_QR_CODE_CLICK =
        Event("c_event_add_friend_my_qr_code_click")

    @EventDesc("加好友页面_扫一扫_点击")
    val EVENT_ADD_FRIEND_SCAN_CLICK = Event("c_event_add_friend_scan_click")

    // 用户资料卡片
    @EventDesc("加好友_点击")
    val EVENT_IM_ADD_FRIEND_CLICK = Event("c_im_add_friend_click")

    @EventDesc("进入聊天页面_点击")
    val EVENT_IM_CHAT_CLICK = Event("c_im_chat_click")

    // 好友Tab
    @EventDesc("好友_未登录页面_登录_展示")
    val EVENT_IM_FRIEND_UN_SIGN_IN_SHOW: Event =
        Event("c_im_friend_un_sign_in_show")

    @EventDesc("好友底栏_消息_展示")
    val EVENT_IM_MESSAGES_TAB_SHOW: Event = Event("c_im_messages_tab_show")

    @EventDesc("好友底栏_联系人_展示")
    val EVENT_IM_CONTACTS_TAB_SHOW: Event = Event("c_im_contacts_tab_show")

    @EventDesc("加好友_图标_点击")
    val EVENT_IM_ADD_FRIEND_PICTURE_CLICK: Event = Event("c_im_add_friend_picture_click")

    // 好友列表
    @EventDesc("发消息_点击")
    val EVENT_FRIEND_SEND_MESSAGE_CLICK: Event = Event("c_event_friend_click_send_message")

    // 消息列表
    @EventDesc("消息页面_长按_删除")
    val EVENT_LONG_CLICK_CONVERSATION_LIST_DELETE: Event =
        Event("c_event_long_click_conversation_list_delete")

    @EventDesc("消息页面_长按_置顶/取消置顶")
    val EVENT_LONG_CLICK_CONVERSATION_LIST_TOP: Event =
        Event("c_event_long_click_conversation_list_top")

    // 会话页面
    @EventDesc("消息页面_会话_长按_点击")
    val EVENT_IM_LONG_CLICK_LIST_ITEM_CLICK =
        Event("c_im_long_click_list_item_click")

    /**
     * type: "1": 私聊; "2": 群聊
     * message_type: "1": 文本和emoji; "2": 图片; "3" 帖子
     * group_id: 群id
     */
    @EventDesc("IM发消息")
    val EVENT_CONVERSATION_TEXT_SEND_CLICK =
        Event("c_event_conversation_text_send_click")

    @EventDesc("用户头像昵称_点击")
    val EVENT_CLICK_COMMUNITY_MAIN_UPORTRAIT: Event =
        Event("c_event_click_community_main_uportrait")

    @EventDesc("会话页面_查看主页_点击")
    val EVENT_CONVERSATION_HOME_PAGE_CLICK =
        Event("c_event_conversation_home_page_click")

    @EventDesc("会话页面_返回_点击")
    val EVENT_CONVERSATION_BACK_CLICK = Event("c_event_conversation_back_click")

    @EventDesc("会话页面_聊天设置_点击")
    val EVENT_CONVERSATION_SETTING_CLICK = Event("c_event_conversation_setting_click")

    @EventDesc("会话页面_复制_点击")
    val EVENT_CONVERSATION_COPY_CLICK = Event("c_im_conversation_copy_click")

    @EventDesc("会话页面_撤回_点击")
    val EVENT_CONVERSATION_RECALL_CLICK = Event("c_im_conversation_recall_click")

    // 聊天设置界面
    @EventDesc("聊天设置页面_修改备注_点击")
    val EVENT_CHAT_SETTING_REMARK_CLICK =
        Event("c_event_chat_setting_modify_remark_click")

    @EventDesc("聊天设置页面_置顶聊天_点击")
    val EVENT_CHAT_SETTING_MSG_TO_TOP_CLICK =
        Event("c_event_chat_setting_top_conversation_click")

    @EventDesc("删除好友_点击")
    val EVENT_CHAT_SETTING_DELETE_FRIEND_CLICK =
        Event("c_event_chat_setting_delete_friend_click")

    @EventDesc("删除好友二次确认_取消")
    val EVENT_CHAT_SETTING_DELETE_FRIEND_CANCEL_CLICK =
        Event("c_event_chat_setting_delete_friend_dialog_cancel_click")

    @EventDesc("删除好友二次确认_删除")
    val EVENT_CHAT_SETTING_DELETE_FRIEND_CONFIRM_CLICK =
        Event("c_event_chat_setting_delete_friend_dialog_confirm_click")

    @EventDesc("聊天设置页面_清空聊天记录_点击")
    val EVENT_CHAT_SETTING_CLEAR_MSG_CLICK =
        Event("c_event_chat_setting_clean_chat_record_click")

    @EventDesc("聊天设置页面_清空聊天记录弹窗_取消")
    val EVENT_CHAT_SETTING_CLEAR_MSG_CANCEL_CLICK =
        Event("c_event_chat_setting_clean_chat_record_dialog_cancel_click")

    @EventDesc("聊天设置页面_清空聊天记录弹窗_清空")
    val EVENT_CHAT_SETTING_CLEAR_MSG_CONFIRM_CLICK =
        Event("c_event_chat_setting_clean_chat_record_dialog_confirm_click")

    // MGS
    @EventDesc("MGS_gpark悬浮球_点击")
    val EVENT_CLICK_MGS_FLOAT: Event = Event("c_event_click_mgs_float")

    @EventDesc("MGS_消息展开收起按钮_点击")
    val EVENT_CLICK_MESSAGE_EXPAND_OR_CLOSE: Event =
        Event("c_event_click_message_expand_or_close")

    @EventDesc("MGS_发消息_发送_点击")
    val EVENT_CLICK_MGS_MESSAGE_SEND: Event = Event("c_event_click_mgs_message_send")

    @EventDesc("MGS_发消息_点击")
    val EVENT_CLICK_MGS_MESSAGE_INPUT: Event = Event("c_event_click_mgs_message_input")

    @EventDesc("MGS_房间页面_展示")
    val EVENT_SHOW_MGS_ROOM: Event = Event("c_event_show_mgs_room")

    @EventDesc("MGS_成员页面_展示")
    val EVENT_SHOW_MGS_MEMBER: Event = Event("c_event_show_mgs_member")

    @EventDesc("MGS_邀请页面_展示")
    val EVENT_SHOW_MGS_INVITE: Event = Event("c_event_show_mgs_invite")

    @EventDesc("MGS_房间页面_展示时长")
    val EVENT_SHOW_MGS_ROOM_TIME: Event = Event("c_event_show_mgs_room_time")

    @EventDesc("MGS_用户资料_点击")
    val EVENT_CLICK_MGS_USER: Event = Event("c_event_click_mgs_user")

    @EventDesc("MGS_用户资料_关闭")
    val EVENT_CLICK_CLOSE_MGS_USER: Event = Event("c_event_click_close_mgs_user")

    @EventDesc("MGS_退出游戏_点击")
    val EVENT_CLICK_MGS_GAME_QUIT: Event = Event("c_event_click_mgs_game_quit")

    @EventDesc("MGS_退出游戏_确定_点击")
    val EVENT_CLICK_MGS_GAME_SURE: Event = Event("c_event_click_mgs_game_sure")

    @EventDesc("MGS_退出游戏_取消_点击")
    val EVENT_CLICK_MGS_GAME_CANCEL: Event = Event("c_event_click_mgs_game_cancel")

    @EventDesc("MGS_好友_登录_点击")
    val EVENT_CLICK_GAME_LOGIN = Event("c_event_click_game_login")

    @EventDesc("MGS_用户资料卡_展示")
    val EVENT_SHOW_USER_CARD = Event("c_event_show_user_card")

    @EventDesc("MGS_加好友_点击")
    val EVENT_MGS_CLICK_ADD_FRIEND = Event("c_event_mgs_click_add_friend")

    @EventDesc("mgs名片点击去装扮")
    val EVENT_CLICK_MGS_AVATAR_EDIT = Event("c_event_click_mgs_avatar_edit")

    @EventDesc("mgs接入avatar名片曝光")
    val EVENT_SHOW_MGS_AVATAR = Event("c_event_show_mgs_avatar")

    @EventDesc("mgs接入avatar名片点击")
    val EVENT_CLICK_MGS_AVATAR = Event("c_event_click_mgs_avatar")

    @EventDesc("mgs名片点击去ugc作品")
    val EVENT_CLICK_MGS_BUILD = Event("c_event_click_mgs_build")

    @EventDesc("游戏邀请弹窗_展示")
    val GAME_INVITE_TOAST_SHOW = Event("c_game_invite_toast_show")

    @EventDesc("游戏邀请弹窗_接受_点击")
    val GAME_INVITE_TOAST_ACCEPT_CLICK = Event("c_game_invite_toast_accept_click")

    @EventDesc("游戏邀请弹窗_拒绝_点击")
    val GAME_INVITE_TOAST_REFUSE_CLICK = Event("c_game_invite_toast_refuse_click")


    @EventDesc("邀请好友_好友列表页面_展示")
    val EVENT_INVITE_FRIEND_FRIEND_LIST_SHOW = Event("c_invite_friend_friend_list_show")

    @EventDesc("邀请好友_复制链接_点击")
    val EVENT_INVITE_COPYLINK_CLICK = Event("c_invite_copylink_click")

    @EventDesc("邀请好友_面对面_点击")
    val EVENT_INVITE_FRIEND_QR_CLICK = Event("c_invite_friend_qr_click")

    @EventDesc("邀请好友_Other_点击")
    val EVENT_INVITE_OTHER_CLICK = Event("c_invite_other_click")

    @EventDesc("邀请好友_邀请按钮_点击")
    val EVENT_INVITE_FRIEND_CHOOSE_PLAYER_CLICK =
        Event("c_invite_friend_choose_player_click")

    @EventDesc("邀请好友_二维码_扫一扫_点击")
    val EVENT_INVITE_FRIEND_QR_PHOTO_CLICK =
        Event("c_invite_friend_qr_photo_click")

    @EventDesc("邀请好友_二维码_展示")
    val EVENT_INVITE_FRIEND_QR_SHOW = Event("c_invite_friend_qr_show")

    @EventDesc("扫一扫 确认登录")
    val EVENT_CONFIRM_LOGIN = Event("c_event_confirm_login")

    @EventDesc("广告-开始加载")
    val EVENT_NEW_AD_LOAD = Event("c_event_new_ad_load")

    @EventDesc("广告-加载成功")
    val EVENT_NEW_AD_LOAD_SUCCESS = Event("c_event_new_ad_load_success")

    @EventDesc("广告-加载失败")
    val EVENT_NEW_AD_LOAD_FAIL = Event("c_event_new_ad_load_fail")

    @EventDesc("广告-FE请求展示")
    val EVENT_NEW_AD_SHOW_CALL = Event("c_event_new_ad_show_call")

    @EventDesc("广告-展示成功")
    val EVENT_NEW_AD_SHOW_SUCCESS = Event("c_event_new_ad_show_success")

    @EventDesc("广告-展示失败")
    val EVENT_NEW_AD_SHOW_FAIL = Event("c_event_new_ad_show_fail")

    @EventDesc("广告-完整展示给予奖励")
    val EVENT_NEW_AD_SHOW_REWARD = Event("c_event_new_ad_show_reward")

    @EventDesc("广告-展示完成关闭")
    val EVENT_NEW_AD_SHOW_CLOSE = Event("c_event_new_ad_show_close")

    @EventDesc("广告-展示中途跳过")
    val EVENT_NEW_AD_SHOW_SKIP = Event("c_event_new_ad_show_skip")

    @EventDesc("广告-点击广告")
    val EVENT_NEW_AD_CLICK = Event("c_event_new_ad_click")

    @EventDesc("广告-广告展示成功fake")
    val EVENT_AD_SHOW_SUCCESS_FAKE = Event("c_event_ad_show_success_fake")

    @EventDesc("强更弹窗_展示")
    val UPDATE_DIALOG_SHOW = Event("c_update_dialog_show")

    @EventDesc("强更弹窗_去更新")
    val UPDATE_BUTTON_CLICK = Event("c_update_button_click")

    @EventDesc("强更弹窗_退出")
    val UPDATE_EXIT_CLICK = Event("c_update_exit_click")

    @EventDesc("点击拉起状态")
    val EVENT_MW_CLICK_STATUS = Event("c_click_status")

    @EventDesc("反馈提交")
    val EVENT_FEEDBACK_SUBMIT = Event("c_feedback_submit")

    @EventDesc("取消反馈")
    val EVENT_FEEDBACK_CANCEL = Event("c_feedback_cancel")

    @EventDesc("展开更多游戏介绍")
    val DETAIL_DESCRIPTION_MORE_CLICK = Event("c_detail_description_more_click")

    @EventDesc("写评论入口点击")
    val WRITE_REVIEW_CLICK = Event("c_write_review_click")

    @EventDesc("游戏评价入口点击")
    val GAME_REVIEW_ENTER_CLICK = Event("game_review_enter_click")

    @EventDesc("别人评论展示")
    val OTHER_REVIEW_SHOW = Event("c_review_show")

    @EventDesc("自己的评论展示")
    val YOUR_REVIEW_SHOW = Event("c_your_review_show")

    @EventDesc("游戏评价单条曝光")
    val GAME_REVIEW_ITEM_SHOW = Event("game_review_item_show")

    @EventDesc("点击全屏评论入口")
    val GAME_REVIEW_SEE_ALL_CLICK = Event("game_review_see_all_click")

    @EventDesc("编辑自己的评论")
    val YOUR_REVIEW_EDIT = Event("c_your_review_edit")

    @EventDesc("删除自己的评论")
    val YOUR_REVIEW_DELETE = Event("c_your_review_delete")

    @EventDesc("确认删除自己的评论")
    val YOUR_REVIEW_DELETE_CONFIRM = Event("c_your_review_delete_confirm")

    @EventDesc("取消删除自己的评论")
    val YOUR_REVIEW_DELETE_CANCEL = Event("c_your_review_delete_cancel")

    @EventDesc("我的评价删除成功")
    val MY_GAME_REVIEW_DELETE_SUCCESS = Event("my_game_review_delete_success")

    @EventDesc("评论有帮助点击")
    val REVIEW_HELPFUL_YES_CLICK = Event("c_review_helpful_yes_click")

    @EventDesc("评论没帮助点击")
    val REVIEW_HELPFUL_NO_CLICK = Event("c_review_helpful_no_click")

    @EventDesc("查看全部评论点击")
    val REVIEW_SEEALL_CLICK = Event("c_review_seeall_click")

    @EventDesc("展开更多评论")
    val REVIEW_MORE_CLICK = Event("c_review_more_click")

    @EventDesc("评论弹窗展示")
    val RATE_POPUPS_SHOW = Event("c_rate_popups_show")

    @EventDesc("跳过评论弹窗")
    val RATE_POPUPS_SKIP_CLICK = Event("c_rate_popups_skip_click")

    @EventDesc("他人评价举报成功")
    val OTHER_GAME_REVIEW_REPORT_SUCCESS = Event("other_game_review_report_success")

    @EventDesc("游戏评价详情页展示")
    val GAME_REVIEW_DETAIL_SHOW = Event("game_review_detail_show")

    @EventDesc("游戏评价点赞-点击")
    val GAME_REVIEW_LIKE_CLICK = Event("game_review_like_click")

    @EventDesc("游戏评价排序点击")
    val GAME_REVIEW_SORT_CLICK = Event("game_review_sort_click")

    @EventDesc("发布评论")
    val REVIEW_POST = Event("c_review_post")

    @EventDesc("退出发评论页面")
    val WRITE_REVIEW_BACK_CLICK = Event("write_review_back_click")

    @EventDesc("游戏评价页面展示")
    val GAME_REVIEW_SHOW = Event("game_review_show")

    @EventDesc("发布评论成功")
    val GAME_REVIEW_PUBLISH_SUCCESS = Event("game_review_publish_success")

    @EventDesc("评论标签点击")
    val REVIEW_TAG_CLICK = Event("c_review_tag_click")

    @EventDesc("系统消息曝光")
    val EVENT_SYSTEM_MSG_SHOW = Event("c_event_system_msg_show")

    @EventDesc("系统消息点击")
    val EVENT_SYSTEM_MSG_CLICK = Event("c_event_system_msg_click")

    @EventDesc("跳转页面的展现")
    val EVENT_SYSTEM_MSG_PAGE_SHOW = Event("c_event_system_msg_page_show")

    @EventDesc("超级推荐位弹窗:展示")
    val EVENT_SUPER_RECOMMEND_GAME_SHOW = Event("c_event_super_recommend_game_show")

    @EventDesc("超级推荐位弹窗：点击前往游戏")
    val EVENT_SUPER_RECOMMEND_GAME_CLICK =
        Event("c_event_super_recommend_game_click")

    @EventDesc("超级推荐位弹窗：关闭")
    val EVENT_SUPER_RECOMMEND_GAME_CLOSE = Event("c_event_super_recommend_game_close")

    @EventDesc("超级推荐位游戏引导:触发")
    val EVENT_SUPER_RECOMMEND_GAME_GUIDE = Event("c_event_super_recommend_game_guide")

    @EventDesc("超级推荐位游戏引导：点击关闭引导提示")
    val EVENT_SUPER_RECOMMEND_GAME_GUIDE_CLOSE = Event("c_event_super_recommend_game_guide_close")

    @EventDesc("超级推荐位弹窗_图标展示结果")
    val EVENT_SUPER_RECOMMEND_GAME_ICON_SHOWN = Event("event_super_recommend_game_icon_shown")

    @EventDesc("探索tab页面展示（过渡版）-点击进入广场")
    val NBLAND_TEMPORARY_PLAYGROUND_CLICK =
        Event("c_nbland_temporary_playground_click")

    @EventDesc("探索tab页面展示（过渡版）-点击去装扮")
    val NBLAND_TEMPORARY_AVATAR_CLICK =
        Event("c_nbland_temporary_avatar_click")

    @EventDesc("探索tab页面展示（过渡版）-去装扮曝光")
    val NBLAND_TEMPORARY_AVATAR_SHOW = Event("c_nbland_temporary_avatar_show")

    @EventDesc("探索tab页面展示（过渡版）-广场曝光")
    val NBLAND_TEMPORARY_PLAYGROUND_SHOW =
        Event("c_nbland_temporary_playground_show")

    @EventDesc("移动端编辑器tab展示")
    val NBLAND_TAB_SHOW = Event("c_nbland_tab_show")

    @EventDesc("移动端编辑器过渡版tab展示")
    val NBLAND_TEMPORARY_TAB_SHOW = Event("c_nbland_temporary_tab_show")

    @EventDesc("探索tab页面展示（正式版）-点击通知")
    val NBLAND_NOTICE_CLICK = Event("c_nbland_notice_click")

    @EventDesc("探索tab页面展示（正式版）-点击去装扮")
    val NBLAND_AVATAR_CLICK = Event("c_nbland_avatar_click")

    @EventDesc("探索tab页面展示（正式版）-点击进入广场")
    val NBLAND_PLAYGROUND_CLICK = Event("c_nbland_playground_click")

    @EventDesc("探索tab页面展示（正式版）-点击进入ugc房间")
    val NBLAND_UGCROOM_CLICK = Event("c_nbland_ugcroom_click")

    @EventDesc("模板页面曝光")
    val UGC_TEMPLATE_PAGE_SHOW = Event("c_ugc_template_page_show")

    @EventDesc("模板页面-模板选择")
    val UGC_TEMPLATE_ITEM_CLICK = Event("c_ugc_template_item_click")

    @EventDesc("模板页面-开始创建")
    val UGC_TEMPLATE_ITEM_NEWBULID_CLICK = Event("c_ugc_template_item_newbulid_click")

    @EventDesc("我的作品页面曝光")
    val UGC_MY_PAGE_SHOW = Event("c_ugc_my_page_show")

    @EventDesc("我的喜欢-作品-展示")
    val UGC_LIKE_PROJECT_SHOW = Event("c_ugc_like_project_show")

    @EventDesc("我的喜欢-作品-点击")
    val UGC_LIKE_PROJECT_CLICK = Event("c_ugc_like_project_click")

    @EventDesc("ugc通知-点击跳转")
    val UGC_NOTICE_CLICK = Event("c_ugc_notice_click")

    @EventDesc("已发布-选择作品")
    val UGC_MY_ESTABLISH_ITEM_CLICK = Event("c_ugc_my_establish_item_click")

    @EventDesc("我的作品-item曝光")
    val UGC_MY_ESTABLISH_ITEM_SHOW = Event("c_ugc_my_establish_item_show")

    @EventDesc("已发布-选择作品-进入房间")
    val UGC_MY_ESTABLISH_ITEM_ROOM_CLICK =
        Event("c_ugc_my_establish_item_room_click")

    @EventDesc("已发布-选择作品-删除作品")
    val UGC_MY_ESTABLISH_ITEM_DELETE_CLICK =
        Event("c_ugc_my_establish_item_delete_click")

    @EventDesc("已发布-选择作品-选择取消")
    val UGC_MY_ESTABLISH_ITEM_CANCLE_CLICK =
        Event("c_ugc_my_establish_item_cancle_click")

    @EventDesc("已发布-选择作品-确认删除弹窗-我再想想")
    val UGC_MY_ESTABLISH_ITEM_DELETE_CONFIRMNOT_CLICK =
        Event("c_ugc_my_establish_item_delete_confirmnot_click")

    @EventDesc("已发布-选择作品-确认删除弹窗-决定删除")
    val UGC_MY_ESTABLISH_ITEM_DELETE_CONFIRMYES_CLICK =
        Event("c_ugc_my_establish_item_delete_confirmyes_click")

    @EventDesc("探索tab页面展示（正式版）-去装扮曝光")
    val NBLAND_AVATAR_SHOW = Event("c_nbland_avatar_show")

    @EventDesc("探索tab页面展示（正式版）-广场曝光")
    val NBLAND_PLAYGROUND_SHOW = Event("c_nbland_playground_show")

    @EventDesc("探索tab页面展示（正式版）-ugc房间曝光")
    val NBLAND_UGCROOM_SHOW = Event("c_nbland_ugcroom_show")

    @EventDesc("拉起角色编辑器(跨进程)")
    val EVENT_GAME_AVATAR_LAUNCH = Event("c_event_game_avatar_launch")

    @EventDesc("点击下载头像")
    val EVENT_DOWNLOAD_PROFILE_PHOTO = Event("c_event_download_profile_photo")

    @EventDesc("palyza申请权限结果")
    val EVENT_AUTH_COMPLETE = Event("c_event_auth_complete")

    @EventDesc("探索tab页面展示（正式版）-PGC游戏曝光")
    val NBLAND_PGC_GAME_SHOW = Event("c_nbland_pgcgame_show")

    @EventDesc("探索tab页面展示（正式版）-PGC游戏点击")
    val NBLAND_PGC_GAME_CLICK = Event("c_nbland_pgcgame_click")

    @EventDesc("游戏详情页Ts房间点击")
    val EVENT_GAME_DETAIL_TS_ROOM_CLICK = Event("c_event_game_detail_ts_room_click")

    @EventDesc("游戏详情页Ts房间列表显示")
    val EVENT_GAME_DETAIL_TS_ROOM_LIST_SHOW =
        Event("c_event_game_detail_ts_room_list_show")

    @EventDesc("游戏详情页Ts房间列表刷新")
    val EVENT_GAME_DETAIL_TS_ROOM_LIST_REFRESH =
        Event("c_event_game_detail_ts_room_list_refresh")

    @EventDesc("游戏详情页Ts房间列表点击加入")
    val EVENT_GAME_DETAIL_TS_ROOM_LIST_CLICK_JOIN =
        Event("c_event_game_detail_ts_room_list_click_join")

    @EventDesc("房间列表无法加入弹窗展示")
    val EVENT_GAME_DETAIL_TS_ROOM_LIST_DIALOG_SHOW =
        Event("c_event_roomlist_dialog_show")

    @EventDesc("好友avatar-点击好友")
    val EVENT_FRIENDS_AVATAR_CLICK = Event("c_friends_avatar_click")

    @EventDesc("好友avatar-点击好友聊天")
    val EVENT_FRIENDS_AVATAR_CHAT_CLICK = Event("c_friends_avatar_chat_click")

    @EventDesc("好友avatar-点击邀请")
    val EVENT_FRIENDS_AVATAR_INVITE = Event("c_friends_avatar_invite")

    @EventDesc("好友avatar-邀请方式")
    val EVENT_FRIENDS_AVATAR_INVITE_TYPE = Event("c_friends_avatar_invite_type")

    @EventDesc("加好友弹层曝光")
    val EVENT_FRIENDS_AVATAR_TOAST_SHOW = Event("c_friends_avatar_toast_show")

    @EventDesc("加好友弹层选择添加")
    val EVENT_FRIENDS_AVATAR_TOAST_ADD = Event("c_friends_avatar_toast_add")

    @EventDesc("加好友弹层选择关闭")
    val EVENT_FRIENDS_AVATAR_TOAST_CLOSE = Event("c_friends_avatar_toast_close")

    @EventDesc("个人主页展示")
    val EVENT_PROFILE_PAGE_SHOW = Event("c_profile_show")

    @EventDesc("主页_设置按钮点击")
    val EVENT_PROFILE_SET_CLICK = Event("c_profile_set_click")

    @EventDesc("编辑个人主页按钮点击")
    val EVENT_PROFILE_EDIT_PROFILE_CLICK = Event("c_profile_editprofile_click")

    @EventDesc("没有个签时添加个签按钮的点击")
    val EVENT_PROFILE_ADD_INFORMATION_CLICK =
        Event("c_profile_add_profile_information")

    @EventDesc("主页_最近玩过点击")
    val EVENT_PROFILE_RECENT_CLICK = Event("c_profile_recent_click")

    @EventDesc("主页_好友列表点击")
    val EVENT_PROFILE_FRIENDS_CLICK = Event("c_profile_friends_click")

    @EventDesc("主页_游戏评论列表点击")
    val EVENT_PROFILE_REVIEWS_CLICK = Event("c_profile_reviews_click")

    @EventDesc("主页_编辑城市完成")
    val EVENT_PROFILE_EDIT_CITY = Event("c_profile_edit_city_done")

    @EventDesc("主页_编辑个签完成")
    val EVENT_PROFILE_EDIT_INFORMATION = Event("c_profile_edit_information_done")

    @EventDesc("奖励代码确认兑换点击")
    val EVENT_REWARD_CODE_CONFIRM_CLICK = Event("c_profile_reward_code_comfirm_click")

    @EventDesc("点击去discord获取奖励代码")
    val EVENT_REWARD_CODE_DISCORD_CLICK =
        Event("c_profile_reward_code_discord_click")

    @EventDesc("获得奖励弹窗展示")
    val EVENT_REWARD_CODE_SUC_DIALOG_SHOW = Event("c_profile_getreward_diglog_show")

    @EventDesc("获得奖励弹窗按钮点击")
    val EVENT_REWARD_CODE_SUC_DIALOG_GREAT_CLICK =
        Event("c_profile_getreward_diglog_great_click")

    @EventDesc("登录授权页展现（拉起SDK）")
    val EVENT_P12_LOGIN_AUTH_SHOW = Event("c_event_p12_login_auth_show")

    @EventDesc("登录结果")
    val EVENT_P12_LOGIN_AUTH_RESULT = Event("c_event_p12_login_auth_result")

    @EventDesc("拉起游戏：MW环境准备")
    val EVENT_MW_ENV_PREPARE = Event("c_event_mw_env_prepare")

    @EventDesc("ts正在拉起中")
    val EVENT_TS_DETAIL_LAUNCHING = Event("c_event_ts_detail_launching")

    @EventDesc("评论举报入口点击")
    val EVENT_REVIEW_REPORT_CLICK = Event("c_review_report_click")

    @EventDesc("账号被限制弹窗展示")
    val EVENT_ACCOUNT_IRREGULARITIES_DIALOG_SHOW =
        Event("c_account_irregularities_dialog_show")

    @EventDesc("被屏蔽弹窗展示")
    val EVENT_USER_BLOCK_DIALOG_SHOW = Event("c_user_block_dialog_show")

    @EventDesc("评论举报弹窗_提交点击")
    val EVENT_REVIEW_REPORT_REPORT_CLICK = Event("c_review_report_report_click")

    @EventDesc("评论举报弹窗_取消点击")
    val EVENT_REVIEW_REPORT_CANCEL_CLICK = Event("c_review_report_cancel_click")

    @EventDesc("用户举报入口点击")
    val EVENT_USER_REPORT_CLICK = Event("c_user_report_click")

    @EventDesc("用户举报弹窗_提交点击")
    val EVENT_USER_REPORT_REPORT_CLICK = Event("c_user_report_report_click")

    @EventDesc("用户举报弹窗_取消点击")
    val EVENT_USER_REPORT_CANCEL_CLICK = Event("c_user_report_cancel_click")

    @EventDesc("用户屏蔽入口点击")
    val EVENT_USER_BLOCK_CLICK = Event("c_user_block_click")

    @EventDesc("屏蔽用户弹窗_屏蔽")
    val EVENT_USER_BLOCK_BLOCK_CLICK = Event("c_user_block_dialog_block_click")

    @EventDesc("屏蔽用户弹窗_取消")
    val EVENT_USER_BLOCK_CANCEL_CLICK = Event("c_user_block_dialog_cancel_click")

    @EventDesc("解除屏蔽用户入口点击")
    val EVENT_USER_UNBLOCK_CLICK = Event("c_user_unblock_click")

    @EventDesc("解除屏蔽用户弹窗_解除")
    val EVENT_USER_UNBLOCK_UNBLOCK_CLICK = Event("c_user_unblock_dialog_unblock_click")

    @EventDesc("解除屏蔽用户弹窗_取消")
    val EVENT_USER_UNBLOCK_CANCEL_CLICK = Event("c_user_unblock_dialog_cancel_click")

    @EventDesc("首页运营消息跳转点击")
    val EVENT_HOME_OPERATION_NEWS_CLICK = Event("c_event_home_operation_news_click")

    @EventDesc("游戏录屏-开始申请权限")
    val EVENT_GAME_RECORD_START_APPLY_PERMISSION =
        Event("c_event_game_record_start_apply_permission")

    @EventDesc("游戏录屏-权限申请结果")
    val EVENT_GAME_RECORD_APPLY_PERMISSION_RESULT =
        Event("c_event_game_record_apply_permission_result")

    @EventDesc("游戏录屏-开始申请用户录屏授权")
    val EVENT_GAME_RECORD_START_APPLY_USER_PERMIT =
        Event("c_event_game_record_start_apply_user_permit")

    @EventDesc("游戏录屏-用户录屏授权结果")
    val EVENT_GAME_RECORD_APPLY_USER_PERMIT_RESULT =
        Event("c_event_game_record_apply_user_permit_result")

    @EventDesc("游戏录屏-倒计时页面的显示")
    val EVENT_GAME_RECORD_COUNT_DOWN_SHOW =
        Event("c_event_game_record_count_down_show")

    @EventDesc("游戏录屏-录制中按钮的点击")
    val EVENT_GAME_RECORD_RECORD_VIEW_CLICK =
        Event("c_event_game_record_record_view_click")

    @EventDesc("游戏录屏-停止后弹窗的展示")
    val EVENT_GAME_RECORD_FINISH_DIALOG_SHOW =
        Event("c_event_game_record_finish_dialog_show")

    @EventDesc("游戏录屏-停止后弹窗页面的点击关闭")
    val EVENT_GAME_RECORD_FINISH_DIALOG_CLOSE =
        Event("c_event_game_record_finish_dialog_close")

    @EventDesc("游戏录屏-停止后弹窗页面的点击")
    val EVENT_GAME_RECORD_FINISH_DIALOG_SHARE_CLICK =
        Event("c_event_game_record_finish_dialog_share_click")

    @EventDesc("视频分享-分享弹窗点击")
    val EVENT_VIDEO_SHARE_DIALOG_CLICK =
        Event("event_video_share_dialog_click")

    @EventDesc("游戏录屏-开始录屏失败")
    val EVENT_GAME_RECORD_START_RECORD_FAILED =
        Event("c_event_game_record_start_record_failed")

    @EventDesc("游戏录屏-结束录屏失败")
    val EVENT_GAME_RECORD_STOP_RECORD_FAILED =
        Event("c_event_game_record_stop_record_failed")

    @EventDesc("游戏录屏-开始录屏成功")
    val EVENT_GAME_RECORD_START_RECORD_SUCCESS =
        Event("c_event_game_record_start_record_success")

    @EventDesc("游戏录屏-结束录屏成功")
    val EVENT_GAME_RECORD_STOP_RECORD_SUCCESS =
        Event("c_event_game_record_stop_record_success")

    @EventDesc("游戏录屏-保存录屏文件成功")
    val EVENT_GAME_RECORD_SAVE_RECORD_FILE_SUCCESS =
        Event("c_event_game_record_save_record_file_success")

    @EventDesc("游戏录屏-保存录屏文件失败")
    val EVENT_GAME_RECORD_SAVE_RECORD_FILE_FAILED =
        Event("c_event_game_record_save_record_file_failed")

    @EventDesc("游戏录屏-录制声音的开启")
    val EVENT_GAME_RECORD_VOICE_OPEN = Event("c_event_game_record_voice_open")

    @EventDesc("游戏录屏-录制声音的关闭")
    val EVENT_GAME_RECORD_VOICE_CLOSE = Event("c_event_game_record_voice_close")

    @EventDesc("点击avatar头像图片")
    val EVENT_AVATAR_CHOOSE_IMAGE_CLICK = Event("c_avatar_choose_image_click")

    @EventDesc("点击选择按钮")
    val EVENT_AVATAR_CHOOSE_CONFIRM = Event("c_avatar_choose_confirm")

    @EventDesc("返回按钮曝光")
    val EVENT_AVATAR_CHOOSE_SKIP_SHOW = Event("c_avatar_choose_skip_show")

    @EventDesc("返回按钮点击")
    val EVENT_AVATAR_CHOOSE_SKIP_CLICK = Event("c_avatar_choose_skip_click")

    @EventDesc("主进程加载角色编辑器开始")
    val EVENT_LOADING_AVATAR_START = Event("c_event_loading_avatar_start")

    @EventDesc("主进程加载角色编辑器完成")
    val EVENT_LOADING_AVATAR_END = Event("c_event_loading_avatar_end")

    @EventDesc("角色通知栏操作")
    val EVENT_AVATAR_NOTICE = Event("event_avatar_notice")

    @EventDesc("加入游戏房间结果")
    val JOIN_ROOM_RESULT = Event("c_join_room_result")

    @EventDesc("玩游戏的时间>=15分钟")
    val EVENT_AF_PLAY_TIME_15 = Event("c_af_playtime15")

    @EventDesc("玩游戏的时间>=20分钟")
    val EVENT_AF_PLAY_TIME_20 = Event("c_af_playtime20")

    @EventDesc("玩游戏的时间>=25分钟")
    val EVENT_AF_PLAY_TIME_25 = Event("c_af_playtime25")

    @EventDesc("玩游戏的时间>=30分钟")
    val EVENT_AF_PLAY_TIME_30 = Event("c_af_playtime30")

    @EventDesc("玩游戏的时间>=40分钟")
    val EVENT_AF_PLAY_TIME_40 = Event("c_af_playtime40")

    @EventDesc("玩游戏的时间>=50分钟")
    val EVENT_AF_PLAY_TIME_50 = Event("c_af_playtime50")

    @EventDesc("次留")
    val EVENT_AF_APP_TIME_1 = Event("c_af_playtime1")

    @EventDesc("2留")
    val EVENT_AF_APP_TIME_2 = Event("c_af_playtime2")

    @EventDesc("3留")
    val EVENT_AF_APP_TIME_3 = Event("c_af_playtime3")

    @EventDesc("7留")
    val EVENT_AF_APP_TIME_7 = Event("c_af_playtime7")

    @EventDesc("用户当天玩了第一个游戏")
    val EVENT_AF_PLAYED_GAME_1 = Event("c_af_played_game_1")

    @EventDesc("用户当天玩了第二个游戏")
    val EVENT_AF_PLAYED_GAME_2 = Event("c_af_played_game_2")

    @EventDesc("用户当天玩了第三个游戏")
    val EVENT_AF_PLAYED_GAME_3 = Event("c_af_played_game_3")

    @EventDesc("用户当天玩了第四个游戏")
    val EVENT_AF_PLAYED_GAME_4 = Event("c_af_played_game_4")

    @EventDesc("用户当天玩了第五个游戏")
    val EVENT_AF_PLAYED_GAME_5 = Event("c_af_played_game_5")

    @EventDesc("玩5个游戏以上")
    val EVENT_AF_PLAYED_GAME_MORE_THAN_5 = Event("c_af_played_game_more_than_5")

    @EventDesc("9-18岁用户")
    val EVENT_AF_AGE_9_18 = Event("c_af_age_9_18")

    @EventDesc("游戏跳游戏-游戏进程")
    val JUMP_GAME_IN_GAME_PROCESS = Event("c_event_jump_game")

    @EventDesc("详情页视频展示")
    val EVENT_GAME_DETAIL_VIDEO_SHOW = Event("c_event_game_detail_video_show")

    @EventDesc("详情页视频开始播放成功")
    val EVENT_GAME_DETAIL_VIDEO_PLAY_SUC =
        Event("c_event_game_detail_video_start_play_success")

    @EventDesc("视频暂停点击")
    val EVENT_GAME_DETAIL_VIDEO_PAUSE = Event("c_event_game_detail_video_play_pause")

    @EventDesc("视频继续播放点击")
    val EVENT_GAME_DETAIL_VIDEO_GO_ON_PLAY =
        Event("c_event_game_detail_video_click_go_on_play")

    @EventDesc("视频进度条点击")
    val EVENT_GAME_DETAIL_VIDEO_SEEK_CLICK =
        Event("c_event_game_detail_video_click_seek_process")

    @EventDesc("视频屏幕点击")
    val EVENT_GAME_DETAIL_VIDEO_CLICK_SCREEN =
        Event("c_event_game_detail_video_click_screen")

    @EventDesc("视频开启声音点击")
    val EVENT_GAME_DETAIL_VIDEO_VOLUME_OPEN =
        Event("c_event_game_detail_video_click_volume_open")

    @EventDesc("视频关闭声音点击")
    val EVENT_GAME_DETAIL_VIDEO_VOLUME_CLOSE =
        Event("c_event_game_detail_video_click_volume_close")

    @EventDesc("视频播放完成（每播放完成1次就发1次）")
    val EVENT_GAME_DETAIL_VIDEO_PLAY_FINISH =
        Event("c_event_game_detail_video_play_finish")

    @EventDesc("视频全屏点击")
    val EVENT_GAME_DETAIL_VIDEO_FULL_SCREEN_CLICK =
        Event("c_event_game_detail_video_click_full_screen")

    @EventDesc("视频全屏返回")
    val EVENT_GAME_DETAIL_VIDEO_FULL_SCREEN_BACK =
        Event("c_event_game_detail_video_click_full_screen_back")

    @EventDesc("首页tab页面-点击小屋")
    val EVENT_NBLAND_HOME_CLICK = Event("c_nbland_home_click")

    @EventDesc("我的页面-选择作品-设置小屋曝光")
    val EVENT_PROFILE_ESTABLISH_ITEM_SETHOME_SHOW =
        Event("c_profile_establish_item_sethome_show")

    @EventDesc("我的页面-选择作品-点击设置小屋")
    val EVENT_PROFILE_ESTABLISH_ITEM_SETHOME_CLICK =
        Event("c_profile_establish_item_sethome_click")

    @EventDesc("我的页面-选择作品-点击进入")
    val EVENT_PROFILE_ESTABLISH_ITEM_ROOM_CLICK =
        Event("c_profile_establish_item_room_click")

    @EventDesc("我的页面-点击小屋")
    val EVENT_PROFILE_HOME_CLICK = Event("c_profile_home_click")

    @EventDesc("我的页面-点击作品标签")
    val EVENT_PROFILE_ITEM_TAB_CLICK = Event("c_profile_item_tab_click")

    @EventDesc("我的页面-点击作品")
    val EVENT_PROFILE_ITEM_CLICK = Event("c_profile_item_click")

    @EventDesc("独立进程MW内核启动失败次数超过最大次数")
    val EVENT_MV_PROXY_RETRY_MAX_COUNT_EXCEEDED =
        Event("c_event_mv_proxy_retry_max_count_exceeded")

    @EventDesc("独立进程MW内核启动超时")
    val EVENT_MV_PROXY_STARTUP_TIMEOUT = Event("c_event_mv_proxy_startup_timeout")

    @EventDesc("独立进程MW内核连接成功")
    val EVENT_MV_PROXY_CONNECTED = Event("c_event_mv_proxy_connected")

    @EventDesc("独立进程MW内核断开连接")
    val EVENT_MV_PROXY_DISCONNECTED = Event("c_event_mv_proxy_disconnected")

    @EventDesc("独立进程MW内核准备就绪")
    val EVENT_MV_PROXY_READY = Event("c_event_mv_proxy_ready")

    @EventDesc("开始启动独立进程MW内核")
    val EVENT_MV_PROXY_START = Event("c_event_mv_proxy_startup")

    @EventDesc("检测到主进程死亡并重启")
    val EVENT_MV_PROXY_HOST_PROCESS_RESTART_DETECTED =
        Event("c_event_mv_proxy_host_process_restart_detected")

    @EventDesc("执行引擎方法超时")
    val EVENT_METHOD_EXEC_TIMEOUT = Event("c_event_mv_proxy_method_exec_timeout")

    @EventDesc("执行引擎方法开始")
    val EVENT_METHOD_EXEC_START = Event("c_event_mv_proxy_method_exec")

    @EventDesc("点击确认按钮")
    val EVENT_INFORMATION_CHOOSE_CONFIRM = Event("c_information_choose_confirm")

    @EventDesc("创角年龄弹窗确认")
    val EVENT_INFORMATION_AGE_CONFIRM = Event("c_information_age_confirm")

    @EventDesc("创角年龄弹窗曝光")
    val EVENT_INFORMATION_AGE_SHOW = Event("c_information_age_show")

    @EventDesc("创角起名页展示")
    val EVENT_INFORMATION_PAGE_SHOW = Event("c_information_page_show")

    @EventDesc("创角起名页-返回按钮曝光")
    val EVENT_INFORMATION_CHOOSE_SKIP_SHOW = Event("c_information_choose_skip_show")

    @EventDesc("创角起名页-返回按钮点击")
    val EVENT_INFORMATION_CHOOSE_SKIP_CLICK = Event("c_information_choose_skip_click")

    @EventDesc("创角起名页-点击昵称随机按钮")
    val EVENT_INFORMATION_NAME_RANDOM_CLICK = Event("c_information_name_random_click")

    @EventDesc("创角起名页-创角年龄弹窗滑动")
    val EVENT_INFORMATION_AGE_CHOOSE_SLIDE = Event("c_information_age_choose_slide")

    @EventDesc("创角起名页点击确认结果")
    val EVENT_INFORMATION_CHOOSE_CONFIRM_RESULT =
        Event("c_information_choose_confirm_result")

    @EventDesc("选择角色页点击确认结果")
    val EVENT_AVATAR_CHOOSE_CONFIRM_RESULT = Event("c_avatar_choose_confirm_result")

    @EventDesc("好友小屋-点击添加")
    val EVENT_FRIENDS_HOME_ADD_CLICK = Event("c_friends_home_add_click")

    @EventDesc("好友小屋主小屋曝光")
    val EVENT_FRIENDS_HOME_SHOW = Event("c_friends_home_show")

    @EventDesc("好友小屋-点击房子")
    val EVENT_FRIENDS_HOME_CLICK = Event("c_friends_home_click")

    @EventDesc("好友小屋-点击回家")
    val EVENT_FRIENDS_HOME_RETURN_CLICK = Event("c_friends_home_return_click")

    @EventDesc("好友小屋-点击好友")
    val EVENT_FRIENDS_HOME_FRIENDS_CLICK = Event("c_friends_home_friends_click")

    @EventDesc("好友小屋-菜单-点击分享")
    val EVENT_FRIENDS_HOME_INVITE_CLICK = Event("c_friends_home_invite_click")

    @EventDesc("好友小屋-菜单-点击模板切换")
    val EVENT_FRIENDS_HOME_PAGE_CLICK = Event("c_friends_home_page_click")

    @EventDesc("好友小屋-作品栏-曝光")
    val EVENT_FRIENDS_HOME_PAGE_SHOW = Event("c_friends_home_page_show")

    @EventDesc("好友小屋-作品栏-点击新建")
    val EVENT_FRIENDS_HOME_PAGE_ADD_CLICK = Event("c_friends_home_page_add_click")

    @EventDesc("好友小屋-作品栏-点击确定")
    val EVENT_FRIENDS_HOME_PAGE_CONFIRM_CLICK =
        Event("c_friends_home_page_confirm_click")

    @EventDesc("好友小屋-新建模板-点击确定")
    val EVENT_FRIENDS_HOME_TEMPLATE_NEWBULID_CLICK =
        Event("c_friends_home_template_newbulid_click")

    @EventDesc("好友小屋-好友菜单-点击拜访")
    val EVENT_FRIENDS_HOME_VISIT_CLICK = Event("c_friends_home_visit_click")

    @EventDesc("好友小屋-好友菜单-点击解除")
    val EVENT_FRIENDS_HOME_RELEASE_CLICK = Event("c_friends_home_release_click")

    @EventDesc("好友小屋-解除弹窗-点击")
    val EVENT_FRIENDS_HOME_RELEASE_TOAST_CLICK =
        Event("c_friends_home_release_toast_click")

    @EventDesc("好友小屋-好友曝光")
    val EVENT_FRIENDS_HOME_FRIENDS_TOAST_SHOW =
        Event("c_friends_home_friends_toast_show")

    @EventDesc("好友小屋-好友界面点击")
    val EVENT_FRIENDS_HOME_FRIENDS_TOAST_CLICK =
        Event("c_friends_home_friends_toast_click")

    @EventDesc("好友小屋-分享方式")
    val EVENT_FRIENDS_HOME_INVITE_TYPE = Event("c_friends_home_invite_type")

    @EventDesc("加好友弹层曝光")
    val EVENT_FRIENDS_HOME_TOAST_SHOW = Event("c_friends_home_toast_show")

    @EventDesc("加好友弹层选择添加")
    val EVENT_FRIENDS_HOME_TOAST_ADD = Event("c_friends_home_toast_add")

    @EventDesc("加好友弹层选择关闭")
    val EVENT_FRIENDS_HOME_TOAST_CLOSE = Event("c_friends_home_toast_close")

    @EventDesc("详情页运营卡片点击")
    val EVENT_GAME_DETAIL_ACTIVITY_CLICK = Event("c_event_game_detail_activity_click")

    @EventDesc("游戏圈入口_点击全部帖子")
    val EVENT_CLICK_COMMUNITY_ALL = Event("c_event_click_community_all")

    @EventDesc("游戏圈帖子展示")
    val EVENT_COMMUNITY_TOPIC_SHOW = Event("c_event_community_topic_show")

    @EventDesc("游戏圈帖子点击")
    val EVENT_COMMUNITY_TOPIC_CLICK = Event("c_event_community_topic_click")

    @EventDesc("赞_点击")
    val EVENT_CLICK_COMMUNITY_MAIN_LIKE_CLICK =
        Event("c_event_click_community_main_like_click")

    @EventDesc("踩_点击")
    val EVENT_CLICK_COMMUNITY_MAIN_NOLIKE_CLICK =
        Event("c_event_click_community_main_nolike_click")

    @EventDesc("回复功能_点击")
    val EVENT_CLICK_COMMUNITY_REPLAY_CLICK = Event("c_event_click_community_replay_click")

    @EventDesc("回复功能_发布_点击")
    val EVENT_CLICK_COMMUNITY_SEND_REPLAY_CLICK =
        Event("c_event_click_community_send_replay_click")

    @EventDesc("删除评论_点击")
    val EVENT_COMMUNITY_REPLAY_DELETE = Event("c_event_community_replay_delete")

    @EventDesc("游戏圈通知消息_展示")
    val EVENT_COMMUNITY_MESSAGE_SHOW = Event("c_event_community_message_show")

    @EventDesc("消息页面_消息_点击")
    val EVENT_CLICK_COMMUNITY_MESSAGE_ITEM =
        Event("c_event_click_community_message_item")

    @EventDesc("发帖加号_点击")
    val EVENT_CLICK_COMMUNITY_PUBLISH = Event("c_event_click_community_publish")

    @EventDesc("发帖_返回_点击")
    val EVENT_CLICK_PUBLISH_BACK = Event("c_event_click_publish_back")

    @EventDesc("发帖_添加图片_点击")
    val EVENT_CLICK_PUBLISH_ADD_IMAGE = Event("c_event_click_publish_add_image")

    @EventDesc("发帖_发布_点击")
    val EVENT_CLICK_PUBLISH_SEND = Event("c_event_click_publish_send")

    @EventDesc("帖子发布成功")
    val EVENT_TOPIC_PUBLISH_SUCCESS = Event("c_event_topic_publish_success")

    @EventDesc("删除帖子_点击")
    val EVENT_COMMUNITY_TOPIC_DELETE = Event("c_event_community_topic_delete")

    @EventDesc("举报_提交_点击")
    val EVENT_INFORM_SUBMIT = Event("c_event_inform_submit")

    @EventDesc("MW拉起系统版本限制")
    val EVENT_MW_LAUNCH_INTERCEPT_SYS_VERSION =
        Event("c_event_mw_launch_intercept_sys_version")

    @EventDesc("MW拉起拦截器拦截")
    val EVENT_MW_LAUNCH_INTERCEPT_INTERCEPTOR =
        Event("c_event_mw_launch_intercept_interceptor")

    @EventDesc("会员支付结果")
    val EVENT_VIP_PLUS_PAY_RESULT = Event("c_event_vip_plus_pay_result")

    @EventDesc("会员状态")
    val EVENT_VIP_PLUS_STATUS = Event("c_event_vip_plus_status")

    @EventDesc("设置账号密码")
    val EVENT_SET_ACCOUNT = Event("c_event_set_account")


    @EventDesc("我的-唤起弹窗-选择重命名")
    val EVENT_UGC_MY_BUILD_ITEM_NAME_CLICK = Event("c_ugc_my_build_item_name_click")

    @EventDesc("我的-重命名展示")
    val EVENT_UGC_MY_BUILD_RENAME_SHOW = Event("c_ugc_my_build_rename_show")

    @EventDesc("我的-重命名确定")
    val EVENT_UGC_MY_BUILD_RENAME_CLICK = Event("c_ugc_my_build_rename_click")

    @EventDesc("当天首个ts游戏进入是否成功")
    val EVENT_FIRST_TS_LAUNCH_RESULTS = Event("c_event_first_ts_launch_results")

    @EventDesc("进入房间")
    val EVENT_MGS_JOIN_ROOM_STATUS = Event("c_mgs_room_friend_status")

    @EventDesc("mgs的ui展示")
    val EVENT_MGS_UI_SHOW = Event("c_mgs_ui_show")

    @EventDesc("拉起角色截图分享")
    val EVENT_SHARE_AVATAR = Event("c_event_share_avatar")

    @EventDesc("角色截图分享方式")
    val EVENT_SHARE_AVATAR_WAY = Event("c_event_share_avatar_way")

    @EventDesc("分享结果")
    val EVENT_SHARE_AVATAR_RESULT = Event("c_event_share_avatar_result")

    @EventDesc("飞轮运营位图标点击")
    val EVENT_EVENT_OPERATION_POSITION_CLICK = Event("c_event_operation_position_click")

    @EventDesc("离开mgs房间")
    val EVENT_MGS_LEAVE_ROOM = Event("c_event_mgs_leave_room")

    @EventDesc("加入房间失败")
    val EVENT_MGS_JOIN_ROOM_ERROR = Event("c_event_mgs_join_room_error")

    @EventDesc("ugc建造页作品数量")
    val EVENT_UGC_CREATE_NUM = Event("c_ugc_create_num")

    @EventDesc("ugc建造页点击模板")
    val EVENT_UGC_CREATE_TEMPLATE_CLICK = Event("c_ugc_create_template_click")

    @EventDesc("ugc建造页点击编辑")
    val EVENT_UGC_CREATE_EDIT_CLICK = Event("c_ugc_create_edit_click")

    @EventDesc("ugc建造页点击游玩")
    val EVENT_UGC_CREATE_PLAY_CLICK = Event("c_ugc_create_play_click")

    @EventDesc("ugc建造页点击设置")
    val EVENT_UGC_CREATE_SET_CLICK = Event("c_ugc_create_set_click")

    @EventDesc("ugc建造页-设置-点击复制")
    val EVENT_UGC_CREATE_SET_COPY_CLICK = Event("c_ugc_create_set_copy_click")

    @EventDesc("ugc建造页-设置-点击删除")
    val EVENT_UGC_CREATE_SET_DELETE_CLICK = Event("c_ugc_create_set_delete_click")

    @EventDesc("ugc建造页-删除弹窗曝光")
    val EVENT_UGC_CREATE_DELETE_SHOW = Event("c_ugc_create_delete_show")

    @EventDesc("ugc建造页-删除弹窗点击确认")
    val EVENT_UGC_CREATE_DELETE_CONFIRM_CLICK =
        Event("c_ugc_create_delete_confirm_click")

    @EventDesc("ugc建造页-删除弹窗点击取消")
    val EVENT_UGC_CREATE_DELETE_CANCEL_CLICK =
        Event("c_ugc_create_delete_cancel_click")

    @EventDesc("首页角色编辑器Tab 去编辑器按钮 点击")
    val EVENT_VIEW_CLICK_DRESS = Event("view_click_dress")

    @EventDesc("服务器Ping时长")
    val EVENT_PING_TIME = Event("c_ping_time")

    @EventDesc("服务器到客户端的端到端时长")
    val EVENT_ECHO_TIME = Event("c_echo_time")

    @EventDesc("ugc详情页展示")
    val UGC_DETAIL_PAGE_SHOW = Event("c_ugc_detail_page_show")

    @EventDesc("ugc详情页-点击进入游戏")
    val UGC_DETAIL_PAGE_CLICK = Event("c_ugc_detail_page_click")

    @EventDesc("ugc详情页-点赞")
    val UGC_DETAIL_PAGE_LIKE = Event("c_ugc_detail_page_like")

    @EventDesc("ugc详情页-跳转个人主页")
    val UGC_DETAIL_PAGE_PROFILE = Event("c_ugc_detail_page_profile")

    @EventDesc("ugc详情页-做同款")
    val UGC_DETAIL_PAGE_DRAFT = Event("c_ugc_detail_page_draft")

    @EventDesc("ugc详情页-做同款点击确认")
    val UGC_DETAIL_PAGE_DRAFT_CONFIRM_CLICK = Event("c_ugc_detail_page_draft_confirm_click")

    @EventDesc("ugc详情页-做同款弹窗点击取消")
    val UGC_DETAIL_PAGE_DRAFT_CANCEL_CLICK = Event("c_ugc_detail_page_draft_cancel_click")

    @EventDesc("ugc点击选模板页签")
    val UGC_TEMPLATE_TAB_PAGE_CLICK = Event("c_ugc_template_tab_page_click")

    @EventDesc("ugc选模板页曝光")
    val UGC_TEMPLATE_TAB_PAGE_SHOW = Event("c_ugc_template_tab_page_show")

    @EventDesc("ugc列表曝光")
    val UGC_TEMPLATE_PAGE_LIST_SHOW = Event("c_ugc_template_page_list_show")

    @EventDesc("ugc列表点击")
    val UGC_TEMPLATE_PAGE_LIST_CLICK = Event("c_ugc_template_page_list_click")

    @EventDesc("ugc提示展示")
    val UGC_CREATE_TIPS_SHOW = Event("c_ugc_create_tips_show")

    @EventDesc("ugc建造页点击进详情页")
    val UGC_CREATE_DETAIL_CLICK = Event("c_ugc_create_detail_click")

    @EventDesc("ugc建造页-本地作品曝光")
    val UGC_CREATE_PRODUCTION_SHOW = Event("c_ugc_create_production_show")

    @EventDesc("首页banner展示")
    val HOME_BANNER_SHOW = Event("home_banner_show")

    @EventDesc("首页banner点击")
    val HOME_BANNER_CLICK = Event("home_banner_click")

    @EventDesc("全屏角色编辑器(跨进程)页面,时长统计")
    val EVENT_GAME_AVATAR_END = Event("event_game_avatar_end")

    @EventDesc("引擎版本不匹配弹窗展示")
    val ENGINE_VERSION_NO_MATCH_DIALOG_SHOW = Event("engine_version_no_match_dialog_show")

    @EventDesc("引擎版本不匹配弹窗点击升级")
    val ENGINE_VERSION_NO_MATCH_DIALOG_SHOW_UPGRADE_CLICK = Event("engine_version_no_match_dialog_show_upgrade_click")

    @EventDesc("语音开启_点击")
    val MGS_VOICE_OPEN_CLICK = Event("c_mgs_voice_open_click")

    @EventDesc("语音关闭_点击")
    val MGS_VOICE_CLOSE_CLICK = Event("c_mgs_voice_close_click")

    @EventDesc("语音时长")
    val MGS_VOICE_TIME = Event("c_mgs_voice_time")

    @EventDesc("语音屏蔽_点击")
    val MGS_VOICE_MUTE_CLICK = Event("c_mgs_voice_mute_click")

    @EventDesc("语音解除屏蔽_点击")
    val MGS_VOICE_UN_MUTE_CLICK = Event("c_mgs_voice_unmute_click")

    @EventDesc("语音全部麦屏蔽_点击")
    val MGS_ALL_VOICE_MUTE_CLICK = Event("mgs_all_voice_mute_click")

    @EventDesc("聊天房-房间卡片点击")
    val EVENT_CLICK_CHATROOM_ROOM_CARD = Event("event_click_chatroom_roomcard")

    @EventDesc("聊天房-房间卡片曝光")
    val EVENT_SHOW_CHATROOM_ROOM_CARD = Event("event_show_chatroom_roomcard")

    @EventDesc("房间详情页-曝光")
    val EVENT_SHOW_CHATROOM_ROOM_CARD_DETAIL = Event("event_show_chatroom_roomcarddetail")

    @EventDesc("房间详情页-进入点击")
    val EVENT_CLICK_CHATROOM_ROOM_CARD_DETAIL_ENTER = Event("event_click_chatroom_roomcarddetail_enter")

    @EventDesc("首页-聊天房模块-曝光")
    val EVENT_SHOW_CHATROOM = Event("event_show_chatroom")

    @EventDesc("首页-聊天房模块-全部房间按钮点击")
    val EVENT_CLICK_CHATROOM_BUTTON_ALL_ROOM = Event("event_click_chatroom_button_allroom")

    @EventDesc("创建房间页面-曝光")
    val EVENT_SHOW_CREATE_ROOM = Event("event_show_creatroom")

    @EventDesc("创建房间页面-发布点击")
    val EVENT_CLICK_CREATE_ROOM_BUTTON_PUBLISH = Event("event_click_creatroom_button_publish")

    @EventDesc("全部房间页面-曝光")
    val EVENT_SHOW_ALL_ROOM = Event("event_show_allroom")

    @EventDesc("全部房间页面-创建房间按钮点击")
    val EVENT_CLICK_ALL_ROOM_BUTTON_CREATE_ROOM = Event("event_click_allroom_button_creatroom")

    @EventDesc("点击进入服装创作")
    val EVENT_AVATAR_DESIGN = Event("event_avatar_design")

    @EventDesc("角色预加载-开始预加载")
    val EVENT_START_ROLE_PRE_LOAD = Event("event_start_role_pre_load")

    @EventDesc("角色预加载-R进程连接情况回调")
    val EVENT_ROLE_R_PROCESS_RESULT = Event("event_role_r_process_result")

    @EventDesc("角色预加载-R进程连接情况过滤")
    val EVENT_ROLE_R_PROCESS_FILTER = Event("event_role_r_process_filter")

    @EventDesc("角色预加载-接口请求角色gameid结果")
    val EVENT_ROLE_GAME_ID_API_RESULT = Event("event_role_game_id_api_result")

    @EventDesc("角色预加载-更新角色gameid数据")
    val EVENT_ROLE_GAME_ID_SYNC = Event("event_role_game_id_sync")

    @EventDesc("角色预加载-token回调")
    val EVENT_ROLE_TOKEN_RESULT = Event("event_role_token_result")

    @EventDesc("角色预加载-token重复过滤")
    val EVENT_ROLE_TOKEN_DISTINCT = Event("event_role_token_distinct")

    @EventDesc("角色预加载-角色gameid回调")
    val EVENT_ROLE_GAME_ID_RESULT = Event("event_role_game_id_result")

    @EventDesc("角色预加载-确定执行预加载")
    val EVENT_ROLE_DO_LOAD = Event("event_role_do_load")

    @EventDesc("腾讯云聊天室发消息失败")
    val MGS_CHAT_ROOM_MESSAGE_SEND_FAIL = Event("mgs_message_send_fail")

    @EventDesc("加入聊天室失败")
    val MGS_CHAT_ROOM_JOIN_FAIL = Event("mgs_chat_room_join_fail")

    @EventDesc("角色预加载-用户信息改变通知全局")
    val EVENT_VISITOR_LOGIN_UPDATE = Event("event_visitor_login_update")

    @EventDesc("开麦年龄限制弹窗展示")
    val EVENT_VOICE_CHAT_DIALOG_SHOW = Event("voice_chat_age_dialog_show")

    @EventDesc("开麦年龄限制弹窗取消点击")
    val EVENT_VOICE_CHAT_DIALOG_CANCEL_CLICK = Event("voice_chat_age_dialog_cancel_click")

    @EventDesc("开麦年龄限制弹窗去修改点击")
    val EVENT_VOICE_CHAT_DIALOG_CHECK_CLICK = Event("voice_chat_age_dialog_check_click")

    @EventDesc("修改年龄完成")
    val EVENT_PROFILE_EDIT_AGE_COMFIRM = Event("profile_edit_age_confirm")

    @EventDesc("半屏详情页五图点击展开")
    val GAME_FIVE_PICTURE_MORE_CLICK = Event("game_five_picture_more_click")

    @EventDesc("拉起游戏结果")
    val EVENT_LAUNCH_GAME_RESULT = Event("event_launch_game_result")

    @EventDesc("个人主页用户认证图标点击")
    val EVENT_PROFILE_ACCOUNT_VERIFIED_ICON_CLICK = Event("profile_account_verified_icon_click")

    //首页好友模块
    @EventDesc("首页好友模块展示")
    val EVENT_PARTY_FRIENDS_SHOW = Event("party_friends_show")

    @EventDesc("首页好友模块滑动")
    val EVENT_PARTY_FRIENDS_SLIDE = Event("party_friends_slide")

    @EventDesc("首页好友模块用户点击")
    val EVENT_PARTY_FRIENDS_USER_CLICK = Event("party_friends_user_click")

    @EventDesc("加好友入口点击")
    val EVENT_ADD_FRIENDS_CLICK = Event("add_friends_click")

    @EventDesc("首页好友模块聊天点击")
    val EVENT_PARTY_FRIENDS_CHAT_CLICK = Event("party_friends_chat_click")

    @EventDesc("首页好友模块查看主页点击")
    val EVENT_PARTY_FRIENDS_VIEW_PROFILE_CLICK = Event("party_friends_view_profile_click")

    @EventDesc("首页好友模块跟房点击")
    val EVENT_PARTY_FRIENDS_JOIN_CLICK = Event("party_friends_join_click")

    @EventDesc("通知消息栏展示")
    val EVENT_NOTICE_SEND_MESSAGE_SHOW = Event("event_notice_send_message_show")

    @EventDesc("点击通知消息栏")
    val EVENT_NOTICE_CLICK_SEND_MESSAGE = Event("event_notice_click_send_message")

    @EventDesc("通知消息已读")
    val EVENT_NOTICE_SEND_MESSAGE_READ = Event("event_notice_send_message_read")

    @EventDesc("通知消息点击跳转")
    val EVENT_NOTICE_SEND_MESSAGE_CLICK_JUMP = Event("event_notice_send_message_click_jump")

    // feedback
    @EventDesc("设置页-feedback点击")
    val EVENT_SETTING_FEEDBACK_CLICK = Event("event_setting_feedback_click")

    @EventDesc("游戏详情页-更多弹窗-feedback点击")
    val EVENT_FEEDBACK_CLICK = Event("event_feedback_click")

    @EventDesc("游戏详情页-更多弹窗展示feedback")
    val EVENT_FEEDBACK_SHOW_PAGE = Event("event_feedback_show_page")

    @EventDesc("feedback-feedback页面展示")
    val EVENT_FEEDBACK_PAGE_SHOW = Event("event_feedback_page_show")

    @EventDesc("feedback-submit点击")
    val EVENT_FEEDBACK_SUBMIT_CLICK = Event("event_feedback_submit_click")

    @EventDesc("举报语音房")
    val EVENT_ROOM_REPORT_REPORT_CLICK = Event("report_live_room")

    @EventDesc("用户资料卡片举报")
    val EVENT_MGS_CARD_REPORT_REPORT_CLICK = Event("report_mgs_user_card_click")

    @EventDesc("ugc模版拉起失败")
    val EVENT_UGC_TEMPLATE_FAIL = Event("ugc_template_fail")

    @EventDesc("新消息通知设置页面")
    val EVENT_NOTIFICATION_SETTING_CLICK = Event("c_notification_setting_click")

    @EventDesc("gpark通知权限申请权限结果")
    val EVENT_NOTIFICATION_APPLICATION = Event("event_notification_application")

    @EventDesc("通知权限开关设置（push开关状态）")
    val EVENT_NOTIFICATION_SWITCH = Event("event_notification_switch")

    @EventDesc("角色加载失败弹窗展现")
    val EVENT_LOADING_AVATAR_FAILSHOW = Event("event_loading_avatar_failshow")

    @EventDesc("角色加载失败弹窗点击")
    val EVENT_LOADING_AVATAR_FAILCLICK = Event("event_loading_avatar_failclick")

    @EventDesc("社区-feed展示")
    val EVENT_COMMUNITY_FEED_SHOW = Event("community_feed_show")

    @EventDesc("社区-feed帖子展示")
    val EVENT_COMMUNITY_POST_SHOW = Event("community_post_show")

    @EventDesc("社区-feed帖子点击")
    val EVENT_COMMUNITY_POST_CLICK = Event("community_post_click")

    @EventDesc("社区-发帖入口点击")
    val EVENT_COMMUNITY_ADD_POST_CLICK = Event("community_add_post_click")

    @EventDesc("社区-帖子详情页展示")
    val EVENT_POST_DETAIL_SHOW = Event("post_detail_show")

    @EventDesc("社区-帖子详情页查看图片")
    val EVENT_POST_VIEW_PICTURE = Event("post_view_picture")

    @EventDesc("社区-帖子详情页播放视频")
    val EVENT_POST_VIEW_VIDEO = Event("post_view_video")

    @EventDesc("全屏播放视频时长")
    val EVENT_POST_VIDEO_PLAY_TIME = Event("post_video_play_time")

    @EventDesc("feed视频时长")
    val EVENT_FEED_POST_VIDEO_PLAY_TIME = Event("feed_post_video_play_time")

    @EventDesc("社区-帖子点赞")
    val EVENT_POST_LIKE_CLICK = Event("post_like_click")

    @EventDesc("社区-评论点赞")
    val EVENT_POST_REPLY_LIKE_CLICK = Event("post_reply_like_click")

    @EventDesc("社区-发布帖子评论")
    val EVENT_POST_REPLY_SEND = Event("post_reply_send")

    @EventDesc("社区-展开更多评论点击")
    val EVENT_POST_SHOW_REPLIES_CLICK = Event("post_show_replies_click")

    @EventDesc("举报曝光")
    val EVENT_REPORT_SHOW = Event("event_report_show")

    @EventDesc("社区-发帖发布点击")
    val EVENT_ADD_POST_COMPLETE = Event("add_post_complete")

    @EventDesc("社区-关注点击")
    val EVENT_FOLLOW_CLICK = Event("follow_click")

    @EventDesc("小屋入口曝光")
    val EVENT_DSHOME_ENTRY_SHOW = Event("c_dshome_entry_show")

    @EventDesc("点击小屋各入口时上报")
    val EVENT_DSHOME_ENTRY_CLICK = Event("c_dshome_entry_click")

    @EventDesc("点击小屋详情页[Join}时上报")
    val EVENT_DSHOME_DETAIL_CLICK = Event("c_dshome_detail_click")

    @EventDesc("ts游戏mw不能兼容弹窗展示")
    val EVENT_MW_TS_INCOMPATIBLE_SHOW = Event("event_mw_ts_incompatible_show")

    @EventDesc("选择avatar页面展示")
    val CREATE_AVATAR_NEXT_SHOW = Event("create_avatar_next_show")

    @EventDesc("选择avatar下一步点击")
    val CREATE_AVATAR_NEXT_CLICK = Event("create_avatar_next_click")

    @EventDesc("新手引导跳过点击")
    val CREATE_AVATAR_PAGE_SKIP_CLICK = Event("create_avatar_page_skip_click")

    @EventDesc("随机昵称点击")
    val NICKNAME_PAGE_RANDOM_CLICK = Event("nickname_page_random_click")

    @EventDesc("选择年龄界面展示")
    val NEWBIE_GUIDE_AGE_SHOW = Event("newbie_guide_age_show")

    @EventDesc("输入昵称界面展示")
    val NICKNAME_PAGE_NEXT_SHOW = Event("nickname_page_next_show")

    @EventDesc("设置昵称下一步点击")
    val NICKNAME_PAGE_NEXT_CLICK = Event("nickname_page_next_click")

    @EventDesc("设置生日完成点击")
    val BIRTHDAY_PAGE_PLAY_CLICK = Event("birthday_page_play_click")

    @EventDesc("精选点击ugc入口")
    val UGC_CREATE_ENTRY_ENTRY = Event("ugc_create_entry_entry")

    @EventDesc("精选ugc引导曝光")
    val UGC_CREATE_ENTRY_GUIDE_SHOW = Event("ugc_create_entry_guide_show")

    @EventDesc("ugc建造页模板引导曝光")
    val UGC_CREATE_GUIDE_POPUP_SHOW = Event("ugc_create_guide_popup_show")

    @EventDesc("ugc建造页模板引导点击")
    val UGC_CREATE_GUIDE_POPUP_CLICK = Event("ugc_create_guide_popup_click")

    @EventDesc("avatar标签页展现")
    val C_EVENT_SHOW_AVATARTAB = Event("c_event_show_avatartab")

    @EventDesc("avatar标签页隐藏")
    val C_EVENT_END_AVATARTAB = Event("c_event_end_avatartab")

    @EventDesc("avatar标签页展现时角色加载完成")
    val C_EVENT_SHOW_AVATARTAB_RESULT = Event("c_event_show_avatartab_result")

    @EventDesc("view页点击moments")
    val EVENT_VIEW_CLICK_MOMENTS = Event("view_click_moments")

    @EventDesc("feed页点击moments")
    val EVENT_FEED_CLICK_MOMENTS = Event("event_feed_click_moments")

    @EventDesc("moments模板页展现")
    val EVENT_MOMENTS_TEMPLATE_SHOW = Event("event_moments_template_show")

    @EventDesc("moments模板页点击模板")
    val EVENT_MOMENTS_TEMPLATE_CLICK = Event("event_moments_template_click")

    @EventDesc("moments模板分享")
    val EVENT_MOMENTS_SHARE = Event("event_moments_share")

    @EventDesc("moments模板保存本地")
    val EVENT_MOMENTS_SAVE = Event("event_moments_save")

    @EventDesc("gpark通知权限申请弹窗展示")
    val EVENT_NOTIFICATION_APPLICATION_SHOW = Event("event_notification_application_show")

    @EventDesc("发帖页面展现")
    val EVENT_ADD_POST_SHOW = Event("add_post_show")

    @EventDesc("设备通知权限开关")
    val EVENT_DEVICE_NOTIFICATION_STATE = Event("event_device_notification_state")

    @EventDesc("ugc聊天房模板新增点击")
    val EVENT_CHATROOM_UGC_TEMPLATE_ENTER_CLICK = Event("event_chatroom_ugc_template_enter_click")

    @EventDesc("ugc聊天房模板选择点击")
    val EVENT_CHATROOM_UGC_TEMPLATE_CHOOSE = Event("event_chatroom_ugc_template_choose")

    @EventDesc("ugc聊天房模板弹窗")
    val EVENT_CHATROOM_UGC_TEMPLATE_POPUP_SHOW = Event("event_chatroom_ugc_template_popup_show")

    @EventDesc("ugc聊天房模板删除点击")
    val EVENT_CHATROOM_UGC_TEMPLATE_DELETE_CLICK = Event("event_chatroom_ugc_template_delete_click")

    @EventDesc("ugc聊天房模板编辑点击")
    val EVENT_CHATROOM_UGC_TEMPLATE_EDIT_CLICK = Event("event_chatroom_ugc_template_edit_click")

    @EventDesc("ugc聊天房模板删除成功")
    val EVENT_CHATROOM_UGC_TEMPLATE_DELETE_SUCCESS = Event("event_chatroom_ugc_template_delete_success")

    @EventDesc("ugc详情页一级评论展示")
    val UGC_DETAIL_PAGE_REVIEW_SHOW = Event("ugc_detail_page_review_show")

    @EventDesc("ugc写评论入口点击")
    val UGC_DETAIL_PAGE_REVIEW_CLICK = Event("ugc_detail_page_review_click")

    @EventDesc("ugc评论发布成功")
    val UGC_GAME_REVIEW_PUBLISH_SUCCESS = Event("ugc_game_review_publish_success")

    @EventDesc("ugc评论详情入口点击")
    val UGC_DETAIL_SUBPAGE_REVIEW_CLICK = Event("ugc_detail_subpage_review_click")

    @EventDesc("ugc评论展开点击")
    val UGC_DETAIL_PAGE_REVIEW_COLLAPSE_CLICK = Event("ugc_detail_page_review_collapse_click")

    @EventDesc("ugc评论收起点击")
    val UGC_DETAIL_PAGE_REVIEW_REPLISE_CLICK = Event("ugc_detail_page_review_replise_click")

    @EventDesc("ugc游戏评论回复点击")
    val UGC_GAME_REVIEW_REPLAY_CLICK = Event("ugc_game_review_replay_click")

    @EventDesc("ugc评论菜单展示")
    val UGC_GAME_REVIEW_SET_SHOW = Event("ugc_game_review_set_show")

    @EventDesc("ugc评论置顶点击")
    val UGC_GAME_REVIEW_TOP_CLICK = Event("ugc_game_review_top_click")

    @EventDesc("ugc评论复制点击")
    val UGC_GAME_REVIEW_COPY_CLICK = Event("ugc_game_review_copy_click")

    @EventDesc("ugc评论删除成功")
    val UGC_GAME_REVIEW_DELETE_SUCCESS = Event("ugc_game_review_delete_success")

    @EventDesc("ugc评论举报成功")
    val UGC_GAME_REVIEW_REPORT_SUCCESS = Event("ugc_game_review_report_success")

    @EventDesc("ugc游戏评论点赞点击")
    val UGC_GAME_REVIEW_LIKE_CLICK = Event("ugc_game_review_like_click")

    @EventDesc("ugc游戏评论排序点击")
    val UGC_GAME_REVIEW_SORT_CLICK = Event("ugc_game_review_sort_click")

    @EventDesc("ugc游戏评论提示弹窗展示")
    val UGC_GAME_REVIEW_PROMPT_POPUP_SHOW = Event("ugc_game_review_prompt_popup_show")

    @EventDesc("ugc游戏评论提示弹窗点击")
    val UGC_GAME_REVIEW_PROMPT_POPUP_CLICK = Event("ugc_game_review_prompt_popup_click")

    @EventDesc("二级评论回复的用户点击")
    val EVENT_GAME_REVIEW_REPLIES_CLICK = Event("c_game_review_replies_click")

    @EventDesc("二级评论回复成功")
    val EVENT_GAME_REVIEW_REPLIES_SUCESS = Event("c_game_review_replies_success")

    @EventDesc("针对某条一级评论展开n条回复用户点击")
    val EVENT_GAME_REVIEW_EXPAND = Event("c_game_review_show_expends_replies_click")

    @EventDesc("针对某条一级评论展示更多的用户点击")
    val EVENT_GAME_REVIEW_EXPAND_USER_CLICK = Event("c_game_review_show_more_replies_click")


    @EventDesc("DynamicLink解析流程")
    val EVENT_DYNAMIC_LINK_PROCESS = Event("event_dynamic_link_process")

    @EventDesc("DynamicLink解析流程失败")
    val EVENT_DYNAMIC_LINK_PROCESS_FAILED = Event("event_dynamic_link_process_failed")

    @EventDesc("发帖页跳转")
    val EVENT_EVENT_PUBLISH_POST_JUMP = Event("event_publish_post_jump")

    @EventDesc("守则页跳转")
    val EVENT_PUBLISH_RULE_JUMP = Event("event_publish_rule_jump")

    @EventDesc("wallet充值完成")
    val EVENT_PAY_RESULT = Event("event_gpark_pay_result")

    @EventDesc("完善账号弹窗曝光")
    val EVENT_ACCOUNT_COMPLETE_SHOW = Event("event_gpark_complete_account_show")

    @EventDesc("完善账号点击")
    val EVENT_ACCOUNT_COMPLETE_CLICK = Event("event_gpark_complete_account_click")

    @EventDesc("消耗G\$曝光")
    val EVENT_GPARK_PAY_COIN_SHOW = Event("event_gpark_pay_coin_show")

    @EventDesc("消耗G\$点击")
    val EVENT_GPARK_PAY_COIN_CLICK = Event("event_gpark_pay_coin_click")

    @EventDesc("消耗G\$结果")
    val EVENT_GPARK_PAY_COIN_SUCCESS = Event("event_gpark_pay_coin_success")

    @EventDesc("G\$不足充值页面曝光")
    val EVENT_NO_ENOUGH_COIN_PAY_VIEW_SHOW = Event("event_client_pay_nocoin_show")

    @EventDesc("G\$不足充值页面点击")
    val EVENT_NO_ENOUGH_COIN_PAY_VIEW_CLICK = Event("event_client_pay_nocoin_click")


    @EventDesc("视频展示")
    val EVENT_VIDEO_FEED_ITEM_SHOW = Event("community_video_feed_show")

    @EventDesc("视频点击暂停")
    val EVENT_ON_CLICK_VIDEO_PAUSE = Event("event_on_click_video_pause")

    @EventDesc("视频暂停恢复播放")
    val EVENT_ON_CLICK_VIDEO_RESUME = Event("event_on_click_video_resume")

    @EventDesc("上滑刷下一个视频")
    val EVENT_ON_SLIDE_TO_NEXT_VIDEO = Event("community_video_slide_next_video")

    @EventDesc("下拉返回上一个视频")
    val EVENT_ON_SLIDE_TO_PREVIOUS_VIDEO = Event("community_video_slide_previous_video")

    @EventDesc("获取或播放视频失败")
    val EVENT_ON_SLIDE_OR_PLAY_VIDEO_FAILED = Event("community_video_show_failed_reason")

    @EventDesc("游戏卡片展示")
    val EVENT_GAME_FEED_VIDEO_SHOW = Event("c_event_game_feed_video_show")

    @EventDesc("视频流游戏卡片点击")
    val EVENT_GAME_FEED_VIDEO_CLICK = Event("c_event_game_feed_video_click")

    @EventDesc("视频流评论按钮点击")
    val EVENT_CLICK_VIDEO_COMMENT = Event("event_click_video_comment")

    @EventDesc("作者头像点击")
    val EVENT_ON_CLICK_VIDEO_AUTHOR_AVATAR = Event("event_on_click_video_author_avatar")

    @EventDesc("进度条点击拖动")
    val EVENT_CLICK_VIDEO_PROGRESS = Event("event_click_video_progress")

    @EventDesc("视频预加载开始")
    val EVENT_VIDEO_PRELOAD_START = Event("video_preload_start")

    @EventDesc("视频预加载完成")
    val EVENT_VIDEO_PRELOAD_FINISH = Event("video_preload_success")

    @EventDesc("视频预加载错误")
    val EVENT_VIDEO_PRELOAD_ERR = Event("video_preload_failed")


    @EventDesc("推送点击")
    val FCM_PUSH_CLICK = Event("fcm_push_click")

    @EventDesc("好友底栏_消息展示_push")
    val C_IM_MESSAGES_TAB_SHOW_PUSH = Event("c_im_messages_tab_show_push")

    @EventDesc("好友请求页面展示")
    val EVENT_SHOW_FRIEND_REQUEST = Event("event_show_friend_request")

    @EventDesc("好友聊天详情页曝光")
    val EVENT_SHOW_CONVERSATION = Event("event_show_conversation")

    @EventDesc("设置面板按钮点击")
    val EVENT_MGS_MW_SETTINGS_CLICK = Event("mgs_mw_settings_click ")

    @EventDesc("从边栏入口进入游戏")
    val C_ENTER_TAB = Event("c_enter_tab")

    @EventDesc("设置_语言入口点击")
    val EVENT_SETTINGS_LANGUAGE_CLICK = Event("settings_language_click")

    @EventDesc("设置_语言切换")
    val EVENT_SETTINGS_LANGUAGE_CHOOSE = Event("settings_language_choose")

    @EventDesc("Post页面分享穿搭按钮点击")
    val POST_SHARE_OUTFIT_CLICK = Event("event_tryon_share_click")

    @EventDesc("个人页试穿按钮点击")
    val PROFILE_TRY_ON_CLICK = Event("event_profile_tryon_click")

    @EventDesc("帖子试穿按钮展示")
    val POST_LIST_OUTFIT_SHOW = Event("event_community_tryon_show")

    @EventDesc("帖子试穿按钮点击")
    val POST_LIST_OUTFIT_CLICK = Event("event_community_tryon_click")

    @EventDesc("post提示打开权限弹窗点击")
    val POST_ENABLE_TRY_ON_PERMISSION_CLICK = Event("event_post_decoration_permission_click")

    @EventDesc("post创角提示点击")
    val POST_CREATE_AVATAR_TIPS_CLICK = Event("event_post_create_avatar_click")

    @EventDesc("“允许他人试穿装扮”开关点击")
    val PRIVACY_SETTINGS_TRY_ON_CLICK = Event("set_decoration_permission_click")

    @EventDesc("首页游戏曝光")
    val C_FEED_ITEM_SHOW = Event("c_feed_item_show")

    @EventDesc("首页游戏点击")
    val C_FEED_ITEM_CLICK = Event("c_feed_item_click")

    @EventDesc("点击首页视频区域更多")
    val C_FEED_VIDEO_CLICK_MORE = Event("c_feed_video_click_more")

    @EventDesc("点击二级页面返回")
    val C_FEED_VIDEO_CLICK_BACK = Event("c_feed_video_click_back")

    @EventDesc("视频二级页面曝光")
    val C_FEED_VIDEO_PAGE_SHOW = Event("c_feed_video_page_show")

    @EventDesc("视频流中关注帖子发布者")
    val POST_FEED_FOLLOW = Event("post_feed_follow")

    @EventDesc("退出沉浸式视频流页面")
    val COMMUNITY_VIDEO_FEED_BACK = Event("community_video_feed_back")

    @EventDesc("首页模板点击")
    val HALFSCREEN_MOMENTS_CLICK = Event("halfscreen_moments_click")

    @EventDesc("首页UGC游戏列表点击")
    val HALFSCREEN_UGCLIST_CLICK = Event("halfscreen_ugclist_click")

    @EventDesc("首页T台游戏列表点击")
    val HALFSCREEN_GAMELIST_CLICK = Event("halfscreen_gamelist_click")

    @EventDesc("设置页面订阅按钮点击")
    val EVENT_SETTING_SUBSCRIBE_CLICK = Event("c_subscribe_click")

    @EventDesc("订阅个人中心入口曝光")
    val EVENT_SUBSCRIBE_SHOW = Event("event_gpark_personal_sub_show")

    @EventDesc("订阅个人中心入口点击")
    val EVENT_USER_SUBSCRIBE_CLICK = Event("event_gpark_personal_sub_click")

    @EventDesc("订阅页面支付信息结果")
    val EVENT_SUBSCRIBE_PAY_RESULT = Event("event_gpark_subscription_pay_result")

    @EventDesc("订阅权益转跳弹窗曝光")
    val EVENT_SUBSCRIBE_PRIVILEGE_SHOW = Event("event_gpark_sub_see_show")

    @EventDesc("订阅权益转跳弹窗点击")
    val EVENT_SUBSCRIBE_PRIVILEGE_CLICK = Event("event_gpark_sub_see_click")

    @EventDesc("页面启动监控")
    val EVENT_PAGE_STARTUP_MONITOR = Event("event_page_startup_monitor")

    @EventDesc("模式选择页面点击")
    val MODE_SELECT_CLICK = Event("mode_select_click")

    @EventDesc("模式选择页面展示")
    val MODE_PAGE_SHOW = Event("mode_page_show")

    @EventDesc("点击话题跳转话题页")
    val COMMUNITY_TAG_CLICK = Event("tag_click")

    @EventDesc("关注话题页")
    val COMMUNITY_TAG_PAGE_FOLLOW = Event("tag_page_follow")

    @EventDesc("话题曝光在热门榜单")
    val COMMUNITY_TAG_RECOMMEND_SHOW = Event("community_tag_recommend")

    @EventDesc("客户端个人页style作品删除弹窗点击")
    val PROFILE_OUTFIT_DELETE_CLICK = Event("event_style_delete_click")

    @EventDesc("客户端个人页试穿作品点赞")
    val PROFILE_OUTFIT_LIKE_CLICK = Event("event_style_like_click")

    @EventDesc("客户端个人页试穿作品点击")
    val PROFILE_OUTFIT_CLICK = Event("event_style_tryon_click")

    @EventDesc("角色运营位点击")
    val AVATAR_TAB_EVENT_CLICK = Event("avatar_tab_event_click")

    @EventDesc("角色运营位显示")
    val AVATAR_TAB_EVENT_SHOW = Event("avatar_tab_event_show")


    @EventDesc("角色运营位弹窗曝光")
    val EVENT_AVATAR_POPUP_SHOW = Event("page_show_avatar_popup")

    @EventDesc("角色运营位弹窗点击")
    val EVENT_AVATAR_POPUP_CLICK = Event("event_click_avatar_popup")

    @EventDesc("角色运营位弹窗关闭(点击关闭按钮/点击返回按钮)")
    val EVENT_AVATAR_POPUP_DISMISS_CLICK = Event("event_click_avatar_popclose")

    @EventDesc("搜索页面展示")
    val EVENT_SEARCH_SHOW = Event("event_search_show")

    @EventDesc("触发搜索")
    val EVENT_SEARCH_TRIGGER = Event("event_search_trigger")

    @EventDesc("搜索结果")
    val EVENT_SEARCH_RESULT = Event("event_search_result")

    @EventDesc("点击取消")
    val SEARCH_BACK_CLICK = Event("search_back_click")


    @EventDesc("点击内流视频发布入口")
    val INTERNAL_VIDEO_PUBLISH_CLICK = Event("internal_video_publish_click")


    @EventDesc("发帖页面-添加标签按钮点击")
    val ADD_POST_ADD_TAG_CLICK = Event("add_post_add_tag_click")

    @EventDesc("发帖页面-添加游戏按钮点击")
    val ADD_POST_ADD_GAME_CLICK = Event("add_post_add_game_click")

    @EventDesc("发帖添加游戏页面-添加游戏按钮点击")
    val ADD_POST_GAME_LIST_ADD_GAME_CLICK = Event("add_post_game_list_add_game_click")

    @EventDesc("发帖添加游戏页面-删除游戏按钮点击")
    val ADD_POST_GAME_LIST_REMOVE_CLICK = Event("add_post_game_list_remove_click")

    @EventDesc("发帖添加游戏页面-确认添加游戏按钮点击")
    val ADD_POST_GAME_LIST_CONFIRM_CLICK = Event("add_post_game_list_confirm_click")

    @EventDesc("发帖页面-删除已添加的游戏卡片")
    val ADD_POST_REMOVE_GAME_CARD = Event("add_post_remove_game_card")

    @EventDesc("发帖页面-添加视频/图片按钮点击")
    val ADD_POST_ADD_MEDIA_CLICK = Event("add_post_add_media_click")

    @EventDesc("发帖页面-发布成功")
    val ADD_POST_PUBLISH_SUCCESS = Event("add_post_publish_success")

    @EventDesc("注册页面展示")
    val SIGN_UP_PAGE_SHOW = Event("sign_up_page_show")

    @EventDesc("google按钮点击")
    val SIGN_UP_GOOGLE_BUTTON_CLICK = Event("sign_up_google_button_click")

    @EventDesc("获取google账号信息")
    val GET_GOOGLE_INFORMATION = Event("get_google_information")

    @EventDesc("google登录成功")
    val SIGN_UP_GOOGLE_CLICK = Event("sign_up_google_click")

    @EventDesc("初始逻辑启动")
    val EVENT_TS_GAME_PRE_DOWNLOAD_OPEN = Event("event_ts_game_pre_download_open")

    @EventDesc("mw资源预下载开始")
    val EVENT_TS_GAME_PRE_DOWNLOAD_STARTS = Event("event_ts_game_pre_download_starts")

    @EventDesc("mw资源预下载完成")
    val EVENT_TS_GAME_PRE_DOWNLOAD_SUCCESS = Event("event_ts_game_pre_download_success")


    @EventDesc("注册页面账密“注册”按钮点击")
    val SIGN_UP_SIGN_UP_CLICK = Event("sign_up_sign_up_click")

    @EventDesc("首页Bot列表曝光")
    val COMMUNITY_APP_BOT_SHOW = Event("event_appbot_show")

    @EventDesc("点击Bot进入聊天框")
    val COMMUNITY_APP_BOT_ENTER = Event("event_appbot_enter")

    @EventDesc("首页筛选器点击")
    val COMMUNITY_APP_BOT_FILTER = Event("event_appbot_filter")

    @EventDesc("关注客户端Bot")
    val COMMUNITY_APP_BOT_FOLLOW = Event("event_appbot_follow")

    @EventDesc("聊天框操作")
    val COMMUNITY_APP_BOT_REPLAY = Event("event_appbot_reply")

    @EventDesc("聊天消息交互")
    val COMMUNITY_APP_BOT_CHAT = Event("event_appbot_chat")

    @EventDesc("点击Bot进入聊天框")
    val COMMUNITY_APP_BOT_PROFILE = Event("event_appbot_profile")

    @EventDesc("WebView渲染进程崩溃")
    val WEB_VIEW_CRASH = Event("web_view_crash")

    @EventDesc("MW调用客户端")
    val EVENT_MW_CALL_CLIENT = Event("event_mw_call_client")

    @EventDesc("消息弹窗的展现")
    val EVENT_IM_UPS_DISPLAY = Event("im_ups_display")

    @EventDesc("“回复”按钮的点击")
    val EVENT_IM_UPS_REPLAY_CLICK = Event("im_ups_reply_click")

    @EventDesc("快捷回复弹窗的“发送”按钮的点击")
    val EVENT_IM_SEND_BUTTON_CLICK = Event("quick_im_send_button_click")

    @EventDesc("【我的】页“消息”按钮的点击")
    val EVENT_IM_BUTTON_CLICK = Event("im_button_click")

    @EventDesc("【我的】页“好友”按钮的点击")
    val EVENT_IM_FRIEND_CLICK = Event("friend_button_click")

    @EventDesc("appbot页面计时(离开profile、消息页时上报)")
    val EVENT_APPBOT_PLAYTIME = Event("event_appbot_playtime")

    @EventDesc("appbot聊天记录重置成功")
    val EVENT_APPBOT_RESET = Event("event_appbot_reset")

    @EventDesc("作品Tab点击")
    val PROFILE_MAPS_TAB_CLICK = Event("profile_maps_tab_click")

    @EventDesc("去创作按钮点击")
    val PROFILE_UGC_CREATE = Event("profile_ugc_create")

    @EventDesc("作品封面展示&点击")
    val PROFILE_UGC_LIST = Event("profile_ugc_list")

    @EventDesc("作品删除按钮点击")
    val PROFILE_UGC_DELETE = Event("profile_ugc_delete")

    @EventDesc("关注作者曝光")
    val EVENT_FOLLOW_CREATOR_SHOW = Event("event_follow_creator_show")

    @EventDesc("关注ugc作品曝光")
    val EVENT_FOLLOW_UGC_SHOW = Event("event_follow_ugc_show")

    @EventDesc("关注ugc作品点击")
    val UGC_FOLLOW_FEED_ITEM_CLICK = Event("ugc_follow_feed_item_click")

    @EventDesc("活动专区运营位曝光")
    val UGC_TAB_EVENT_SHOW = Event("ugc_tab_event_show")

    @EventDesc("活动专区运营位点击")
    val UGC_TAB_EVENT_CLICK = Event("ugc_tab_event_click")

    @EventDesc("明星创作者曝光")
    val EVENT_STAR_CREATOR_SHOW = Event("event_star_creator_show")

    @EventDesc("明星创作者分类点击")
    val EVENT_STAR_CREATOR_TAG_CLICK = Event("event_star_creator_tag_click")

    @EventDesc("派对管理页面展示")
    val EVENT_PARTY_MANAGEPAGE_SHOW = Event("event_party_managepage_show")

    @EventDesc("派对管理游戏删除")
    val EVENT_PARTY_MANAGEPAGE_DELETE_RESULT = Event("event_party_managepage_delete_result")

    @EventDesc("mw存储自动清理完成")
    val EVENT_MW_STORAGE_AUTO_CLEANUP = Event("event_mw_storage_auto_cleanup")

    @EventDesc("mw存储自动清理前后对比")
    val EVENT_MW_STORAGE_AUTO_CLEANUP_CONTRAST = Event("event_mw_storage_auto_cleanup_contrast")


    @EventDesc("ugc作品曝光(底部feed流)")
    val UGC_FEED_ITEM_SHOW = Event("ugc_feed_item_show")

    @EventDesc("ugc作品点击(底部feed流)")
    val UGC_FEED_ITEM_CLICK = Event("ugc_feed_item_click")

    @EventDesc("ugc分类点击")
    val UGC_CATEGORYID_CLICK = Event("ugc_categoryid_click")

    @EventDesc("ugc作品曝光(为你推荐)")
    val UGC_RECOMMEND_FEED_ITEM_SHOW = Event("ugc_recommend_feed_item_show")

    @EventDesc("ugc作品点击(为你推荐)")
    val UGC_RECOMMEND_FEED_ITEM_CLICK = Event("ugc_recommend_feed_item_click")

    //防止打扰、
    @EventDesc("防打扰按钮的下发")
    val EVENT_IM_PRIVATE_CLOSE_SHOW = Event("im_ups_no_reminders_show")

    @EventDesc("防打扰按钮的点击")
    val EVENT_IM_PRIVATE_CLOSE_CLICK = Event("im_ups_no_reminders_click")

    @EventDesc("防打扰toast的下发")
    val EVENT_IM_PRIVATE_CLOSE_TIPS = Event("no_reminders_toast_show")

    @EventDesc("设置页-聊天消息通知开关的点击")
    val EVENT_SET_IM_PRIVATE_CLOSE_CLICK = Event("setting_im_reminders_switch_click")

    @EventDesc("【Avatar generated】页面点击“back”")
    val EVENT_AI_BOT_GENERATE_BACK = Event("ai_bot_avatar_generated_page_click_back")

    @EventDesc("API平均请求时间小于80ms")
    val C_SE_API_REQUEST_TIME_LE80 = Event("c_se_api_request_time_le80")

    @EventDesc("API平均请求时间小于100ms")
    val C_SE_API_REQUEST_TIME_LE100 = Event("c_se_api_request_time_le100")

    @EventDesc("API平均请求时间小于120ms")
    val C_SE_API_REQUEST_TIME_LE120 = Event("c_se_api_request_time_le120")

    @EventDesc("API平均请求时间小于140ms")
    val C_SE_API_REQUEST_TIME_LE140 = Event("c_se_api_request_time_le140")

    @EventDesc("API平均请求时间小于160ms")
    val C_SE_API_REQUEST_TIME_LE160 = Event("c_se_api_request_time_le160")

    @EventDesc("API平均请求时间小于180ms")
    val C_SE_API_REQUEST_TIME_LE180 = Event("c_se_api_request_time_le180")


    @EventDesc("调用角色保存分享")
    val ROLE_SAVE_SHARE = Event("role_save_share")

    @EventDesc("点击角色保存分享关闭")
    val ROLE_SAVE_CLOSE = Event("role_save_close")

    @EventDesc("角色保存分享点击分享方式")
    val ROLE_SAVE_SHARE_CLICK = Event("role_save_share_click")

    @EventDesc("角色保存分享结果")
    val ROLE_SAVE_SHARE_RESULT = Event("role_save_share_result")

    object From {
        /**
         * 个人主页来源
         * [需求文档] https://meta.feishu.cn/wiki/WVLDw0VlbiPwnOkjGyQc9fKOn9e
         */
        const val FROM_KOL_CREATOR_TAB_FOLLOWED = "tab_followed"
        const val FROM_KOL_CREATOR_MORE_FOLLOWED = "more_followed"
        const val FROM_KOL_CREATOR_MORE_DISCOVERIES = "more_discoveries"
        const val FROM_KOL_CREATOR_TAB_STAR = "tab_star"
        const val FROM_KOL_CREATOR_MORE_STAR = "more_star"
    }

    object Type {
        /**
         * 个人主页ugc
         * [需求文档] https://meta.feishu.cn/wiki/WVLDw0VlbiPwnOkjGyQc9fKOn9e
         */
        const val TYPE_UGC_SHOW = "1"
        const val TYPE_UGC_CLICK = "2"

        /**
         * kol运营位
         * [需求文档] https://meta.feishu.cn/wiki/Q2gKwVmeuiI2vTkY4ZDcNzAYnlc
         */
        const val TYPE_OPERATION_KOL_FLYWHEEL = "flywheel"
        const val TYPE_OPERATION_BANNER = "banner"

        /**
         * kol创作者
         * [需求文档] https://meta.feishu.cn/wiki/Q2gKwVmeuiI2vTkY4ZDcNzAYnlc
         */
        const val TYPE_KOL_CREATOR_TAB_FOLLOWED = "tab_followed"
        const val TYPE_KOL_CREATOR_MORE_FOLLOWED = "more_followed"
        const val TYPE_KOL_CREATOR_MORE_DISCOVERIES = "more_discoveries"
        const val TYPE_KOL_CREATOR_TAB_STAR = "tab_star"
        const val TYPE_KOL_CREATOR_MORE_STAR = "more_star"

        const val TYPE_STAR_CREATOR_LABEL_TAB_BRIEF = "tab_brief"
        const val TYPE_STAR_CREATOR_LABEL_TAB_ALL = "tab_all"
        const val TYPE_STAR_CREATOR_LABEL_MORE_BRIEF = "more_brief"
        const val TYPE_STAR_CREATOR_LABEL_MORE_ALL = "more_all"
    }

    object Result {
        /**
         * [需求文档] https://meta.feishu.cn/wiki/WVLDw0VlbiPwnOkjGyQc9fKOn9e
         */
        const val RESULT_UGC_DELETE_CONFIRM = "1"
        const val RESULT_UGC_DELETE_CANCEL = "2"
    }

    @EventDesc("首页应用的通知权限申请弹窗展示")
    val EVENT_APP_FIRST_PAGE_DIALOG_SHOW = Event("app_firstpage_notification_dialog_show")

    @EventDesc("首页应用的通知权限申请弹窗点击")
    val EVENT_APP_FIRST_PAGE_DIALOG_CLICK = Event("app_firstpage_notification_dialog_click")

    @EventDesc("帖子互动开权限提醒展示")
    val EVENT_INTERACT_PUS_POST_SHOW = Event("post_interact_push_notifications_dialog_show")

    @EventDesc("帖子互动开权限提醒点击")
    val EVENT_INTERACT_PUS_POST_CLICK = Event("post_interact_push_notifications_dialog_click")

    @EventDesc("好友聊天开权限提醒展示")
    val EVENT_CHAT_PUSH_POST_SHOW = Event("chat_push_notifications_dialog_show")

    @EventDesc("好友聊天开权限提醒点击")
    val EVENT_CHAT_PUSH_POST_CLICK = Event("chat_push_notifications_dialog_click")

    @EventDesc("互动界面常驻小标签展示")
    val EVENT_FIRST_PUSH_POST_SHOW = Event("chat_push_notifications_steer_show")

    @EventDesc("互动界面常驻小标签“去开启”点击")
    val EVENT_FIRST_PUSH_POST_CLICK = Event("chat_push_notifications_steer_click")

    @EventDesc("互动界面常驻小标签“关闭”点击")
    val EVENT_FIRST_PUSH_POST_CLOSE = Event("chat_push_notifications_steer_off_click")

    @EventDesc("好评引导弹窗展示")
    val EVENT_ACCLAIM_UPS_SHOW = Event("event_acclaim_ups_show")

    @EventDesc("好评引导弹窗的点击")
    val EVENT_ACCLAIM_UPS_CLICK = Event("event_acclaim_ups_click")

    @EventDesc("AI bot 入口点击")
    val EVENT_AI_BOT_CREATE_CLICK = Event("ai_bot_create_click")

    @EventDesc("【选择性别】页面点击“开始")
    val EVENT_AI_BOT_GENDER_CLICK = Event("ai_bot_gender_click_start")

    @EventDesc("【选择性别】页面点击“关闭")
    val EVENT_AI_BOT_GENDER_CLOSE = Event("ai_bot_gender_click_close")

    @EventDesc("达到次数上限的 toast 曝光")
    val EVENT_AI_BOT_GENDER_LIMIT = Event("ai_bot_generate_limit_exposure")

    @EventDesc("【Avatar】 页面点击“generate")
    val EVENT_AI_BOT_AVATAR_CLICK = Event("ai_bot_avatar_page_click")

    @EventDesc("【Avatar generated】 页面点击“confirm")
    val EVENT_AI_BOT_AVATAR__GENERATE_CLICK = Event("ai_bot_avatar_generated_page_click_next")

    @EventDesc("【Avatar confirm】 页面点击“next” ")
    val EVENT_AI_BOT_AVATAR_COMFIRM_CLICK = Event("ai_bot_avatar_confirm_page_click_next")

    @EventDesc("【Character】 页面点击“AI Writer” ")
    val EVENT_AI_BOT_CHARACTER_WRITE_CLICK = Event("ai_bot_character_page_click_ai_writer")

    @EventDesc("【Character】 页面点击“generate” ")
    val EVENT_AI_BOT_CHARACTER_GENERATE_CLICK = Event("ai_bot_character_page_click_generate")


    @EventDesc("请求google登录/注册接口失败")
    val SIGN_UP_GOOGLE_REQUEST_API_FAIL = Event("sign_up_google_request_api_fail")

    // 冷启动到首页图片展示时长
    @EventDesc("冷启动到首页图片展示时长")
    val EVENT_FEED_COLD_START_SHOW = Event("event_feed_cold_start_show")

    @EventDesc("iap开始支付获取余额时间")
    val EVENT_IAP_TIME_PAY_GET_BALANCE = Event("event_iap_time_pay_get_balance")

    @EventDesc("展示web弹框时间")
    val EVENT_IAP_TIME_SHOW_DIALOG = Event("event_iap_time_show_dialog")

    @EventDesc("webview加载时间")
    val EVENT_IAP_TIME_WEB_LOAD = Event("event_iap_time_web_load")

    @EventDesc("点击pay到拉起弹窗的时间")
    val EVENT_IAP_PAY_DIALOG_SHOW = Event("event_iap_google_pay_dialog_show")

    @EventDesc("支付中到支付完成")
    val EVENT_IAP_PAY_ALL_TIME = Event("event_iap_google_pay_all_time")

    @EventDesc("分享激活")
    val EVENT_SHARE_ACTIVE = Event("event_share_active")

    @EventDesc("TS三方分享按钮点击")
    val EVENT_MOMENTS_SHARE_CLICK = Event("event_moments_share_click")

    @EventDesc("TS三方分享渠道点击")
    val EVENT_MOMENTS_SHARE_RESULT = Event("event_moments_share_result")

    @EventDesc("点击分享按钮")
    val EVENT_SHARE_CLICK = Event("event_share_click")

    @EventDesc("分享页面曝光")
    val EVENT_SHARE_SHOW = Event("event_share_show")

    @EventDesc("选择分享方式")
    val EVENT_SHARE_TYPE = Event("event_share_type")

    @EventDesc("底栏加号点击")
    val EVENT_CREATE_TAB_CLICK = Event("event_create_tab_click")

    @EventDesc("底栏加号-创建地图点击")
    val EVENT_CREATE_TAB_MAPS_CLICK = Event("event_create_tab_maps_click")

    @EventDesc("底栏加号-拍剧点击")
    val EVENT_CREATE_TAB_MOMENTS_CLICK = Event("event_create_tab_moments_click")

    @EventDesc("底栏加号-发帖点击")
    val EVENT_CREATE_TAB_POSTS_CLICK = Event("event_create_tab_posts_click")

    @EventDesc("底栏加号-服装ugc点击")
    val EVENT_CREATE_TAB_DESIGNS_CLICK = Event("event_create_tab_design_click")

    @EventDesc("搜索结果曝光")
    val SEARCH_ITEM_SHOW = Event("search_item_show")

    @EventDesc("搜索结果点击")
    val SEARCH_ITEM_CLICK = Event("search_item_click")

    @EventDesc("官号列表的点击")
    val EVENT_NOTICE_GAME_CLICK = Event("event_notice_game_click")

    @EventDesc("【我的】页【设置】按钮的点击")
    val HOMEPAGE_SETTING_CLICK = Event("homepage_setting_click")

    @EventDesc("公开展示好友、关注和粉丝列表")
    val SWITCH_EXPOSE_SOCIAL_LIST = Event("switch_expose_social_list")

    @EventDesc("个人主页_关注列表_点击")
    val EVENT_CLICK_FOLLOW_LIST = Event("event_click_follow_list")

    @EventDesc("个人主页_粉丝列表_点击")
    val EVENT_CLICK_FANS_LIST = Event("event_click_fans_list")

    @EventDesc("个人主页_获赞列表_点击")
    val EVENT_CLICK_LIKE_LIST = Event("event_click_like_list")

    @EventDesc("发布时绑定弹窗展示")
    val PUBLISH_CONNECT_DIALOG_SHOW = Event("publish_connect_dialog_show")

    @EventDesc("发布时绑定弹窗关闭")
    val PUBLISH_CONNECT_DIALOG_CLOSE = Event("publish_connect_dialog_close")

    @EventDesc("发布时绑定弹窗确认")
    val PUBLISH_CONNECT_DIALOG_CONFIRM = Event("publish_connect_dialog_confirm")

    // 自动备份
    @EventDesc("备份按钮曝光")
    val UGC_BACKUP_EXPOSURE = Event("ugc_backup_exposure")

    @EventDesc("备份按钮点击")
    val UGC_BACKUP_CLICK = Event("ugc_backup_click")

    @EventDesc("备份详情页曝光")
    val BACKUP_PROFILE_EXPOSURE = Event("backup_profile_exposure")

    @EventDesc("备份详情页点击")
    val BACKUP_PROFILE_CLICK = Event("backup_profile_click")

    @EventDesc("二级确认界面")
    val SECONDARY_BACKUP_PROFILE = Event("secondary_backup_profile")

    @EventDesc("用户替换本地存档为备份文件结果")
    val UGC_BACKUP_RESULT = Event("ugc_backup_result")

    @EventDesc("用户作品列表曝光次数")
    val USERMAPS_LIST_SHOW = Event("usermaps_list_show")

    @EventDesc("用户作品点击次数")
    val USERMAPS_LIST_CLICK = Event("usermaps_list_click")

    @EventDesc("聊天入口点击")
    val CHAT_ENTANCE_CLICK = Event("chat_entance_click")

    @EventDesc("客户端活动入口只展示/点击")
    val EVENT_ACTIVITY_CLICK = Event("robux_enter")

    @EventDesc("建造运营位banner曝光")
    val UGC_MY_PAGE_BANNER_SHOW = Event("ugc_my_page_banner_show")

    @EventDesc("建造运营位banner点击")
    val UGC_MY_PAGE_BANNER_CLICK = Event("ugc_my_page_banner_click")

    @EventDesc("签到弹窗曝光事件")
    val EVENT_DAILYBONUS_POPUP_SHOW = Event("event_dailybonus_popup_show")

    @EventDesc("通过签到弹窗完成签到的人数")
    val EVENT_DAILYBONUS_POPUP_RESULT = Event("event_dailybonus_popup_result")

    @EventDesc("通过签到弹窗跳转进入任务Web页人数")
    val EVENT_DAILYBONUS_POPUP_VIEWMORE = Event("event_dailybonus_popup_viewmore")

    @EventDesc("用户进入ugc编辑态时，就立即上报。代表用户开始建造了")
    val EVENT_UGC_CREATE_EDIT_START = Event("se_ugc_create_edit_start")

    @EventDesc("素材库tab栏界面的展示")
    val TAB_MATERIAL_LIBRARY_SHOW = Event("tab_material_library_show")

    @EventDesc("素材item信息流缩略展示")
    val LIBRARY_ITEM_SHOW = Event("library_item_show")

    @EventDesc("素材item信息流缩略点击")
    val LIBRARY_ITEM_CLICK = Event("library_item_click")

    @EventDesc("素材库信息流按照默认顺序展示")
    val LIBRARY_DEFAULT_ORDER_SHOW = Event("library_default_order_show")

    @EventDesc("素材库信息流按照热度顺序展示")
    val LIBRARY_HOT_ORDER_SHOW = Event("library_hot_order_show")

    @EventDesc("素材库信息流按照最新顺序展示")
    val LIBRARY_NEWEST_ORDER_SHOW = Event("library_newest_order_show")

    @EventDesc("素材库详情页展示")
    val LIBRARY_DETAIL_SHOW = Event("library_detail_show")

    @EventDesc("素材元素的点赞点击")
    val LIBRARY_ITEM_DETAIL_LIKE_CLICK = Event("library_item_detail_like_click")

    @EventDesc("素材库详情页关注点击")
    val LIBRARY_DETAIL_FOLLOW_CLICK = Event("library_detail_follow_click")

    @EventDesc("素材库详情页分享成功点击")
    val LIBRARY_DETAIL_SHARE_CLICK = Event("library_detail_share_click")

    @EventDesc("素材试穿点击")
    val LIBRARY_METARIAL_TRYON_CLICK = Event("library_metarial_tryon_click")

    @EventDesc("素材获取点击")
    val LIBRARY_METARIAL_GET_CLICK = Event("library_metarial_get_click")

    @EventDesc("主态-作者点击编辑按钮点击")
    val LIBRARY_METARIAL_EDIT_CLICK = Event("library_metarial_edit_click")

    @EventDesc("主态-作者点击编辑界面展示")
    val LIBRARY_METARIAL_EDIT_PAGE_SHOW = Event("library_metarial_edit_page_show")

    @EventDesc("主态-作者编辑点击发布")
    val LIBRARY_METARIAL_EDIT_PAGE_PUBLISH_CLICK = Event("library_metarial_edit_page_publish_click")

    @EventDesc("用户第一次点击详情页新手引导曝光")
    val LIBRARY_USE_STEER_SHOW = Event("library_use_steer_show")

    @EventDesc("素材详情写评论入口点击")
    val LIBRARY_ITEM_DETAIL_PAGE_REVIEW_CLICK = Event("library_item_detail_page_review_click")

    @EventDesc("评论发布点击")
    val LIBRARY_ITEM_REVIEW_PUBLISH_CLICK = Event("library_item_review_publish_click")

    @EventDesc("素材详情评论展开点击")
    val LIBRARY_ITEM_DETAIL_REVIEW_COLLAPSE_CLICK = Event("library_item_detail_review_collapse_click")

    @EventDesc("素材评论收起点击")
    val LIBRARY_ITEM_DETAIL_PAGE_REVIEW_REPLISE_CLICK = Event("library_item_detail_page_review_replise_click")

    @EventDesc("素材评论回复点击")
    val LIBRARY_ITEM_REVIEW_REPLAY_CLICK = Event("library_item_review_replay_click")

    @EventDesc("素材库评论复制点击")
    val LIBRARY_REVIEW_COPY_CLICK = Event("library_review_copy_click")

    @EventDesc("素材库评论删除成功")
    val LIBRARY_REVIEW_DELETE_SUCCESS = Event("library_review_delete_success")

    @EventDesc("素材库评论举报成功")
    val LIBRARY_REVIEW_REPORT_SUCCESS = Event("library_review_report_success")

    @EventDesc("素材库素材评论点赞点击")
    val LIBRARY_REVIEW_LIKE_CLICK = Event("library_review_like_click")

    @EventDesc("客态最近穿搭展示")
    val CURRENTLY_CLOTHING_BAR_SHOW = Event("currently_clothing_bar_show")

    @EventDesc("设计师作品点击")
    val CURRENTLY_CLOTHING_BAR_DESIGN_CLICK = Event("currently_clothing_bar_design_click")

    @EventDesc("个人素材库tab点击")
    val MY_LIBRARY_TAB_CLICK = Event("my_library_tab_click")

    @EventDesc("发送表情贴纸")
    val EVENT_STICKERS_POST = Event("stickers_post")

    @EventDesc("拍剧分享渠道点击")
    val EVENT_COMMUNITY_POST_REPLY_ADD_PICTURE_CLICK = Event("event_community_post_reply_add_picture_click")

    @EventDesc("素材库设计入口点击")
    val LIBRARY_DESIGN_CLICK = Event("library_design_click")

    @EventDesc("游戏详情页点赞的点击")
    val GAME_DETAIL_PAGE_LIKE = Event("game_detail_page_like")

    @EventDesc("游戏详情页取消点赞")
    val GAME_DETAIL_PAGE_LIKE_CANCEL = Event("game_detail_page_like_cancel")

    @EventDesc("联系人列表在线状态的下发")
    val FRIENDS_LIST_USER_STATE = Event("friends_list_user_state")

    @EventDesc("聊天窗口在线状态的下发")
    val IM_CHAT_USER_STATE = Event("im_chat_user_state")

    @EventDesc("好友申请弹窗的展现")
    val FRIENDS_REQUEST_UPS_DISPLAY = Event("friends_request_ups_display")

    @EventDesc("好友申请弹窗的接受点击")
    val FRIENDS_REQUEST_UPS_ACCEPT = Event("friends_request_ups_accept")

    @EventDesc("好友申请弹窗的上滑关闭")
    val FRIENDS_REQUEST_UPS_CLOSE = Event("friends_request_ups_close")

    @EventDesc("收起按钮的点击")
    val GAME_REVIEW_COLLAPSE_CLICK = Event("game_review_collapse_click")

    @EventDesc("评论置顶按钮的点击")
    val GAME_REVIEW_PIN_CLICK = Event("game_review_pin_click")

    @EventDesc("评论取消置顶按钮的点击")
    val GAME_CANCEL_REVIEW_PIN_CLICK = Event("game_cancel_review_pin_click")

    @EventDesc("评论删除按钮的点击")
    val GAME_REVIEW_DELETE_CLICK = Event("game_review_delete_click")

    @EventDesc("写一级评论的点击")
    val GAME_FIRST_REVIEW_WRITE_CLICK = Event("game_first_review_write_click")

    @EventDesc("写多级评论的点击")
    val GAME_MORE_REVIEW_WRITE_CLICK = Event("game_more_review_write_click")

    @EventDesc("发送评论的点击")
    val GAME_REVIEW_PUBLISH_CLICK = Event("game_review_publish_click")

    @EventDesc("发表情的点击")
    val GAME_EMOJI_REVIEW_CLICK = Event("game_emoji_review_click")

    @EventDesc("发图片的点击")
    val GAME_PICTURE_REVIEW_CLICK = Event("game_picture_review_click")

    @EventDesc("评论发布成功")
    val GAME_REVIEW_REPLIES_SUCCESS = Event("game_review_replies_success")

    @EventDesc("评论筛选按钮的点击")
    val GAME_REVIEW_TYPE_CHOOSE_CLICK = Event("game_review_type_choose_click")

    @EventDesc("评论点赞")
    val C_GAME_REVIEW_LIKE_CLICK = Event("c_game_review_like_click")

    @EventDesc("取消评论点赞")
    val C_GAME_REVIEW_LIKE_CANCEL_CLICK = Event("c_game_review_like_cancel_click")

    @EventDesc("发帖成功的时长")
    val POST_SUCCESS_TIME_CONSUMING = Event("post_success_time_consuming")

    @EventDesc("删除图片/视频/游戏卡片")
    val POST_ITEM_DELETE = Event("post_item_delete")

    @EventDesc("搜索游戏卡片按钮的点击")
    val GAME_CARD_SEARCH_CLICK = Event("game_card_search_click")

    @EventDesc("搜索结果列表的下发")
    val GAME_CARD_SEARCH_LIST_SHOW = Event("game_card_search_list_show")

    @EventDesc("挽留弹窗的下发")
    val RETENTION_UP_SHOW = Event("retention_up_show")

    @EventDesc("挽留弹窗的点击")
    val RETENTION_UP_CLICK = Event("retention_up_click")

    @EventDesc("右上角3个点-编辑帖子按钮的点击")
    val POST_EDIT_CLICK = Event("post_edit_click")

    @EventDesc("右上角3个点-删除帖子按钮的点击")
    val POST_DELETE_CLICK = Event("post_delete_click")

    @EventDesc("删除帖子确认删除的点击")
    val POST_DELETE_CONFIRM = Event("post_delete_confirm")

    @EventDesc("点击详情页页点亮按钮")
    val UGC_LIGHT_UP_CLICK = Event("client_light_up_click")

    @EventDesc("点亮弹窗展示")
    val UGC_LIGHT_UP_SHOW = Event("client_light_up_show")

    @EventDesc("关闭点亮弹窗")
    val UGC_LIGHT_UP_CLOSE = Event("client_light_up_close")

    @EventDesc("选择不同火花")
    val UGC_LIGHT_UP_CHOOSE = Event("client_light_up_choose")

    @EventDesc("从详情页点击了解火花提示")
    val UGC_LIGHT_UP_TIPS_DETAIL = Event("client_light_up_tips_detail")

    @EventDesc("点击个人主页火花余额")
    val UGC_LIGHT_UP_USERPAGE = Event("client_light_up_userpage")

    @EventDesc("建造新手引导界面展示")
    val BUILD_NEWBIE_GUIDE_SHOW = Event("build_newbie_guide_show")

    @EventDesc("去玩游戏点击")
    val GO_PLAY_THE_GAME_CLICK = Event("go_play_the_game_click")

    @EventDesc("做个游戏点击")
    val MAKE_A_GAME_CLICK = Event("make_a_game_click")

    @EventDesc("蒙层展示")
    val BOOT_MASKED_SHOW = Event("boot_masked_show")

    @EventDesc("引导后进map的点击")
    val LEADS_BACKWARD_MAP_CLICK = Event("leads_backward_map_click")

    /**
     * 协议弹框event
     */
    @EventDesc("点击同意协议1")
    val AGREE_YHXY_CLICK_ONE = Event("agree_yhxy_click_one")

    @EventDesc("点击不同意协议1")
    val DISAGREE_YHXY_CLICK_ONE = Event("disagree_yhxy_click_one")

    @EventDesc("点击同意协议2")
    val AGREE_YHXY_CLICK_TWO = Event("agree_yhxy_click_two")

    @EventDesc("点击使用基础模式2")
    val AGREE_USE_PRIVACY_MODE_TWO = Event("agree_use_privacy_mode_two")

    @EventDesc("点击退出应用")
    val YHXY_QUIT_APP_CLICK = Event("yhxy_quit_app_click")

    @EventDesc("点击同意使用完整模式")
    val AGREE_USE_FULL_MODE_CLICK = Event("agree_use_full_mode_click")

    @EventDesc("点击同意更新的隐私政策1")
    val AGREE_UPDATE_YHXY_CLICK_ONE = Event("agree_update_yhxy_click_one")

    @EventDesc("点击不同意更新的隐私政策1")
    val DISAGREE_UPDATE_YHXY_CLICK_ONE = Event("disagree_update_yhxy_click_one")

    @EventDesc("点击同意更新的隐私政策2")
    val AGREE_UPDATE_YHXY_CLICK_TWO = Event("agree_update_yhxy_click_two")

    @EventDesc("更新隐私政策的弹窗点击退出应用")
    val UPDATE_YHXY_QUIT_APP_CLICK = Event("update_yhxy_quit_app_click")

    @EventDesc("更新隐私政策弹窗1显示")
    val SHOW_UPDATE_YHXY_DIALOG_ONE = Event("show_update_yhxy_dialog_one")

    @EventDesc("更新隐私政策弹窗2显示")
    val SHOW_UPDATE_YHXY_DIALOG_TWO = Event("show_update_yhxy_dialog_two")

    @EventDesc("是否同意个人协议")
    val EVENT_CONTRACT_ACCESS = Event("event_contract_access")

    @EventDesc("拉起角色分享")
    val EVENT_ROLE_SHARE_AVATAR = Event("role_share_avatar")

    @EventDesc("角色分享方式")
    val EVENT_ROLE_SHARE_AVATAR_WAY = Event("role_share_avatar_way")

    @EventDesc("取消角色分享")
    val EVENT_ROLE_SHARE_AVATAR_CANCEL = Event("role_share_avatar_cancel")

    @EventDesc("分享结果")
    val EVENT_ROLE_SHARE_AVATAR_RESULT = Event("role_share_avatar_result")

    @EventDesc("主题选择页面展示")
    val BUILD_NEWBIE_GUIDE_CREATE_THEME_SHOW = Event("build_newbie_guide_create_theme_show")

    @EventDesc("主题选择页面点击")
    val BUILD_NEWBIE_GUIDE_CREATE_THEME_CLICK = Event("build_newbie_guide_create_theme_click")

    @EventDesc("点击+号建造蒙层展示")
    val BUILD_NEWBIE_GUIDE_SKIP_SHOW = Event("build_newbie_guide_skip_show")

    @EventDesc("“我想先逛逛”页面点击")
    val BUILD_NEWBIE_GUIDE_SKIP_CLICK = Event("build_newbie_guide_skip_click")

    @EventDesc("建造页面引导展示")
    val BUILD_NEWBIE_GUIDE_BULID_SHOW = Event("build_newbie_guide_bulid_show")

    @EventDesc("建造页面引导点击")
    val BUILD_NEWBIE_GUIDE_BUILD_CLICK = Event("build_newbie_guide_build_click")

    @EventDesc("加号入口建造icon点击")
    val BUILD_NEWBIE_GUIDE_BUILD_MODE_CLICK = Event("build_newbie_guide_build_mode_click")

    @EventDesc("UGC母工程下载异常")
    val EVENT_UGC_PROJECT_DOWNLOAD_ABNORMAL = Event("event_ugc_project_download_abnormal")

    @EventDesc("UGC母工程解压异常")
    val EVENT_UGC_PROJECT_UNZIP_ABNORMAL = Event("event_ugc_project_unzip_abnormal")

    @EventDesc("UGC编辑态度工程复制异常")
    val EVENT_UGC_PROJECT_COPY_ABNORMAL = Event("event_ugc_project_copy_abnormal")

    @EventDesc("素材库排序选择点击")
    val LIBRARY_SORT_CHOICE_CLICK = Event("library_sort_choice_click")

    @EventDesc("素材库一级分类选择点击")
    val LIBRARY_FIRST_CLASSIFICATION_CHOICE_CLICK = Event("library_first_classification_choice_click")

    @EventDesc("素材库排序选择点击")
    val LIBRARY_SECOND_CLASSIFICATION_CHOICE_CLICK = Event("library_second_classification_choice_click")

    @EventDesc("素材库公告点击")
    val LIBRARY_NOTICE_CLICK = Event("library_notice_click")

    @EventDesc("素材库创作入口点击")
    val LIBRARY_CREATE_CLICK = Event("library_create_click")

    @EventDesc("素材元素的取消点赞点击")
    val LIBRARY_ITEM_DETAIL_CANCEL_LIKE_CLICK = Event("library_item_detail_cancel_like_click")

    @EventDesc("个人素材库分类tab-点击更多")
    val MY_LIBRARY_TAB_MORE_CLICK = Event("my_library_tab_more_click")

    @EventDesc("个人素材库tab置顶管理点击")
    val MY_LIBRARY_TAB_PIN_CLICK = Event("my_library_tab_pin_click")

    @EventDesc("个人素材库tab公开管理点击")
    val MY_LIBRARY_TAB_HIDE_TAB_CLICK = Event("my_library_tab_hide_tab_click")

    @EventDesc("个人素材库tab隐藏管理点击")
    val MY_LIBRARY_TAB_UNHIDE_TAB_CLICK = Event("my_library_tab_unhide_tab_click")

    @EventDesc("充值页面曝光")
    val EVENT_CLIENT_WALLET_SHOW = Event("event_client_wallet_show")

    @EventDesc("充值页面代币点击充值")
    val EVENT_CLIENT_CLICK_PAY = Event("event_client_click_pay")

    @EventDesc("充值页面明细曝光")
    val EVENT_CLIENT_WALLET_DETAIL_SHOW = Event("event_client_wallet_detail_show")

    @EventDesc("充值页面反馈点击")
    val EVENT_CLIENT_WALLET_FEEDBACK_CLICK = Event("event_client_wallet_feedback_click")

    @EventDesc("充值页面订阅点击")
    val EVENT_CLIENT_WALLET_SUB_CLICK = Event("event_client_wallet_sub_click")

    /**
     * 评论输入框: 底部的快捷输入的Tag曝光
     * source: 上级过来的 pagename
     * tagid: 具体指向的 T 台配置的 tagid, 只显示一部分的也要上报
     */
    @EventDesc("猜你想评曝光")
    val EVENT_GUESS_RECOMMENT_SHOW = Event("event_guess_recomment_show")

    /**
     * 评论输入框: 底部的快捷输入的Tag点击
     * source: 上级过来的 pagename
     * tagid: 具体指向的 T 台配置的 tagid
     */
    @EventDesc("猜你想评点击")
    val EVENT_GUESS_RECOMMENT_CLICK = Event("event_guess_recomment_click")

    @EventDesc("模组入口点击")
    val MOD_ENTER_CLICK = Event("mod_enter_click")

    @EventDesc("模组已发布tab对应页面展示")
    val MOD_PUBLISHED_TAB_VIEW = Event("mod_published_tab_view")

    @EventDesc("模组管理页建造模组入口点击")
    val MOD_MANAGEMENT_BUILD_ENTER_CLICK = Event("mod_management_build_enter_click")

    @EventDesc("模组管理页继续建造点击")
    val MOD_MANAGEMENT_CONTINUE_BUILD_CLICK = Event("mod_management_continue_build_click")

    @EventDesc("模组草稿复制按钮点击")
    val MOD_DRAFT_COPY_CLICK = Event("mod_draft_copy_click")

    @EventDesc("模组草稿删除按钮点击")
    val MOD_DRAFT_DELETE_CLICK = Event("mod_draft_delete_click")

    @EventDesc("模组排序选择点击")
    val MOD_SORT_CHOICE_CLICK = Event("mod_sort_choice_click")

    @EventDesc("模组公开私有选择点击")
    val MOD_PUBLIC_CLASSIFICATION_CHOICE_CLICK = Event("mod_public_classification_choice_click")

    @EventDesc("模组二级分类选择点击")
    val MOD_CLASSIFICATION_CHOICE_CLICK = Event("mod_classification_choice_click")

    @EventDesc("模组已发布模组的item点击")
    val MOD_PUBLISHED_ITEM_CLICK = Event("mod_published_item_click")

    @EventDesc("模组重命名点击")
    val MOD_RENAME_CLICK = Event("mod_rename_click")

    @EventDesc("模组设置私有共用状态点击")
    val MOD_VISIBILITY_TOGGLE_CLICK = Event("mod_visibility_toggle_click")

    @EventDesc("模组删除点击")
    val MOD_PUBLISHED_DELETE_CLICK = Event("mod_published_delete_click")

    @EventDesc("点击+号模组蒙层展示")
    val BUILD_NEWBIE_GUIDE_MODULE_SKIP_SHOW = Event("build_newbie_guide_module_skip_show")

    @EventDesc("点击+号服装蒙层展示")
    val BUILD_NEWBIE_GUIDE_OUTFIT_SKIP_SHOW = Event("build_newbie_guide_outfit_skip_show")

    @EventDesc("加号入口模组icon点击")
    val BUILD_NEWBIE_GUIDE_BUILD_MODULE_CLICK = Event("build_newbie_guide_build_module_click")

    @EventDesc("加号入口服装icon点击")
    val BUILD_NEWBIE_GUIDE_BUILD_OUTFIT_CLICK = Event("build_newbie_guide_build_outfit_click")

    @EventDesc("模组页展示")
    val BUILD_NEWBIE_GUIDE_BUILD_MODULE_SHOW = Event("build_newbie_guide_build_module_show")

    @EventDesc("模组页+号点击")
    val BUILD_NEWBIE_GUIDE_BUILD_MODULE_START_CLICK = Event("build_newbie_guide_build_module_start_click")

    @EventDesc("banner点击")
    val MOD_BANNER_CLICK = Event("mod_banner_click")

    @EventDesc("点击+号画画蒙层展示")
    val BUILD_NEWBIE_GUIDE_DRAW_SKIP_SHOW = Event("build_newbie_guide_draw_skip_show")

    @EventDesc("加号入口画图icon点击（蒙层）")
    val BUILD_NEWBIE_GUIDE_BUILD_DRAW_CLICK = Event("build_newbie_guide_build_draw_click")

    @EventDesc("加号入口画图icon点击")
    val EVENT_CREATE_TAB_DRAW_CLICK = Event("event_create_tab_draw_click")

    @EventDesc("素材新手引导游戏进入点击")
    val MATERIAL_GUIDE_GAME_ENTER_CLICK = Event("material_guide_game_enter_click")

    @EventDesc("素材新手引导弹窗展示")
    val MATERIAL_GUIDE_POPUP_SHOW = Event("material_guide_popup_show")

    @EventDesc("素材新手引导弹窗退出点击")
    val MATERIAL_GUIDE_POPUP_EXIT_CLICK = Event("material_guide_popup_exit_click")

    @EventDesc("素材新手引导弹窗不再展示点击")
    val MATERIAL_GUIDE_POPUP_DONT_SHOW_CLICK = Event("material_guide_popup_dont_show_click")

    @EventDesc("新人专区二级页展示")
    val NEWBIE_ZONE_SECOND_PAGE_SHOW = Event("newbie_zone_second_page_show")

    @EventDesc("新手引导按钮资源位展示")
    val NEWBIE_GUIDE_BUTTON_SHOW = Event("newbie_guide_button_show")

    @EventDesc("新手引导按钮资源位点击")
    val NEWBIE_GUIDE_BUTTON_CLICK = Event("newbie_guide_button_click")

    @EventDesc("循环引导第一步展示")
    val LOOP_GUIDE_FIRST_SHOW = Event("loop_guide_first_show")

    @EventDesc("循环引导加号点击")
    val LOOP_GUIDE_ADD_CLICK = Event("loop_guide_add_click")

    @EventDesc("循环引导第二步展示")
    val LOOP_GUIDE_SECOND_SHOW = Event("loop_guide_second_show")

    @EventDesc("循环引导模组点击")
    val LOOP_GUIDE_MODULE_CLICK = Event("loop_guide_module_click")

    @EventDesc("素材新手引导勋章点击")
    val MATERIAL_GUIDE_BADGE_CLICK = Event("material_guide_badge_click")

    @EventDesc("一分钟去创作点击")
    val GUIDE_CREATE_ONE_MINUTE_CLICK = Event("guide_create_one_minute_click")

    @EventDesc("素材引导中断弹窗展示")
    val MATERIAL_GUIDE_INTERRUPT_POPUP_SHOW = Event("material_guide_interrupt_popup_show")

    @EventDesc("素材引导中断弹窗点击")
    val MATERIAL_GUIDE_INTERRUPT_POPUP_CLICK = Event("material_guide_interrupt_popup_click")

    @EventDesc("内部评分引导弹框展示")
    val EVENT_APP_EVALUATE_SHOW = Event("event_app_evaluate_show")

    @EventDesc("内部评分引导弹框点击")
    val CLICK_STORE_RATE_GUIDE_POPUP = Event("event_app_evaluate_click")

    @EventDesc("内部评分引导弹框点击关闭")
    val CLICK_STORE_RATE_GUIDE_POPUP_CLOSE = Event("event_app_evaluate_click_close")

    @EventDesc("商店评分弹框展示")
    val EVENT_STORE_EVALUATE_SHOW = Event("event_store_evaluate_show")

    @EventDesc("聊天页面加号入口点击")
    val CHAT_ADD_CLICK = Event("chat_add_click")

    @EventDesc("创建群聊入口点击")
    val GROUP_CREATE_CLICK = Event("group_create_click")

    /**
     * result: "1" 成功; "2" 失败
     * members: 上报勾选的成员数
     */
    @EventDesc("创建群聊完成点击")
    val GROUP_CREATE_CONFIRM_CLICK = Event("group_create_confirm_click")

    @EventDesc("搜索群聊入口点击")
    val CHAT_SEARCH_GROUP_CLICK = Event("chat_search_group_click")

    /**
     * group_id: 群id
     */
    @EventDesc("加入群聊")
    val GROUP_JOIN = Event("group_join")

    /**
     * group_id: 群id
     */
    @EventDesc("确认退群点击")
    val GROUP_LEAVE_GROUP_FINISH = Event("group_leave_group_finish")

    @EventDesc("pgc服装作品点击")
    val CURRENTLY_PGC_CLOTHING_BAR_DESIGN_CLICK = Event("currently_pgc_clothing_bar_design_click")

    @EventDesc("pgc服装作品试穿点击")
    val CURRENTLY_PGC_CLOTHING_TRY_ON_CLICK = Event("currently_pgc_clothing_try_on_click")

    @EventDesc("可二创和不可二创问号点击")
    val ASSET_REMIX_OR_NOT_BTN_CLICK = Event("asset_remix_or_not_btn_click")

    @EventDesc("点击+号跑酷蒙层展示")
    val BUILD_NEWBIE_GUIDE_OBBY_SKIP_SHOW = Event("build_newbie_guide_obby_skip_show")

    @EventDesc("点击+号赛车蒙层展示")
    val BUILD_NEWBIE_GUIDE_CAR_SKIP_SHOW = Event("build_newbie_guide_car_skip_show")

    @EventDesc("点击+号射击蒙层展示")
    val BUILD_NEWBIE_GUIDE_SHOOT_SKIP_SHOW = Event("build_newbie_guide_shoot_skip_show")

    @EventDesc("加号入口跑酷icon点击（蒙层）")
    val BUILD_NEWBIE_GUIDE_BUILD_OBBY_CLICK = Event("build_newbie_guide_build_obby_click")

    @EventDesc("加号入口赛车icon点击（蒙层）")
    val BUILD_NEWBIE_GUIDE_BUILD_CAR_CLICK = Event("build_newbie_guide_build_car_click")

    @EventDesc("加号入口射击icon点击（蒙层）")
    val BUILD_NEWBIE_GUIDE_BUILD_SHOOT_CLICK = Event("build_newbie_guide_build_shoot_click")

    @EventDesc("加号入口跑酷icon点击")
    val EVENT_CREATE_TAB_OBBY_CLICK = Event("event_create_tab_obby_click")

    @EventDesc("加号入口赛车icon点击")
    val EVENT_CREATE_TAB_CAR_CLICK = Event("event_create_tab_car_click")

    @EventDesc("加号入口射击icon点击")
    val EVENT_CREATE_TAB_SHOOT_CLICK = Event("event_create_tab_shoot_click")
}

object Source {
    const val SOURCE_MINE = "mine"
    const val SOURCE_LOGIN = "loginpage"
    const val SOURCE_SIGNUP = "signuppage"
}