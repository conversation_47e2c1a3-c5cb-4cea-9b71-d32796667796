package com.socialplay.gpark.function.im

import com.bin.cpbus.CpEventBus
import com.ly123.tes.mgs.metacloud.IConnectStatusListener
import com.ly123.tes.mgs.metacloud.ISendTextMessageListener
import com.ly123.tes.mgs.metacloud.MetaCloud
import com.ly123.tes.mgs.metacloud.message.TextMessage
import com.ly123.tes.mgs.metacloud.model.BaseConstants.ERR_SDK_NOT_LOGGED_IN
import com.ly123.tes.mgs.metacloud.model.BaseConstants.ERR_SVR_GROUP_PERMISSION_DENY
import com.ly123.tes.mgs.metacloud.model.BaseConstants.ERR_SVR_SSO_D2_EXPIRED
import com.ly123.tes.mgs.metacloud.model.BaseConstants.ERR_SDK_NOT_INITIALIZED
import com.ly123.tes.mgs.metacloud.model.BaseConstants.ERR_SVR_GROUP_ALLREADY_MEMBER
import com.ly123.tes.mgs.metacloud.model.BaseConstants.ERR_SVR_GROUP_INVALID_GROUPID
import com.ly123.tes.mgs.metacloud.model.Conversation
import com.ly123.tes.mgs.metacloud.model.Message
import com.ly123.tes.mgs.metacloud.model.UserInfo
import com.meta.biz.mgs.data.model.MGSJoinErrorEvent
import com.meta.biz.mgs.data.model.MGSSendErrorEvent
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.event.MgsSendGroupTxtEvent
import com.socialplay.gpark.data.model.event.MgsSendTxtEvent
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.im.RongImHelper.FROM_CHAT_ROOM
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.core.context.GlobalContext
import timber.log.Timber
import kotlin.coroutines.resume

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/03/27
 *     desc   :
 * </pre>
 */
object GameImHelper  : CoroutineScope by MainScope() {

    private val accountInteractor by lazy { GlobalContext.get().get<AccountInteractor>() }
    private var listener : IConnectStatusListener?=null
    fun init() {
        CpEventBus.register(this)
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: MGSSendErrorEvent) {
        Analytics.track(
            EventConstants.MGS_CHAT_ROOM_MESSAGE_SEND_FAIL, mapOf(
                "gameid" to event.gameId,
                "reason" to (event.desc?:""),
                "code" to event.code
            )
        )
        if (event.code == ERR_SVR_SSO_D2_EXPIRED || event.code == ERR_SDK_NOT_LOGGED_IN || event.code == ERR_SDK_NOT_INITIALIZED) {
            //聊天室发消息提示 登录过期或者未登录
            Timber.d("mgs_chat_room_expired")
            RongImHelper.login()
        } else if (event.code == ERR_SVR_GROUP_PERMISSION_DENY && event.desc.equals("only group member can send msg")) {
            //发送消息提示用户 不是当前房间的成员（后端异步执行的创建房间，可能还没创建成功就调用了加入）
            Timber.d("mgs_chat_room_join_room")
            MetaCloud.joinChatRoom(event.chatRoomId, -1)
        }
    }
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent(event: MgsSendTxtEvent) {
        Timber.i("send start $event")
        val userInfo = accountInteractor.accountLiveData.value
        val user = UserInfo(
            userInfo?.uuid,
            userInfo?.nickname,
            userInfo?.portrait
        )
        user.avatar = userInfo?.portrait
        MetaCloud.sendTextMessage(
            event.targetUuid,
            event.txt,
            Conversation.ConversationType.PRIVATE,
            null,
            null,
            user,
            "",
            object : ISendTextMessageListener {
                override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                    Timber.i("send fail $imMessage $errorCode $desc")
                }

                override fun onSuccess(imMessage: Message) {
                    Timber.i("send success $imMessage")

                }

            })
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent(event: MgsSendGroupTxtEvent) {
        Timber.i("send start $event")
        val currentUserInfo = accountInteractor.accountLiveData.value
        val user = UserInfo(
            currentUserInfo?.uuid,
            currentUserInfo?.nickname,
            currentUserInfo?.portrait
        )
        user.avatar = currentUserInfo?.portrait

        val textMessage = Message()
        textMessage.messageType = Message.MessageType.TXT
        textMessage.conversationType = Conversation.ConversationType.GROUP
        textMessage.targetId = event.customData?.groupInfo?.imId
        textMessage.content = TextMessage().apply {
            content = event.txt
            userInfo = user
        }
        textMessage.atUserIdList = event.customData?.groupAtInfoList?.map { atInfo ->
            atInfo.memberInfo?.uuid ?: ""
        }?.filter {
            it.isNotEmpty()
        }?.distinct() ?: emptyList()
        textMessage.customData = event.customData
        MetaCloud.sendMessage(
            textMessage,
            "",
            object : ISendTextMessageListener {
                override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                    Timber.i("send fail $imMessage $errorCode $desc")
                }

                override fun onSuccess(imMessage: Message) {
                    Timber.i("send success $imMessage")
                }

            })
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: MGSJoinErrorEvent) {
        Analytics.track(
            EventConstants.MGS_CHAT_ROOM_JOIN_FAIL, mapOf(
                "gameid" to (event.gameId?:""),
                "reason" to( event.desc?:""),
                "code" to (event.code?:"")
            )
        )
        //加入聊天室出错信息
        launch { retryMGSJoinRoom(event, 200) }
    }



    private suspend fun retryMGSJoinRoom(event: MGSJoinErrorEvent, retryDelay: Long): Boolean {
        Timber.d("retryMGSJoinRoom ${event.chatRoomId} ${event.gameId} ${event.code} ${event.desc} $retryDelay")
        val joinRoomResult = when (event.code) {
            ERR_SDK_NOT_INITIALIZED, ERR_SVR_SSO_D2_EXPIRED, ERR_SDK_NOT_LOGGED_IN -> {
                val connected = connectWithListener()
                Timber.d("retryMGSJoinRoom connectWithListener $connected")
                if (connected) {
                    suspendJoinChatRoom(event.chatRoomId, event.gameId)
                } else false to null
            }

            ERR_SVR_GROUP_INVALID_GROUPID -> {
                suspendJoinChatRoom(event.chatRoomId, event.gameId)
            }

            ERR_SVR_GROUP_ALLREADY_MEMBER -> {
                true to null
            }

            else -> false to null
        }
        return if (!joinRoomResult.first) {
            if (retryDelay > 0) {
                Timber.d("retryMGSJoinRoom retry -- ")
                delay(retryDelay)
                retryMGSJoinRoom(joinRoomResult.second ?: event, retryDelay)
            } else {
                false
            }
        } else {
            true
        }
    }

    private suspend fun suspendJoinChatRoom(chatRoomId: String, gameId: String?) =
        suspendCancellableCoroutine { c ->
            MetaCloud.joinChatRoom(chatRoomId, -1) { success, code, desc ->
                Timber.d("suspendJoinChatRoom $chatRoomId $gameId")
                if (success) {
                    c.resume(true to null)
                } else {
                    c.resume(false to MGSJoinErrorEvent(gameId ?: "", chatRoomId, code, desc))
                }
            }
        }

    private suspend fun connectWithListener() = suspendCancellableCoroutine { c ->
        unRegisterListener()
        listener = object : IConnectStatusListener {
            override fun onConnected() {
                Timber.d("connectWithListener onConnected")
                unRegisterListener()
                c.resume(true)
            }

            override fun onDisconnected(errorCode: Int, errorMessage: String) {
                Timber.d("connectWithListener onDisconnected $errorCode $errorMessage")
                unRegisterListener()
                c.resume(false)
            }

            override fun onUserSigExpired() {
                Timber.d("connectWithListener onUserSigExpired")
                unRegisterListener()
                c.resume(false)
            }
        }
        listener?.let { MetaCloud.registerConnectListener(it) }
        RongImHelper.needConnect(FROM_CHAT_ROOM)
        Timber.d("connectWithListener needConnect")
    }

    private fun unRegisterListener(){
        listener?.let { MetaCloud.unRegisterConnectListener(it) }
        listener= null
    }

}