package com.socialplay.gpark.function.deeplink.linkhandler

import android.content.Context
import android.os.Bundle
import androidx.core.os.bundleOf
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.post.PostMediaResource
import com.socialplay.gpark.data.model.post.PostMomentCard
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.PublishPostLinkBundle
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.deeplink.DeeplinkAnalysisUtil
import com.socialplay.gpark.function.deeplink.JumpPublishPostUtil
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.util.GsonUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * 处理跳转到游戏详情页
 */
class PublishPostLinkHandler : LinkHandler {

    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        DeeplinkAnalysisUtil.enterPublishHandler(data.uri.toString())
        val dataString = data.uri.getQueryParameter("data") ?: run {
            DeeplinkAnalysisUtil.failed(data.uri.toString(), "handle:data is null")
            return LinkHandleResult.Failed("no data")
        }
        Timber.d("checkcheck_data ${dataString}")
        val context = GlobalContext.get().get<Context>()
        val arguments = GsonUtil.gsonSafeParse<PublishPostLinkBundle>(dataString)

        val fromGamePkgName = arguments?.fromPackageName
        val fromGameId = arguments?.fromGameId

        val content = arguments?.content
        val tagIdList = arguments?.tagIdList
        val briefMediaList = arguments?.briefMediaList
        val completeCardListString = arguments?.completeCardList
        val clearTopBackFeed = arguments?.clearTopBackFeed == true

        val showCategoryId = arguments?.showCategoryId ?: CategoryId.SCHEME_DEFAULT
        val templateId = arguments?.templateId
        val customCacheKey = arguments?.customCacheKey
        val enableOutfitShare = arguments?.enableOutfitShare == true
        val isFromMoment = arguments?.isFromMoment == true
        val moments = arguments?.moments

        if (!needExtraAssemble(arguments)) {
            jumpPublish(
                data,
                content,
                null,
                completeCardListString,
                null,
                fromGameId,
                fromGamePkgName,
                clearTopBackFeed,
                showCategoryId,
                templateId,
                customCacheKey,
                enableOutfitShare,
                isFromMoment,
                moments
            )
        } else {
            GlobalScope.launch(Dispatchers.IO) {
                val mediaList =
                    JumpPublishPostUtil.assembleMediaList(context, briefMediaList)
                withContext(Dispatchers.Main) {
                    jumpPublish(
                        data,
                        content,
                        mediaList,
                        completeCardListString,
                        null,
                        fromGameId,
                        fromGamePkgName,
                        clearTopBackFeed,
                        showCategoryId,
                        templateId,
                        customCacheKey,
                        enableOutfitShare,
                        isFromMoment,
                        moments
                    )
                }
            }
        }
        return LinkHandleResult.Success
    }

    private fun needExtraAssemble(arguments: PublishPostLinkBundle?): Boolean {
        // TODO 先不做话题
        return !arguments?.briefMediaList.isNullOrEmpty()
//        return !arguments?.tagIdList.isNullOrEmpty() || !arguments?.briefMediaList.isNullOrEmpty()
    }

    private fun jumpPublish(
        data: LinkData,
        content: String?,
        mediaList: List<PostMediaResource>?,
        completeCardListString: List<PostCardInfo>?,
        tagList: List<PostTag>?,
        fromGameId: String?,
        fromGamePkgName: String?,
        clearTopBackFeed: Boolean,
        showCategoryId: Int,
        templateId: Int?,
        customCacheKey: String?,
        enableOutfitShare: Boolean,
        isFromMoment: Boolean,
        moments: List<PostMomentCard>?,
    ) {
        DeeplinkAnalysisUtil.readyJumpPublish(data.uri.toString())
        val result = MetaRouter.Post.goPublishPost(
            data.navHost,
            content = content,
            medias = mediaList,
            games = completeCardListString,
            tags = tagList,
            fromGameId = fromGameId,
            fromPkgName = fromGamePkgName,
            clearTopBackFeed = clearTopBackFeed,
            resIdBean = ResIdBean.newInstance().setCategoryID(showCategoryId),
            templateId = templateId,
            customCacheKey = customCacheKey,
            enableOutfitShare = enableOutfitShare,
            isFromMoment = isFromMoment,
            moments = moments
        )
        if (result.first) {
            DeeplinkAnalysisUtil.publishHandleEnd(data.uri.toString())
        } else {
            DeeplinkAnalysisUtil.failed(data.uri.toString(), "jump failed:${result.second ?:""}")
        }
    }
}