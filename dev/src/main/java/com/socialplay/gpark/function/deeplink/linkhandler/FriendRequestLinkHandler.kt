package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.router.MetaRouter

/**
 * 跳转应用市场
 */
class FriendRequestLinkHandler : LinkHandler {
    override fun handle(chain: <PERSON>HandlerChain, data: LinkData): LinkHandleResult {
        MetaRouter.IM.goFriendGroupsRequestList(data.navHost, data.source)
        return LinkHandleResult.Success
    }
}