package com.socialplay.gpark.ui.post.v2

import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.graphics.Rect
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import androidx.activity.addCallback
import androidx.core.animation.addListener
import androidx.core.view.isVisible
import androidx.fragment.app.clearFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.google.android.exoplayer2.MediaItem
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectLimitType
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.SimpleListData
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.post.PostMediaResource
import com.socialplay.gpark.data.model.post.PostPublish
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.data.model.share.ShareResult
import com.socialplay.gpark.databinding.FragmentPublishPostBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.exoplayer.SharedVideoPlayerControllerInteractor
import com.socialplay.gpark.function.locale.MetaLanguages
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.dialog.ListDialog
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivityArgs
import com.socialplay.gpark.ui.imgpre.ImgPreDialogFragment
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.ui.view.ReEditTextListener
import com.socialplay.gpark.util.CustomEpoxyTouchHelper
import com.socialplay.gpark.util.CustomMediaPlayerEngine
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.KeyboardHeightUtilV2
import com.socialplay.gpark.util.PermissionUtil
import com.socialplay.gpark.util.PictureSelectorUtil
import com.socialplay.gpark.util.extension.doOnLayoutChanged
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.getVisibleItemViews
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.resumeGameById
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.toastLong
import com.socialplay.gpark.util.extension.unsetOnTouch
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import com.socialplay.gpark.util.glide.GlideEngine
import com.socialplay.gpark.util.glide.LubanCompressEngine
import com.socialplay.gpark.util.ifNullOrEmpty
import org.koin.android.ext.android.inject
import timber.log.Timber


/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/09/22
 *     desc   :
 * </pre>
 */
class PublishPostFragment :
    BaseFragment<FragmentPublishPostBinding>(R.layout.fragment_publish_post) {

    companion object {
        private const val DIRECTION_FORWARD = 1
        private const val DIRECTION_BACKWARD = 2

        const val REQUEST_BACK = "request_back_to_publish_post"
        const val TAG_RULE: String = "#"
    }

    private val vm: PublishPostViewModel by fragmentViewModel()

    private val mediaController by lazy { buildMediaController() }
    private val gameController by lazy { buildGameController() }
    private val tagController by lazy { buildTagController() }

    private var blueColor = 0
    private var redColor = 0
    private var pinkColor = 0

    private var canDrag = false
    private var canDrop = false
    private var imgItem: PublishPostMediaItem? = null
    private var imgCleared = true
    private var gameItem: PostGameItem? = null
    private var gameCleared = true
    private var dragView: View? = null
    private val rect = Rect()
    private var relativeY = 0

    private var needInvokeKeyboard = true
    private var canInvokeKeyboard = true
    private var isKeyboardActive = false

    private var deleteAnimator: ValueAnimator? = null
    private var deleteTitleIndex = 0

    private var initContent = false
    private val args: PublishPostFragmentArgs by args()

    private var lessThan10 = true

    private var autoToggleOutfit = false
    private var editOutfit = false
    private var objectAnimator: ObjectAnimator? = null
    private var lastVisible: Boolean = false

    private var deleteTitle = ""
    private var deleteFullTitle = ""
    private var direction = DIRECTION_BACKWARD
    private val isForward get() = direction == DIRECTION_FORWARD
    private var publishFlag = false

    private val enableOutfitShare get() = PandoraToggle.enableTryOnShare && (args.enableOutfitShare || vm.enableOutfitShare)
    private val isPublishVideo get() = args.isPublishVideo || vm.isPublishVideo

    private val onTouchListener = object : View.OnTouchListener {
        override fun onTouch(v: View, event: MotionEvent): Boolean {
            when (event.action) {
                MotionEvent.ACTION_MOVE -> {
                    if (canDrag) {
                        if ((dragView?.y ?: 0.0f) + (dragView?.height ?: 0) > relativeY) {
                            if (!canDrop) {
                                canDrop = true
                                binding.lavDelete.progress = 1F
                                animateDeleteTitle(true)
                            }
                        } else {
                            if (canDrop) {
                                canDrop = false
                                binding.lavDelete.progress = 0.0f
                                animateDeleteTitle(false)
                            }
                        }
                    }
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    if (canDrag) {
                        val result = if (canDrop) {
                            imgItem?.let {
                                Analytics.track(
                                    EventConstants.POST_ITEM_DELETE
                                )
                                vm.removeMedia(it.item)
                            }
                            gameItem?.let {
                                Analytics.track(
                                    EventConstants.POST_ITEM_DELETE
                                )
                                val gameCardInfo = it.item
                                Analytics.track(EventConstants.ADD_POST_REMOVE_GAME_CARD) {
                                    put("game_source", gameCardInfo.resourceType)
                                    put("gameid", gameCardInfo.gameId)
                                }
                                vm.removeGame(gameCardInfo)
                            }
                            true
                        } else {
                            false
                        }
                        dragView = null
                        imgItem = null
                        gameItem = null
                        canDrag = false
                        canDrop = false
                        binding.lavDelete.progress = 0F
                        animateDeleteTitle(false)
                        binding.llDelArea.animate().translationY(binding.llDelArea.height.toFloat())
                        if (binding.tvShareOutfit.isVisible) {
                            binding.clBottom.animate().alpha(1.0f)
                        }
                        return result
                    }
                }
            }
            return false
        }
    }

    private val itemListener = object : IPublishPostListener {
        override fun openMedia(media: PostMediaResource, position: Int) {
            if (media.isImage) {
                hideKeyboardInvokeLater()
                val imageUrls = withState(vm) { it.medias }
                    ?.filter { it.isImage }
                    ?.map { it.resPath }
                    ?.toTypedArray()
                if (!imageUrls.isNullOrEmpty()) {
                    ImgPreDialogFragment.show(
                        requireActivity(),
                        imageUrls,
                        EventParamConstants.IMG_PRE_FROM_PUBLISH_POST,
                        position.coerceAtMost(imageUrls.size - 1),
                        true
                    )
                }
            } else if (media.isVideo) {
                hideKeyboardInvokeLater()
                val playerController by inject<SharedVideoPlayerControllerInteractor>()
                val toRestoreUri = MediaItem.fromUri(media.resPath).buildUpon()
                    .setCustomCacheKey("${media.resPath}${args.customCacheKey ?: ""}").build()
                playerController.setMediaItem(toRestoreUri)
                playerController.changeMuteState(false)
                MetaRouter.Video.fullScreenPlayer(
                    this@PublishPostFragment,
                    "publish_post"
                )
            }
        }

        override fun clickTag(tag: PostTag) {
            binding.etContent.setReplaceTopic(TAG_RULE + tag.tagName + " ")
            setTopicListVisible(false)
        }


        override fun deleteTag(tag: PostTag) {
            vm.removeTag()
        }

        override fun openAlbum() {
            Analytics.track(EventConstants.ADD_POST_ADD_MEDIA_CLICK) {
                ResIdUtils.getAnalyticsMap(args.resIdBean)
            }
            selectPicturesWithPermission()
        }

        override fun delMedia(media: PostMediaResource) {
            Analytics.track(
                EventConstants.POST_ITEM_DELETE
            )
            vm.removeMedia(media)
        }

        override fun delGame(game: PostCardInfo) {
            Analytics.track(
                EventConstants.POST_ITEM_DELETE
            )
            vm.removeGame(game)
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentPublishPostBinding? {
        return FragmentPublishPostBinding.inflate(inflater, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Analytics.track(EventConstants.EVENT_ADD_POST_SHOW) {
            put("show_categoryid", args.resIdBean.getCategoryID())
            put("reqId", args.resIdBean.getReqId() ?: "")
            args.templateId?.also { put("templateid", it) }
        }
        mediaController.onRestoreInstanceState(savedInstanceState)
        gameController.onRestoreInstanceState(savedInstanceState)
        tagController.onRestoreInstanceState(savedInstanceState)

        deleteTitle = getString(R.string.delete_cap)
        deleteFullTitle = getString(R.string.release_to_delete)
        direction = if (MetaLanguages.getAppCurrentLanguage(requireContext()).dynamicImpl.postDeleteForward()) {
            deleteTitleIndex = deleteTitle.length
            DIRECTION_FORWARD
        } else {
            deleteTitleIndex = deleteFullTitle.length - deleteTitle.length
            DIRECTION_BACKWARD
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mediaController.onSaveInstanceState(outState)
        gameController.onSaveInstanceState(outState)
        tagController.onSaveInstanceState(outState)
    }

    override fun onResume() {
        super.onResume()
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
            leaveOrNot()
        }
        KeyboardHeightUtilV2.registerKeyboardHeightListener(requireActivity()) {
            isKeyboardActive = it > 200
        }
        invokeKeyboardIfNeed()
        if (editOutfit) {
            editOutfit = false
            vm.updateOutfitCard()
        } else if (autoToggleOutfit) {
            autoToggleOutfit = false
            vm.autoToggleOutfitCardOn()
        }
    }

    override fun onPause() {
        hideKeyboard()
        KeyboardHeightUtilV2.unregisterKeyboardHeightListener(requireActivity())
        super.onPause()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        blueColor = view.getColorByRes(R.color.color_4AB4FF)
        redColor = view.getColorByRes(R.color.color_FF5F42)
        pinkColor = view.getColorByRes(R.color.color_FF5F42_30)
        initMediaSelectionView()
        initGameView()
        initTagView()
        initTagData()
        binding.etContent.onTextChangedListener(viewLifecycleOwner) {
            vm.updateContent(it)
        }
        binding.root.autoHideKeyboard(viewLifecycleOwner, conditional = ::shouldCloseKeyboard)
        binding.tbl.setOnBackAntiViolenceClickedListener {
            leaveOrNot()
        }
        binding.etContent.onKeyListener(viewLifecycleOwner)
        binding.etContent.setOnReEditTextListener(object : ReEditTextListener {
            override fun onTopicRemove() {
                //清空话题列表
                setTopicListVisible(false)
                vm.fetchTags("")
            }

            override fun onTopicEnter(topic: String) {
                vm.fetchTags(topic)
            }
        })
        binding.clBottom.doOnLayoutChanged(viewLifecycleOwner) {
            if (binding.spaceOutfit.height != it.height) {
                binding.spaceTopic.setHeight(it.height - binding.clTop.height.coerceAtLeast(binding.clTop.measuredHeight))
                binding.spaceOutfit.setHeight(it.height)
            }
        }

        binding.tvAddGame.setOnAntiViolenceClickListener {
            Analytics.track(
                EventConstants.ADD_POST_ADD_GAME_CLICK,
                ResIdUtils.getAnalyticsMap(args.resIdBean)
            )

            hideKeyboardInvokeLater()
            MetaRouter.Post.addGameCard(this, PostPublish.MAX_GAME_COUNT)
        }
        binding.tvAddTopic.setOnAntiViolenceClickListener {
            Analytics.track(
                EventConstants.ADD_POST_ADD_TAG_CLICK,
                ResIdUtils.getAnalyticsMap(args.resIdBean)
            )
            binding.etContent.addStartTag(TAG_RULE)
        }
        binding.ivRule.setOnAntiViolenceClickListener {
            hideKeyboardInvokeLater()
            if (isPublishVideo) {
                MetaRouter.Video.goVideoPublishRule(this)
            } else {
                MetaRouter.Post.goPostRule(this)
            }
        }
        binding.tvPostBtn.setOnAntiViolenceClickListener {
            val (ok, msg) = vm.checkPostPublish()
            if (!ok) {
                toast(msg)
            } else {
                if (vm.canPublish()) {
                    vm.updateTopicList(binding.etContent.getTopicList())
                    vm.setCanPublish(false)
                    binding.vCover.visible()
                    vm.checkUserMuteStatus(args.resIdBean)
                } else {
                    toast(R.string.post_publishing_tip)
                }
            }
        }
        binding.tvTopicSure.setOnAntiViolenceClickListener {
            if (!binding.etContent.setTopicComplete()) {
                binding.etContent.clearFocus()
            }
            setTopicListVisible(false)
            hideKeyboard()
        }

        childFragmentManager.setFragmentResultListener(REQUEST_BACK, viewLifecycleOwner) { _, _ ->
            clearFragmentResult(REQUEST_BACK)
            invokeKeyboardIfNeed()
        }

        vm.onEach(PublishPostState::games) {
            val show = !it.isNullOrEmpty()
            binding.rvGame.visible(show)
        }
        vm.onEach(PublishPostState::randomTags) { tags ->
            if (tags.invoke().isNullOrEmpty()) {
                setTopicListVisible(false)
                Timber.d("tags isNullOrEmpty")
            } else {
                setTopicListVisible(true)
                Timber.d("tags notNullOrEmpty")
            }
        }
        vm.onEach(PublishPostState::content) {
            if (!initContent) {
                initContent = true
                if (!it.isNullOrEmpty()) {
                    binding.etContent.setText(it)
                }
            }
            val letterCount = it?.length ?: 0
            lessThan10 = letterCount < PostPublish.MIN_LETTER_COUNT
            val remainLetterCount = PostPublish.MAX_LETTER_COUNT - letterCount

            if (remainLetterCount == PostPublish.MAX_LETTER_COUNT) {
                vm.setCanPublish(true)
                binding.pbLetterCount.gone()
                binding.tvLetterCount.invisible()
            } else if (remainLetterCount >= 0) {
                vm.setCanPublish(true)
                binding.pbLetterCount.visible()
                binding.pbLetterCount.progress = 100 - remainLetterCount / 10
                if (remainLetterCount > 10) {
                    binding.pbLetterCount.setIndicatorColor(blueColor)
                    binding.tvLetterCount.invisible()
                } else {
                    binding.pbLetterCount.setIndicatorColor(redColor)
                    binding.tvLetterCount.visible()
                    binding.tvLetterCount.text = remainLetterCount.toString()
                }
            } else {
                vm.setCanPublish(false)
                binding.pbLetterCount.gone()
                binding.tvLetterCount.visible()
                binding.tvLetterCount.text = remainLetterCount.toString()
            }
        }
        vm.onEach(PublishPostState::muteStatus, uniqueOnly()) {
            if (!it.complete) return@onEach
            if (it.invoke()?.isMuted == true) {
                toast(getString(R.string.community_ban, getString(R.string.posting)))
                vm.setCanPublish(true)
                binding.vCover.gone()
            } else if (vm.canPublish()) {
                toast(R.string.publish_in_progress)
                vm.savePost(args.resIdBean)
                args.shareReqId?.let {
                    ShareResult.notifySuccess(it, SharePlatform.PLATFORM_POST)
                }
                publishFlag = true
                popUp()
            } else {
                toast(R.string.post_publishing_tip)
                vm.setCanPublish(true)
                binding.vCover.gone()
            }
        }
        vm.onEach(PublishPostState::delayKeyboard, uniqueOnly()) {
            if (it != null && !isKeyboardActive && binding.etContent.hasFocus()) {
                InputUtil.showSoftBoard(binding.etContent)
            }
        }
        vm.onEach(PublishPostState::permissionCallback, uniqueOnly()) {
            when (it?.first) {
                PublishPostViewModel.PERMISSION_GRANTED -> {
                    selectPictures()
                }

                PublishPostViewModel.PERMISSION_DENIED -> {
                    invokeKeyboardIfNeed()
                }
            }
        }
        vm.onEach(PublishPostState::isFromMoment, uniqueOnly()) {
            visibleList(
                binding.vSyncVideoFeedBg,
                binding.tvSyncVideoFeed,
                visible = it
            )
            if (it) {
                vm.onEach(PublishPostState::syncVideoFeedSelected) { sync ->
                    binding.tvSyncVideoFeed.isSelected = sync
                }
                binding.tvSyncVideoFeed.setOnAntiViolenceClickListener {
                    vm.toggleSyncVideoFeed()
                }
            }
        }
        vm.onEach(PublishPostState::ugcDesign) {
            val ugcDesign = it
            if (ugcDesign == null) {
                binding.includeUgcDesign.root.gone()
            } else {
                binding.includeUgcDesign.root.visible()
                Glide.with(this)
                    .load(ugcDesign.cover)
                    .into(binding.includeUgcDesign.ivOutfitThumbnail)
                binding.includeUgcDesign.tvOutfitDesc.text =
                    ugcDesign.title.ifNullOrEmpty { getString(R.string.fashion_design) }
            }
        }
        if (enableOutfitShare) {
            visibleList(
                binding.vShareOutfitBg,
                binding.vShareOutfitSplit,
                binding.tvShareOutfit,
                visible = true
            )

            binding.includeOutfit.tvOutfitEdit.setOnAntiViolenceClickListener {
                hideKeyboardInvokeLater()
                editOutfit = true
                MetaRouter.MobileEditor.fullScreenRole(
                    requireContext(),
                    FullScreenEditorActivityArgs(categoryId = CategoryId.OUTFIT_SHARE_POST_EDIT)
                )
            }
            binding.tvShareOutfit.setOnAntiViolenceClickListener {
                if (binding.ivShareOutfitGuide.isVisible) {
                    vm.delayShareOutfitGuideDismiss(false)
                }
                vm.toggleOutfitCard()
            }

            vm.onEach(PublishPostState::outfits) {
                val outfit = it?.firstOrNull { it.isOutfit }
                if (outfit == null) {
                    binding.includeOutfit.root.gone()
                    binding.tvShareOutfit.isSelected = false
                } else {
                    binding.includeOutfit.root.visible()
                    binding.tvShareOutfit.isSelected = true
                    glide?.run {
                        load(outfit.wholeBodyImage).placeholder(R.drawable.avatar_friend_placeholder)
                            .into(binding.includeOutfit.ivOutfitThumbnail)
                    }
                }
            }
            vm.onEach(PublishPostState::outfitCallback, deliveryMode = uniqueOnly()) {
                when (it?.first) {
                    PublishPostViewModel.OUTFIT_NO_DATA -> {
                        hideKeyboardInvokeLater()
                        ConfirmDialog.Builder(this)
                            .image(R.drawable.ic_dialog_light_smile)
                            .content(getString(R.string.failed_get_avatar_info_tips))
                            .confirmBtnTxt(getString(R.string.go))
                            .cancelBtnTxt(getString(R.string.dialog_cancel))
                            .dismissCallback { action ->
                                val result = if (action == ConfirmDialog.CONFIRM) {
                                    autoToggleOutfit = true
                                    MetaRouter.MobileEditor.fullScreenRole(
                                        requireContext(),
                                        FullScreenEditorActivityArgs(categoryId = CategoryId.OUTFIT_SHARE_POST_EDIT)
                                    )
                                    0
                                } else {
                                    invokeKeyboardIfNeed()
                                    vm.trackOutfitShareResult(
                                        PublishPostViewModel.ACTION_FAIL,
                                        PublishPostViewModel.REASON_NO_ROLE_DATA
                                    )
                                    1
                                }
                                Analytics.track(
                                    EventConstants.POST_CREATE_AVATAR_TIPS_CLICK,
                                    "result" to result.toString()
                                )
                            }
                            .show()
                    }

                    PublishPostViewModel.OUTFIT_NO_PERMISSION -> {
                        hideKeyboardInvokeLater()
                        ConfirmDialog.Builder(this)
                            .image(R.drawable.ic_dialog_light_smile)
                            .content(getString(R.string.need_try_on_permission_tips))
                            .confirmBtnTxt(getString(R.string.to_set_up))
                            .cancelBtnTxt(getString(R.string.dialog_cancel))
                            .dismissCallback { action ->
                                invokeKeyboardIfNeed()
                                val result = if (action == ConfirmDialog.CONFIRM) {
                                    vm.enableTryOn()
                                    0
                                } else {
                                    vm.trackOutfitShareResult(
                                        PublishPostViewModel.ACTION_FAIL,
                                        PublishPostViewModel.REASON_NO_PERMISSION
                                    )
                                    1
                                }
                                Analytics.track(
                                    EventConstants.POST_ENABLE_TRY_ON_PERMISSION_CLICK,
                                    "result" to result.toString()
                                )
                            }
                            .show()
                    }
                }
            }
            vm.onEach(PublishPostState::outfitGuideCallback) {
                when (it) {
                    PublishPostState.GUIDE_OUTFIT_YES -> {
                        binding.ivShareOutfitGuide.visible()
                        binding.ivShareOutfitGuide.setImageResource(
                            R.drawable.ic_publish_post_outfit_tips
                        )
                        val anim = AnimationUtils.loadAnimation(
                            requireContext(),
                            R.anim.popup_in_bottom_left_to_top_right
                        )
                        binding.ivShareOutfitGuide.clearAnimation()
                        binding.ivShareOutfitGuide.startAnimation(anim)
                        vm.delayShareOutfitGuideDismiss(true)
                    }

                    PublishPostState.GUIDE_OUTFIT_DISMISS -> {
                        val anim = AnimationUtils.loadAnimation(
                            requireContext(),
                            R.anim.popup_in_top_right_to_bottom_left
                        )
                        binding.ivShareOutfitGuide.clearAnimation()
                        binding.ivShareOutfitGuide.startAnimation(anim)
                        vm.delayShareOutfitGuideHide()
                    }

                    PublishPostState.GUIDE_OUTFIT_HIDE -> {
                        unloadShareOutfitGuide()
                    }
                }
            }

            vm.checkShareOutfitGuide()
        }
        vm.onEach(PublishPostState::enablePublish) {
            binding.tvPostBtn.enableWithAlpha(it)
        }
    }

    private fun initTagData() {
        val data = StringBuilder()
        args.tags?.forEach {
            data.append(TAG_RULE).append(it.tagName).append(" ")
        }
        if (!args.tags.isNullOrEmpty()) {
            binding.etContent.addTopic(data.toString())
        }
    }

    private fun isSelectedVideo(): Boolean {
        return withState(vm) { state ->
            state.medias?.any { it.isVideo } ?: false
        }
    }

    private fun popUp() {
        if (args.clearTopBackFeed) {
            MetaRouter.Main.tabFeed(requireContext(), null)
        } else {
            leave()
        }
    }

    private fun initMediaSelectionView() {
        binding.rvImg.layoutManager = LinearLayoutManager(
            requireContext(),
            LinearLayoutManager.HORIZONTAL,
            false
        )
        binding.rvImg.setController(mediaController)
        CustomEpoxyTouchHelper.initDragging(mediaController)
            .withRecyclerView(binding.rvImg)
            .withDirections(ItemTouchHelper.DOWN or ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT)
            .withTarget(PublishPostMediaItem::class.java)
            .andCallbacks(
                viewLifecycleOwner,
                object : CustomEpoxyTouchHelper.DragCallbacks<PublishPostMediaItem>() {
                    override fun onDragStarted(
                        model: PublishPostMediaItem,
                        itemView: View,
                        adapterPosition: Int
                    ) {
                        if (!imgCleared) return
                        imgCleared = false
                        itemView.getGlobalVisibleRect(rect)
                        if (rect.height() == itemView.height) {
                            val srcTop = rect.top
                            binding.llDelArea.getGlobalVisibleRect(rect)
                            val targetTop = rect.top - binding.llDelArea.translationY.toInt()
                            canDrag = true
                            imgItem = model
                            dragView = itemView
                            relativeY = targetTop - srcTop + itemView.y.toInt()
                            binding.llDelArea.animate().translationY(0.0f)
                            if (binding.tvShareOutfit.isVisible) {
                                binding.clBottom.animate().alpha(0.0f)
                            }
                        }
                    }

                    override fun onModelMoved(
                        fromPosition: Int,
                        toPosition: Int,
                        modelBeingMoved: PublishPostMediaItem,
                        itemView: View
                    ) {
                        vm.moveMedia(fromPosition, toPosition)
                    }

                    override fun clearView(model: PublishPostMediaItem, itemView: View) {
                        imgCleared = true
                    }
                }
            )
        binding.rvImg.setOnTouchListener(onTouchListener)
    }

    private fun initGameView() {
        binding.rvGame.layoutManager = LinearLayoutManager(requireContext())
        binding.rvGame.setController(gameController)
        CustomEpoxyTouchHelper.initDragging(gameController)
            .withRecyclerView(binding.rvGame)
            .withDirections(ItemTouchHelper.DOWN)
            .withTarget(PostGameItem::class.java)
            .andCallbacks(
                viewLifecycleOwner,
                object : CustomEpoxyTouchHelper.DragCallbacks<PostGameItem>() {
                    override fun onDragStarted(
                        model: PostGameItem,
                        itemView: View,
                        adapterPosition: Int
                    ) {
                        if (!gameCleared) return
                        gameCleared = false
                        itemView.getGlobalVisibleRect(rect)
                        if (rect.height() == itemView.height) {
                            val srcTop = rect.top
                            binding.llDelArea.getGlobalVisibleRect(rect)
                            val targetTop = rect.top - binding.llDelArea.translationY.toInt()
                            canDrag = true
                            gameItem = model
                            dragView = itemView
                            relativeY = targetTop - srcTop + itemView.y.toInt()
                            binding.llDelArea.animate().translationY(0.0f)
                            if (binding.tvShareOutfit.isVisible) {
                                binding.clBottom.animate().alpha(0.0f)
                            }
                        }
                    }

                    override fun onModelMoved(
                        fromPosition: Int,
                        toPosition: Int,
                        modelBeingMoved: PostGameItem,
                        itemView: View
                    ) {

                    }

                    override fun isDragEnabledForModel(model: PostGameItem): Boolean {
                        return gameItem == null || gameItem == model
                    }

                    override fun clearView(model: PostGameItem, itemView: View) {
                        gameCleared = true
                    }
                }
            )
        binding.rvGame.setOnTouchListener(onTouchListener)
    }

    private fun initTagView() {
        binding.rvTopic.layoutManager = LinearLayoutManager(requireContext())
        binding.rvTopic.setController(tagController)
    }

    private fun setTopicListVisible(isVisible: Boolean) {
        if (lastVisible == isVisible) {
            //当前的状态和上一次状态一样，不需要处理
            return
        }
        lastVisible = isVisible
        binding.rvTopic.visible(isVisible)
        binding.spaceTopic.visible(isVisible)
        binding.rvGame.visible(!isVisible)
        binding.tvTopicSure.visible(isVisible)
        binding.tvPostBtn.visible(!isVisible)
        binding.spaceBottom.visible(!isVisible)
        binding.spaceOutfit.visible(!isVisible)
        val oldState = vm.oldState
        binding.includeOutfit.root.visible(!isVisible && oldState.outfit != null)
        binding.includeUgcDesign.root.visible(!isVisible && oldState.ugcDesign != null)
        move(isVisible)
    }

    private fun move(isCollapse: Boolean) {
        getAnimator(isCollapse).start()
    }

    private fun getMoveHeight(): Int {
        return (binding.clTop.measuredHeight)
    }

    private fun getAnimator(isCollapse: Boolean): ObjectAnimator {
        objectAnimator = if (isCollapse) {
            ObjectAnimator.ofFloat(
                binding.clRoot, "translationY", 0f, (-getMoveHeight()).toFloat()
            ).setDuration(200)

        } else {
            ObjectAnimator.ofFloat(
                binding.clRoot, "translationY", (-getMoveHeight()).toFloat(), 0f
            ).setDuration(200)
        }
        return objectAnimator!!
    }


    private fun animateDeleteTitle(full: Boolean) {
        if ((full && binding.tvDeleteTitle.text == deleteFullTitle) || (!full && binding.tvDeleteTitle.text == deleteTitle)) {
            return
        }
        deleteAnimator?.cancel()
        deleteAnimator = if (full) {
            ValueAnimator.ofInt(
                deleteTitleIndex, if (isForward) {
                    deleteFullTitle.length
                } else {
                    0
                }
            )
        } else {
            ValueAnimator.ofInt(
                deleteTitleIndex, if (isForward) {
                    deleteTitle.length
                } else {
                    deleteFullTitle.length - deleteTitle.length
                }
            )
        }.apply {
            duration = 100
            addUpdateListener {
                deleteTitleIndex = it.animatedValue as Int
                binding.tvDeleteTitle.text = deleteFullTitle.substring(
                    if (isForward) 0 else deleteTitleIndex,
                    if (isForward) deleteTitleIndex else deleteFullTitle.length
                )
            }
            addListener(onEnd = {
                deleteAnimator = null
                if (!full) {
                    binding.tvDeleteTitle.text = deleteTitle
                }
            })
            start()
        }
    }

    private fun buildMediaController() = simpleController(vm, PublishPostState::medias) {
        var hasVideo = false
        var imgCount = 0
        var itemIdx = 0
        it?.forEach { media ->
            if (media.isSupported) {
                if (media.isVideo) {
                    hasVideo = true
                } else if (media.isImage) {
                    imgCount++
                } else {
                    error("Unsupported media type")
                }
                publishPostMediaItem(media, itemIdx, itemListener)
                itemIdx++
            }
        }
        if (!hasVideo && imgCount in 0 until PostPublish.MAX_IMG_COUNT) {
            publishPostMediaAddItem(itemListener)
        }
    }

    private fun buildGameController() = simpleController(vm, PublishPostState::games) {
        val dp10 = dp(10)
        it?.forEachIndexed { index, game ->
            postGameItem(game, if (index == 0) 0 else dp10, false, itemListener)
        }
    }

    private fun buildTagController() = simpleController(vm, PublishPostState::randomTags) {
        if (it is Success) {
            it.invoke()?.forEach { tag ->
                postNewTagItem(tag, itemListener)
            }
        }
    }

    private fun selectPicturesWithPermission() {
        hideKeyboardInvokeLater()
        if (PermissionUtil.needPermission4PublishPost(requireActivity())) {
            canInvokeKeyboard = false
            PermissionRequest.with(requireActivity())
                .permissions(*PermissionRequest.getMediaPermission().toTypedArray())
                .allowAnyPermissionIsOk()
                .enableGoSettingDialog()
                .granted {
                    canInvokeKeyboard = true
                    vm.handlePermissionResult(PublishPostViewModel.PERMISSION_GRANTED)
                }
                .denied { goSysSettings ->
                    canInvokeKeyboard = true
                    if (!goSysSettings) {
                        vm.handlePermissionResult(PublishPostViewModel.PERMISSION_DENIED)
                    }
                }
                .branch(PermissionRequest.SCENE_ALBUM)
                .request()
        } else {
            selectPictures()
        }
    }

    private fun selectPictures() {
        if (isPublishVideo) {
            selectVideos()
            return
        }
        context?.let { context ->
            withState(vm) { state ->
                var hasVideo = false
                var imgCount = 0
                state.medias?.forEach {
                    if (it.isVideo) {
                        hasVideo = true
                    } else if (it.isImage) {
                        imgCount++
                    }
                }
                if (hasVideo || imgCount >= PostPublish.MAX_IMG_COUNT) {
                    toast(R.string.post_select_media_max)
                    return@withState
                }
                val chooseMode = if (imgCount > 0) {
                    SelectMimeType.ofImage()
                } else {
                    SelectMimeType.ofAll()
                }
                PictureSelector.create(this)
                    .openGallery(chooseMode)
                    .setMaxSelectNum(PostPublish.MAX_IMG_COUNT - imgCount)
                    .setMaxVideoSelectNum(PostPublish.MAX_VIDEO_COUNT)
                    .setSelectMaxFileSize(300 * 1024)
                    .setImageEngine(GlideEngine)
                    .setCompressEngine(LubanCompressEngine())
                    .setVideoPlayerEngine(CustomMediaPlayerEngine())
                    .setSelectorUIStyle(PictureSelectorUtil.getCommonStyle(context))
                    .setSelectLimitTipsListener { ctx, _, _, limitType ->
                        val res = if (limitType == SelectLimitType.SELECT_MAX_FILE_SIZE_LIMIT) {
                            R.string.big_video_tips
                        } else {
                            R.string.post_select_media_max
                        }
                        ctx?.toast(res)
                        true
                    }
                    .forResult(object : OnResultCallbackListener<LocalMedia> {
                        override fun onResult(result: ArrayList<LocalMedia>?) {
                            vm.updateMedias(result)
                        }

                        override fun onCancel() {}
                    })
            }
        }
    }

    private fun selectVideos() {
        context?.let { context ->
            withState(vm) { state ->

                // TODO 文案：只能选择一个视频
                if (isSelectedVideo()) {
                    toast(R.string.video_select_media_max)
                    return@withState
                }

                val pictureSelector = PictureSelector.create(this)
                    .openGallery(SelectMimeType.ofVideo())
                    .setMaxSelectNum(1)
                    .setFilterVideoMaxSecond(60 * 30)
                    .setRecordVideoMaxSecond(60 * 30)
                    .setImageEngine(GlideEngine)
                    .setCompressEngine(LubanCompressEngine())
                    .setSelectorUIStyle(PictureSelectorUtil.getCommonStyle(context))
                    .setSelectLimitTipsListener { ctx, _, _, limitType ->
                        // TODO 文案：视频时长
                        val res = if (limitType == SelectLimitType.SELECT_MAX_FILE_SIZE_LIMIT) {
                            R.string.video_select_duration_exceeded
                        } else {
                            R.string.video_select_media_max
                        }
                        ctx?.toast(res)
                        true
                    }

                pictureSelector.forResult(object : OnResultCallbackListener<LocalMedia> {
                    override fun onResult(result: ArrayList<LocalMedia>?) {
                        vm.updateMedias(result)
                    }

                    override fun onCancel() {}
                })
            }
        }
    }

    private fun shouldCloseKeyboard(rect: Rect, x: Int, y: Int): Boolean {

        val views = buildList {
            add(binding.tbl)
            addAll(binding.rvImg.getVisibleItemViews())
            addAll(binding.rvGame.getVisibleItemViews())
            add(binding.includeOutfit.root)
            add(binding.includeUgcDesign.root)
            addAll(binding.rvTopic.getVisibleItemViews())
            add(binding.tvShareOutfit)
            add(binding.tvAddTopic)
            add(binding.tvAddGame)
            add(binding.tvTopicSure)
        }
        for (view in views) {
            view.getGlobalVisibleRect(rect)
            if (rect.contains(x, y)) {
                return false
            }
        }
        return true
    }

    private fun hideKeyboard() {
        InputUtil.hideKeyboard(binding.etContent)
        isKeyboardActive = false
    }

    private fun hideKeyboardInvokeLater() {
        needInvokeKeyboard = isKeyboardActive
        hideKeyboard()
    }

    private fun invokeKeyboardIfNeed() {
        if (canInvokeKeyboard && needInvokeKeyboard) {
            needInvokeKeyboard = false
            binding.etContent.requestFocusFromTouch()
            vm.delayKeyboard()
        }
    }

    override fun invalidate() {}

    private fun leaveOrNot() {
        withState(vm) {
            if (!it.hasContent) {
                leave()
            } else {
                hideKeyboardInvokeLater()
                val save = SimpleListData(
                    getString(R.string.save_for_draft),
                    bgResource = R.drawable.selector_button_warn
                )
                val leave = SimpleListData(getString(R.string.post_leave_cap),)
                val cancel  = SimpleListData(getString(R.string.cancel))
                val list = buildList {
                    if (it.postId.isNullOrEmpty()) {
                        add(save)
                    }
                    add(leave)
                    add(cancel)
                }
                ListDialog()
                    .list(list)
                    .content(getString(R.string.post_leave_tip))
                    .onViewCreateCallback {
                        Analytics.track(
                            EventConstants.RETENTION_UP_SHOW
                        )
                    }
                    .clickCallback {
                        when (it) {
                            leave -> {
                                Analytics.track(
                                    EventConstants.RETENTION_UP_CLICK,
                                    EventParamConstants.KEY_TYPE to 1
                                )
                                leave()
                            }

                            save -> {
                                toastLong(R.string.save_for_draft_toast)
                                Analytics.track(
                                    EventConstants.RETENTION_UP_CLICK,
                                    EventParamConstants.KEY_TYPE to 2
                                )
                                vm.saveForDraft()
                                leave()
                            }

                            else -> {
                                Analytics.track(
                                    EventConstants.RETENTION_UP_CLICK,
                                    EventParamConstants.KEY_TYPE to 3
                                )
                                invokeKeyboardIfNeed()
                            }
                        }
                    }.show(childFragmentManager, "PublishPostLeaveDialog")
            }
        }
    }

    private fun leave() {
        val fromGameId = args.fromGameId
        val fromPkgName = args.fromPkgName
        if (!fromGameId.isNullOrEmpty() && !fromPkgName.isNullOrEmpty()) {
            back2Game(fromGameId, fromPkgName)
        }
        findNavController().popBackStack(R.id.publish_post, true)
    }

    private fun back2Game(fromGameId: String, fromPkgName: String) {
        resumeGameById(fromGameId)
//        val isUgc = GlobalContext.get().get<MetaKV>().analytic.getLaunchResIdBeanWithId(fromGameId)
//            ?.getTsType() == ResIdBean.TS_TYPE_UCG
//        if (!TsLaunchUtil.resumeToGame(fromGameId)) {
//            TsLaunchUtil.resumeFromGame(
//                fromGameId,
//                fromPkgName,
//                requireContext().applicationContext,
//                isUgc
//            )
//        }

    }

    private fun unloadShareOutfitGuide() {
        if (binding.ivShareOutfitGuide.isVisible) {
            binding.ivShareOutfitGuide.clearAnimation()
            binding.ivShareOutfitGuide.setImageBitmap(null)
            binding.ivShareOutfitGuide.gone()
            vm.shareOutfitGuideHidden()
        }
    }

    override fun onDestroyView() {
        binding.rvImg.unsetOnTouch()
        binding.rvImg.clear()
        binding.rvGame.unsetOnTouch()
        binding.rvGame.clear()
        canDrag = false
        imgItem = null
        imgCleared = true
        gameItem = null
        gameCleared = true
        dragView = null
        deleteAnimator?.cancel()
        deleteAnimator?.removeAllUpdateListeners()
        deleteAnimator?.removeAllListeners()
        deleteAnimator = null
        deleteTitleIndex = if (isForward) {
            deleteTitle.length
        } else {
            deleteFullTitle.length - deleteTitle.length
        }
        unloadShareOutfitGuide()
        objectAnimator?.cancel()
        objectAnimator = null
        binding.etContent.setOnReEditTextListener(null)
        super.onDestroyView()
    }

    override fun getPageName() = PageNameConstants.FRAGMENT_PUBLISH_POST

    override fun onDestroy() {
        args.shareReqId?.let {
            if (!publishFlag) {
                ShareResult.notifyCancel(it, SharePlatform.PLATFORM_POST)
            }
        }
        super.onDestroy()
    }
}