package com.socialplay.gpark.ui.party

import androidx.lifecycle.*
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.choice.ChoiceCardType
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * create by: bin on 2023/3/1
 */
class PartyViewModel(private val repository: IMetaRepository):ViewModel() {

    private val _gameListLiveData: MutableLiveData<Pair<String?,List<ChoiceGameInfo>?>> = MutableLiveData()
    val gameListLiveData: LiveData<Pair<String?, List<ChoiceGameInfo>?>> = _gameListLiveData

    val errMsgFlow = _gameListLiveData.asFlow().distinctUntilChangedBy { it }.map { it.first }
        .shareIn(viewModelScope, SharingStarted.Eagerly)


    fun loadData() = viewModelScope.launch {
        repository.getChoiceCardList().collectLatest {
            if (it.succeeded) {
                val gameList = it.data?.dataList?.filter { it.gameList != null && it.cardType == ChoiceCardType.BANNER }?.flatMap { it.gameList!! }?.toList()
                _gameListLiveData.postValue(null to gameList)
            } else {
                _gameListLiveData.postValue(it.message to null)
            }
        }
    }

    suspend fun loadMetaAppInfo(gameId:String) = repository.fetchGameInfoByIdFromRemoteWithCache(gameId).map { it.data }
}