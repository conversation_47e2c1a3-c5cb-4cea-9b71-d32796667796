package com.socialplay.gpark.ui.gamedetail.dialog

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.flower.FlowerLeaderboardItemData
import com.socialplay.gpark.databinding.FragmentFlowerLeaderboardDialogBinding
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRVBottomSheetDialogFragment
import com.socialplay.gpark.ui.core.MavericksViewEx
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.FlowerGiftingGuidelineDialog
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * 花朵排行榜弹框
 */
class FlowerLeaderboardDialog : BaseRVBottomSheetDialogFragment(), MavericksViewEx {

    override val binding by viewBinding(FragmentFlowerLeaderboardDialogBinding::inflate)
    private val viewModel: FlowerLeaderboardViewModel by fragmentViewModel()
    private val metaKV: MetaKV = GlobalContext.get().get<MetaKV>()
    private val args: FlowerLeaderboardDialogArgs by args()
    override val recyclerView: EpoxyRecyclerView
        get() = binding.rvLeaderboard

    private var onSendFlowerClicked: (() -> Unit)? = null
    private var feedbackConfigInitialized: Boolean = false
    private var dataInitialized: Boolean = false
    private var dataIsEmpty: Boolean = false
    private var feedbackConfig: String? = null

    // 设置一个较小的高度百分比，当数据少时看起来会更合适
    override var heightPercent: Float = 0.5F

    override fun epoxyController(): EpoxyController = simpleController(
        viewModel, FlowerLeaderboardModelState::list, FlowerLeaderboardModelState::currentUser, FlowerLeaderboardModelState::isInTop
    ) { list, currentUser, isInTop ->
        // 打印列表内容
        Timber.d("List size: ${list.size}, isOwner: ${args.isOwner}, currentUser: $currentUser, isInTop: $isInTop")

        list.forEach { item ->
            flowerLeaderboardItem(
                item, ::glide
            ) { clickedItem ->
                Analytics.track(
                    EventConstants.C_GAMEDETAIL_FLOWER_RANK_CLICK,
                    "gameid" to args.gameId,
                    "user_type" to if (args.isOwner) "owner" else "visitor",
                    "creatortype" to args.gameType.toString(),
                    "click" to "profile",
                )
                // 点击排行榜项目，跳转到用户详情页
                MetaRouter.Profile.other(
                    fragment = this@FlowerLeaderboardDialog, otherUuid = clickedItem.uid, from = "flower_leaderboard", checkFollow = false
                )
            }
        }

        lifecycleScope.launch(Dispatchers.Main) {
            updateSelfInfo(list, currentUser, isInTop)
            // 根据数据量动态调整Dialog高度
            adjustDialogHeight(list.size)
        }
    }

    companion object {
        fun newInstance(
            gameId: String,
            gameType: Int,
            isOwner: Boolean = false,
            onSendFlowerClicked: (() -> Unit)? = null,
        ): FlowerLeaderboardDialog {
            return FlowerLeaderboardDialog().apply {
                this.onSendFlowerClicked = onSendFlowerClicked
                arguments = FlowerLeaderboardDialogArgs(
                    gameId,
                    gameType,
                    isOwner
                ).asMavericksArgs()
            }
        }
    }

    override fun init() {
        skipCollapsed()

        lifecycleScope.launch(Dispatchers.IO) {
            // 去T台获取反馈按钮跳转的帖子ID，如果没有则不展示反馈按钮
            val tTaiValueHelper = GlobalContext.get().get<TTaiInteractor>().getTTaiValueHelper(TTaiKV.ID_FLOWER_LEADERBOARD_FEEDBACK_POST_ID)
            Timber.d("T台获取反馈按钮跳转的帖子ID: $tTaiValueHelper")
            feedbackConfig = tTaiValueHelper
            feedbackConfigInitialized = true
            if (dataInitialized) {
                refreshFeedbackUI()
            }
        }


        // 设置圆角背景
        dialog?.window?.decorView?.background = null
        getBottomSheet()?.background = ContextCompat.getDrawable(requireContext(), R.drawable.flower_leaderboard_dialog_bg)

        // 设置关闭按钮点击事件
        binding.ivClose.setOnAntiViolenceClickListener {
            dismissAllowingStateLoss()
        }

        // 问号按钮点击事件
        binding.ivQuestion.setOnAntiViolenceClickListener {
            val h5PageConfigInteractor: H5PageConfigInteractor = GlobalContext.get().get<H5PageConfigInteractor>()
            val item = h5PageConfigInteractor.getH5PageConfigItem(H5PageConfigInteractor.SEND_FLOWER_FLOWER_RANK_README_URL)
            MetaRouter.Web.navigate(
                this,
                item.title,
                item.url,
                true,
            )
        }

        // 送花按钮点击事件
        binding.btnSendFlower.setOnAntiViolenceClickListener {
            Analytics.track(
                EventConstants.C_GAMEDETAIL_FLOWER_RANK_CLICK,
                "gameid" to args.gameId,
                "user_type" to if (args.isOwner) "owner" else "visitor",
                "creatortype" to args.gameType.toString(),
                "click" to "flower",
            )

            // 不允许自己给自己送花
            if (args.isOwner) {
                return@setOnAntiViolenceClickListener
            }

            // 客态第一次点击需要先显示送花须知弹窗
            if (metaKV.account.showSendFlowerGuidelineDialog) {
                FlowerGiftingGuidelineDialog.show(childFragmentManager) {
                    // 只有用户点了我知道了, 才不弹弹窗
                    metaKV.account.showSendFlowerGuidelineDialog = false
                    dismissAllowingStateLoss()
                    onSendFlowerClicked?.invoke()
                    onSendFlowerClicked = null
                }
            } else {
                dismissAllowingStateLoss()
                onSendFlowerClicked?.invoke()
                onSendFlowerClicked = null
            }
        }

        // 设置刷新加载
        viewModel.onAsync(FlowerLeaderboardModelState::refresh, onLoading = { data ->
            if (data == null) {
                binding.loading.showLoading()
            }
        }, onFail = { _, data ->
            if (data == null) {
                binding.loading.showError()
            }
        }, onSuccess = { data ->
            if (data.rankings.isNullOrEmpty()) {
                binding.loading.showOtherEmpty(getString(R.string.flower_leaderboard_empty_tip), R.drawable.icon_no_network_connection)
                dataIsEmpty = true
            } else {
                binding.loading.hide()
                dataIsEmpty = false
            }
            dataInitialized = true
            if (feedbackConfigInitialized) {
                refreshFeedbackUI()
            }
        })

        // 设置加载重试
        binding.loading.setRetry {
            viewModel.refreshList()
        }

        // 设置RecyclerView
        binding.rvLeaderboard.setController(epoxyController)

        // 根据isOwner参数控制UI
        updateUIByOwnerState()
    }

    private fun refreshFeedbackUI() {
        if ((!feedbackConfig.isNullOrEmpty() || feedbackConfig == "-1") && dataIsEmpty) {
            // 配置不为空，数据为空，显示按钮
            // T 台配置配成-1时, 也不显示去反馈按钮
            binding.btnGoFeedback.visible(true)
            binding.btnGoFeedback.setOnAntiViolenceClickListener {
                // 跳转到帖子详情页
                MetaRouter.Post.goPostDetail(
                    this@FlowerLeaderboardDialog,
                    feedbackConfig!!,
                    "flower_leaderboard",
                )
            }
        } else {
            binding.btnGoFeedback.visible(false)
        }
    }


    private fun updateUIByOwnerState() {
        withState(viewModel) { state ->
            // 如果是主态（游戏发布者），不显示当前用户信息和送花按钮
            binding.layoutCurrentUser.visible(!state.isOwner)
            binding.btnSendFlower.visible(!state.isOwner)

            // 调整列表的底部约束
            val params = binding.rvLeaderboard.layoutParams as ConstraintLayout.LayoutParams
            if (state.isOwner) {
                // 主态模式，列表直接到底部
                params.bottomToTop = ConstraintLayout.LayoutParams.UNSET
                params.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                params.bottomMargin = resources.getDimensionPixelSize(R.dimen.dp_65)
                binding.rvLeaderboard.layoutParams = params

                // 打印日志
                Timber.d("Owner mode: bottomToTop = UNSET, bottomMargin = ${params.bottomMargin}")
            } else {
                // 客态模式，列表到当前用户信息上方
                params.bottomToTop = R.id.layout_current_user
                params.bottomToBottom = ConstraintLayout.LayoutParams.UNSET
                params.bottomMargin = 0
                binding.rvLeaderboard.layoutParams = params

                // 打印日志
                Timber.d("Guest mode: bottomToTop = R.id.layout_current_user, bottomMargin = ${params.bottomMargin}")
            }
        }
    }


    private fun updateSelfInfo(list: List<FlowerLeaderboardItemData>, currentUser: FlowerLeaderboardItemData?, isInTop: Boolean) {

        // 设置当前用户信息和按钮的可见性
        val isEmpty = list.isEmpty()
        withState(viewModel) { state ->
            val isOwner = state.isOwner
            binding.layoutCurrentUser.visibility = if (isOwner || isEmpty) View.GONE else View.VISIBLE
            binding.btnSendFlower.visibility = if (isOwner || isEmpty) View.GONE else View.VISIBLE

            // 打印日志
            Timber.d("updateSelfInfo: isOwner = $isOwner, isEmpty = $isEmpty, layoutCurrentUser.visibility = ${binding.layoutCurrentUser.visibility}, btnSendFlower.visibility = ${binding.btnSendFlower.visibility}")
        }

        // 更新当前用户信息
        if (currentUser != null) {
            binding.tvCurrentUserName.text = currentUser.nickname

            binding.ivCurrentUserAvatar.post {
                glide?.load(currentUser.portrait)?.placeholder(R.drawable.icon_default_avatar)?.into(binding.ivCurrentUserAvatar)
            }

            // 设置排名
            when (currentUser.rank) {
                1 -> {
                    // 第一名显示图标
                    binding.tvCurrentUserRank.visibility = View.GONE
                    binding.ivCurrentUserRank.visibility = View.VISIBLE
                    binding.ivCurrentUserRank.setImageResource(R.drawable.flower_leaderboard_self_no1)
                    binding.layoutCurrentUser.setBackgroundResource(R.drawable.bg_flower_current_user_no1)
                }

                2 -> {
                    // 第二名显示图标
                    binding.tvCurrentUserRank.visibility = View.GONE
                    binding.ivCurrentUserRank.visibility = View.VISIBLE
                    binding.ivCurrentUserRank.setImageResource(R.drawable.flower_leaderboard_self_no2)
                    binding.layoutCurrentUser.setBackgroundResource(R.drawable.bg_flower_current_user_no2)
                }

                3 -> {
                    // 第三名显示图标
                    binding.tvCurrentUserRank.visibility = View.GONE
                    binding.ivCurrentUserRank.visibility = View.VISIBLE
                    binding.ivCurrentUserRank.setImageResource(R.drawable.flower_leaderboard_self_no3)
                    binding.layoutCurrentUser.setBackgroundResource(R.drawable.bg_flower_current_user_no3)
                }

                else -> {
                    binding.ivCurrentUserRank.visibility = View.GONE
                    binding.tvCurrentUserRank.visibility = View.VISIBLE
                    binding.tvCurrentUserRank.text = if (currentUser.rank > 0) getString(R.string.flower_leaderboard_rank_format, currentUser.rank) else getString(R.string.flower_leaderboard_not_ranked)
                    binding.layoutCurrentUser.setBackgroundResource(R.drawable.bg_flower_current_user_other)
                }
            }

            // 设置花朵数量
            binding.tvCurrentUserFlowerCount.text = "×" + currentUser.tippingCnt.toString()
        }

        // 如果当前用户不在排行榜中，但有排行榜数据
        if (currentUser != null && !isInTop && list.isNotEmpty()) {
            binding.ivCurrentUserRank.visibility = View.GONE
            binding.tvCurrentUserRank.visibility = View.VISIBLE
            binding.tvCurrentUserRank.text = getString(R.string.flower_leaderboard_not_ranked)
            binding.tvCurrentUserFlowerCount.text = "×0"
        }
    }

    override fun invalidate() {
        epoxyController.requestModelBuild()
    }

    override fun getPageName(): String = PageNameConstants.DIALOG_FLOWER_LEADERBOARD

    override fun needCountTime(): Boolean = true

    override fun loadFirstData() {
        // 已在init中调用refreshList()
    }

    /**
     * 根据数据量动态调整Dialog高度
     */
    private fun adjustDialogHeight(itemCount: Int) {
        val targetHeight = when {
            itemCount == 0 -> {
                // 空数据时使用70%
                (ScreenUtil.screenHeight * 0.7f).toInt()
            }
            itemCount <= 5 -> {
                // 数据量少时，计算实际需要的高度
                calculateMinimumHeight(itemCount)
            }
            else -> {
                // 数据量多时，使用默认高度
                (ScreenUtil.screenHeight * 0.8f).toInt()
            }
        }

        // 应用新的高度
        getBottomSheet()?.let { bottomSheet ->
            val layoutParams = bottomSheet.layoutParams as CoordinatorLayout.LayoutParams
            layoutParams.height = targetHeight
            bottomSheet.layoutParams = layoutParams
            bottomSheet.requestLayout()

            Timber.d("FlowerLeaderboard: Adjusted height to $targetHeight for $itemCount items")
        }
    }

    /**
     * 计算最小需要的高度
     */
    private fun calculateMinimumHeight(itemCount: Int): Int {
        val titleHeight = resources.getDimensionPixelSize(R.dimen.dp_32) + resources.getDimensionPixelSize(R.dimen.dp_24) // title + margin
        val subtitleHeight = resources.getDimensionPixelSize(R.dimen.dp_8) + resources.getDimensionPixelSize(R.dimen.dp_16) // subtitle + margin
        val itemHeight = resources.getDimensionPixelSize(R.dimen.dp_64) // 每个item的高度
        val currentUserHeight = if (args.isOwner) 0 else resources.getDimensionPixelSize(R.dimen.dp_58) + resources.getDimensionPixelSize(R.dimen.dp_16) // 当前用户信息
        val sendFlowerButtonHeight = if (args.isOwner) 0 else resources.getDimensionPixelSize(R.dimen.dp_48) + resources.getDimensionPixelSize(R.dimen.dp_55) // 送花按钮
        val padding = resources.getDimensionPixelSize(R.dimen.dp_32) // 额外padding

        val totalHeight = titleHeight + subtitleHeight + (itemHeight * itemCount) + currentUserHeight + sendFlowerButtonHeight + padding

        // 确保不超过屏幕高度的80%
        return minOf(totalHeight, (ScreenUtil.screenHeight * 0.8f).toInt())
    }

}
