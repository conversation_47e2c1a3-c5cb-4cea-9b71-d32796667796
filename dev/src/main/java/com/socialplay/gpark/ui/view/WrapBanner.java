package com.socialplay.gpark.ui.view;


import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.ViewConfiguration;

import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.youth.banner.Banner;
import com.youth.banner.adapter.BannerAdapter;

public class WrapBanner<T, BA extends BannerAdapter<T, ? extends RecyclerView.ViewHolder>> extends Banner<T, BA> {

    private int mTouchSlop;
    private float mStartX, mStartY;
    private boolean hasIntercepted = false;

    public WrapBanner(Context context) {
        super(context);
        init(context);
    }

    public WrapBanner(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public WrapBanner(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        mTouchSlop = ViewConfiguration.get(context).getScaledTouchSlop();
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        if (!getViewPager2().isUserInputEnabled()) {
            return super.onInterceptTouchEvent(event);
        }
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mStartX = event.getX();
                mStartY = event.getY();
                getParent().requestDisallowInterceptTouchEvent(true);
                hasIntercepted = false;
                break;
            case MotionEvent.ACTION_MOVE:
                if (!hasIntercepted) {
                    float endX = event.getX();
                    float endY = event.getY();
                    float distanceX = Math.abs(endX - mStartX);
                    float distanceY = Math.abs(endY - mStartY);

                    if (distanceX > mTouchSlop || distanceY > mTouchSlop) {
                        if (getViewPager2().getOrientation() == ViewPager2.ORIENTATION_HORIZONTAL) {
                            if (distanceX < mTouchSlop) {
                                getParent().requestDisallowInterceptTouchEvent(false);
                            }
                        } else {
                            if (distanceY < mTouchSlop) {
                                getParent().requestDisallowInterceptTouchEvent(false);
                            }
                        }
                        hasIntercepted = true;
                    }
                }

                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                getParent().requestDisallowInterceptTouchEvent(false);
                break;
        }
        return false;
    }
}
