package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout
import com.socialplay.gpark.util.extension.observeOnMainThreadWhenNotDestroyed
import kotlin.math.abs

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/03/12
 *     desc   : 针对DrawerLayout等父布局，对ViewPager2同方向的事件进行拦截
 * </pre>
 */
class DrawerViewPager2Host @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AllowStateLossFrameLayout(context, attrs, defStyleAttr) {

    private var mViewPager2: ViewPager2? = null

    private var startX = 0
    private var startY = 0
    private var hasIntercepted = false // ACTION_MOVE 拦截后不再处理

    private var mOnEventListener: OnEventListener? = null

    interface OnEventListener {
        fun onEvent(
            ev: MotionEvent,
            vp2: ViewPager2,
            adapter: RecyclerView.Adapter<*>,
            intercept: Boolean
        )
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        for (i in 0 until childCount) {
            val childView = getChildAt(i)
            if (childView is ViewPager2) {
                mViewPager2 = childView
                break
            }
        }
        if (mViewPager2 == null) {
            throw IllegalStateException("The root child of ViewPager2Host must contains a ViewPager2")
        }
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        handleInterceptEvent(ev)
        return super.onInterceptTouchEvent(ev)
    }

    /**
     * 根据滑动方向、距离来[requestDisallowInterceptTouchEvent]
     */
    private fun handleInterceptEvent(ev: MotionEvent) {
        val vp2 = mViewPager2 ?: return
        val adapter = vp2.adapter ?: return

        val noNeedToIntercept = !vp2.isUserInputEnabled || adapter.itemCount <= 1
        if (noNeedToIntercept) {
            return
        }

        when (ev.action) {
            MotionEvent.ACTION_DOWN -> {
                hasIntercepted = false
                startX = ev.x.toInt()
                startY = ev.y.toInt()
                parent.requestDisallowInterceptTouchEvent(false)
            }

            MotionEvent.ACTION_MOVE -> {
                if (!hasIntercepted) {
                    hasIntercepted = true
                    val endX = ev.x.toInt()
                    val endY = ev.y.toInt()
                    val disX = abs(endX - startX)
                    val disY = abs(endY - startY)
                    val intercept = if (vp2.orientation == ViewPager2.ORIENTATION_HORIZONTAL) {
                        onHorizontalActionMove(vp2, adapter, endX, disX, disY)
                    } else {
                        onVerticalActionMove(vp2, adapter, endY, disX, disY)
                    }
                    mOnEventListener?.onEvent(ev, vp2, adapter, intercept)
                    parent.requestDisallowInterceptTouchEvent(intercept)
                }
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                parent.requestDisallowInterceptTouchEvent(false)
            }
        }
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        handleDispatchTouchEvent(ev)
        return super.dispatchTouchEvent(ev)
    }

    /**
     * 防止子布局[ViewPager2]调用[requestDisallowInterceptTouchEvent]导致接收不到[MotionEvent.ACTION_UP]
     * 或[MotionEvent.ACTION_CANCEL]
     */
    private fun handleDispatchTouchEvent(ev: MotionEvent?) {
        val vp2 = mViewPager2 ?: return
        val adapter = vp2.adapter ?: return

        when (ev?.action) {
            MotionEvent.ACTION_DOWN, MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                mOnEventListener?.onEvent(ev, vp2, adapter, false)
            }
        }
    }

    private fun onHorizontalActionMove(
        vp2: ViewPager2,
        adapter: RecyclerView.Adapter<*>,
        endX: Int,
        disX: Int,
        disY: Int
    ): Boolean {
        return if (disX >= disY) {
            vp2.currentItem > 0 || endX - startX < 0
        } else {
            false
        }
    }

    private fun onVerticalActionMove(
        vp2: ViewPager2,
        adapter: RecyclerView.Adapter<*>,
        endY: Int,
        disX: Int,
        disY: Int
    ): Boolean {
        return if (disY >= disX) {
            vp2.currentItem > 0 || endY - startY < 0
        } else {
            false
        }
    }

    fun setOnEventListener(owner: LifecycleOwner, onEventListener: OnEventListener) {
        owner.observeOnMainThreadWhenNotDestroyed(
            register = {
                setOnEventListener(onEventListener)
            },
            unregister = {
                unsetOnEventListener()
            }
        )
    }

    fun setOnEventListener(onEventListener: OnEventListener) {
        mOnEventListener = onEventListener
    }

    fun unsetOnEventListener() {
        mOnEventListener = null
    }
}