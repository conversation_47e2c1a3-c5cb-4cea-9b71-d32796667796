package com.socialplay.gpark.ui.im.conversation

import android.content.Context
import androidx.core.app.NotificationManagerCompat
import androidx.core.net.toUri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ly123.metacloud.ext.CustomMessageType
import com.ly123.tes.mgs.metacloud.IResultListener
import com.ly123.tes.mgs.metacloud.ISendMediaMessageListener
import com.ly123.tes.mgs.metacloud.ISendSystemMessageListener
import com.ly123.tes.mgs.metacloud.ISendTextMessageListener
import com.ly123.tes.mgs.metacloud.MetaCloud
import com.ly123.tes.mgs.metacloud.message.ImageMessage
import com.ly123.tes.mgs.metacloud.message.InformationMultiTypeMessage
import com.ly123.tes.mgs.metacloud.message.OverseaPgcGameCardMessage
import com.ly123.tes.mgs.metacloud.message.OverseaUgcGameCardMessage
import com.ly123.tes.mgs.metacloud.message.PostCardMessage
import com.ly123.tes.mgs.metacloud.message.ProfileCardMessage
import com.ly123.tes.mgs.metacloud.message.TextMessage
import com.ly123.tes.mgs.metacloud.message.UgcDesignCardMessage
import com.ly123.tes.mgs.metacloud.message.VideoFeedCardMessage
import com.ly123.tes.mgs.metacloud.model.Conversation
import com.ly123.tes.mgs.metacloud.origin.GroupMemberInfo
import com.ly123.tes.mgs.metacloud.model.Message
import com.ly123.tes.mgs.metacloud.model.SentMessageErrorCode
import com.ly123.tes.mgs.metacloud.model.UserInfo
import com.ly123.tes.mgs.metacloud.origin.GroupAtInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BanBlockInteractor
import com.socialplay.gpark.data.interactor.ImInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.LoadStatus
import com.socialplay.gpark.data.model.LoadType
import com.socialplay.gpark.data.model.editor.UgcDetailInfo
import com.socialplay.gpark.data.model.event.OnReceiveMessageProgressEvent
import com.socialplay.gpark.data.model.im.LocalMessageInfo
import com.socialplay.gpark.data.model.outfit.UgcDesignDetail
import com.socialplay.gpark.data.model.post.PostShareDetail
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.request.AddPvRequest
import com.socialplay.gpark.data.model.profile.UserProfileInfo
import com.socialplay.gpark.data.model.share.ShareContent
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.data.model.videofeed.VideoFeedItem
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.im.conversation.provider.InfoNotificationMuLtiTypeMsgItemProvider
import com.socialplay.gpark.ui.notice.NotificationPermissionManager
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.SingleLiveData
import com.socialplay.gpark.util.ToastUtil
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.io.File

class ChatViewModel(
    val metaRepository: IMetaRepository,
    private val banBlockInteractor: BanBlockInteractor,
    val metaKV: MetaKV,
    val application: Context
) : ViewModel() {
    private val accountInteractor by lazy { GlobalContext.get().get<AccountInteractor>() }
    private val imInteractor by lazy { GlobalContext.get().get<ImInteractor>() }
    private val _localMessageLiveData by lazy { MutableLiveData<Pair<LocalMessageInfo?, List<Message?>?>?>() }
    val localMessageLiveData: LiveData<Pair<LocalMessageInfo?, List<Message?>?>?> =
        _localMessageLiveData
    private val _remoteLiveData by lazy { MutableLiveData<Pair<LoadStatus?, List<Message?>?>?>() }
    val remoteLiveData = _remoteLiveData
    private val _lastMentionedLiveData by lazy { MutableLiveData<List<Message?>?>() }
    val lastMentionLiveData = _lastMentionedLiveData
    private val _onDeleteMessageLiveData by lazy { MutableLiveData<Message?>() }
    val onDeleteMessageLiveData = _onDeleteMessageLiveData
    private val _sendMessageResultLiveData by lazy { SingleLiveData<Boolean>() }
    val sendMessageResultLiveData = _sendMessageResultLiveData
    var blocked: Boolean = false
    private var haveTipNotification = false

    private val _showIMNotificationLiveData = MutableStateFlow<Boolean>(false)
    private val _openNotification = MutableStateFlow<Boolean?>(null)
    val openNotification = _openNotification
    private val _unReadCountFlow = MutableStateFlow<Int?>(null)
    val unReadCountFlow = _unReadCountFlow
    private val _firstSend = MutableStateFlow<Boolean>(false)

    private lateinit var targetUUID: String

    val showIMNotificationRecord =
        combine(_showIMNotificationLiveData, _unReadCountFlow, _firstSend) { a, b, c ->
            Timber.d("_showIMNotificationLiveData- %s %s %s", a, b, c)
            if (a && ((b ?: 0) > 0)) {
                Timber.d("_showIMNotificationLiveData-true")
                //当前周第一次收到消息
                NotificationPermissionManager.updateImPermissionTime()
                sendNotification(targetUUID)
                true
            } else if (a && c) {
                //当周第一次发送消息
                NotificationPermissionManager.updateImPermissionTime()
                sendNotification(targetUUID)
                Timber.d("_showIMNotificationLiveData-true")
                true
            } else {
                Timber.d("_showIMNotificationLiveData-false")
                false
            }
        }

    init {
        checkImNotification()
    }

    private fun checkImNotification() = viewModelScope.launch {
        _showIMNotificationLiveData.value =
            NotificationPermissionManager.imNeedPermission(application)
    }

    /**
     *清除未读消息
     */
    fun clearMessageUnReadStatus(type: Conversation.ConversationType, targetId: String) =
        viewModelScope.launch {
            imInteractor.clearMessageUnReadStatus(type, targetId)
        }

    /**
     *同步未读消息
     */
    fun syncConversationReadStatus(
        type: Conversation.ConversationType,
        targetId: String,
        timestamp: Long
    ) {
        MetaCloud.syncConversationReadStatus(type, targetId, timestamp) {
        }
    }

    /**
     * 发送文本消息
     */
    fun sendTextMessageWithRiskReview(
        targetId: String,
        conversationType: Conversation.ConversationType,
        text: String,
        atUserIdList: List<String> = emptyList(),
        groupAtInfoList: List<GroupAtInfo> = emptyList(),
        groupSenderInfo: GroupMemberInfo? = null,
    ) = viewModelScope.launch {
        // 群聊也用私聊的数美审核
        metaRepository.reviewPrivateMessageRisk(content = text, null).collect {
            if (!it.succeeded || it.data?.checkPass() != true) {
                _sendMessageResultLiveData.postValue(false)
            } else {
                _sendMessageResultLiveData.postValue(true)
                sendText(
                    targetId,
                    text,
                    conversationType,
                    atUserIdList,
                    groupAtInfoList,
                    groupSenderInfo
                )
            }
        }
    }


    private fun sendText(
        targetId: String,
        text: String,
        conversationType: Conversation.ConversationType,
        atUserIdList: List<String> = emptyList(),
        groupAtInfoList: List<GroupAtInfo> = emptyList(),
        groupSenderInfo: GroupMemberInfo? = null,
    ) {
        val currentUserInfo = accountInteractor.accountLiveData.value ?: return
        val textMessage = Message()
        textMessage.messageType = Message.MessageType.TXT
        textMessage.conversationType = conversationType
        textMessage.targetId = targetId
        textMessage.content = TextMessage().apply {
            content = text
            userInfo = UserInfo(
                currentUserInfo.uuid,
                currentUserInfo.nickname,
                currentUserInfo.portrait
            )
        }
        textMessage.atUserIdList = atUserIdList
        textMessage.groupAtInfoList = groupAtInfoList
        textMessage.groupSenderInfo = groupSenderInfo
        MetaCloud.sendMessage(
            textMessage,
            "",
            object : ISendTextMessageListener {
            override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                EventBus.getDefault().postSticky(imMessage)
                Timber.d("metaCloud  onError imMessage: %s  errorCode:%s", imMessage, errorCode)
            }

            override fun onSuccess(imMessage: Message) {
                Timber.d("metaCloud  onSuccess  %s", imMessage)
                EventBus.getDefault().postSticky(imMessage)
                if (!_firstSend.value) {
                    viewModelScope.launch {
                        _firstSend.emit(true)
                    }
                }
            }

        })
    }

    /**
     * 系统消息
     */
    fun sendSystem(
        targetId: String,
        uuid: String,
        conversationType: Conversation.ConversationType,
        systemStr: String
    ) {
        MetaCloud.insertIncomingMessage(targetId, uuid, conversationType, systemStr, object :
            ISendSystemMessageListener {
            override fun onError(errorCode: Int, desc: String?) {
                Timber.d("metaCloud %s", errorCode)
            }

            override fun onSuccess(imMessage: Message) {
                EventBus.getDefault().postSticky(imMessage)
            }

        })
    }

    private fun sendNotification(targetId: String) = viewModelScope.launch {
        _showIMNotificationLiveData.value = false
        _firstSend.value = false
        Analytics.track(EventConstants.EVENT_CHAT_PUSH_POST_SHOW)
        accountInteractor.accountLiveData.value?.uuid?.let {
            val message = InformationMultiTypeMessage.InformationMultiType(
                InfoNotificationMuLtiTypeMsgItemProvider.TYPE_IM_NOTIFICATION,
                application.getString(R.string.notification_im_title)
            )
            MetaCloud.insertLocalInformationMultiTypeMessage(
                targetId,
                it,
                Conversation.ConversationType.PRIVATE,
                message,
                object : ISendSystemMessageListener {
                    override fun onError(errorCode: Int, desc: String?) {

                    }

                    override fun onSuccess(imMessage: Message) {
                        (imMessage.content as InformationMultiTypeMessage).show = true
                        EventBus.getDefault().postSticky(imMessage)
                    }

                }
            )
        }
    }

    /**
     * 获取远程消息
     */
    fun getRemoteHistoryMessages(
        conversationType: Conversation.ConversationType,
        targetId: String,
        message: Message?,
        reqCount: Int
    ) {
        MetaCloud.getRemoteHistoryMessages(targetId, conversationType, message, reqCount, object :
            IResultListener {
            override fun onError(errorMsg: String?) {
                _remoteLiveData.value = LoadStatus(status = LoadType.Fail) to null
            }

            override fun onSuccess(messages: List<Message?>?) {

                _remoteLiveData.value = LoadStatus(
                    updateSize = reqCount,
                    status = LoadType.LoadMore
                ) to messages
            }

        })
    }

    fun getHistoryMessage(
        conversationType: Conversation.ConversationType,
        targetId: String,
        localMessageInfo: LocalMessageInfo
    ) {
        MetaCloud.getRemoteHistoryMessages(
            targetId,
            conversationType,
            localMessageInfo.eldestMessage,
            localMessageInfo.reqCount,
            object :
                IResultListener {
                override fun onError(errorMsg: String?) {
                    localMessageInfo.isSuccess = false
                    _localMessageLiveData.value = localMessageInfo to null
                }

                override fun onSuccess(messages: List<Message?>?) {
                    val newList = filterNotificationMessage(messages, localMessageInfo)
                    localMessageInfo.isSuccess = true
                    Timber.d("getHistoryMessage" + messages?.size + "  newList" + newList.size)
                    _localMessageLiveData.value = localMessageInfo to messages
                }
            })
    }

    /**
     * 过滤出系统通知消息，只展示最后一条
     */
    fun filterNotificationMessage(
        messages: List<Message?>?,
        localMessageInfo: LocalMessageInfo
    ): ArrayList<Message> {
        val list = messages?.filterNotNull() ?: emptyList()
        val newList = ArrayList<Message>()
        newList.addAll(list)
        if (localMessageInfo.eldestMessage == null) {
            //刷新数据时，提示也需要更新
            haveTipNotification = false
        }
        if (PandoraToggle.isChatPushNotification) {
            val removeidList = ArrayList<String>()
            for (i in newList.lastIndex.downTo(0)) {
                val item = newList[i]
                if (item.content is InformationMultiTypeMessage) {
                    val data = (item.content as InformationMultiTypeMessage)
                    val result =
                        GsonUtil.gsonSafeParse<InformationMultiTypeMessage.InformationMultiType>(
                            data.data
                        )
                    if (result?.type.equals(InfoNotificationMuLtiTypeMsgItemProvider.TYPE_IM_NOTIFICATION)) {
                        if (haveTipNotification) {
                            //已经有展示的了
                            data.show = false
                            removeidList.add(item.messageId)
                        } else {
                            haveTipNotification = true
                            data.show = true
                        }
                    }
                }
            }
            deleteMessageList(targetUUID, removeidList)
        }
        return newList
    }

    private fun getUnreadCountImpl(conversationType: Conversation.ConversationType, targetId: String) =
        viewModelScope.launch {
            MetaCloud.getUnReadCount(conversationType, targetId) {
                viewModelScope.launch {
                    _unReadCountFlow.emit(it)
                }
                clearMessageUnReadStatus(conversationType, targetId)
            }
        }

    fun sendReadReceiptMessage(conversationType: Conversation.ConversationType, targetId: String) {
        MetaCloud.sendReadReceiptMessage(conversationType, targetId)
    }

    private fun deleteMessageList(targetId: String, list: List<String>) {
        val removeList = ArrayList<String>()
        removeList.addAll(list)
        GlobalScope.launch {
            removeList.forEach {
                MetaCloud.deleteMessages(targetId, it, null)
            }
        }
    }

    fun getUnreadCount(targetId: String, conversationType: Conversation.ConversationType) =
        viewModelScope.launch {
            targetUUID = targetId
            getUnreadCountImpl(conversationType, targetId)
        }

    /**
     * 清楚缓存信息
     */
    fun cleanAllMessage() {
        if (_localMessageLiveData.value?.first != null) {
            val first: LocalMessageInfo? = _localMessageLiveData.value?.first
            first?.isSuccess = true
            first?.isClean = true
            _localMessageLiveData.value = first to null
        }
        if (_remoteLiveData.value?.first != null) {
            _remoteLiveData.value = _remoteLiveData.value?.first to null
        }
        if (_onDeleteMessageLiveData.value != null) {
            _onDeleteMessageLiveData.value = null
        }
    }

    override fun onCleared() {
        super.onCleared()
    }

    /**
     *删除消息
     */
    fun deleteMessages(data: Message) {
        MetaCloud.deleteMessages(data.targetId, data.messageId) {
            _onDeleteMessageLiveData.postValue(data)
            data.messageId = "0"
            onResendItemClick(data)
        }
    }

    /**
     * 消息重发
     */
    private fun onResendItemClick(message: Message) {
        if (message.content is ImageMessage) {
            val imageMessage = message.content as ImageMessage
            if (imageMessage.remoteUri != null && !imageMessage.remoteUri.toString()
                    .startsWith("file")
            ) {
                MetaCloud.sendMessage(message, "", object : ISendTextMessageListener {
                    override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                        EventBus.getDefault().postSticky(imMessage)
                    }

                    override fun onSuccess(imMessage: Message) {
                        EventBus.getDefault().postSticky(imMessage)
                    }

                })
            } else {
                MetaCloud.sendImageMessage(message, object : ISendMediaMessageListener {
                    override fun onSend(message: Message?, progress: Int) {
                        Timber.d("metacloud image onSend  %s %s", message, progress)
                        val result =
                            OnReceiveMessageProgressEvent()
                        result.message = message
                        result.progress = progress
                        EventBus.getDefault().postSticky(result)

                    }

                    override fun onSendError(
                        message: Message?,
                        messageErrorCode: SentMessageErrorCode?,
                        desc: String
                    ) {
                        EventBus.getDefault().postSticky(message)
                    }

                    override fun onSendUpdate(message: Message?) {
                        EventBus.getDefault().postSticky(message)
                        Timber.d("metacloud image onSendUpdate  %s ", message)
                    }

                    override fun onStartSend(message: Message?) {
                        EventBus.getDefault().postSticky(message)
                    }

                    override fun onSuccess(message: Message) {
                        EventBus.getDefault().postSticky(message)
                        Timber.d("metacloud image onSuccess  %s ", message)
                    }
                })
            }
        } else {
            MetaCloud.sendMessage(message, "", object : ISendTextMessageListener {

                override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                    EventBus.getDefault().postSticky(imMessage)
                }

                override fun onSuccess(imMessage: Message) {
                    EventBus.getDefault().postSticky(imMessage)
                }

            })
        }
    }

    fun isSelfSendMessage(senderUserId: String?): Boolean {
        return accountInteractor.accountLiveData.value?.uuid.equals(senderUserId)
    }

    fun checkBlockRelation(otherUuid: String) = viewModelScope.launch {
        blocked = banBlockInteractor.checkBeingBlocked(otherUuid)
    }

    fun getUserInfo(): UserInfo {
        return UserInfo(
            accountInteractor.accountLiveData.value?.uuid,
            accountInteractor.accountLiveData.value?.nickname,
            accountInteractor.accountLiveData.value?.portrait
        )
    }

    fun quickSharing(shareContent: String?, targetId: String) {
        shareContent ?: return
        val shareData = GsonUtil.gsonSafeParse<ShareContent>(shareContent)
        when (shareData?.type) {
            ShareContent.TYPE_TOPIC -> {
                val postTag = GsonUtil.gsonSafeParse<PostTag>(shareData.content) ?: return
                MetaCloud.sendCustomMessage(
                    targetId,
                    Conversation.ConversationType.PRIVATE,
                    GsonUtil.safeToJson(postTag, ""),
                    CustomMessageType.CUSTOM_SHARE_TOPIC,
                    null,
                    object : ISendTextMessageListener {
                        override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                            Timber.d(
                                "metaCloud  onError imMessage: %s  errorCode:%s",
                                imMessage,
                                errorCode
                            )
                            EventBus.getDefault().postSticky(imMessage)
                        }

                        override fun onSuccess(imMessage: Message) {
                            EventBus.getDefault().postSticky(imMessage)
                            ToastUtil.showShort(R.string.share_successfully)
                            Timber.d("metaCloud  onSuccess imMessage: %s  ", imMessage)
                        }

                    })

            }

            ShareContent.TYPE_POST -> {
                val post = GsonUtil.gsonSafeParse<PostShareDetail>(shareData.content) ?: return
                viewModelScope.launch {
                    metaRepository.commonAddPvCount(
                        AddPvRequest.RES_TYPE_POST_SHARE,
                        post.postId
                    ).collect {}
                }
                MetaCloud.sendPostCardMessage(
                    targetId,
                    Conversation.ConversationType.PRIVATE,
                    PostCardMessage.PostInfo(post.postId, null, post.image, post.content),
                    "",
                    object :
                        ISendTextMessageListener {
                        override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                            Timber.d(
                                "metaCloud  onError imMessage: %s  errorCode:%s",
                                imMessage,
                                errorCode
                            )
                            EventBus.getDefault().postSticky(imMessage)
                        }

                        override fun onSuccess(imMessage: Message) {
                            EventBus.getDefault().postSticky(imMessage)
                            ToastUtil.showShort(R.string.share_successfully)
                            Timber.d("metaCloud  onSuccess imMessage: %s  ", imMessage)
                        }

                    })
            }

            ShareContent.TYPE_IMAGE -> {
                val image = shareData.content
                MetaCloud.sendImages(
                    Conversation.ConversationType.PRIVATE,
                    targetId,
                    arrayListOf(File(image).toUri()),
                    true,
                    getUserInfo(),
                    "",
                    object : ISendMediaMessageListener {
                        override fun onSend(message: Message?, progress: Int) {
                            message ?: return
                            Timber.d("Chat image onSend %s %s", message, progress)
                            val result = OnReceiveMessageProgressEvent()
                            result.message = message
                            result.progress = progress
                            EventBus.getDefault().postSticky(result)
                        }

                        override fun onSendError(
                            message: Message?,
                            messageErrorCode: SentMessageErrorCode?,
                            desc: String
                        ) {
                            message ?: return
                            Timber.d(
                                "Chat image onSendError %s %s %s",
                                message,
                                messageErrorCode,
                                desc
                            )
                            EventBus.getDefault().postSticky(message)
                        }

                        override fun onSendUpdate(message: Message?) {
                            message ?: return
                            Timber.d("Chat image onSendUpdate %s", message)
                            EventBus.getDefault().postSticky(message)
                        }

                        override fun onStartSend(message: Message?) {
                            message ?: return
                            Timber.d("Chat image onStartSend %s", message)
                            MetaCloud.sendTypingStatus(
                                Conversation.ConversationType.PRIVATE,
                                targetId,
                                Message.MessageType.IMAGE
                            )
                            EventBus.getDefault().postSticky(message)
                        }

                        override fun onSuccess(message: Message) {
                            Timber.d("Chat image onSuccess %s", message)
                            ToastUtil.showShort(R.string.share_successfully)
                            EventBus.getDefault().postSticky(message)
                        }
                    }
                )
            }

            ShareContent.TYPE_PROFILE -> {
                val profile = GsonUtil.gsonSafeParse<UserProfileInfo>(shareData.content) ?: return
                MetaCloud.sendProfileCardMessage(
                    targetId,
                    Conversation.ConversationType.PRIVATE,
                    ProfileCardMessage.ProfileCardInfo(
                        uid = profile.uid,
                        userNumber = profile.userNumber,
                        nickname = profile.nickname,
                        gender = profile.gender,
                        birth = profile.birth,
                        city = profile.city,
                        portrait = profile.portrait,
                        signature = profile.signature,
                        commentTotal = profile.commentTotal,
                        friendTotal = profile.friendTotal,
                        followCount = profile.followCount,
                        fansCount = profile.fansCount,
                        likeCount = profile.likeCount
                    ),
                    "",
                    object : ISendTextMessageListener {
                        override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                            Timber.d(
                                "metaCloud  onError imMessage: %s  errorCode:%s",
                                imMessage,
                                errorCode
                            )
                            EventBus.getDefault().postSticky(imMessage)
                        }

                        override fun onSuccess(imMessage: Message) {
                            EventBus.getDefault().postSticky(imMessage)
                            ToastUtil.showShort(R.string.share_successfully)
                            Timber.d("metaCloud  onSuccess imMessage: %s  ", imMessage)
                        }
                    }
                )
            }

            ShareContent.TYPE_PGC_DETAIL -> {
                val pgc = GsonUtil.gsonSafeParse<ShareRawData.Game>(shareData.content) ?: return
                MetaCloud.sendOverseaPgcGameCardMessage(
                    targetId,
                    Conversation.ConversationType.PRIVATE,
                    OverseaPgcGameCardMessage.PgcCardInfo(
                        id = pgc.id,
                        name = pgc.name,
                        icon = pgc.icon,
                        description = pgc.description,
                        pkg = pgc.pkg,
                        shareCount = pgc.shareCount,
                        authorId = pgc.authorId,
                        authorName = pgc.authorName,
                        authorAvatar = pgc.authorAvatar,
                        avgScore = pgc.avgScore,
                        playerCount = pgc.playerCount,
                        likeCount = pgc.likeCount,
                        isBannerHor = pgc.isBannerHor
                    ),
                    "",
                    object : ISendTextMessageListener {
                        override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                            Timber.d(
                                "metaCloud  onError imMessage: %s  errorCode:%s",
                                imMessage,
                                errorCode
                            )
                            EventBus.getDefault().postSticky(imMessage)
                        }

                        override fun onSuccess(imMessage: Message) {
                            EventBus.getDefault().postSticky(imMessage)
                            ToastUtil.showShort(R.string.share_successfully)
                            Timber.d("metaCloud  onSuccess imMessage: %s  ", imMessage)
                        }
                    }
                )
            }

            ShareContent.TYPE_UGC_DETAIL -> {
                val ugc = GsonUtil.gsonSafeParse<UgcDetailInfo>(shareData.content) ?: return
                MetaCloud.sendOverseaUgcGameCardMessage(
                    targetId,
                    Conversation.ConversationType.PRIVATE,
                    OverseaUgcGameCardMessage.UgcCardInfo(
                        id = ugc.id,
                        packageName = ugc.packageName,
                        ugcGameName = ugc.ugcGameName,
                        gameCode = ugc.availableGameCode,
                        banner = ugc.banner,
                        ugcGameDesc = ugc.ugcGameDesc,
                        userName = ugc.userName,
                        userUuid = ugc.userUuid,
                        userIcon = ugc.userIcon,
                        userReleaseCount = ugc.userReleaseCount,
                        loveQuantity = ugc.loveQuantity,
                        pageView = ugc.pageView,
                        parentIcon = ugc.parentIcon,
                        shareCount = ugc.shareCount
                    ),
                    "",
                    object : ISendTextMessageListener {
                        override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                            Timber.d(
                                "metaCloud  onError imMessage: %s  errorCode:%s",
                                imMessage,
                                errorCode
                            )
                            EventBus.getDefault().postSticky(imMessage)
                        }

                        override fun onSuccess(imMessage: Message) {
                            EventBus.getDefault().postSticky(imMessage)
                            ToastUtil.showShort(R.string.share_successfully)
                            Timber.d("metaCloud  onSuccess imMessage: %s  ", imMessage)
                        }
                    }
                )
            }

            ShareContent.TYPE_VIDEO_FEED -> {
                val videoFeed = GsonUtil.gsonSafeParse<VideoFeedItem>(shareData.content) ?: return
                MetaCloud.sendVideoFeedCardMessage(
                    targetId,
                    Conversation.ConversationType.PRIVATE,
                    VideoFeedCardMessage.VideoFeedCardInfo(
                        videoId = videoFeed.videoId,
                        videoUrl = videoFeed.videoUrl,
                        videoCover = videoFeed.videoCover,
                        videoLikeCount = videoFeed.videoLikeCount,
                        videoCommentCount = videoFeed.videoCommentCount,
                        videoContent = videoFeed.videoContent,
                        videoWidth = videoFeed.videoWidth,
                        videoHeight = videoFeed.videoHeight,
                        videoShareCount = videoFeed.shareCount,
                        videoAuthorUuid = videoFeed.author.uuid,
                        videoAuthorName = videoFeed.author.name,
                        videoAuthorAvatar = videoFeed.author.avatar
                    ),
                    "",
                    object : ISendTextMessageListener {
                        override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                            Timber.d(
                                "metaCloud  onError imMessage: %s  errorCode:%s",
                                imMessage,
                                errorCode
                            )
                            EventBus.getDefault().postSticky(imMessage)
                        }

                        override fun onSuccess(imMessage: Message) {
                            EventBus.getDefault().postSticky(imMessage)
                            ToastUtil.showShort(R.string.share_successfully)
                            Timber.d("metaCloud  onSuccess imMessage: %s  ", imMessage)
                        }
                    }
                )
            }

            ShareContent.TYPE_UGC_DESIGN_DETAIL -> {
                val ugcDesignDetail =
                    GsonUtil.gsonSafeParseCollection<UgcDesignDetail>(shareData.content) ?: return
                MetaCloud.sendUgcDesignCardMessage(
                    targetId,
                    Conversation.ConversationType.PRIVATE,
                    UgcDesignCardMessage.UgcDesignInfo(
                        icon = ugcDesignDetail.cover,
                        title = ugcDesignDetail.title,
                        itemId = ugcDesignDetail.feedId
                    ),
                    "",
                    object : ISendTextMessageListener {
                        override fun onSuccess(imMessage: Message) {
                            EventBus.getDefault().postSticky(imMessage)
                            ToastUtil.showShort(R.string.share_successfully)
                            Timber.d("metaCloud  onSuccess imMessage: %s  ", imMessage)
                        }

                        override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                            Timber.d(
                                "metaCloud  onError imMessage: %s  errorCode:%s",
                                imMessage,
                                errorCode
                            )
                            EventBus.getDefault().postSticky(imMessage)
                        }
                    }
                )
            }
        }
    }

    fun checkNotification() = viewModelScope.launch {
        if (PandoraToggle.isChatPushNotification) {
            val enabled = NotificationManagerCompat.from(application).areNotificationsEnabled()
            NotificationPermissionManager.notificationOpen = enabled
            _openNotification.emit(enabled)
            Analytics.track(
                EventConstants.EVENT_CHAT_PUSH_POST_CLICK,
                map = mapOf("result" to if (enabled) "0" else "1")
            )
        }
    }
}
