package com.socialplay.gpark.ui.mgs.danmu.advanced

import android.graphics.Canvas
import com.bytedance.danmaku.render.engine.control.DanmakuConfig
import com.bytedance.danmaku.render.engine.data.DanmakuData
import com.bytedance.danmaku.render.engine.render.draw.DrawItem
import com.bytedance.danmaku.render.engine.render.draw.bitmap.BitmapData
import com.bytedance.danmaku.render.engine.render.draw.bitmap.BitmapDrawItem


/**
 * Created by dss886 on 2021/07/07.
 */

class DiggData : DanmakuData() {

    override var drawType: Int = DRAW_TYPE_ADVANCED

    var diggState: Int = DIGG_STATE_SELF_DIGG
    var diggIcon: BitmapData? = null
    var diggBgIcon: BitmapData? = null
    var diggText: LevelData? = null

}

class DiggDrawItem : DrawItem<DiggData>() {

    override var x: Float = 0F
        set(value) {
            field = value
            updateXY()
        }
    override var y: Float = 0F
        set(value) {
            field = value
            updateXY()
        }
    override var height: Float = 70F

    private val mDiggIconItem: BitmapDrawItem = BitmapDrawItem()
    private val mDiggCountItem: LevelDrawItem = LevelDrawItem()
    private var mDiggBgItem: BitmapDrawItem = BitmapDrawItem()


    private var mPaddingLeft = 0f
    private var mPaddingRight = 10f
    private var mCountMarginLeft = 7f

    override fun getDrawType(): Int {
        return DRAW_TYPE_ADVANCED
    }

    override fun onBindData(data: DiggData) {
        data.diggIcon?.let {
            mDiggIconItem.bindData(it)
        }
        data.diggText?.let {
            mDiggCountItem.bindData(it)
        }
        data.diggBgIcon?.let {
            mDiggBgItem.bindData(it)
        }
    }

    override fun onMeasure(config: DanmakuConfig) {
        mDiggIconItem.measure(config)
        mDiggCountItem.measure(config)
        mDiggBgItem.measure(config)
        height = mDiggBgItem.height
        width = mDiggBgItem.width + mPaddingLeft + mPaddingRight
    }

    override fun onDraw(canvas: Canvas, config: DanmakuConfig) {
        if (data != null) {
            mDiggIconItem.draw(canvas, config)
            if (data?.diggBgIcon?.bitmap != null) {
                mDiggBgItem.draw(canvas, config)
            }
            mDiggCountItem.draw(canvas, config)
        }
    }

    override fun recycle() {
        super.recycle()
        mDiggIconItem.recycle()
        mDiggCountItem.recycle()
        mDiggBgItem.recycle()
    }

    /**
     * Update the x/y of sub-item when the parent's x/y changed
     */
    private fun updateXY() {
        mDiggIconItem.x = x + (mDiggBgItem.width - mDiggIconItem.width) / 2
        mDiggIconItem.y = y + (height - mDiggIconItem.height) / 2

        mDiggCountItem.x = x + mDiggBgItem.width - mDiggCountItem.width - mCountMarginLeft
        mDiggCountItem.y = y + height - mDiggCountItem.height - mCountMarginLeft

        mDiggBgItem.x = x
        mDiggBgItem.y = y + (height - mDiggBgItem.height) / 2

    }

}