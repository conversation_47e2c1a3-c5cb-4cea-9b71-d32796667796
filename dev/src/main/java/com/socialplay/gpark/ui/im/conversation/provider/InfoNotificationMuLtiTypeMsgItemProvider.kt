package com.socialplay.gpark.ui.im.conversation.provider

import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ly123.tes.mgs.im.base.IContainerItemProvider.MessageProvider
import com.ly123.tes.mgs.im.base.MessageListAdapter
import com.ly123.tes.mgs.im.model.ProviderTag
import com.ly123.tes.mgs.im.model.UIMessage
import com.ly123.tes.mgs.metacloud.message.InformationMultiTypeMessage
import com.ly123.tes.mgs.metacloud.message.InformationNotificationMessage
import com.socialplay.gpark.R
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.notice.NotificationPermissionManager
import com.socialplay.gpark.ui.view.MetaTextView
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.visible

/**
 * author : yi.zhang
 * e-mail : <EMAIL>
 * time   : 2021/04/29
 * desc   : 系统消息
 */
@ProviderTag(
    messageContent = InformationMultiTypeMessage::class,
    showPortrait = false,
    showProgress = false,
    showWarning = false,
    centerInHorizontal = true,
    showSummaryWithName = false
)
class InfoNotificationMuLtiTypeMsgItemProvider :
    MessageProvider<InformationMultiTypeMessage?>() {
        companion object{
            const val TYPE_IM_NOTIFICATION= "app_notification"
        }

    override fun bindView(
        v: View,
        position: Int,
        content: InformationMultiTypeMessage?,
        message: UIMessage,
        messageClickListener: MessageListAdapter.OnMessageClickListener
    ) {
        val viewHolder = v.tag as ViewHolder
        viewHolder.rlNotification?.gone()
        viewHolder.rlAPPNotification?.gone()
        val data =
            GsonUtil.gsonSafeParse<InformationMultiTypeMessage.InformationMultiType>(content?.data)
            when (data?.type) {
                TYPE_IM_NOTIFICATION -> {
                    if (PandoraToggle.isChatPushNotification && !NotificationPermissionManager.notificationOpen && content?.show == true) {
                        viewHolder.rlNotification?.gone()
                        viewHolder.rlAPPNotification?.visible()
                        viewHolder.rlAPPNotification?.setOnClickListener {
                            messageClickListener.onFamilyPhotoInviteClick(
                                TYPE_IM_NOTIFICATION,
                                ""
                            )
                        }
                    } else {
                        viewHolder.rlNotification?.gone()
                        viewHolder.rlAPPNotification?.gone()
                    }
                }

                else                 -> {
                    viewHolder.rlNotification?.gone()
                    viewHolder.rlAPPNotification?.gone()
                }
            }

    }

    fun getContentSummary(data: InformationNotificationMessage?): Spannable? {
        return null
    }

    override fun getContentSummary(
        context: Context,
        data: InformationMultiTypeMessage?
    ): Spannable {
        return SpannableString("")
    }

    override fun onItemClick(
        view: View,
        position: Int,
        content: InformationMultiTypeMessage?,
        message: UIMessage,
        onMessageClickListener: MessageListAdapter.OnMessageClickListener
    ) {
    }

    override fun newView(context: Context, group: ViewGroup): View {
        val view = LayoutInflater.from(context)
            .inflate(R.layout.item_information_notification_mutitype_message, null as ViewGroup?)
        val viewHolder = ViewHolder()
        viewHolder.contentTextView = view.findViewById<View>(R.id.rc_msg) as TextView
        viewHolder.contentTextView!!.movementMethod = LinkMovementMethod.getInstance()
        viewHolder.rlNotification = view.findViewById(R.id.rl_notification)
        viewHolder.rlAPPNotification = view.findViewById(R.id.rl_app_notification)
        viewHolder.btnConfirm= view.findViewById(R.id.btnConfirm)
        view.tag = viewHolder
        return view
    }

    private class ViewHolder {
        var contentTextView: TextView? = null
        var rlNotification :RelativeLayout? = null
        var rlAPPNotification :ConstraintLayout? = null
        var btnConfirm :MetaTextView? = null
    }
}
