package com.socialplay.gpark.ui.post.feed.tag

import android.content.Context
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.View
import androidx.core.content.ContextCompat
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.PostTag

/**
 * 社区帖子正文可点击tag
 */
class CommunityTagSpannable(
    val context: Context,
    private val tagInfo: PostTag,
    val clickCallback: (tagClickCallback: PostTag) -> Unit
) : ClickableSpan() {
    override fun onClick(widget: View) {
        clickCallback.invoke(tagInfo)
    }

    override fun updateDrawState(ds: TextPaint) {
        ds.isUnderlineText = false
        ds.color = ContextCompat.getColor(context, R.color.color_4AB4FF)
    }
}