package com.socialplay.gpark.ui.main

import android.os.Bundle
import android.view.WindowManager
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.startup.GuideInfo
import com.socialplay.gpark.databinding.DialogGuideBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.BaseRecyclerViewDialogFragment
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.core.views.spacer
import com.socialplay.gpark.ui.dialog.DialogScene
import com.socialplay.gpark.ui.dialog.IDialogManager
import com.socialplay.gpark.util.extension.canShowDialog
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.property.viewBinding
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.getSharedViewModel
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import timber.log.Timber

class GuideDialog : BaseRecyclerViewDialogFragment(), IDialogManager {

    var onDismissCallback: ((Boolean) -> Unit)? = null
    override val binding by viewBinding(DialogGuideBinding::inflate)
    private val vm: GuideViewModel by fragmentViewModel()
    private val mainVM: MainViewModel by sharedViewModel()
    private val metaKV: MetaKV by inject()

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rv

    private var tempItem: GuideInfo? = null

    private val itemListener = object : IGuideListener {
        override fun click(item: GuideInfo) {
            Analytics.track(
                EventConstants.BUILD_NEWBIE_GUIDE_CREATE_THEME_CLICK,
                "type" to item.trackType
            )
            willNestDialog = true
            tempItem = item
            dismissAllowingStateLoss()
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    override fun epoxyController() = simpleController(vm, GuideState::guideConfigs) {
        if (it.isNullOrEmpty()) {
            loadMoreFooter(Loading(LoadMoreState()), idStr = "GuideLoading") {}
            spacer(1, dp(12), idStr = "GuideSpace")
        } else {
            it.forEachIndexed { index, guide ->
                guideCardItem(guide, index, itemListener)
            }
        }
    }

    override fun init() {
        Timber.d("WTF ${System.identityHashCode(this)}")
        recyclerView.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
        epoxyController.requestModelBuild()
    }

    override fun onDestroyView() {
        tempItem = null
        super.onDestroyView()
    }

    override fun dimAmount() = 0.6f
    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT
    override fun getStyle() = R.style.DialogStyleNonFullScreen
    override fun isCancelable() = false
    override fun isClickOutsideDismiss() = false
    override fun isBackPressedDismiss() = false
    override fun onBackPressed() = nestFading

    override fun afterNestFaded() {
        tempItem?.let { guide ->
            parentFragment?.let {
                if (it.canShowDialog) {
                    MainAddDialog.show(it, guide, onDismissCallback)
                }
            }
        }
        tempItem = null
    }

    override suspend fun initData(finishCallback: (Boolean) -> Unit) {
        finishCallback.invoke(true)
    }

    override fun needShow(fragment: Fragment, scene: DialogScene, args: Bundle?, needShowCallback: (Boolean) -> Unit) {
        val mainViewModel = fragment.getSharedViewModel<MainViewModel>()
        if (mainViewModel.buildMode == 1 && PandoraToggle.enableNewbieGuideCreateTheme) {
            needShowCallback(true)
        } else {
            needShowCallback(false)
        }
    }

    override fun showByDialogManager(fragment: Fragment, onDismissCallback: (Boolean) -> Unit) {
        this.onDismissCallback = onDismissCallback
        show(fragment.childFragmentManager, "GuideDialog")
        Analytics.track(EventConstants.BUILD_NEWBIE_GUIDE_CREATE_THEME_SHOW)
        metaKV.account.needUgcBuildModeGuide = false
    }

    override fun exeDismiss() {
        this.dismissAllowingStateLoss()
    }
}