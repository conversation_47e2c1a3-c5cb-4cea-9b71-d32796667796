package com.socialplay.gpark.ui.locale

import android.os.Bundle
import com.socialplay.gpark.databinding.ActivityWebBinding
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.property.viewBinding

/**
 * Created by bo.li
 * Date: 2024/4/22
 * Desc: 重启MainActivity，清空栈
 */
class LanguageRecreateActivity: BaseActivity() {
    override val binding by viewBinding(ActivityWebBinding::inflate)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        StatusBarUtil.setTransparent(this)
        MetaRouter.Main.restartMainActivity(this)
    }
}