package com.socialplay.gpark.ui.pay

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.pay.CoinsRecord
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.views.LoadMoreState
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get


data class TransactionDetailsState(
    val refreshFlag: Int = 0,
    val coinsRecordList: Async<List<CoinsRecord>> = Uninitialized,
    val loadMore: Async<LoadMoreState> = Uninitialized,
) : MavericksState

class TransactionDetailsViewModel(
    initialState: TransactionDetailsState,
    val metaRepository: IMetaRepository,
) : BaseViewModel<TransactionDetailsState>(initialState) {

    fun getList(refresh: Boolean) = withState { state ->
        if (state.coinsRecordList is Loading || state.loadMore is Loading) {
            return@withState
        }
        if (refresh) {
            setState {
                copy(
                    refreshFlag = state.refreshFlag + 1,
                    coinsRecordList = Loading()
                )
            }
        } else {
            setState {
                copy(loadMore = Loading())
            }
        }
        val dataList = state.coinsRecordList.invoke() ?: emptyList()
        val lastCoinsRecordId = if (refresh || dataList.isEmpty()) {
            null
        } else {
            dataList.last().id
        }
        viewModelScope.launch {
            val dataResult = metaRepository.getCoinsRecord(lastCoinsRecordId, 20)
            if (dataResult.succeeded) {
                val records = dataResult.data?.records ?: emptyList()
                setState {
                    copy(
                        coinsRecordList = Success(
                            if (refresh || dataList.isEmpty()) {
                                records
                            } else {
                                dataList + records
                            }
                        ),
                        loadMore = Success(LoadMoreState(isEnd = records.isEmpty()))
                    )
                }
            } else {
                if (refresh) {
                    setState {
                        copy(
                            coinsRecordList = Fail(
                                dataResult.exception ?: Exception("unknown exception")
                            )
                        )
                    }
                } else {
                    setState {
                        copy(
                            loadMore = Fail(
                                dataResult.exception ?: Exception("unknown exception")
                            )
                        )
                    }
                }
            }
        }
    }

    companion object :
        KoinViewModelFactory<TransactionDetailsViewModel, TransactionDetailsState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: TransactionDetailsState
        ): TransactionDetailsViewModel {
            return TransactionDetailsViewModel(state, get())
        }
    }
}