package com.socialplay.gpark.ui.editor.create.v3

import android.animation.ValueAnimator
import android.graphics.Color
import android.graphics.Point
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.view.marginTop
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.meta.lib.mwbiz.MWBiz
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.editor.FormworkList
import com.socialplay.gpark.databinding.FragmentEditorCreateV3TemplateBinding
import com.socialplay.gpark.databinding.HeaderUgcBuildViewBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.kernel.PandoraInit
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.apm.apiStart
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.editor.BaseEditorFragment
import com.socialplay.gpark.ui.editor.banner.UgcBannerAdapter
import com.socialplay.gpark.ui.editor.create.EditorCreateGuideDialog
import com.socialplay.gpark.ui.editor.create.EditorCreateV2FormworkAdapter
import com.socialplay.gpark.ui.editor.create.EditorCreateViewModel
import com.socialplay.gpark.util.UniJumpUtil
import com.socialplay.gpark.util.extension.addOnScrollListener
import com.socialplay.gpark.util.extension.apiMonitor
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getLocationOnScreenEx
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setOnAntiViolenceItemClickListener
import com.socialplay.gpark.util.extension.sharedViewModelFromParentFragment
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.youth.banner.listener.OnPageChangeListener
import com.zhpan.indicator.enums.IndicatorSlideMode
import com.zhpan.indicator.enums.IndicatorStyle
import kotlinx.coroutines.delay
import org.koin.android.ext.android.inject
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import kotlin.math.absoluteValue

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/04/06
 *     desc   :
 * </pre>
 */
class EditorCreateV3FormworkFragment : BaseEditorFragment<FragmentEditorCreateV3TemplateBinding>() {

    private val viewModel by sharedViewModelFromParentFragment<EditorCreateViewModel>()

    private val formworkAdapter by lazy { EditorCreateV3GameAdapter(Glide.with(this)) }
    private val bannerAdapter by lazy { UgcBannerAdapter(arrayListOf()) }
    private var bannerPosition = 1
    private val metaKV: MetaKV by inject()
    private var headerUgcBuildViewBinding: HeaderUgcBuildViewBinding? = null
    private var buildFormwork: FormworkList.Formwork? = null

    private var templatePosition = 0
    private val formworkListener by lazy {
        object : EditorCreateV2FormworkAdapter.FormworkListener {
            var showingGuide = false

            override fun onClickTemplate(listPos: Int, formwork: FormworkList.Formwork) {
                val context = context ?: return
                if (!MWBiz.isAvailable() && !PandoraInit.checkEsVersion(context)) {
                    toast(R.string.opengl_es_tips)
                } else {
                    templatePosition = listPos + 1
                    viewModel.getTemplateByGameCode(formwork, 2L)
                }
            }

            override fun onClickGame(
                listPos: Int,
                cardPos: Int,
                formwork: FormworkList.Formwork,
                game: FormworkList.FormworkGame
            ) {
                Analytics.track(
                    EventConstants.UGC_TEMPLATE_PAGE_LIST_CLICK,
                    "listtype" to formwork.formworkCode.orEmpty(),
                    "ugcid" to game.id.toString(),
                    "parentid" to (game.gameCode ?: formwork.gameCode.orEmpty())
                )
                val resIdBean = ResIdBean().setCategoryID(CategoryId.UGC_CRAFT_LAND_HOME)
                if (PandoraToggle.enableUgcDetail) {
                    MetaRouter.MobileEditor.ugcDetail(
                        this@EditorCreateV3FormworkFragment,
                        game.id.toString(),
                        game.gameCode,
                        resIdBean
                    )
                } else {
                    editorGameLaunchHelper?.startUgcGame(
                        this@EditorCreateV3FormworkFragment,
                        game.id.toString(),
                        game.packageName,
                        game.ugcGameName.orEmpty(),
                        game.gameCode,
                        resIdBean
                    )
                }
            }

            override fun onLoadMoreGame(listPos: Int, formwork: FormworkList.Formwork) {
                if (!binding.srl.isRefreshing) {
                    viewModel.loadMoreFormwork(listPos, formwork)
                }
            }

            override fun onItemShow(listPos: Int, item: FormworkList.Formwork, view: View) {
                if (listPos == 0 && isAdded && !showingGuide) {
                    showingGuide = true
                    viewLifecycleOwner.lifecycleScope.launchWhenResumed {
                        delay(10)
                        if (!metaKV.appKV.isUgcGuideShow1) {
                            showGuide1(item, view, listPos)
                        } else if (!metaKV.appKV.isUgcGuideShow2 && binding.srl.isAnimateFinished()) {
                            showGuide2(item, view, listPos)
                        }
                    }

                }
            }

            private fun showGuide1(
                item: FormworkList.Formwork,
                view: View,
                listPos: Int
            ) {
                metaKV.appKV.isUgcGuideShow1 = true
                EditorCreateGuideDialog.show(
                    childFragmentManager,
                    item,
                    true,
                    view.getLocationOnScreenEx()
                ) { status1 ->
                    when (status1) {
                        EditorCreateGuideDialog.CallbackStatus.NEXT -> {
                            showGuide2(item, view, listPos)
                        }

                        EditorCreateGuideDialog.CallbackStatus.BUILD -> {
                            onClickTemplate(listPos, item)
                        }

                        else -> {}
                    }
                }
            }

            private fun showGuide2(
                item: FormworkList.Formwork,
                view: View,
                listPos: Int
            ) {
                metaKV.appKV.isUgcGuideShow2 = true
                EditorCreateGuideDialog.show(
                    childFragmentManager,
                    item,
                    false,
                    Point(
                        view.getLocationOnScreenEx().x + view.width,
                        view.getLocationOnScreenEx().y
                    )
                ) { status2 ->
                    if (status2 == EditorCreateGuideDialog.CallbackStatus.UGC) {
                        item.gameList?.firstOrNull()?.let { it1 -> onClickGame(listPos, 0, item, it1) }
                    }
                }
            }
        }
    }

    private val listVerticalScroll = AtomicInteger(0)
    private val bottomBuildButtonControl = AtomicBoolean(true)
    private var valueAnimator: ValueAnimator? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.formworkV3LiveData.apiMonitor(this) {
            it != null
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentEditorCreateV3TemplateBinding? {
        return FragmentEditorCreateV3TemplateBinding.inflate(inflater, container, false)
    }

    override fun init() {
        super.init()
        initView()
        initData()
    }

    private fun initView() {
        binding.lv.showLoading(true, null, Color.WHITE)
        binding.srl.setOnRefreshListener {
            refresh()
        }
        binding.lv.setRetry {
            refresh()
        }
        initAdapter()
        initBuildButton()
    }

    private fun initData() {
        viewModel.formworkV3LiveData.observe(viewLifecycleOwner) {
            binding.srl.isRefreshing = false
            if (it != null) {
                binding.lv.hide()
                val gameList = it.games
                formworkAdapter.setList(gameList)
                buildFormwork = it.formwork?.apply {
                    notifyLoadMoreStatus(this)
                }
            } else {
                if (formworkAdapter.data.isEmpty()) {
                    binding.lv.showError()
                } else {
                    binding.lv.hide()
                    toast(R.string.common_failed)
                }
            }
        }
        viewModel.loadMoreCallback.observe(viewLifecycleOwner) { pos, formwork ->
//            adapter.notifyLoadMoreDone(pos, formwork)
            val gameList = formwork.gameList ?: listOf()
            formworkAdapter.setList(gameList)
            notifyLoadMoreStatus(formwork)
        }
        viewModel.templateCallback.observe(viewLifecycleOwner) {
            viewModel.needSwitchToMine = true
            viewModel.needRefreshMine = true
            Analytics.track(
                EventConstants.EVENT_UGC_CREATE_TEMPLATE_CLICK,
                "parent_type" to templatePosition.toString(),
                "gameidentity" to it.gameIdentity.orEmpty(),
                "gameid" to it.gid.orEmpty()
            )
            editorGameLaunchHelper?.startTemplateGame(
                this,
                it,
                ResIdBean.newInstance().setCategoryID(CategoryId.MOBILE_EDITOR_TEMPLATE)
            )
        }
        viewModel.errorToastLiveData.observe(viewLifecycleOwner) {
            // 报错
            toast(it ?: getString(R.string.common_failed))
        }
        viewModel.ugcBannerListLiveData.observe(viewLifecycleOwner) {
            // banner
            updateBanner(it.data)
        }
    }

    private fun notifyLoadMoreStatus(formwork: FormworkList.Formwork) {
        if (formwork.loading) {
            formworkAdapter.loadMoreModule.loadMoreToLoading()
        } else if (formwork.fail) {
            formworkAdapter.loadMoreModule.loadMoreFail()
        } else if (formwork.end) {
            formworkAdapter.loadMoreModule.loadMoreEnd(true)
        } else {
            formworkAdapter.loadMoreModule.loadMoreComplete()
        }
    }

    private fun initBuildButton() {
        val startBuilder = {
            Analytics.track(EventConstants.EVENT_UGC_CREATE_EDIT_START)
            buildFormwork?.also {
                formworkListener.onClickTemplate(0, it)
            }
        }
        val header = headerUgcBuildViewBinding ?: HeaderUgcBuildViewBinding.inflate(layoutInflater)
        formworkAdapter.setHeaderView(header.root)
        header.layoutBuildCard.setOnAntiViolenceClickListener {
            startBuilder.invoke()
        }
        headerUgcBuildViewBinding = header
        binding.layoutBuildCard.setOnAntiViolenceClickListener {
            startBuilder.invoke()
        }
        binding.rv.addOnScrollListener(viewLifecycleOwner,
            object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    val offset = listVerticalScroll.addAndGet(dy)
                    if (offset > 0) {
                        val bannerHeight = headerUgcBuildViewBinding?.cvBanner?.height ?: 0
                        val bannerOffset = if (bannerHeight > 0) {
                            bannerHeight + (headerUgcBuildViewBinding?.cvBanner?.marginTop ?: 0)
                        } else {
                            0
                        }
                        val headerHeight = recyclerView.dp(64) + bannerOffset
                        controlBottomBuildButton(offset > headerHeight)
                    }
                }
            })
    }

    private fun controlBottomBuildButton(isShow: Boolean) {
        if (bottomBuildButtonControl.compareAndSet(isShow, !isShow)) {
            valueAnimator?.cancel()
            val cy = binding.layoutBuildCardContainer.translationY
            valueAnimator = ValueAnimator.ofFloat(cy, if (isShow) -84.dp.toFloat() else 0F).apply {
                setDuration(300)
                addUpdateListener {
                    val valueY = it.animatedValue as Float
                    binding.layoutBuildCardContainer.translationY = valueY
                    binding.srl.updateLayoutParams<MarginLayoutParams> {
                        bottomMargin = valueY.toInt().absoluteValue
                    }
                }
                start()
            }
        }
    }

    private fun initAdapter() {
        formworkAdapter.setOnItemShowListener { item, position ->
            Analytics.track(
                EventConstants.UGC_TEMPLATE_PAGE_LIST_SHOW,
                "listtype" to buildFormwork?.formworkCode.orEmpty()
            )
            buildFormwork?.also { formwork ->
                headerUgcBuildViewBinding?.layoutBuildCard.let {
                    formworkListener.onItemShow(position, formwork, headerUgcBuildViewBinding?.layoutBuildCard!!)
                }
            }
        }
        formworkAdapter.setOnAntiViolenceItemClickListener { adapter, _, cardPos ->
            buildFormwork?.also { formwork ->
                formworkListener.onClickGame(
                    0, cardPos, formwork, adapter.getItem(cardPos)
                )
            }
        }
        formworkAdapter.removeAllHeaderView()

        binding.rv.layoutManager = GridLayoutManager(requireContext(), 2)
        binding.rv.adapter = formworkAdapter

//        formworkAdapter.loadMoreModule.apply {
//            isEnableLoadMore = true
////            loadMoreView = VerticalNoTextLoadMoreView()
//            setOnLoadMoreListener {
//                buildFormwork?.also { formwork ->
////                    formworkListener.onLoadMoreGame(0, formwork)
//                }
//            }
//        }
    }

    private fun updateBanner(list: List<UniJumpConfig>?) {
        if (list.isNullOrEmpty()) {
            headerUgcBuildViewBinding?.cvBanner?.gone()
        } else {
            headerUgcBuildViewBinding?.apply {
                cvBanner.visible()
                val size = list.size
                indicatorBanner.gone(size <= 1)
                indicatorBanner.apply {
                    setIndicatorStyle(IndicatorStyle.ROUND_RECT)
                    setSlideMode(IndicatorSlideMode.NORMAL)
                    setSliderWidth(dp(5).toFloat(), dp(10).toFloat())
                    setSliderHeight(dp(5).toFloat())
                    setSliderGap(dp(3).toFloat())
                    setPageSize(size)
                    notifyDataChanged()
                }
                bannerAdapter.updateData(list)
                if (bannerPosition !in 1..size) {
                    bannerPosition = 1
                }
                if (bannerPosition in 1..size) {
                    indicatorBanner.setCurrentPosition(bannerPosition - 1)
                    wb.startPosition = bannerPosition
                    list.getOrNull(bannerPosition - 1)?.let { data ->
                        Analytics.track(
                            EventConstants.UGC_MY_PAGE_BANNER_SHOW,
                            "name" to data.title.orEmpty(),
                            "operation_id" to data.id.orEmpty(),
                            "categoryid" to CategoryId.OPERATION_EDITOR_BANNER
                        )
                    }
                }
                if (wb.adapter == null) {
                    wb.setAdapter(bannerAdapter)
                }
                wb.isAutoLoop(size > 1)
                    .setLoopTime(3000L)
                    .addBannerLifecycleObserver(viewLifecycleOwner)
                    .removeIndicator()
                    .setOnBannerListener { data, _ ->
                        if (data !is UniJumpConfig) {
                            return@setOnBannerListener
                        }
                        Analytics.track(
                            EventConstants.UGC_MY_PAGE_BANNER_CLICK,
                            "name" to data.title.orEmpty(),
                            "operation_id" to data.id.orEmpty(),
                            "categoryid" to CategoryId.OPERATION_EDITOR_BANNER
                        )
                        UniJumpUtil.jump(
                            this@EditorCreateV3FormworkFragment,
                            data,
                            LinkData.SOURCE_EDITOR_CREATE,
                            CategoryId.OPERATION_EDITOR_BANNER,
                            mapOf("videoPlayAnalyticsFrom" to "editor_banner")
                        )
                    }
                    .addOnPageChangeListener(object : OnPageChangeListener {
                        override fun onPageScrolled(
                            position: Int,
                            positionOffset: Float,
                            positionOffsetPixels: Int
                        ) {
                            indicatorBanner.onPageScrolled(
                                position,
                                positionOffset,
                                positionOffsetPixels
                            )
                        }

                        override fun onPageSelected(position: Int) {
                            bannerPosition = position + 1
                            indicatorBanner.onPageSelected(position)
                            list.getOrNull(position)?.let { data ->
                                Analytics.track(
                                    EventConstants.UGC_MY_PAGE_BANNER_SHOW,
                                    "name" to data.title.orEmpty(),
                                    "operation_id" to data.id.orEmpty(),
                                    "categoryid" to CategoryId.OPERATION_EDITOR_BANNER
                                )
                            }
                        }

                        override fun onPageScrollStateChanged(state: Int) {
                            indicatorBanner.onPageScrollStateChanged(state)
                        }
                    })
            }
        }
    }

    fun refresh() {
        apiStart()
        viewModel.featSingleUgcModeData()
    }

    override fun loadFirstData() {
        refresh()
    }

    override fun onDestroyView() {
        binding.rv.adapter = null
        binding.rv.clearOnScrollListeners()
        headerUgcBuildViewBinding?.wb?.destroy()
        headerUgcBuildViewBinding = null
        headerUgcBuildViewBinding = null
        valueAnimator?.cancel()
        valueAnimator = null
        super.onDestroyView()
    }

    override fun getFragmentName() = PageNameConstants.FRAGMENT_NEW_CREATE_TEMPLATE

}