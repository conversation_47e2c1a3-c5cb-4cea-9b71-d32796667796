package com.socialplay.gpark.ui.gamedetail.unify

import android.content.DialogInterface
import android.os.Parcelable
import android.view.WindowManager
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogGameDetailMoreBinding
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.ui.share.GlobalShareArgs
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/12/24
 *     desc   :
 * </pre>
 */
@Parcelize
data class GameDetailMoreDialogArgs(
    val enableShare: Boolean
) : Parcelable

class GameDetailMoreDialog : BaseDialogFragment() {

    companion object {
        const val TAG = "GameDetailMoreDialog"

        const val KEY_FUNCTION = "function"

        const val FUNCTION_SHARE = 1
        const val FUNCTION_FEEDBACK = 2

        fun show(fragment: Fragment, enableShare: Boolean) {
            val dialog = GameDetailMoreDialog()
            dialog.arguments = GameDetailMoreDialogArgs(enableShare).asMavericksArgs()
            dialog.show(fragment.childFragmentManager, TAG)
        }
    }

    override var navColorRes = R.color.white

    override val binding by viewBinding(DialogGameDetailMoreBinding::inflate)
    private val args: GameDetailMoreDialogArgs by args()

    private var isClickOutside = true

    override fun init() {
        binding.tvShare.visible(args.enableShare)
        binding.root.setOnAntiViolenceClickListener {
            dismissAllowingStateLoss()
        }
        binding.tvCancel.setOnAntiViolenceClickListener {
            dismissAllowingStateLoss()
        }
        binding.tvShare.setOnAntiViolenceClickListener {
            setFragmentResult(TAG, bundleOf(KEY_FUNCTION to FUNCTION_SHARE))
            isClickOutside = false
            dismissAllowingStateLoss()
        }
        binding.tvFeedback.setOnAntiViolenceClickListener {
            setFragmentResult(TAG, bundleOf(KEY_FUNCTION to FUNCTION_FEEDBACK))
            isClickOutside = false
            dismissAllowingStateLoss()
        }
    }

    override fun dimAmount() = 0.6f

    override fun getStyle() = R.style.BottomSheetDialog_NavWhite

    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT
}