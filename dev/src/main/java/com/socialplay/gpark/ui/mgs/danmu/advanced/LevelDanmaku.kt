package com.socialplay.gpark.ui.mgs.danmu.advanced

import android.graphics.Canvas
import android.graphics.Paint
import com.bytedance.danmaku.render.engine.control.DanmakuConfig
import com.bytedance.danmaku.render.engine.data.DanmakuData
import com.bytedance.danmaku.render.engine.render.draw.DrawItem

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/02/20
 *     desc   :
 *
 */
class LevelData: DanmakuData() {

    override var drawType: Int = DRAW_TYPE_LEVE
    var LevelText: GradientTextData? = null
}
class LevelDrawItem : DrawItem<LevelData>() {

    override var x: Float = 0F
        set(value) {
            field = value
            updateXY()
        }
    override var y: Float = 0F
        set(value) {
            field = value
            updateXY()
        }
    override var height: Float = 32f


    private val mDiggCountItem: GradientTextDanmakuDrawItem = GradientTextDanmakuDrawItem()



    private val mDiggBgPaint = Paint(Paint.ANTI_ALIAS_FLAG or Paint.DITHER_FLAG)


    override fun getDrawType(): Int {
        return DRAW_TYPE_LEVE
    }

    override fun onBindData(data: LevelData) {
        data.LevelText?.let {
            mDiggCountItem.bindData(it)
        }
    }


    override fun onMeasure(config: DanmakuConfig) {
        mDiggCountItem.measure(config)
        height = mDiggCountItem.height
        width = mDiggCountItem.width
    }

    override fun onDraw(canvas: Canvas, config: DanmakuConfig) {
        if (data != null) {
            mDiggCountItem.draw(canvas, config)
        }
    }

    override fun recycle() {
        super.recycle()
        mDiggBgPaint.reset()
        mDiggCountItem.recycle()
    }


    /**
     * Update the x/y of sub-item when the parent's x/y changed
     */
    private fun updateXY() {
        mDiggCountItem.x = x
        mDiggCountItem.y = y
    }

}
