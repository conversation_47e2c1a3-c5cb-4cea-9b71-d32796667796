package com.socialplay.gpark.ui.gamedetail
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils

/**
 * Created by bo.li
 * Date: 2021/9/15
 * Desc:
 */
class GameDetailAnalyticsObserver {

    companion object {
        /**
         * 发送点击按钮埋点
         * 0：开始游戏
         * 1：下载
         * 4：cdn错误
         * 5：继续下载
         * 6：暂停下载
         * 7: 外部安装：没了
         * 8：下载更新
         * 9：继续下载更新
         * 10: 暂停下载更新
         * 11：安装更新
         */
        const val CLICK_TYPE_START_GAME = 0
        const val CLICK_TYPE_START_DOWNLOAD = 1
        const val CLICK_TYPE_CDN_WRONG = 4
        const val CLICK_TYPE_CONTINUE_DOWNLOAD = 5
        const val CLICK_TYPE_PAUSE_DOWNLOAD = 6
        const val CLICK_TYPE_START_DOWNLOAD_UPDATE = 8
        const val CLICK_TYPE_CONTINUE_DOWNLOAD_UPDATE = 9
        const val CLICK_TYPE_CONTINUE_PAUSE_UPDATE = 10
        const val CLICK_TYPE_CONTINUE_INSTALL_UPDATE = 11
        const val CLICK_TYPE_NOT_SUPPORT_VIRTUAL_DOWNLOAD = 12
        const val CLICK_TYPE_NOT_SUPPORT_VIRTUAL_UPDATE = 13
    }

    /**
     * 发送进入游戏次数埋点
     */
    fun sendEnterGameDetailAnalytic(
        gameid: String,
        packageName: String,
        enteredTimes: Long,
        type: String,
        resIdBean: ResIdBean,
        creatorType: Int? = null
    ) {
        Analytics.track(EventConstants.EVENT_APP_DETAIL) {
            putAll(ResIdUtils.getAnalyticsMap(resIdBean))
            put("gameid", gameid)
            put("packagename", packageName)
            put("enteredTimes", enteredTimes)
            put("game_type", type)
            if (creatorType != null) {
                put("creatortype", creatorType)
            }
        }
    }

    /**
     * 发送点击按钮埋点
     * 0：开始游戏
     * 1：下载
     * 4：cdn错误
     * 5：继续下载
     * 6：暂停下载
     * 7: 外部安装：没了
     * 8：下载更新
     * 9：继续下载更新
     * 10: 暂停下载更新
     * 11：安装更新
     */
    fun sendClickDownloadAnalytic(infoEntity: GameDetailInfo, clickType: Int, resIdBean: ResIdBean) {
        Analytics.track(EventConstants.EVENT_CLICK_DOWNLOAD) {
            putAll(ResIdUtils.getAnalyticsMap(resIdBean))
            put("gameid", infoEntity.id)
            put("clickType", clickType)
            put("packagename", infoEntity.packageName)
            put("game_type", infoEntity.typeToString())
        }
    }
}