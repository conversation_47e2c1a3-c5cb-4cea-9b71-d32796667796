package com.socialplay.gpark.ui.post

import android.content.Context
import com.socialplay.gpark.databinding.DialogPostMoreBinding
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding

/**
 * create by: bin on 2022/2/25
 */
class PostMoreDialog : BaseDialogFragment() {

    companion object{
        val CALLBACK_KEY_COPY = "copy"
        val CALLBACK_KEY_DELETE = "delete"
        val CALLBACK_KEY_CANCEL = "cancel"
        val CALLBACK_KEY_REPORT = "report"
    }

    var isSelf: Boolean = false
    var needCopy: Boolean = true
    var callback: (String) -> Unit = {}

    override val binding by viewBinding(DialogPostMoreBinding::inflate)

    override fun init() {
        binding.tvDelete.setOnClickListener {
            // delete
            callback.invoke(CALLBACK_KEY_DELETE)
            dismiss()
        }
        binding.tvCopy.setOnClickListener {
            // copy
            callback.invoke(CALLBACK_KEY_COPY)
            dismiss()
        }
        binding.tvCancel.setOnClickListener {
            // cancel
            callback.invoke(CALLBACK_KEY_CANCEL)
            dismiss()
        }
        binding.tvReport.setOnClickListener {
            callback.invoke(CALLBACK_KEY_REPORT)
            dismiss()
        }
        if (isSelf) {
            binding.tvReport.gone()
            binding.tvDelete.visible()
        }
        if (!needCopy){
            binding.tvCopy.gone()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        callback = {}
    }

    override fun marginHorizontal(context: Context): Int = 16.dp

    override fun loadFirstData() {
    }
}