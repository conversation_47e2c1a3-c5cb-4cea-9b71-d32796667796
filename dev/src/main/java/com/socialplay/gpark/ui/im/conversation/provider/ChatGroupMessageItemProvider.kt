package com.socialplay.gpark.ui.im.conversation.provider

import android.content.ClipboardManager
import android.content.Context
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.ly123.tes.mgs.im.base.IContainerItemProvider
import com.ly123.tes.mgs.im.base.MessageListAdapter
import com.ly123.tes.mgs.im.model.ProviderTag
import com.ly123.tes.mgs.im.model.UIMessage
import com.ly123.tes.mgs.metacloud.message.ChatGroupSystemMessage
import com.ly123.tes.mgs.metacloud.message.RecallNotificationMessage
import com.ly123.tes.mgs.metacloud.message.TextMessage
import com.socialplay.gpark.R
import com.socialplay.gpark.util.SpannableHelper


@ProviderTag(
    messageContent = ChatGroupSystemMessage::class,
    showReadState = false,
    showWarning = false,
    showSenderPortrait = false,
    showReceiverPortrait = false,
    showSenderName = false,
    showTime = false,
    showPortrait = false,
    showProgress = false,
    showSummaryWithName = false,
    centerInHorizontal = true
)
class ChatGroupMessageItemProvider : IContainerItemProvider.MessageProvider<ChatGroupSystemMessage>() {
    override fun newView(context: Context, group: ViewGroup): View {
        val view = LayoutInflater.from(context).inflate(R.layout.rc_chat_room_message, null as ViewGroup?)
        val holder = ViewHolder()
        holder.message = view.findViewById<TextView>(R.id.tv_message)
        view.tag = holder
        return view
    }


    override fun getContentSummary(context: Context?, data: ChatGroupSystemMessage?): Spannable? {
        return when {
            data == null -> {
                null
            }

            else -> {
                getShowContent(context, data)
            }
        }
    }

    fun getShowContent(context: Context?, data: ChatGroupSystemMessage): SpannableStringBuilder? {
        val defaultHintColor = R.color.color_B3B3B3
        val defaultNameColor = R.color.color_666666
        try {
            var sb: SpannableStringBuilder = when (data.msgType) {
                ChatGroupSystemMessage.TYPE_CREATE_GROUP -> {
                    SpannableHelper.Builder()
                        .text(context?.getString(R.string.group_msg_create_group))
                        .colorRes(defaultHintColor)
                        .build()
                }

                ChatGroupSystemMessage.TYPE_MEMBER_JOIN -> {
                    SpannableHelper.Builder()
                        .text(data.contentMsg?.joinUsers?.values?.joinToString(","))
                        .colorRes(defaultNameColor)
                        .text(" ")
                        .text(context?.getString(R.string.group_msg_member_join))
                        .colorRes(defaultHintColor)
                        .build()
                }

                ChatGroupSystemMessage.TYPE_MEMBER_LEFT -> {
                    SpannableHelper.Builder()
                        .text(context?.getString(R.string.group_msg_member_left, data.contentMsg?.leftUser?.second))
                        .colorRes(defaultHintColor)
                        .build()
                }

                ChatGroupSystemMessage.TYPE_MEMBER_REMOVE -> {
                    val text = context?.getString(R.string.group_msg_member_remove) ?: ""
                    val split = text.split("%s")
                    if (split.size >= 3) {
                        val builder = SpannableHelper.Builder()
                        if (split[0].isNotEmpty()) {
                            builder.text(split[0])
                                .colorRes(defaultHintColor)
                        }
                        builder.text(data.contentMsg?.removeUser?.second)
                            .colorRes(defaultNameColor)
                        if (split[1].isNotEmpty()) {
                            builder.text(split[1])
                                .colorRes(defaultHintColor)
                        }
                        builder.text(data.contentMsg?.manageUser?.second)
                            .colorRes(defaultNameColor)
                        if (split[2].isNotEmpty()) {
                            builder.text(split[2])
                                .colorRes(defaultHintColor)
                        }
                        builder.build()
                    } else {
                        SpannableHelper.Builder()
                            .text(text)
                            .colorRes(defaultHintColor)
                            .build()
                    }
                }

                ChatGroupSystemMessage.TYPE_NAME_CHANGE -> {
                    SpannableHelper.Builder()
                        .text(context?.getString(R.string.group_msg_name_change))
                        .colorRes(defaultHintColor)
                        .text(" ")
                        .text(data.contentMsg?.newName)
                        .colorRes(defaultNameColor)
                        .build()
                }

                ChatGroupSystemMessage.TYPE_COMMON_NTF -> {
                    SpannableHelper.Builder()
                        .text(data.contentMsg?.msgStr)
                        .colorRes(defaultHintColor)
                        .build()
                }

                else -> {
                    SpannableHelper.Builder()
                        .text(context?.getString(R.string.imrongyun_rc_message_unknown))
                        .colorRes(defaultHintColor)
                        .build()
                }
            }
            return sb
        } catch (e: Exception) {
            return null
        }
    }

    override fun onItemClick(view: View, position: Int, content: ChatGroupSystemMessage?, message: UIMessage, onMessageClickListener: MessageListAdapter.OnMessageClickListener?) {
        val holder = view.tag as ViewHolder
        if (content != null && content.isDestruct && message.message.readTime <= 0L) {
            this.processTextView(view, position, content, message, holder.message, onMessageClickListener)
        }

    }

    override fun bindView(v: View, position: Int, content: ChatGroupSystemMessage, data: UIMessage, messageClickListener: MessageListAdapter.OnMessageClickListener?) {
        val holder = v.tag as ViewHolder
        val textView = holder.message
        this.processTextView(v, position, content, data, textView, messageClickListener)
    }

    private fun processTextView(
        v: View,
        position: Int,
        content: ChatGroupSystemMessage,
        data: UIMessage,
        pTextView: TextView?,
        onMessageClickListener: MessageListAdapter.OnMessageClickListener?,
    ) {
        try {
            pTextView?.text = getShowContent(pTextView?.context, content)
//            //todo schema跳转
//            pTextView?.setOnLongClickListener {
//                popWindow(v.context, v, data, v, onMessageClickListener)
//                true
//            }
//            pTextView?.stripUnderlines()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    /*复制*/
    private fun copyMessage(context: Context, message: UIMessage) {
        val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        if (message.content is RecallNotificationMessage) {
        } else {
            if (message.content is ChatGroupSystemMessage) {
                clipboard.text = (message.content as TextMessage).content
            }
        }
    }

    private class ViewHolder {
        var message: TextView? = null
    }

}
