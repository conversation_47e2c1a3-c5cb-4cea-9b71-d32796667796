package com.socialplay.gpark.ui.editor.share

import android.graphics.Rect
import android.os.Bundle
import android.view.View
import android.view.View.MeasureSpec
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.meta.biz.ugc.model.AvatarSaveShareNoRemindersTodayMsg
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.editor.AvatarSaveShareDialogArgs
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.databinding.DialogAvatarShareBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.umw.UMW
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.view.PagerLinearLayoutManager
import com.socialplay.gpark.util.MaxPerAxisFlexboxLayoutManager
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.property.viewBinding
import timber.log.Timber

/**
 * 游戏内保存角色形象分享弹窗
 */
class AvatarSaveShareDialog : BaseDialogFragment() {

    override val binding by viewBinding(DialogAvatarShareBinding::inflate)

    companion object {
        fun show(
            fragmentManager: FragmentManager,
            images: List<String>,
            shareToPostNeedFinish: Boolean
        ) {
            val tag = AvatarSaveShareDialog::class.java.name
            val exists = fragmentManager.findFragmentByTag(tag)
            if (exists == null) {
                AvatarSaveShareDialog().apply {
                    arguments = AvatarSaveShareDialogArgs(images, shareToPostNeedFinish).asMavericksArgs()
                }.show(fragmentManager, tag)
            }
        }

        fun update(fragmentManager: FragmentManager, images: List<String>) {
            val tag = AvatarSaveShareDialog::class.java.name
            val exists = fragmentManager.findFragmentByTag(tag) as? AvatarSaveShareDialog
            exists?.updateImages(images)
        }
    }

    override var navColorRes = R.color.white

    private val viewModel by fragmentViewModel(AvatarShareViewModel::class)

    private val _sharePlatformController by lazy { sharePlatformController() }
    private val _carouselController by lazy { carouselController() }

    private val args by args<AvatarSaveShareDialogArgs>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _sharePlatformController.onRestoreInstanceState(savedInstanceState)
        _carouselController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        _sharePlatformController.onSaveInstanceState(outState)
        _carouselController.onSaveInstanceState(outState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.rvSharePlatformList.layoutManager =
            MaxPerAxisFlexboxLayoutManager.horizontal(requireContext(), 5)
        binding.rvSharePlatformList.setController(_sharePlatformController)
        binding.carouselImageList.setController(_carouselController)

        val carouselLayoutManager =
            PagerLinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
        carouselLayoutManager.offscreenPageLimit = 10

        binding.carouselImageList.layoutManager = carouselLayoutManager
        binding.carouselImageList.setItemSpacingDp(12)

        binding.acbNoRemindersToday.setOnCheckedChangeListener { _, isChecked ->
            UMW.current?.sendProtocol(
                ProtocolSendConstant.PROTOCOL_AVATAR_SAVE_SHARE_DIALOG_NO_REMINDERS,
                AvatarSaveShareNoRemindersTodayMsg(isChecked)
            )
        }

        binding.ivClose.setOnClickListener {

            val isLoaded = withState(viewModel) {
                it.compositingList.all { !it.body.isPlaceholder }
            }

            Analytics.track(
                EventConstants.ROLE_SAVE_CLOSE,
                mapOf("is_loaded" to (if (isLoaded) 1 else 0))
            )
            dismissNow()
        }

        viewModel.onEach(AvatarShareViewModelState::toastMsg, deliveryMode = uniqueOnly()) {
            toast(it.getMsg(requireContext()))
        }
    }

    override fun init() {
    }

    private fun updateImages(images: List<String>) {
        viewModel.updateImages(images)
    }

    private fun sharePlatformController() =
        simpleController(viewModel, AvatarShareViewModelState::sharePlatformList) { items ->
            items.forEachIndexed { index, platform ->
                avatarSharePlatformListItem(index, platform) { _, item ->

                    val isLoaded = withState(viewModel) {
                        it.compositingList.all { !it.body.isPlaceholder }
                    }

                    Analytics.track(
                        EventConstants.ROLE_SAVE_SHARE_CLICK,
                        mapOf(
                            "type" to SharePlatform.platformTrackParam(platform.platform),
                            "is_loaded" to (if (isLoaded) 1 else 0),
                        )
                    )

                    if (!isLoaded) {
                        toast(R.string.loading)
                        return@avatarSharePlatformListItem
                    }

                    share(platform)
                }
            }
        }

    private fun carouselController() =
        simpleController(viewModel, AvatarShareViewModelState::compositingList) { items ->

            items.forEachIndexed { index, compositingImage ->
                avatarShareImageListItem(index, compositingImage, glide)
            }
        }

    private fun share(platform: SharePlatform) {
        viewModel.shareTo(this, requireActivity(), platform)
    }
}