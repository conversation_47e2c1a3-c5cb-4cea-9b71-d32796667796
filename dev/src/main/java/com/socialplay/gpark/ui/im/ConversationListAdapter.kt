package com.socialplay.gpark.ui.im

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import com.bumptech.glide.RequestManager
import com.ly123.tes.mgs.im.ImMessageHelper
import com.ly123.tes.mgs.metacloud.model.Message
import com.ly123.tes.mgs.metacloud.model.MessageContent
import com.socialplay.gpark.R
import com.socialplay.gpark.R.string
import com.socialplay.gpark.data.model.editor.EditorNotice
import com.socialplay.gpark.data.model.im.AbsConversationMessage
import com.socialplay.gpark.data.model.im.ConversationMessage
import com.socialplay.gpark.data.model.im.GroupConversationMessage
import com.socialplay.gpark.databinding.AdapterConversationBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.view.UserLabelListener
import com.socialplay.gpark.util.DateUtil.formatAgoStyleForChat
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.ThreadHelper
import com.socialplay.gpark.util.extension.displayName
import com.socialplay.gpark.util.extension.tagIds
import com.socialplay.gpark.util.extension.visible

/**
 * @author: ning.wang
 * @date: 2021-06-21 8:35 下午
 * @desc:
 */
class ConversationListAdapter(
    private val glide: RequestManager,
    private val listener: UserLabelListener
) : BaseAdapter<AbsConversationMessage, AdapterConversationBinding>() {

    companion object {
        val CALLBACK = object : DiffUtil.ItemCallback<ConversationMessage>() {
            override fun areItemsTheSame(
                oldItem: ConversationMessage,
                newItem: ConversationMessage,
            ): Boolean {
                return oldItem.friendInfo.uuid == newItem.friendInfo.uuid
            }

            override fun areContentsTheSame(
                oldItem: ConversationMessage,
                newItem: ConversationMessage,
            ): Boolean {
                return oldItem == newItem
            }

        }
    }


    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int,
    ): AdapterConversationBinding {
        return AdapterConversationBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    }

    override fun convert(
        holder: BindingViewHolder<AdapterConversationBinding>,
        item: AbsConversationMessage,
        position: Int,
    ) {
        when (item) {
            is ConversationMessage -> {
                bindConversationMessage(holder, item)
            }
            is GroupConversationMessage ->{
                bindGroupConversationMessage(holder, item)
            }
            is AbsConversationMessage.SysConversation    -> {
                bindSysMessage(holder,item)
            }
            is AbsConversationMessage.NoticeConversation ->{
                bindNoticeConversationMessage(holder, item)
            }

        }
    }

    private fun bindNoticeConversationMessage(
        holder: BindingViewHolder<AdapterConversationBinding>,
        item: AbsConversationMessage.NoticeConversation,
    ) {

        var noticeIconRes = R.drawable.icon_home_notice
        var noticePublisherName = string.app_name

        if (item.notice.type == EditorNotice.OuterShowNotice.TYPE_EDITOR) {
            noticeIconRes = R.drawable.icon_home_notice
            noticePublisherName = string.notice
        } else if (item.notice.type == EditorNotice.OuterShowNotice.TYPE_OPERATION) {
            noticeIconRes = R.drawable.icon_home_notice
            noticePublisherName = string.app_name
        } else if (item.notice.type == EditorNotice.OuterShowNotice.TYPE_POST) {
            noticeIconRes = R.drawable.icon_home_notice
            noticePublisherName = string.interactions
        }

        holder.binding.apply {
            tvUserName.text = context.getString(noticePublisherName)
            ivUserAvatar.setImageDrawable(ContextCompat.getDrawable(context, noticeIconRes))
            holder.binding.root.isSelected = false
            updateUnread(holder, item.notice)
            updateContent(holder, item.notice)
        }
    }

    private fun updateContent(holder: BindingViewHolder<AdapterConversationBinding>, item: EditorNotice.OuterShowNotice) {
        val hasNotice = !item.content.isNullOrEmpty()
        holder.binding.tvMessageTime.text = if (!hasNotice || item.sendTime <= 0) "" else item.sendTime.formatAgoStyleForChat(holder.binding.ivUserAvatar.context)
        holder.binding.tvMessageContent.text = item.content
        holder.binding.tvMessageContent.isVisible = hasNotice
    }

    private fun updateUnread(holder: BindingViewHolder<AdapterConversationBinding>, item: EditorNotice.OuterShowNotice) {
        holder.binding.tvUnReadTip.isVisible = item.hasUnread
    }

    private fun bindSysMessage(
        holder: BindingViewHolder<AdapterConversationBinding>,
        item: AbsConversationMessage.SysConversation,
    ) {

        val info = item.info
        holder.binding.tvUserName.text = info.title
        holder.binding.apply {
            labelGroup.hide()
            ThreadHelper.runOnUiThreadCatching {
                glide.load(info.icon).placeholder(R.drawable.icon_default_avatar).centerCrop().into(ivUserAvatar)
            }
        }

        holder.binding.tvMessageContent.text = info.lastMsgSimple
        holder.binding.tvMessageContent.visible(!info.lastMsgSimple.isNullOrEmpty())
        holder.binding.tvMessageTime.text = info.lastModifyTime.formatAgoStyleForChat()
        holder.binding.tvUnReadTip.visible(info.unread > 0)
        holder.binding.tvUnReadTip.text = if (info.unread > 99) "99+" else "${info.unread}"

        holder.binding.root.isSelected = false
    }

    private fun bindConversationMessage(
        holder: BindingViewHolder<AdapterConversationBinding>,
        item: ConversationMessage,
    ) {
        val userInfo = item.friendInfo
        val conversation = item.conversation

        ViewCompat.setTransitionName(holder.binding.tvUserName, "trans_${userInfo.uuid}")

        holder.binding.apply {
            tvUserName.text = userInfo.displayName
            ThreadHelper.runOnUiThreadCatching {
                labelGroup.show(
                    userInfo.tagIds,
                    userInfo.labelInfo,
                    glide = glide
                )
                glide.load(userInfo.avatar).placeholder(R.drawable.icon_default_avatar).centerCrop()
                    .into(ivUserAvatar)
            }
        }

        if (conversation != null) {
            holder.binding.tvMessageContent.text = getContent(conversation.messageContent)
            if (conversation.latestMessageId != null) {
                holder.binding.tvMessageContent.visible(true)
                holder.binding.tvMessageTime.text =
                    conversation.sentTime?.formatAgoStyleForChat(holder.binding.ivUserAvatar.context)
            } else {
                holder.binding.tvMessageContent.visible(false)
                holder.binding.tvMessageTime.text = null
            }
            holder.binding.tvUnReadTip.visible(conversation.unReadMessageCount > 0)
            holder.binding.tvUnReadTip.text = conversation.unReadMessageCount.toString()

            holder.binding.root.isSelected = conversation.isTop == true
        } else {
            holder.binding.root.isSelected = false
            holder.binding.tvMessageContent.text = null
            holder.binding.tvMessageContent.visible(false)
            holder.binding.tvMessageTime.text = null
            holder.binding.tvUnReadTip.visible(false)
        }
    }

    private fun bindGroupConversationMessage(
        holder: BindingViewHolder<AdapterConversationBinding>,
        item: GroupConversationMessage,
    ) {
        val conversation = item.conversation
        if (conversation != null) {
            holder.binding.apply {
                tvUserName.text = conversation.showName
                labelGroup.hide()
                ThreadHelper.runOnUiThreadCatching {
                    glide.load(conversation.avatarUrl)
                        .placeholder(R.drawable.icon_default_group_chat_avatar)
                        .centerCrop()
                        .into(ivUserAvatar)
                }
            }
            // TODO 群聊消息预览, 带 AT 信息
            var atType = conversation.receiveAtTypes?.firstOrNull {
                it == Message.ATType.AT_ME
            }
            if (atType == null) {
                atType = conversation.receiveAtTypes?.firstOrNull { it == Message.ATType.AT_ALL_AND_AT_ME }
            }
            if (atType == null) {
                atType = conversation.receiveAtTypes?.firstOrNull { it == Message.ATType.AT_ALL }
            }
            // 目前api没有提供at消息的发送方信息
            val atText = when (atType) {
                Message.ATType.AT_ME, Message.ATType.AT_ALL_AND_AT_ME -> context.getString(string.conversation_content_prefix_at_you)
                Message.ATType.AT_ALL -> context.getString(string.conversation_content_prefix_at_all)
                else -> ""
            }
            val messageSenderNickName = conversation.groupSenderInfo?.nickname
            val messageContent = getContent(conversation.messageContent)
            val text = if (messageSenderNickName.isNullOrEmpty()) {
                messageContent
            } else {
                "$messageSenderNickName: $messageContent"
            }

            if (atText.isEmpty()) {
                holder.binding.tvMessageContent.text = text
            } else {
                holder.binding.tvMessageContent.text = SpannableHelper.Builder()
                    .text(atText)
                    .textAppearance(context, R.style.MetaTextView_S12_PoppinsSemiBold600)
                    .colorRes(R.color.color_FF5F42)
                    .text(text)
                    .textAppearance(context, R.style.MetaTextView_S12_PoppinsRegular400)
                    .colorRes(R.color.color_B3B3B3)
                    .build()
            }
            if (conversation.latestMessageId != null) {
                holder.binding.tvMessageContent.visible(true)
                holder.binding.tvMessageTime.text =
                    conversation.sentTime?.formatAgoStyleForChat(holder.binding.ivUserAvatar.context)
            } else {
                holder.binding.tvMessageContent.visible(false)
                holder.binding.tvMessageTime.text = null
            }
            // 未读消息数
            val unreadText = if (conversation.unReadMessageCount > 99) {
                "99+"
            } else {
                conversation.unReadMessageCount.toString()
            }
            when (item.conversation.messageRevOpt) {
                Message.MessageRevOpt.RECEIVE_NOT_NOTIFY, Message.MessageRevOpt.RECEIVE_NOT_NOTIFY_EXCEPT_AT -> {
                    holder.binding.tvUnReadTip.visible(conversation.unReadMessageCount > 0)
                    holder.binding.tvUnReadTip.text = unreadText
                    holder.binding.tvUnReadTip.setBackgroundResource(R.drawable.bg_friend_unread_message_grey)
                }

                Message.MessageRevOpt.NOT_RECEIVE -> {
                    holder.binding.tvUnReadTip.visible(false)
                }

                else -> {
                    holder.binding.tvUnReadTip.visible(conversation.unReadMessageCount > 0)
                    holder.binding.tvUnReadTip.text = unreadText
                    holder.binding.tvUnReadTip.setBackgroundResource(R.drawable.bg_friend_unread_message)
                }
            }

            holder.binding.root.isSelected = conversation.isTop == true
        } else {
            holder.binding.root.isSelected = false
            holder.binding.tvMessageContent.text = null
            holder.binding.tvMessageContent.visible(false)
            holder.binding.tvMessageTime.text = null
            holder.binding.tvUnReadTip.visible(false)
        }
    }

    fun getContent(messageContent: MessageContent?): CharSequence {
        if (messageContent == null) {
            return ""
        }
        val messageProvider = ImMessageHelper.getInstance().getMessageTemplate(messageContent.javaClass)
        if (messageProvider != null) {
            val content = messageProvider.getContentSummary(context, messageContent)
            content?.let {
                return content.toString().replace("\n", " ")
            }
        }
        return context.getString(string.im_unknown_content)
    }

    override fun onViewAttachedToWindow(holder: BindingViewHolder<AdapterConversationBinding>) {
        super.onViewAttachedToWindow(holder)
        holder.binding.labelGroup.setListener(listener)
    }

    override fun onViewDetachedFromWindow(holder: BindingViewHolder<AdapterConversationBinding>) {
        holder.binding.labelGroup.setListener(null)
        super.onViewDetachedFromWindow(holder)
    }
}