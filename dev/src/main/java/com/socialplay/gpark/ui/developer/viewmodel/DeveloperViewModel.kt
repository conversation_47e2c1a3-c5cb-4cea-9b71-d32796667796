package com.socialplay.gpark.ui.developer.viewmodel

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.ui.developer.DemoWrapper
import com.socialplay.gpark.ui.developer.bean.DevAction
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext

/**
 * xingxiu.hou
 * 2021/6/8
 */
class DeveloperViewModel : ViewModel() {

    companion object {
        private const val META_APP_DEVELOPER_TOGGLE = "meta_app_developer_toggle"

        const val SHOW_BANNER = -2
        const val SHOW_INTERSTITIAL = -3
        const val SHOW_REWARDED = -4
    }

    private val mmkv: MetaKV by lazy { GlobalContext.get().get() }

    private val actionList = mutableListOf<DevAction>()

    private val _developerToggleLivedata = MutableLiveData<Boolean>()
    val developerToggleLivedata: LiveData<Boolean> = _developerToggleLivedata

    private val _developerListLivedata = MutableLiveData<List<DevAction>>()
    val developerListLivedata: LiveData<List<DevAction>> = _developerListLivedata

    fun getToggleStatus() {
        _developerToggleLivedata.postValue(isOpenDeveloper())
    }

    /**
     * 1.Debug模式直接默认开启开发者模式
     * 2.非Debug模式，需要密码触发开启开发者模式
     */
    private fun isOpenDeveloper(): Boolean {
        return mmkv.developer.getBoolean(META_APP_DEVELOPER_TOGGLE, BuildConfig.DEBUG)
    }

    fun checkDeveloperOpen(input: String) {
        if (input == "869233") {
            viewModelScope.launch {
                delay(500)
                mmkv.developer.putBoolean(META_APP_DEVELOPER_TOGGLE, true)
                _developerToggleLivedata.postValue(true)
            }
        }
    }

    fun requestListData() {
        val context = GlobalContext.get().get<Context>()
        actionList.add(DevAction("BuildConfig", R.id.devBuildConfigFragment))
        actionList.add(DevAction(context.getString(R.string.debug_local_toggle_title), R.id.devPandoraToggleFragment))
        actionList.add(DevAction(context.getString(R.string.debug_env_legacy), R.id.devEnvFragment))
        actionList.add(DevAction("Demo Fragment", R.id.devDemoFragment))
        actionList.add(DevAction("Demo List Fragment", R.id.devDemoListFragment))
        actionList.add(DevAction(context.getString(R.string.debug_app_params), R.id.devAppParamsFragment))
        actionList.add(DevAction(context.getString(R.string.debug_sign_key), R.id.appSigningInfoFragment))
        actionList.add(DevAction("Applovin Debugger", -1))
        actionList.add(DevAction(context.getString(R.string.debug_encourage_ad), SHOW_REWARDED))
        actionList.add(DevAction("Banner ad", SHOW_BANNER))
        actionList.add(DevAction("test share", R.id.devShareFragment))
        actionList.add(DevAction("MW", R.id.devMwFragment))
        actionList.add(DevAction(context.getString(R.string.debug_game_review), R.id.devReviewGame))
        actionList.add(DevAction(context.getString(R.string.debug_open_maidian), R.id.devShowEvent))
        actionList.add(DevAction(context.getString(R.string.debug_mmkv), R.id.devMMKVFragment))
        DemoWrapper.getActionList().forEach {
            actionList.add(DevAction(it.key, it.value))
        }

        _developerListLivedata.postValue(actionList)
    }


}