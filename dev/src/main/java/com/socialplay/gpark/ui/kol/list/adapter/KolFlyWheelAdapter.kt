package com.socialplay.gpark.ui.kol.list.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.databinding.AdapterKolCreatorFlywheelBinding
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.ui.base.adapter.BasicQuickAdapter
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setSize
import com.socialplay.gpark.util.extension.setWidth
import com.socialplay.gpark.util.extension.visible

/**
 * Created by bo.li
 * Date: 2024/8/5
 * Desc: Kol创作者作品列表
 */
class KolFlyWheelAdapter(
    data: MutableList<UniJumpConfig>,
    private val itemWidth: Int,
    private val glide: RequestManager
) : BasicQuickAdapter<UniJumpConfig, AdapterKolCreatorFlywheelBinding>(data) {

    override fun convert(
        holder: BaseVBViewHolder<AdapterKolCreatorFlywheelBinding>,
        item: UniJumpConfig
    ) {
        holder.binding.ivIcon.setSize(itemWidth, itemWidth)
        glide.load(item.iconUrl)
            .placeholder(R.drawable.placeholder)
            .into(holder.binding.ivIcon)
        holder.binding.tvName.text = item.title
        if (item.jumpType == UniJumpConfig.POS_DAILY_TASK_REWARD) {
            holder.binding.tvTips.visible(item.localShowRedBadge)
            holder.binding.vUnread.gone()
        } else {
            holder.binding.tvTips.gone()
            holder.binding.vUnread.visible(item.localShowRedBadge)
        }
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterKolCreatorFlywheelBinding {
        return AdapterKolCreatorFlywheelBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }

}