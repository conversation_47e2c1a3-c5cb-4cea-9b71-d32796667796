package com.socialplay.gpark.ui.aibot.ugc.adapter

import android.view.LayoutInflater
import android.view.RoundedCorner
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.aibot.AIBotStyle
import com.socialplay.gpark.databinding.ItemAiBotCreateStyleBinding
import com.socialplay.gpark.databinding.ItemAiBotGenderBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setWidth
import com.socialplay.gpark.util.extension.visible

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/23
 *     desc   :
 *
 */
class AIBotStyleAdapter() :BaseAdapter<AIBotStyle,ItemAiBotCreateStyleBinding>() {
    val with = (ScreenUtil.screenWidth - 55.dp) / 4

    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): ItemAiBotCreateStyleBinding {
        return ItemAiBotCreateStyleBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(
        holder: BindingViewHolder<ItemAiBotCreateStyleBinding>,
        item: AIBotStyle,
        position: Int
    ) {
        val layoutParams = holder.binding.clRoot.layoutParams
        layoutParams.width = with
        holder.binding.root.layoutParams = layoutParams
        Glide.with(context).load(item.coverUrl) .transform(CenterCrop(), RoundedCorners(12.dp)).into(holder.binding.imgStyle)
        holder.binding.tvName.text = item.name
        holder.binding.viewSpace.visible(item.isSelect)
        holder.binding.imgSel.visible(item.isSelect)
    }
}