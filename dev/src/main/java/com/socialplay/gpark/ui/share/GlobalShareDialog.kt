package com.socialplay.gpark.ui.share

import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ImageView
import androidx.core.view.isGone
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.GenericTransitionOptions
import com.bumptech.glide.RequestBuilder
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.request.transition.DrawableCrossFadeFactory
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.share.ShareFeature
import com.socialplay.gpark.data.model.share.SharePendingData
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.databinding.DialogGlobalShareBinding
import com.socialplay.gpark.databinding.ViewSharePgcCardBinding
import com.socialplay.gpark.databinding.ViewShareProfileCardBinding
import com.socialplay.gpark.databinding.ViewShareScreenshotBinding
import com.socialplay.gpark.databinding.ViewShareUgcCardBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.share.GlobalShareHelper
import com.socialplay.gpark.function.share.IGlobalShareCallback
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.function.share.screenshot.ScreenshotMonitor
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.friend.contacts.ContactListFragment
import com.socialplay.gpark.util.BlurTransformation
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.QRCode
import com.socialplay.gpark.util.StatusBarUtil.getStatusBarHeight
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.UnitUtilWrapper
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setFragmentResultListenerByActivity
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/22
 *     desc   :
 * </pre>
 */
@Parcelize
data class GlobalShareArgs(
    val requestKey: String,
    val scene: String,
    val raw: String,
    val isPortrait: Boolean,
    val isInGame: Boolean,
    val features: List<ShareFeature>?
) : Parcelable

class GlobalShareDialog : BaseDialogFragment(), IGlobalShareDialogMixin {

    companion object {
        const val OP_INVISIBLE = 1
        const val OP_GONE = 2
        const val OP_SKIP = 3

        const val KEY_SHARE_COUNT = "shareCount"
        const val KEY_RAW_DATA = "rawData"

        fun show(
            fm: FragmentManager,
            raw: ShareRawData,
            requestKey: String = "",
            isPortrait: Boolean = true,
            isInGame: Boolean = false,
            features: List<ShareFeature>? = null
        ) {
            val map = HashMap<String, Any>()
            raw.reqId?.let {
                map["reqid"] = it
            }
            map["source"] = raw.trackSource
            when (raw.scene) {
                ShareHelper.SCENE_PGC_DETAIL -> {
                    raw.pgcGame?.id?.let {
                        map["gameid"] = it
                    }
                }

                ShareHelper.SCENE_UGC_DETAIL -> {
                    raw.ugcGame?.id?.let {
                        map["ugcid"] = it
                    }
                }

                ShareHelper.SCENE_POST_DETAIL -> {
                    raw.postDetail?.postId?.let {
                        map["postid"] = it
                    }
                }

                ShareHelper.SCENE_VIDEO_FEED -> {
                    raw.videoFeed?.videoId?.let {
                        map["postid"] = it
                    }
                }

                ShareHelper.SCENE_OC_MOMENT -> {
                    raw.gameId?.let {
                        map["gameid"] = it
                    }
                }

                ShareHelper.SCENE_SCREENSHOT -> {
                    raw.gameId?.let {
                        map["gameid"] = it
                    }
                }

                ShareHelper.SCENE_UGC_DESIGN_DETAIL -> {
                    raw.ugcDesignDetail?.let {
                        map["metrialidid"] = it.trackId
                    }
                }
            }
            Analytics.track(EventConstants.EVENT_SHARE_CLICK, map)
            if (ScreenshotMonitor.instance.isSharing) return
            val dialog = GlobalShareDialog()
            dialog.arguments =
                GlobalShareArgs(
                    requestKey,
                    raw.scene,
                    GsonUtil.safeToJson(raw),
                    isPortrait,
                    isInGame,
                    features
                ).asMavericksArgs()
            dialog.show(fm, "GlobalShareDialog_$requestKey")
            Analytics.track(EventConstants.EVENT_SHARE_SHOW, map)
        }
    }

    override var navColorRes = R.color.white

    override val binding by viewBinding(DialogGlobalShareBinding::inflate)
    private val args: GlobalShareArgs by args()
    private val vm: GlobalShareViewModel by fragmentViewModel()

    private val platformController by lazy { buildPlatformController() }
    private val featureController by lazy { buildFeatureController() }
    private val longImagePlatformController by lazy { buildLongImagePlatformController() }

    private val canQuit get() = binding.ivClose.isGone

    private val itemListener = object : IGlobalShareListener {
        override fun clickPlatform(item: SharePlatform) {
            vm.createShare(this@GlobalShareDialog, item.platform)
            if (item.platform != SharePlatform.PLATFORM_LONG_IMAGE) return
            val rawData = vm.oldState.raw
            val icon = when (rawData.scene) {
                ShareHelper.SCENE_PGC_DETAIL -> {
                    rawData.pgcGame?.icon
                }

                ShareHelper.SCENE_UGC_DETAIL -> {
                    rawData.ugcGame?.banner
                }

                else -> {
                    null
                }
            }
            if (icon.isNullOrBlank()) return
            glide?.let {
                binding.ivClose.visible()
                binding.ivBlur.animate().alpha(1.0f)
                it.load(icon)
                    .placeholder(R.color.transparent)
                    .centerCrop()
                    .apply(RequestOptions.bitmapTransform(BlurTransformation(25)))
                    .transition(
                        GenericTransitionOptions.with(
                            DrawableCrossFadeFactory.Builder(300)
                                .setCrossFadeEnabled(true)
                                .build()
                        )
                    )
                    .into(binding.ivBlur)
            }
        }

        override fun clickFeature(item: ShareFeature) {
            dismissAllowingStateLoss()
            (parentFragment as? IGlobalShareCallback)?.invokeShareFeature(item)
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    private var firstTime = true

    override fun init() {
        binding.groupLoading.referencedIds = intArrayOf(R.id.tv_loading, R.id.pb_loading)

        binding.ivClose.setOnAntiViolenceClickListener(233) {
            close()
        }
        binding.root.setOnAntiViolenceClickListener(233) {
            if (canQuit) {
                close()
            }
        }

        binding.rvPlatform.itemAnimator = null
        binding.rvPlatform.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
        binding.rvPlatform.setControllerAndBuildModels(platformController)

        binding.rvFeature.itemAnimator = null
        binding.rvFeature.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
        binding.rvFeature.setControllerAndBuildModels(featureController)

        binding.rvLongImagePlatform.itemAnimator = null
        binding.rvLongImagePlatform.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
        binding.rvLongImagePlatform.setControllerAndBuildModels(longImagePlatformController)

        handleType(vm.oldState.type)
        vm.onEach(GlobalShareState::type) {
            handleType(it)
        }
        vm.onEach(
            GlobalShareState::raw,
            GlobalShareState::type,
            GlobalShareState::data,
            GlobalShareState::longImageStatus,
            deliveryMode = uniqueOnly()
        ) { raw, type, dataAsync, longImageStatus ->
            if (type in GlobalShareState.TYPE_LONG_IMAGE_PRE..GlobalShareState.TYPE_LONG_IMAGE_POST
                && longImageStatus == GlobalShareState.STATUS_INIT
            ) {
                when (raw.scene) {
                    ShareHelper.SCENE_PROFILE -> {
                        longImageProfile(raw)
                        return@onEach
                    }

                    ShareHelper.SCENE_SCREENSHOT -> {
                        GlobalShareHelper.longImageScreenshot(
                            raw,
                            vm,
                            getShareContainer(),
                            args.isPortrait,
                            this
                        )
                        return@onEach
                    }
                }
            }
            val data = dataAsync.invoke() ?: return@onEach
            when (type) {
                GlobalShareState.TYPE_NORMAL -> {
                    vm.share(this, data)
                }

                GlobalShareState.TYPE_LONG_IMAGE_PRE -> {
                    when (raw.scene) {
                        ShareHelper.SCENE_PROFILE -> {
                            when (data.platform) {
                                SharePlatform.PLATFORM_FRIEND -> {
                                    vm.share(this, data)
                                }

                                else -> {
                                    longImageHelper1(raw, type, data, longImageStatus)
                                }
                            }
                        }
                    }
                }

                GlobalShareState.TYPE_LONG_IMAGE_POST -> {
                    when (raw.scene) {
                        ShareHelper.SCENE_SCREENSHOT -> {
                            when (data.platform) {
                                SharePlatform.PLATFORM_FRIEND -> {
                                    vm.share(this, data)
                                }

                                else -> {
                                    longImageHelper1(raw, type, data, longImageStatus)
                                }
                            }
                        }
                    }
                }

                GlobalShareState.TYPE_LONG_IMAGE -> {
                    longImageHelper1(raw, type, data, longImageStatus)
                }
            }
        }
        vm.onAsync(
            GlobalShareState::data,
            deliveryMode = uniqueOnly(),
            onFail = { _ ->
                toast(R.string.share_fail)
            }
        )
        vm.onEach(
            GlobalShareState::longImageStatus,
            deliveryMode = uniqueOnly()
        ) {
            when (it) {
                GlobalShareState.STATUS_LOADING -> {
                    showLoading()
                    binding.tvLoading.setText(R.string.loading)
                }

                else -> {
                    showLoading(show = false)
                }
            }
        }
        vm.onEach(
            GlobalShareState::pendingData,
            GlobalShareState::progress,
            GlobalShareState::paths,
            deliveryMode = uniqueOnly()
        ) { pendingData, progress, paths ->
            if (pendingData != null && progress is Success && !paths.isNullOrEmpty()) {
                showLoading(show = false)
                vm.share(this, pendingData)
            } else if (progress is Loading) {
                showLoading()
                val percent = progress.invoke() ?: 0
                binding.tvLoading.setTextWithArgs(R.string.loading_percent, "${percent}%")
            } else {
                showLoading(show = false)
            }
        }
        vm.onEach(
            GlobalShareState::type,
            GlobalShareState::longImagePath
        ) { type, longImagePath ->
            if (type in GlobalShareState.TYPE_LONG_IMAGE..GlobalShareState.TYPE_LONG_IMAGE_PRE
                && !longImagePath.isNullOrBlank()
            ) {
                binding.ivPreview.visible()
                glide?.load(longImagePath)
                    ?.into(binding.ivPreview)
            } else {
                binding.ivPreview.gone()
            }
        }
        vm.onEach(GlobalShareState::result, deliveryMode = uniqueOnly()) {
            val (ok, data) = it ?: return@onEach
            if (ok) {
                when (data.platform) {
                    SharePlatform.PLATFORM_SAVE -> {
                        toast(R.string.save_successfully_cap)
                    }

                    SharePlatform.PLATFORM_LONG_IMAGE -> {
                        when (data.subPlatform) {
                            SharePlatform.PLATFORM_SAVE -> {
                                toast(R.string.save_successfully_cap)
                            }
                        }
                    }

                    SharePlatform.PLATFORM_LINK -> {
                        toast(R.string.copied_to_clipboard)
                    }
                }
            } else {
                when (data.platform) {
                    SharePlatform.PLATFORM_SAVE -> {
                        toast(R.string.save_failed)
                    }

                    SharePlatform.PLATFORM_LONG_IMAGE -> {
                        when (data.subPlatform) {
                            SharePlatform.PLATFORM_SAVE -> {
                                toast(R.string.save_failed)
                            }
                        }
                    }

                    SharePlatform.PLATFORM_SYSTEM -> {
                        toast(R.string.share_fail)
                    }
                }
            }
        }
        vm.incrementCallback.observe(viewLifecycleOwner) {
            (parentFragment as? IGlobalShareCallback)?.onShareCountIncrease(vm.oldState.raw)
        }
        vm.registerToast(GlobalShareState::toast)

        setFragmentResultListenerByActivity(vm.requestId) { _, bundle ->
            val ok = bundle.getBoolean(ContactListFragment.SHARE_CALLBACK, false)
            val uuid = bundle.getString(ContactListFragment.KEY_SELECT_UUID)
            vm.notifyShareToFriend(ok, uuid)
        }
    }

    private fun handleType(type: Int) {
        when (type) {
            GlobalShareState.TYPE_UNKNOWN -> {
                toast(R.string.unknown_error)
                dismissAllowingStateLoss()
            }

            GlobalShareState.TYPE_NORMAL,
            GlobalShareState.TYPE_LONG_IMAGE_PRE,
            GlobalShareState.TYPE_LONG_IMAGE_POST -> {
                binding.rvPlatform.visible()
                val hasFeature = !args.features.isNullOrEmpty()
                binding.vDividerMid.visible(hasFeature)
                binding.rvFeature.visible(hasFeature)
                binding.rvLongImagePlatform.gone()
            }

            GlobalShareState.TYPE_LONG_IMAGE -> {
                binding.rvPlatform.gone()
                binding.vDividerMid.gone()
                binding.rvFeature.gone()
                binding.rvLongImagePlatform.visible()
            }
        }
    }

    private fun longImageHelper1(
        raw: ShareRawData,
        type: Int,
        data: SharePendingData,
        longImageStatus: Int
    ) {
        when (longImageStatus) {
            GlobalShareState.STATUS_INIT,
            GlobalShareState.STATUS_RESET -> {
                longImageHelper2(raw, data)
            }

            GlobalShareState.STATUS_DONE -> {
                when (type) {
                    GlobalShareState.TYPE_LONG_IMAGE -> {
                        if (data.subPlatform != null) {
                            vm.share(this, data)
                        }
                    }

                    GlobalShareState.TYPE_LONG_IMAGE_PRE -> {
                        vm.share(this, data)
                    }

                    GlobalShareState.TYPE_LONG_IMAGE_POST -> {
                        vm.share(this, data)
                    }
                }
            }

            GlobalShareState.STATUS_FAIL -> {
                toast(R.string.common_error)
                quitLongImage()
            }
        }
    }

    private fun longImageHelper2(
        raw: ShareRawData,
        data: SharePendingData
    ) {
        when (raw.scene) {
            ShareHelper.SCENE_PROFILE -> {
                longImageProfile(raw)
            }

            ShareHelper.SCENE_PGC_DETAIL -> {
                longImagePgc(raw, data)
            }

            ShareHelper.SCENE_UGC_DETAIL -> {
                longImageUgc(raw, data)
            }

            ShareHelper.SCENE_SCREENSHOT -> {
                GlobalShareHelper.longImageScreenshot(
                    raw,
                    vm,
                    getShareContainer(),
                    args.isPortrait,
                    this
                )
            }
        }
    }

    override fun longImageHelper3(
        raw: ShareRawData,
        key: Long,
        url: String?,
        targetView: ImageView,
        rootView: View,
        operation: Int,
        onSuccess: ((Drawable) -> Unit)?,
        onBuild: ((RequestBuilder<*>) -> Unit)?
    ) {
        if (url.isNullOrBlank()) {
            when (operation) {
                OP_INVISIBLE -> {
                    targetView.invisible()
                }

                OP_GONE -> {
                    targetView.gone()
                }

                OP_SKIP -> {
                    // Do nothing.
                }
            }
            return
        } else {
            targetView.visible()
        }
        targetView.tag = key
        val builder = glide?.asDrawable()
            ?.load(url)
            ?.listener(object : RequestListener<Drawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable>,
                    isFirstResource: Boolean
                ): Boolean {
                    vm.longImageFail(url = model as? String, key = targetView.tag as? Long)
                    return true
                }

                override fun onResourceReady(
                    resource: Drawable,
                    model: Any,
                    target: Target<Drawable>?,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    if (isBindingAvailable()) {
                        onSuccess?.invoke(resource)
                        val statusBarHeight =
                            if (args.isPortrait && raw.scene == ShareHelper.SCENE_SCREENSHOT) {
                                getStatusBarHeight(requireContext())
                            } else {
                                0
                            }
                        val cachedKey = targetView.tag as? Long
                        if (model is String && cachedKey != null) {
                            vm.longImageProgress(rootView, statusBarHeight, model, cachedKey)
                        } else {
                            vm.longImageFail(url = model as? String, key = cachedKey)
                        }
                    }
                    return false
                }
            })
        builder?.let { onBuild?.invoke(it) }
        builder?.into(targetView)
    }

    private fun longImageProfile(raw: ShareRawData) {
        val user = raw.user
        val userExtra = raw.userExtra
        if (user == null || userExtra == null) {
            vm.longImageFail()
            return
        }
        val recent = userExtra.recentGames.firstOrNull()
        val recentIconUrl = recent?.gamePic
        var hasRecent = false
        val tempKey = vm.longImageLoading(buildList {
            if (!user.portrait.isNullOrBlank()) {
                add(user.portrait)
            }
            if (!recentIconUrl.isNullOrBlank()) {
                hasRecent = true
                add(recentIconUrl)
            }
        })
        val tempBinding = ViewShareProfileCardBinding.bind(getShareContainer())

        tempBinding.root.invisible(true)
        tempBinding.tvLongImageUname.text = user.nickname
        tempBinding.tvLongImageId.text = user.userNumber
        tempBinding.tvLongImageFollowersCount.text = UnitUtil.formatKMCount(user.fansCount)
        tempBinding.tvLongImageFollowingCount.text = UnitUtil.formatKMCount(user.followCount)
        tempBinding.tvLongImageLikesCount.text = UnitUtil.formatKMCount(user.likeCount)
        val qrCodeBitmap = QRCode.newQRCodeUtil()
            .margin("0")
            .content(userExtra.qrCode)
            .size(tempBinding.dp(150))
            .build()
        tempBinding.ivLongImageQrCode.setImageBitmap(qrCodeBitmap)
        longImageHelper3(
            raw,
            tempKey,
            user.portrait,
            tempBinding.ivLongImageAvatar,
            tempBinding.root
        )
        if (hasRecent) {
            tempBinding.groupLongImageGame.visible()
            tempBinding.tvLongImageGameName.text = recent?.name
            tempBinding.tvLongImageGamePlayTime.text =
                UnitUtilWrapper.formatPlayTime(requireContext(), recent?.duration ?: 0L)
            tempBinding.ivLongImageQrCode.setMargin(top = tempBinding.dp(97))
            longImageHelper3(
                raw,
                tempKey,
                recentIconUrl,
                tempBinding.ivLongImageGameIcon,
                tempBinding.root
            )
        } else {
            tempBinding.groupLongImageGame.gone()
            tempBinding.ivLongImageQrCode.setMargin(top = tempBinding.dp(20))
        }
    }

    private fun longImagePgc(raw: ShareRawData, data: SharePendingData) {
        val pgcGame = raw.pgcGame
        if (pgcGame == null) {
            vm.longImageFail()
            return
        }
        val banners = raw.images?.filter { it.isNotBlank() }?.take(3)
        val tempKey = vm.longImageLoading(buildList {
            if (!pgcGame.icon.isNullOrBlank()) {
                add(pgcGame.icon)
            }
            if (pgcGame.isBannerHor) {
                banners?.firstOrNull()?.let {
                    add(it)
                }
            } else {
                banners?.forEach {
                    add(it)
                }
            }
        })
        val tempBinding = ViewSharePgcCardBinding.bind(getShareContainer())
        tempBinding.tvLongImageGameName.text = pgcGame.name
        tempBinding.tvLongImageGamePv.text = UnitUtil.formatPlayerCount(pgcGame.playerCount ?: 0)
        tempBinding.tvLongImageGameLike.text = UnitUtil.formatKMCount(pgcGame.likeCount ?: 0)
        tempBinding.tvLongImageGameDesc.text = pgcGame.description
        val qrCodeBitmap = QRCode.newQRCodeUtil()
            .margin("0")
            .content(data.url)
            .size(tempBinding.dp(150))
            .build()
        tempBinding.ivLongImageQrCode.setImageBitmap(qrCodeBitmap)
        longImageHelper3(
            raw,
            tempKey,
            pgcGame.icon,
            tempBinding.ivLongImageGameIcon,
            tempBinding.root
        )
        if (banners.isNullOrEmpty()) {
            visibleList(
                tempBinding.ivLongImageGameBannerSingle,
                tempBinding.ivLongImageGameBannerDouble1,
                tempBinding.ivLongImageGameBannerDouble2,
                tempBinding.ivLongImageGameBannerTriple1,
                tempBinding.ivLongImageGameBannerTriple2,
                tempBinding.ivLongImageGameBannerTriple3,
                visible = false
            )
        } else if (pgcGame.isBannerHor) {
            tempBinding.ivLongImageGameBannerSingle.visible()
            visibleList(
                tempBinding.ivLongImageGameBannerDouble1,
                tempBinding.ivLongImageGameBannerDouble2,
                tempBinding.ivLongImageGameBannerTriple1,
                tempBinding.ivLongImageGameBannerTriple2,
                tempBinding.ivLongImageGameBannerTriple3,
                visible = false
            )
            longImageHelper3(
                raw,
                tempKey,
                banners.getOrNull(0),
                tempBinding.ivLongImageGameBannerSingle,
                tempBinding.root
            )
        } else if (banners.size == 1) {
            tempBinding.ivLongImageGameBannerSingle.visible()
            visibleList(
                tempBinding.ivLongImageGameBannerDouble1,
                tempBinding.ivLongImageGameBannerDouble2,
                tempBinding.ivLongImageGameBannerTriple1,
                tempBinding.ivLongImageGameBannerTriple2,
                tempBinding.ivLongImageGameBannerTriple3,
                visible = false
            )
            longImageHelper3(
                raw,
                tempKey,
                banners.getOrNull(0),
                tempBinding.ivLongImageGameBannerSingle,
                tempBinding.root
            )
        } else if (banners.size == 2) {
            visibleList(
                tempBinding.ivLongImageGameBannerDouble1,
                tempBinding.ivLongImageGameBannerDouble2,
            )
            visibleList(
                tempBinding.ivLongImageGameBannerSingle,
                tempBinding.ivLongImageGameBannerTriple1,
                tempBinding.ivLongImageGameBannerTriple2,
                tempBinding.ivLongImageGameBannerTriple3,
                visible = false
            )
            longImageHelper3(
                raw,
                tempKey,
                banners.getOrNull(0),
                tempBinding.ivLongImageGameBannerDouble1,
                tempBinding.root
            )
            longImageHelper3(
                raw,
                tempKey,
                banners.getOrNull(1),
                tempBinding.ivLongImageGameBannerDouble2,
                tempBinding.root
            )
        } else {
            visibleList(
                tempBinding.ivLongImageGameBannerTriple1,
                tempBinding.ivLongImageGameBannerTriple2,
                tempBinding.ivLongImageGameBannerTriple3
            )
            visibleList(
                tempBinding.ivLongImageGameBannerSingle,
                tempBinding.ivLongImageGameBannerDouble1,
                tempBinding.ivLongImageGameBannerDouble2,
                visible = false
            )
            longImageHelper3(
                raw,
                tempKey,
                banners.getOrNull(0),
                tempBinding.ivLongImageGameBannerTriple1,
                tempBinding.root
            )
            longImageHelper3(
                raw,
                tempKey,
                banners.getOrNull(1),
                tempBinding.ivLongImageGameBannerTriple2,
                tempBinding.root
            )
            longImageHelper3(
                raw,
                tempKey,
                banners.getOrNull(2),
                tempBinding.ivLongImageGameBannerTriple3,
                tempBinding.root
            )
        }
    }

    private fun longImageUgc(raw: ShareRawData, data: SharePendingData) {
        val ugcGame = raw.ugcGame
        if (ugcGame == null) {
            vm.longImageFail()
            return
        }
        val banner = raw.image
        var hasBanner = false
        val tempKey = vm.longImageLoading(buildList {
            if (!banner.isNullOrBlank()) {
                hasBanner = true
                add(banner)
            }
        })
        val tempBinding = ViewShareUgcCardBinding.bind(getShareContainer())
        tempBinding.tvLongImageGameName.text = ugcGame.ugcGameName
        tempBinding.tvLongImageGamePv.text = UnitUtil.formatPlayerCount(ugcGame.pageView)
        tempBinding.tvLongImageGameLike.text = UnitUtil.formatKMCount(ugcGame.loveQuantity)
        tempBinding.tvLongImageGameDesc.text = ugcGame.ugcGameDesc
        val qrCodeBitmap = QRCode.newQRCodeUtil()
            .margin("0")
            .content(data.url)
            .size(tempBinding.dp(150))
            .build()
        tempBinding.ivLongImageQrCode.setImageBitmap(qrCodeBitmap)
        if (hasBanner) {
            tempBinding.ivLongImageGameBannerSingle.visible()
            longImageHelper3(
                raw,
                tempKey,
                banner,
                tempBinding.ivLongImageGameBannerSingle,
                tempBinding.root,
                operation = OP_GONE,
                onSuccess = {
                    tempBinding.ivLongImageGameIcon.setImageDrawable(it)
                }
            )
        } else {
            tempBinding.ivLongImageGameBannerSingle.gone()
        }
    }

    private fun showLoading(show: Boolean = true) {
        if (show) {
            binding.groupLoading.visible()
            binding.vCover.visible()
            binding.rvPlatform.enableWithAlpha(false)
            binding.rvFeature.enableWithAlpha(false)
            binding.rvLongImagePlatform.enableWithAlpha(false)
        } else {
            binding.groupLoading.gone()
            binding.vCover.gone()
            binding.rvPlatform.enableWithAlpha(true)
            binding.rvFeature.enableWithAlpha(true)
            binding.rvLongImagePlatform.enableWithAlpha(true)
        }
    }

    private fun getShareContainer(): View = binding.root.getViewById(R.id.cl_long_image_container)

    private fun buildPlatformController() = simpleController(
        vm,
        GlobalShareState::raw,
        GlobalShareState::platforms,
    ) { raw, platforms ->
        if (raw.scene == ShareHelper.SCENE_SCREENSHOT && !args.isInGame) {
            raw.image?.let {
                globalShareScreenshotPreviewItem(it, args.isPortrait, itemListener)
            }
        }
        platforms.forEach {
            globalSharePlatformItem(it, itemListener)
        }
    }

    private fun buildFeatureController() = simpleController(
        vm,
        GlobalShareState::features,
    ) { features ->
        features?.forEach {
            globalShareFeatureItem(it, itemListener)
        }
    }

    private fun buildLongImagePlatformController() = simpleController(
        vm,
        GlobalShareState::longImagePlatforms
    ) { longImagePlatforms ->
        longImagePlatforms.forEach {
            globalSharePlatformItem(it, itemListener)
        }
    }

    private fun close() {
        if (canQuit && vm.oldState.type == GlobalShareState.TYPE_LONG_IMAGE) {
            binding.ivBlur.animate().alpha(0.0f)
            binding.ivClose.gone()
            quitLongImage()
        } else {
            dismissAllowingStateLoss()
        }
    }

    private fun quitLongImage() {
        vm.longImageQuit()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        platformController.onRestoreInstanceState(savedInstanceState)
        featureController.onRestoreInstanceState(savedInstanceState)
        longImagePlatformController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return super.onCreateView(inflater, container, savedInstanceState)?.apply {
            when (args.scene) {
                ShareHelper.SCENE_PROFILE -> {
                    ViewShareProfileCardBinding.inflate(inflater, binding.root, true).apply {
                        groupLongImageGame.referencedIds = intArrayOf(
                            R.id.iv_long_image_game_icon,
                            R.id.tv_long_image_game_name,
                            R.id.tv_long_image_game_play_time,
                            R.id.v_long_image_game_divider_cover_left,
                            R.id.iv_long_image_game_divider,
                            R.id.v_long_image_game_divider_cover_right
                        )
                    }
                    binding.ivPreview.setCornerRadius(binding.dp(12))
                    binding.ivPreview.setMargin(top = binding.dp(48), bottom = binding.dp(48))
                }

                ShareHelper.SCENE_PGC_DETAIL -> {
                    ViewSharePgcCardBinding.inflate(inflater, binding.root, true)
                    binding.ivPreview.setCornerRadius(binding.dp(16))
                }

                ShareHelper.SCENE_UGC_DETAIL -> {
                    ViewShareUgcCardBinding.inflate(inflater, binding.root, true)
                    binding.ivPreview.setCornerRadius(binding.dp(16))
                }

                ShareHelper.SCENE_SCREENSHOT -> {
                    ViewShareScreenshotBinding.inflate(inflater, binding.root, true)
                }
            }
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        platformController.onSaveInstanceState(outState)
        featureController.onSaveInstanceState(outState)
        longImagePlatformController.onSaveInstanceState(outState)
    }

    override fun onResume() {
        super.onResume()
        ScreenshotMonitor.instance.isSharing = true
    }

    override fun onPause() {
        ScreenshotMonitor.instance.isSharing = false
        super.onPause()
    }

    override fun onDestroyView() {
        firstTime = false
        super.onDestroyView()
    }

    override fun isFullScreen() = true

    override fun isHideNavigation(): Boolean {
        return !args.isPortrait
    }

    override fun dimAmount() = 0.6f

    override fun getStyle() = R.style.GlobalShareDialog

    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT

    override fun onBackPressed(): Boolean {
        if (canQuit) {
            close()
        }
        return true
    }
}