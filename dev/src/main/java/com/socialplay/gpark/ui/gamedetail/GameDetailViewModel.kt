package com.socialplay.gpark.ui.gamedetail

import android.os.SystemClock
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.UpdateAppInteractor
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.account.UserFollowEvent
import com.socialplay.gpark.data.model.user.RelationType
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.util.extension.LifecycleCallback
import com.socialplay.gpark.util.extension.registerHermes
import com.socialplay.gpark.util.extension.unregisterHermes
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.filterNot
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe

/**
 * Created by bo.li
 * Date: 2021/9/15
 * Desc:
 */
class GameDetailViewModel(
    val repository: IMetaRepository,
    val accountInteractor: AccountInteractor,
    val updateAppInteractor: UpdateAppInteractor
) : ViewModel() {

    val requestKey = "GameDetailViewModel_${SystemClock.elapsedRealtime()}"

    private val _shareCountLiveData = MutableLiveData<Long>()
    val shareCountLiveData: LiveData<Long> = _shareCountLiveData

    //游戏详细信息
    private val _gameDetailFlow = MutableStateFlow<Pair<DataResult<GameDetailInfo>, Long>?>(null)
    val gameDetailFlow get() = _gameDetailFlow.asLiveData(viewModelScope.coroutineContext)

    private val _noticeList = MutableStateFlow<DataResult<List<UniJumpConfig>?>>(DataResult.Success(emptyList(), true))
    val noticeList: StateFlow<DataResult<List<UniJumpConfig>?>> = _noticeList

    private val _followLiveData = MutableLiveData<Boolean>()
    val followLiveData: LiveData<Boolean> = _followLiveData

    val toastLifeCallback: LifecycleCallback<(String) -> Unit> = LifecycleCallback()

    val authorUuid get() = _gameDetailFlow.value?.first?.data?.author?.id
    val gameDetail get() = _gameDetailFlow.value?.first?.data
    val replyTargetName
        get() = _gameDetailFlow.value?.first?.data?.let {
            it.author?.name ?: it.name
        }

    init {
        registerHermes()
    }

    /**
     * 通游戏id获取游戏详情
     */
    fun getGameDetailById(gameId: String, isRefresh: Boolean) = viewModelScope.launch {
        //远端添加字段,则不能使用缓存的数据,需要使用远程数据
        repository.fetchGameInfoByIdFromRemoteWithCache(gameId)
            .filterNot {
                it is DataResult.Error && _gameDetailFlow.value?.first is DataResult.Success
            }
            .collect {
                val lastGameDetailInfo = _gameDetailFlow.value?.first?.data
                if (!isRefresh) {
                    if (lastGameDetailInfo != null && lastGameDetailInfo == it.data) return@collect
                }
                _gameDetailFlow.value = it to System.currentTimeMillis()
                _followLiveData.value = it.data?.author?.follow ?: false
                _shareCountLiveData.value = it.data?.shareCount ?: 0L
            }
    }

    /**
     * 获取进入详情页次数
     */
    fun getGameDetailEnteredTimes(gameId: String): Long {
        return repository.getGameDetailEnteredTimes(gameId)
    }

    fun getCurrentGameInfo(): GameDetailInfo? {
        return _gameDetailFlow.value?.first?.data
    }

    fun follow() = viewModelScope.launch {
        val authorId = authorUuid ?: return@launch
        val oldFollow = _followLiveData.value ?: return@launch
        val toFollow = !oldFollow
        Analytics.track(
            EventConstants.EVENT_FOLLOW_CLICK,
            "userid" to authorId,
            "location" to "3",
            "type" to (if (toFollow) "1" else "2"),
            "creation_type" to "1"
        )
        if (toFollow) {
            repository.relationAdd(authorId, RelationType.Follow.value)
        } else {
            repository.relationDel(authorId, RelationType.Follow.value)
        }.collect {
            if (it.succeeded) {
                EventBus.getDefault().post(UserFollowEvent(authorId, toFollow, UserFollowEvent.FROM_GAME_DETAIL))

                _followLiveData.value = toFollow
            } else {
                it.message?.let { message ->
                    toastLifeCallback.dispatchOnMainThread { invoke(message) }
                }
            }
        }
    }

    fun follow(isFollow: Boolean) {
        if (_followLiveData.value != isFollow) {
            _followLiveData.value = isFollow
        }
    }

    fun isMe(uuid: String?) = accountInteractor.isMe(uuid)

    fun shareCountIncrement() {
        _shareCountLiveData.value = (_shareCountLiveData.value ?: 0L) + 1
    }

    @Subscribe
    fun onUserFollowEvent(event: UserFollowEvent) {
        if (isMe(event.uuid)
            || authorUuid != event.uuid
            || _followLiveData.value == event.followStatus
        ) return
        follow(event.followStatus)
    }

    override fun onCleared() {
        unregisterHermes()
        super.onCleared()
    }
}