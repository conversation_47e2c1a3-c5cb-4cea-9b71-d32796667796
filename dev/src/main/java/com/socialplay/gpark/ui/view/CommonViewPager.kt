package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.viewpager.widget.ViewPager

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/08/09
 *     desc   : 控制ViewPager是否可以滑动
 *
 */
class CommonViewPager : ViewPager {

    var canScroll = true
    var isSmoothScroll = true

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)


    override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
        kotlin.runCatching {
            return shouldTouchEvent() && super.onInterceptTouchEvent(ev)
        }
        return false
    }

    override fun onTouchEvent(ev: MotionEvent?): Boolean {
        kotlin.runCatching {
            return shouldTouchEvent() && super.onTouchEvent(ev)
        }
        return false
    }

    private fun shouldTouchEvent(): <PERSON>olean {
        return canScroll
    }

    override fun setCurrentItem(item: Int) {
        super.setCurrentItem(item, isSmoothScroll)
    }

}