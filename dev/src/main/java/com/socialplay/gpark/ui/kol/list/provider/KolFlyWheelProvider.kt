package com.socialplay.gpark.ui.kol.list.provider

import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.RequestManager
import com.chad.library.adapter.base.provider.BaseItemProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.creator.CreatorMultiInfo
import com.socialplay.gpark.data.model.creator.CreatorMultiType
import com.socialplay.gpark.data.model.creator.CreatorOperationListInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.ui.kol.list.IKolCreatorAdapterListener
import com.socialplay.gpark.ui.kol.list.adapter.KolFlyWheelAdapter
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setPaddingEx
import kotlin.math.ceil

/**
 * Kol飞轮位
 */
class KolFlyWheelProvider(
    val glide: RequestManager,
    val listener: IKolCreatorAdapterListener?
) : BaseItemProvider<CreatorMultiInfo>() {

    override val layoutId: Int = R.layout.provider_creator_fly_wheel

    override val itemViewType: Int = CreatorMultiType.TYPE_OPERATION_FLY_WHEEL

    companion object {
        const val SHOW_ITEM_COUNT = 4.6F
        const val RV_KEY_FLYWHEEL = "flywheel"
    }

    override fun convert(helper: BaseViewHolder, item: CreatorMultiInfo) {
        val info = item.toOperationListInfo()
        val adapter = initAdapter(helper, info)
        val rv = helper.getView<RecyclerView>(R.id.rvHor)
        listener?.popRvStoredState(RV_KEY_FLYWHEEL)?.let {
            rv.layoutManager?.onRestoreInstanceState(it)
        }
        rv.setPaddingEx(16.dp)
        rv.adapter = adapter
        adapter.setList(info.operationList)
    }

    override fun onViewDetachedFromWindow(holder: BaseViewHolder) {
        holder.getViewOrNull<RecyclerView>(R.id.rvHor)?.layoutManager?.onSaveInstanceState()?.let {
            listener?.saveRvState(RV_KEY_FLYWHEEL, it)
        }
        super.onViewDetachedFromWindow(holder)
    }

    private fun initAdapter(
        holder: BaseViewHolder,
        info: CreatorOperationListInfo
    ): KolFlyWheelAdapter {
        val itemWidth = calculateItemWidth(holder)
        val adapter =
            KolFlyWheelAdapter(info.operationList.toMutableList(), itemWidth, glide)
        adapter.setOnItemShowListener { item, position ->
            Analytics.track(
                EventConstants.UGC_TAB_EVENT_SHOW,
                EventParamConstants.KEY_OPERATION_ID to item.id.toString(),
                EventParamConstants.KEY_TITLE to item.title.toString(),
                EventParamConstants.KEY_TYPE to EventConstants.Type.TYPE_OPERATION_KOL_FLYWHEEL
            )
        }
        adapter.setOnItemClickListener { _, view, position ->
            val item = adapter.getItem(position)
            Analytics.track(
                EventConstants.UGC_TAB_EVENT_CLICK,
                EventParamConstants.KEY_OPERATION_ID to item.id.toString(),
                EventParamConstants.KEY_TITLE to item.title.toString(),
                EventParamConstants.KEY_TYPE to EventConstants.Type.TYPE_OPERATION_KOL_FLYWHEEL
            )
            listener?.goUniJump(
                item,
                LinkData.SOURCE_KOL_FLY_WHEEL,
                CategoryId.CATEGORY_ID_KOL_FLY_WHEEL
            )
            if (item.jumpType == UniJumpConfig.POS_DAILY_TASK_REWARD) {
                listener?.notifyNeedUpdateDailyTaskStatus()
            }
        }
        return adapter
    }

    private fun calculateItemWidth(holder: BaseViewHolder): Int {
        return if (holder.isPad) {
            holder.dp(64)
        } else {
            val space = holder.dp(16)
            val allSpaceWidth = ceil(SHOW_ITEM_COUNT).toInt() * space
            val itemWidth = (holder.screenWidth - allSpaceWidth) / SHOW_ITEM_COUNT
            itemWidth.toInt()
        }
    }
}