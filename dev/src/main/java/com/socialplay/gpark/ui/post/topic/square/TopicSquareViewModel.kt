package com.socialplay.gpark.ui.post.topic.square

import android.content.ComponentCallbacks
import androidx.lifecycle.Observer
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.CommunityFeedWrapper
import com.socialplay.gpark.data.model.post.CommunityTopicBlockWrap
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.request.PostTagFeedRequest
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.post.feed.base.BaseCommunityFeedViewModel
import com.socialplay.gpark.ui.post.feed.base.ICommunityFeedModelState
import com.socialplay.gpark.util.ToastData
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flow
import org.koin.android.ext.android.get

/**
 * Created by bo.li
 * Date: 2024/4/7
 * Desc:
 */
data class TopicSquareModelState(
    override val refresh: Async<List<CommunityFeedInfo>> = Uninitialized,
    override val toastMsg: ToastData = ToastData.EMPTY,
    override val loadMore: Async<LoadMoreState> = Uninitialized,
    override val nextPage: Int = 1,
    override val notifyCheckVideo: Async<Long> = Uninitialized,
    val myTopics: Async<List<PostTag>?> = Uninitialized,
    val liveRooms: List<ChatRoomInfo>? = null,
    val currentUuid: String? = null
) : ICommunityFeedModelState {

    override fun updateFeedData(list: List<CommunityFeedInfo>): ICommunityFeedModelState {
        return copy(refresh = refresh.copyEx(list))
    }

    override fun toast(toastMsg: ToastData): ICommunityFeedModelState {
        return copy(toastMsg = toastMsg)
    }

    override fun checkVideo(checkVideo: Async<Long>): ICommunityFeedModelState {
        return copy(notifyCheckVideo = checkVideo)
    }

    override fun feedRefresh(result: Async<CommunityFeedWrapper>): ICommunityFeedModelState {
        val newRefresh = result.map { wrapper ->
            wrapper.dataList.distinctBy { it.postId }
        }
        return copy(
            refresh = newRefresh,
            nextPage = nextPage + if (result is Success) 1 else 0,
            loadMore = result.map { LoadMoreState(result()?.end ?: true) }
        )
    }

    override fun feedLoadMore(result: Async<CommunityFeedWrapper>): ICommunityFeedModelState {
        return copy(
            refresh = if (result is Success) {
                val oldList = refresh.invoke()
                result.map { wrapper ->
                    if (oldList.isNullOrEmpty()) {
                        wrapper.dataList
                    } else {
                        oldList + wrapper.dataList
                    }.distinctBy { it.postId }
                }
            } else {
                refresh
            },
            nextPage = nextPage + if (result is Success) 1 else 0,
            loadMore = result.map { LoadMoreState(result()?.end ?: true) }
        )
    }
}

class TopicSquareViewModel(
    private val repository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    initialState: TopicSquareModelState
) : BaseCommunityFeedViewModel<TopicSquareModelState>(repository, accountInteractor, initialState) {

    init {
        // [java.lang.reflect.InvocationTargetException] 继承BaseCommunityFeedViewModel时，init代码块里加东西，可能会导致fragmentViewModel找不到类
    }

    private val accountObserve = Observer<MetaUserInfo?> {
        it ?: return@Observer
        withState { oldState ->
            if (oldState.myTopics is Success && !it.uuid.isNullOrEmpty() && it.uuid != oldState.currentUuid) {
                refreshMyTopic()
            }
        }
    }

    fun topicObserveAccount() {
        setState {
            copy(currentUuid = accountInteractor.curUuid)
        }
        accountInteractor.accountLiveData.observeForever(accountObserve)
    }

    override fun onCleared() {
        accountInteractor.accountLiveData.removeObserver(accountObserve)
        super.onCleared()
    }

    @Throws
    private fun refreshFeedFlow() = flow {
        emit(
            repository.getCommunityFeed(
                PostTagFeedRequest.ORDER_TYPE_NEWEST,
                PostTagFeedRequest.POST_TYPE_TOPIC,
                null,
                null,
                PAGE_SIZE,
                1
            ).map {
                notifyCheckVideo()
                it
            }.invoke()
        )
    }

    @Throws
    private fun fetchLiveRooms() = flow {
        if (PandoraToggle.topicSquareShowLiveRoom) {
            val result = repository.getHomeRoomListV2(
                1,
                LIVE_ROOM_SIZE
            ).invoke().dataList?.distinctBy { it.roomId }
            emit(result)
        } else {
            emit(null)
        }
    }

    @Throws
    private fun refreshMyTopicsFlow() = flow {
        emit(repository.fetchMyFollowTopics(TOPIC_PAGE_SIZE, 1).invoke().dataList?.distinctBy { it.tagId })
    }

    fun refresh() {
        combine(
            refreshMyTopicsFlow(),
            refreshFeedFlow(),
            fetchLiveRooms()
        ) { topic, feed, liveRoom ->
            Triple(topic, feed, liveRoom)
        }.execute { result ->
            copy(
                myTopics = result.map { it.first },
                liveRooms = result.map { it.third }(),
                currentUuid = result.map { accountInteractor.curUuidOrNull }()
            ).feedRefresh(result.map { it.second }) as TopicSquareModelState
        }
    }

    private fun refreshMyTopic() {
        withState {
            if (it.myTopics is Loading) return@withState
            refreshMyTopicsFlow().execute { result ->
                copy(myTopics = result)
            }
        }
    }

    fun loadMoreFeed() {
        withState { oldState ->
            if (oldState.loadMore is Loading) return@withState
            val nextPage = oldState.nextPage
            repository.getCommunityFeed(
                1,
                PostTagFeedRequest.POST_TYPE_TOPIC,
                null,
                null,
                PAGE_SIZE,
                nextPage
            ).execute { result ->
                feedLoadMore(result) as TopicSquareModelState
            }
        }
    }

    companion object : KoinViewModelFactory<TopicSquareViewModel, TopicSquareModelState>() {

        private const val PAGE_SIZE = 20
        private const val TOPIC_PAGE_SIZE = 10
        private const val LIVE_ROOM_SIZE = 3

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: TopicSquareModelState
        ): TopicSquareViewModel {
            return TopicSquareViewModel(get(), get(), state)
        }
    }
}