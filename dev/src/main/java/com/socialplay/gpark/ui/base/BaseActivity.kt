package com.socialplay.gpark.ui.base

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import androidx.annotation.CallSuper
import androidx.appcompat.app.AppCompatActivity
import androidx.viewbinding.ViewBinding
import com.socialplay.gpark.function.locale.MetaLanguages
import com.socialplay.gpark.ui.locale.LanguageSettingFragment
import com.socialplay.gpark.util.extension.LifecycleCallback
import com.socialplay.gpark.util.property.ViewBindingLifecycleOwner
import com.socialplay.gpark.util.property.ViewBindingLifecycleOwnerProvider
import timber.log.Timber

/**
 * Created by yaqi.liu on 2021/5/7
 */
abstract class BaseActivity : AppCompatActivity(), ViewBindingLifecycleOwnerProvider {

    protected abstract val binding: ViewBinding
    private var viewBindingLifecycleOwner: ViewBindingLifecycleOwner? = null
    private val onNewIntentCallback: LifecycleCallback<OnNewIntentIntentCallback> by lazy { LifecycleCallback() }
    protected var needSetContent = true

    @CallSuper
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewBindingLifecycleOwner = ViewBindingLifecycleOwner()
        if (needSetContent) setContentView(binding.root)
    }

    /**
     * 目前剔除广告AdActivity
     */
    open fun isNeedAnalytics(): Boolean = true

    @CallSuper
    override fun onNewIntent(intent: Intent?) {
        onNewIntentCallback.dispatch {
            invoke(intent)
        }
        super.onNewIntent(intent)
    }

    fun observerOnNewIntentCallback(callback: OnNewIntentIntentCallback) {
        onNewIntentCallback.observe(this, callback)
    }

    override fun onDestroy() {
        super.onDestroy()
        viewBindingLifecycleOwner?.onDestroyView()
        viewBindingLifecycleOwner = null
    }

    override fun viewBindingLayoutInflater(): LayoutInflater {
        return layoutInflater
    }

    override fun viewBindingLifecycleOwner(): ViewBindingLifecycleOwner {
        return viewBindingLifecycleOwner ?: error("view not create or destroy")
    }

    override fun attachBaseContext(newBase: Context?) {
        Timber.tag(LanguageSettingFragment.TAG).d("attachBaseContext $newBase ${this.javaClass}")
        super.attachBaseContext(MetaLanguages.attachContext(newBase))
    }
}

typealias OnNewIntentIntentCallback = (intent: Intent?) -> Unit