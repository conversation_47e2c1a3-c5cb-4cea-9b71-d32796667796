package com.socialplay.gpark.ui.developer.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.socialplay.gpark.databinding.AdapterDeveloperActionItemBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.developer.bean.DevAction

/**
 * xingxiu.hou
 * 2021/6/7
 */
class ActionAdapter : BaseAdapter<DevAction, AdapterDeveloperActionItemBinding>() {

    override fun createViewBinding(layoutInflater: LayoutInflater, parent: ViewGroup, viewType: Int): AdapterDeveloperActionItemBinding {
        return AdapterDeveloperActionItemBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(holder: BindingViewHolder<AdapterDeveloperActionItemBinding>, item: DevAction, position: Int) {
        holder.binding.tvTitle.text = item.name
    }

}