package com.socialplay.gpark.ui.im.conversation.provider

import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.ly123.tes.mgs.im.base.IContainerItemProvider
import com.ly123.tes.mgs.im.base.MessageListAdapter
import com.ly123.tes.mgs.im.model.ProviderTag
import com.ly123.tes.mgs.im.model.UIMessage
import com.ly123.tes.mgs.metacloud.message.CustomShareTopicMessage
import com.ly123.tes.mgs.metacloud.model.Message
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.share.ShareContent
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.dp

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/04/11
 *     desc   :
 *
 */
@ProviderTag(messageContent = CustomShareTopicMessage::class, showReadState = false, showWarning = true, showSenderPortrait = true)
class ShareTopicMessageItemProvider : IContainerItemProvider.MessageProvider<CustomShareTopicMessage>() {
    override fun newView(context: Context?, viewGroup: ViewGroup?): View {
        val view = LayoutInflater.from(context).inflate(R.layout.item_im_share_topic, null as ViewGroup?)
        view.tag = ViewHolder(view)
        return view
    }

    override fun bindView(
        view: View,
        position: Int,
        content: CustomShareTopicMessage?,
        message: UIMessage?,
        messageClickListener: MessageListAdapter.OnMessageClickListener?
    ) {
        val holder: ViewHolder = view.tag as ViewHolder
        val data = GsonUtil.gsonSafeParse<PostTag>(content?.content)
        holder.tvTitle.text = data?.tagName
        if (message?.messageDirection == Message.MessageDirection.SEND) {
            holder.root.setBackgroundResource(R.drawable.bg_chat_message_send)
            holder.root.setPadding(14.dp, 8.dp, 28.dp, 8.dp)
            holder.tvTitle.setTextColor(ContextCompat.getColor(view.context, R.color.textColorPrimary))
        } else {
            holder.root.setBackgroundResource(R.drawable.bg_chat_message_received)
            holder.root.setPadding(28.dp, 8.dp, 14.dp, 8.dp)
            holder.tvTitle.setTextColor(
                ContextCompat.getColor(
                    view.context,
                    R.color.textColorPrimary
                )
            )
        }
        view.setOnClickListener {
            data?.tagId?.let { it1 -> messageClickListener?.postTopicCardClick(it1.toString(), data.tagName?:"") }
        }
    }



    override fun getContentSummary(context: Context?, data: CustomShareTopicMessage?): Spannable {
        return SpannableString(context?.getString(R.string.share_topic_content))
    }

    override fun onItemClick(var1: View?, var2: Int, var3: CustomShareTopicMessage?, var4: UIMessage?, messageClickListener: MessageListAdapter.OnMessageClickListener?) {
    }

    private class ViewHolder(view: View) {
        var tvTitle: TextView = view.findViewById(R.id.tv_title)
        var root : View = view.findViewById(R.id.cl_root)

    }

}