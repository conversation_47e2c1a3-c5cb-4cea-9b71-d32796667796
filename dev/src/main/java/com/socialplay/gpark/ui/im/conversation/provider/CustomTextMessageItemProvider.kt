package com.socialplay.gpark.ui.im.conversation.provider

import com.socialplay.gpark.R
import android.app.Activity
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.net.Uri
import android.text.Spannable
import android.text.SpannableString
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import androidx.core.content.ContextCompat
import androidx.core.widget.PopupWindowCompat
import com.ly123.tes.mgs.im.base.IContainerItemProvider
import com.ly123.tes.mgs.im.base.MessageListAdapter
import com.ly123.tes.mgs.im.model.ProviderTag
import com.ly123.tes.mgs.im.model.UIMessage
import com.ly123.tes.mgs.im.view.AutoLinkTextView
import com.ly123.tes.mgs.metacloud.message.RecallNotificationMessage
import com.ly123.tes.mgs.metacloud.message.TextMessage
import com.ly123.tes.mgs.metacloud.model.Message
import com.ly123.tes.mgs.metacloud.origin.GroupAtInfo
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.extension.dp


@ProviderTag(messageContent = TextMessage::class, showReadState = false, showWarning = true, showSenderPortrait = true)
class CustomTextMessageItemProvider : IContainerItemProvider.MessageProvider<TextMessage>() {
    override fun newView(context: Context, group: ViewGroup): View {
        val view = LayoutInflater.from(context).inflate(R.layout.rc_item_destruct_text_message, null as ViewGroup?)
        val holder = ViewHolder()
        holder.message = view.findViewById<AutoLinkTextView>(R.id.tv_message)
        view.tag = holder
        return view
    }


    override fun getContentSummary(context: Context?, data: TextMessage?): Spannable? {
        return when {
            data == null -> {
                null
            }

            data.isDestruct -> {
                SpannableString(context?.getString(R.string.rc_message_content_burn))
            }

            else -> {
                try {
                    var content: String? = data.content
                    if (content != null) {
                        if (content.length > 100) {
                            content = content.substring(0, 100)
                        }
                        SpannableString(context?.let { content })
                    } else {
                        null
                    }
                } catch (e: Exception) {
                    null
                }
            }
        }
    }

    override fun onItemClick(view: View, position: Int, content: TextMessage?, message: UIMessage, onMessageClickListener: MessageListAdapter.OnMessageClickListener?) {
        val holder = view.tag as ViewHolder
        if (content != null && content.isDestruct && message.message.readTime <= 0L) {
            this.processTextView(view, position, content, message, holder.message, onMessageClickListener)
        }

    }

    override fun bindView(v: View, position: Int, content: TextMessage, data: UIMessage, messageClickListener: MessageListAdapter.OnMessageClickListener?) {
        val holder = v.tag as ViewHolder
        if (data.messageDirection == Message.MessageDirection.SEND) {
            holder.message?.setBackgroundResource(R.drawable.bg_chat_message_send)
            holder.message?.setTextColor(ContextCompat.getColor(v.context, R.color.color_1A1A1A))
            holder.message?.setPadding(14.dp, 14.dp, 28.dp, 14.dp)
        } else {
            holder.message?.setBackgroundResource(R.drawable.bg_chat_message_received)
            holder.message?.setTextColor(ContextCompat.getColor(v.context, R.color.color_17191C))
            holder.message?.setPadding(28.dp, 14.dp, 14.dp, 14.dp)
        }
        val textView = holder.message
        this.processTextView(v, position, content, data, textView, messageClickListener)
    }

    private fun processTextView(
        v: View,
        position: Int,
        content: TextMessage,
        data: UIMessage,
        pTextView: AutoLinkTextView?,
        onMessageClickListener: MessageListAdapter.OnMessageClickListener?,
    ) {
        pTextView ?: return
        try {
            val groupAtInfoList = data.groupAtInfoList
            val text = if (content.content.isNullOrEmpty() || groupAtInfoList.isNullOrEmpty()) {
                content.content
            } else {
                val context = pTextView.context
                val originText = content.content ?: ""
                var flag = 0
                val sb = SpannableHelper.Builder()
                groupAtInfoList.forEach { atInfo ->
                    if (atInfo != null && atInfo.isRangeOk(originText.length)) {
                        val index = atInfo.start
                        if (flag < index) {
                            sb.text(originText.substring(flag, index))
                                .textAppearance(
                                    context,
                                    R.style.MetaTextView_S14_PoppinsRegular400
                                )
                                .colorRes(R.color.color_17191C)
                        }
                        val nickname = when (atInfo.atType) {
                            GroupAtInfo.AT_TYPE_MEMBER -> {
                                atInfo.memberInfo?.nickname ?: originText.substring(
                                    atInfo.start,
                                    atInfo.end
                                )
                            }

                            GroupAtInfo.AT_TYPE_ALL -> {
                                context.getString(R.string.group_chat_at_all_value)
                            }

                            else -> {
                                ""
                            }
                        }
                        if (nickname.isNotEmpty()) {
                            sb.text("@$nickname")
                                .textAppearance(
                                    pTextView.context,
                                    R.style.MetaTextView_S14_PoppinsSemiBold600
                                )
                                .colorRes(R.color.color_4AB4FF)
                        }
                        flag = atInfo.end
                    }
                }
                if (flag < originText.length) {
                    sb.text(originText.substring(flag))
                        .textAppearance(
                            pTextView.context,
                            R.style.MetaTextView_S14_PoppinsRegular400
                        )
                        .colorRes(R.color.color_17191C)
                }
                sb.build()
            }
            pTextView?.text = text
            //todo schema跳转
            pTextView?.setOnLongClickListener {
                popWindow(v.context, v, data, v, onMessageClickListener)
                true
            }
            pTextView?.stripUnderlines()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 跳转到我们自己的页面
     */
    private fun gotoActivityByScheme(link: String, context: Context) {
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(link))
        if (context !is Activity) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        context.startActivity(intent)
    }

    private fun popWindow(context: Context, messageView: View, message: UIMessage, v: View, onMessageClickListener: MessageListAdapter.OnMessageClickListener?) {
        val view = LayoutInflater.from(context).inflate(R.layout.view_message_manager, null, false)
        val pw = PopupWindow(view, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, true)
        pw.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        pw.isOutsideTouchable = false // 设置是否允许在外点击使其消失，到底有用没？
        view.setOnClickListener {
            pw.dismiss()
            copyMessage(context, message)
            onMessageClickListener?.copy(message.message)
        }

        view.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)


        val height = -ScreenUtil.dp2px(context, 28f)
        // 消息的一半
        val px = view.measuredWidth / 2

        val vx = messageView.measuredWidth / 2
        if (message.messageDirection == Message.MessageDirection.SEND) {
            PopupWindowCompat.showAsDropDown(pw, v, vx - px, height, Gravity.START or Gravity.TOP)
        } else {
            PopupWindowCompat.showAsDropDown(pw, messageView, vx - px, height, Gravity.TOP)
        }
    }

    /*撤回*/
    private fun recallMessage(context: Context, message: UIMessage) {

    }

    /*检查能否撤回 */
    private fun isRecall(message: UIMessage): Boolean {
        return false
    }

    /*复制*/
    private fun copyMessage(context: Context, message: UIMessage) {
        val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        if (message.content is RecallNotificationMessage) {
        } else {
            if (message.content is TextMessage) {
                clipboard.text = (message.content as TextMessage).content
            }
        }
    }

    private class ViewHolder {
        var message: AutoLinkTextView? = null
    }

}
