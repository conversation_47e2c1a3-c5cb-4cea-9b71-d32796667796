package com.socialplay.gpark.ui.kol.list.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.KolCreatorInfo
import com.socialplay.gpark.databinding.AdapterFollowedCreatorBinding
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.ui.base.adapter.BasicQuickAdapter
import com.socialplay.gpark.util.extension.setSize

/**
 * Created by bo.li
 * Date: 2024/8/5
 * Desc: ugc Kol创作者列表-样式1
 */
class FollowedCreatorAdapter(
    data: MutableList<KolCreatorInfo>,
    private val itemWidth: Int,
    private val glide: RequestManager
) :
    BasicQuickAdapter<KolCreatorInfo, AdapterFollowedCreatorBinding>(data) {
    override fun convert(
        holder: BaseVBViewHolder<AdapterFollowedCreatorBinding>,
        item: KolCreatorInfo
    ) {
        holder.binding.iv.setSize(itemWidth, itemWidth)
        glide.load(item.avatar).circleCrop().placeholder(R.drawable.icon_default_avatar).into(holder.binding.iv)
        holder.binding.ivOnline.isVisible = item.online
        holder.binding.tvUserName.text = item.nickname
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterFollowedCreatorBinding {
        return AdapterFollowedCreatorBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }

}