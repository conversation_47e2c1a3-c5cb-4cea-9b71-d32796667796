package com.socialplay.gpark.ui.main.maps.newgame

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.model.MapsNewestList
import com.socialplay.gpark.data.repository.RecommendRepository
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.mapRetained
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.util.ToastData
import org.koin.android.ext.android.get

data class NewGamesModelState(
    val refresh: Async<List<MapsNewestList>> = Uninitialized,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val toastMsg: ToastData = ToastData.EMPTY,
    val msg: String = "",
) : MavericksState {
    val list: List<MapsNewestList> = refresh.invoke() ?: emptyList()

}

class NewGamesViewModel(
    private val recommendRepository: RecommendRepository, initialState: NewGamesModelState
) : BaseViewModel<NewGamesModelState>(initialState) {
    private var page = 1
    private var pageSize = 20
    private var lastItemOrderNum: Long? = null

    init {
        refresh()
    }

    fun refresh() = withState { oldState ->
        if (oldState.refresh is Loading) return@withState
        page = 1
        lastItemOrderNum = null
        recommendRepository.postMapsNewest(page, pageSize, lastItemOrderNum).map {
            lastItemOrderNum = it.list?.lastOrNull()?.orderNum
            it.list?.distinctBy { it.ugid }.orEmpty()
        }.execute(retainValue = NewGamesModelState::refresh) { result ->
            when (result) {
                is Success -> {
                    copy(
                        refresh = result,
                        loadMore = Uninitialized,
                    )
                }

                else -> {
                    copy(
                        refresh = result
                    )
                }
            }
        }
    }

    fun loadMore() = withState { oldState ->
        if (oldState.list.isEmpty()) return@withState
        if (oldState.loadMore is Loading) return@withState
        page++
        recommendRepository.postMapsNewest(page, pageSize, lastItemOrderNum).execute { result ->

            when (result) {
                is Success -> {
                    lastItemOrderNum = result().list?.lastOrNull()?.orderNum
                    val newData = result().list ?: emptyList()
                    val mergedSet = LinkedHashSet(oldState.list).apply { addAll(newData) }
                    val mergedList = ArrayList(mergedSet)
                    copy(
                        refresh = Success(mergedList),
                        loadMore = Success(LoadMoreState(result().isEnd)),
                    )
                }

                else -> {
                    copy(
                        loadMore = result.mapRetained(loadMore)
                    )
                }
            }
        }
    }

    companion object : KoinViewModelFactory<NewGamesViewModel, NewGamesModelState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext, state: NewGamesModelState
        ): NewGamesViewModel {
            return NewGamesViewModel(get(), state)
        }
    }
}