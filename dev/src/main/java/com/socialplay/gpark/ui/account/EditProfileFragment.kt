package com.socialplay.gpark.ui.account

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.navigation.fragment.navArgs
import com.bumptech.glide.Glide
import com.meta.ipc.IPC
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BanBlockInteractor
import com.socialplay.gpark.data.model.user.Gender
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.databinding.FragmentEditProfileBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants.IMG_PRE_FROM_EDIT_PROFILE
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.ipc.provider.ts.IInGameIntentStarter
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivityArgs
import com.socialplay.gpark.ui.imgpre.ImgPreDialogFragment
import com.socialplay.gpark.util.DateUtil.formatYmdDate
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.*
import com.socialplay.gpark.util.property.viewBinding
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber
import java.util.*


class EditProfileFragment : BaseFragment<FragmentEditProfileBinding>() {

    private val viewModel by viewModel<EditProfileViewModel>()

    private val accountInteractor: AccountInteractor by inject()
    private val banBlockInteractor: BanBlockInteractor by inject()
    private val ipc by lazy { IPC.getInstance() }
    private val timePickerObserver by lazy {
        com.socialplay.gpark.function.account.TimePickerObserver(
            this
        )
    }
    private val args: EditProfileFragmentArgs by navArgs<EditProfileFragmentArgs>()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentEditProfileBinding? {
        return FragmentEditProfileBinding.inflate(inflater, container, false)
    }

    override fun getFragmentName() = PageNameConstants.FRAGMENT_NAME_EDIT_PROFILE

    override fun init() {
        initView()
        initData()
    }


    @SuppressLint("ClickableViewAccessibility")
    private fun initView() {
        binding.root.curFoucsViewListener = {
            activity?.currentFocus
        }
        binding.tblTitleBar.setOnBackClickedListener {
            goBack()
        }
        binding.clAvatar.setOnAntiViolenceClickListener {
            val userInfo = accountInteractor.accountLiveData.value
            val array = arrayOf(userInfo?.portrait ?: "")
            val imgAction = getString(R.string.image_dialog_change_avatar)
            ImgPreDialogFragment.show(requireActivity(), array, IMG_PRE_FROM_EDIT_PROFILE, 0, true, imgAction)?.setOnAction {
                dismiss()
                Analytics.track(EventConstants.EVENT_GAME_AVATAR_LAUNCH) {
                    putAll(ResIdUtils.getAnalyticsMap(ResIdBean().setCategoryID(CategoryId.JUMP_ROLE_GAME_FROM_PROFILE_PHOTO)))
                    put("from", "profile_photo")
                }
                MetaRouter.MobileEditor.fullScreenRole(
                    requireContext(),
                    FullScreenEditorActivityArgs(categoryId = CategoryId.JUMP_ROLE_GAME_FROM_PROFILE_PHOTO)
                )
            }
        }
        binding.clName.setOnClickListener {
            MetaRouter.Account.nickNameReset(
                this,
                binding.tvName.text.toString()
            )
        }
        binding.clGender.setOnAntiViolenceClickListener {
            timePickerObserver.showGenderPicker(
                viewModel.genderLiveData.value,
                { viewModel.postGenderData(it) }
            ) {}
        }
        binding.clCity.setOnClickListener {
            MetaRouter.Account.cityReset(
                this,
                binding.tvCity.text.toString()
            )
        }
        binding.clInformation.setOnClickListener {
            MetaRouter.Account.informationReset(this, accountInteractor.accountLiveData.value?.signature)
        }
        binding.clBirthday.setOnClickListener {
            timePickerObserver.showDatePicker(viewModel.getWheelDefaultDate(), { date ->
                if (isVisible && !isStateSaved && !isDetached) {
                    setBirthText(date)
                    viewModel.updateUserBirthday(date, args.gameId)
                }
            })
        }
        binding.clLink.setOnClickListener {
            MetaRouter.Account.editLink(this, GsonUtil.safeToJson(viewModel.userProfile.value?.externalLinks?:""))
        }


    }

    private fun initData() {
        fillData(accountInteractor.accountLiveData.value)
        viewModel.accountBanLiveData.observe(viewLifecycleOwner) {
            banBlockInteractor.showBanDialog(BanBlockInteractor.REASON_EDIT_PROFILE, this)
        }
        viewModel.isCreate.observe(viewLifecycleOwner){
            binding.clLink.visible(it?:false)
        }
        viewModel.genderChangeLiveData.observe(viewLifecycleOwner) {
            Timber.d("gender data: $it")
            if (it) {
                ToastUtil.showShort(requireContext(), R.string.profile_infomation_save_success)
            }
        }

        viewModel.genderLiveData.observe(viewLifecycleOwner) {
            Timber.d("gender data: $it")
            it?.let {
                val genderText = getGenderText(it)
                if (binding.tvGender.text == genderText) {
                    return@let
                }
                binding.tvGender.text = genderText
                viewModel.updateUserGender(gender = it.value)
            }
        }
        viewModel.getUserProfile()
    }

    private fun getGenderText(gender: Gender): String {
        return getString(
            when (gender) {
                Gender.Male   -> R.string.male
                Gender.Female -> R.string.female
                Gender.Other  -> R.string.other
            }
        )
    }

    override fun onResume() {
        super.onResume()
        //返回按键监听
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
            goBack()
        }
    }

    private fun goBack() {
        val gameId = args.gameId
        if (!gameId.isNullOrEmpty()) {
            backToTsGame(gameId)
        }
        navigateUp()
    }

    private fun backToTsGame(gameId: String) {
        Timber.d("backToTsGame:  $gameId")
        //合并任务栈，不需要IPC
        resumeGameById(gameId)
//        ipc.runSafety(IInGameIntentStarter) {
//            backToGame()
//        }
    }

    override fun loadFirstData() {

    }

    private fun fillData(userInfo: MetaUserInfo?) {
        if (userInfo == null) return

        userInfo.nickname?.let { binding.tvName.text = it }
        if (userInfo.birth != null) {
            setBirthText(Date(userInfo.birth!!))
        }
        val gender = Gender.parse(userInfo.gender) ?: Gender.Other
        viewModel.postGenderData(gender)
        binding.tvGender.text = getGenderText(gender)
        Glide.with(this).load(userInfo.portrait).placeholder(R.drawable.icon_default_avatar)
            .circleCrop()
            .into(binding.ivAvatar)
        userInfo.city?.let { binding.tvCity.text = it }
        binding.tvInformation.text = userInfo.signature ?: getString(R.string.profile_infomation_hint_text)
    }
    private fun setBirthText(birth: Date) {
        binding.tvBirthday.text = birth.formatYmdDate()
    }

}