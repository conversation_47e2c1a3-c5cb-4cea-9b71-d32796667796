package com.socialplay.gpark.ui.main

import android.app.Activity
import android.app.Application
import android.os.Build
import android.os.Bundle
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.handle.AppLaunchAnalytics
import com.socialplay.gpark.util.PackageUtil
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import timber.log.Timber

object HomeImageShowAnalytics {

    private var startLoadSet = mutableSetOf<Int>()
    private var finishLoadSet = mutableSetOf<Int>()
    var startBootTime = 0L
    private var end = false
    private var onEnterGuideLogin = false

    private var applicationOnCreateTime = 0L
    private var application: Application? = null
    private var activityCreateDuration = 0L

    fun onEnterGuideLogin() {
        if (end) return
        onEnterGuideLogin = true
        Timber.tag("HomeImageShow").d("onEnterGuideLogin")
    }

    private val activityCallback: Application.ActivityLifecycleCallbacks by lazy {
        object : Application.ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                if (startBootTime <= 0) {
                    onBootFail("startBootTime <= 0")
                    return
                }
                if (applicationOnCreateTime <= 0) {
                    onBootFail("applicationOnCreateTime <= 0")
                    return
                }
                if (savedInstanceState != null) {
                    // 恢复页面
                    onBootFail("savedInstanceState not null")
                    return
                }
                if (activityCreateDuration != 0L) {
                    onBootFail("alreadyCreated")
                    return
                }
                if (activity !is MainActivity){
                    onBootFail("activity !is MainActivity")
                    return
                }
                val referrerString = activity.referrer?.toString()
                if (referrerString.isNullOrEmpty() || !PackageUtil.isLauncher(
                        referrerString,
                        activity
                    )
                ) {
                    onBootFail("referrerString is null or not launcher")
                    return
                }
                val time = System.currentTimeMillis() - applicationOnCreateTime
                if (time > 4000 || time < 0) {
                    onBootFail("activityOnCreateTime > 5000 || activityOnCreateTime < 0 $time")
                    return
                }
                activityCreateDuration = System.currentTimeMillis() - startBootTime
                Timber.tag("HomeImageShow")
                    .d("onActivityCreated ${activity.javaClass} $activityCreateDuration")
            }

            override fun onActivityStarted(activity: Activity) {
            }

            override fun onActivityResumed(activity: Activity) {
            }

            override fun onActivityPaused(activity: Activity) {
            }

            override fun onActivityStopped(activity: Activity) {
                Timber.tag("HomeImageShow").d("onActivityStopped ${activity.javaClass}")
                onBootFail("onActivityStopped")
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
            }

            override fun onActivityDestroyed(activity: Activity) {
            }

        }
    }

    fun onApplicationCreate(app: Application) {
        applicationOnCreateTime = System.currentTimeMillis()
        if (startBootTime <= 0 || applicationOnCreateTime - startBootTime > 8000) {
            onBootFail("startBootTime <= 0 || applicationOnCreateTime - startBootTime > 8000")
            return
        }
        application = app
        app.registerActivityLifecycleCallbacks(activityCallback)
    }

    fun onHomeStartLoadImage(url: String?, position: Int) {
        if (end) return
        if (url == null) return
        startLoadSet.add(position)
        if (BuildConfig.DEBUG) {
            Timber.tag("HomeImageShow").d("onHomeStartLoadImage startLoadSet ${startLoadSet.size}, finishLoadSet ${finishLoadSet.size}")
        }
    }

    fun onHomeFinishLoadImage(url: String?, position: Int) {
        if (end) return
        if (url == null) return
        finishLoadSet.add(position)
        if (BuildConfig.DEBUG) {
            Timber.tag("HomeImageShow").d("onHomeFinishLoadImage startLoadSet ${startLoadSet.size}, finishLoadSet ${finishLoadSet.size}")
        }
        if (startLoadSet.size <= finishLoadSet.size) {
            onEnd("recommend")
        }
    }

    fun onHomeApiFail() {
        if (end) return
        onBootFail("api_fail")
    }

    fun onSelectItem(mainBottomNavigationItem: MainBottomNavigationItem?) {
        if (end) {
            return
        }
        if (mainBottomNavigationItem == null) {
            return
        }
        if (mainBottomNavigationItem != MainBottomNavigationItem.PARTY) {
            onBootFail("onSelectItem $mainBottomNavigationItem")
            return
        }
    }

    fun onSuperRecommendGameIconShown(status: String) {
        onEnd("dialog_$status")
    }

    private fun onEnd(status: String) {
        if (startBootTime == 0L) {
            Timber.tag("HomeImageShow").d("onEnd startBootTime == 0L")
            return
        }
        if (end) {
            Timber.tag("HomeImageShow").d("onEnd end")
            return
        }

        if (activityCreateDuration <= 0){
            Timber.tag("HomeImageShow").d("onEnd activityCreateDuration <= 0")
            onBootFail("activityCreateDuration0")
            return
        }

        val l = System.currentTimeMillis() - startBootTime
        if (l > 50000) {
            Timber.tag("HomeImageShow").d("onEnd invalidTime $status $l")
            onBootFail("invalidTime50000")
            return
        }
        if (l < 200) {
            Timber.tag("HomeImageShow").d("onEnd invalidTime $status $l")
            onBootFail("invalidTime200")
            return
        }

        if (l < activityCreateDuration) {
            Timber.tag("HomeImageShow").d("onEnd invalidTime $status $l")
            onBootFail("invalidTime-act")
            return
        }

        if (alreadyEnd()) {
            return
        }

        Timber.tag("HomeImageShow").d("onEnd $status $l")
        Analytics.track(
            EventConstants.EVENT_FEED_COLD_START_SHOW,
            "boot_invalid_status" to if (onEnterGuideLogin) 1 else -2,
            "cost_time" to l,
            "end_status" to status
        )
    }

    fun onMainFragmentStop() {
        if (end) return
        Timber.tag("HomeImageShow").d("onMainFragmentStop")
        onBootFail("onMainFragmentStop")
    }

    fun onBootFail(message: String) {
        if (alreadyEnd()) {
            return
        }
        Timber.tag("HomeImageShow").d("onBootFail $message")
    }

    private fun alreadyEnd(): Boolean {
        if (end) {
            return true
        }
        synchronized(this) {
            if (end) {
                return true
            }

            end = true
            application?.unregisterActivityLifecycleCallbacks(activityCallback)
        }
        return false
    }

}