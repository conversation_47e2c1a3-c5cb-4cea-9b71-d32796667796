package com.socialplay.gpark.ui.view

import android.content.Context
import android.os.Build
import android.util.AttributeSet
import com.google.android.material.internal.ThemeEnforcement
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.theme.overlay.MaterialThemeOverlay
import com.socialplay.gpark.R

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2023/08/03
 *     desc   :
 *
 */
class AutoFillTextInputEditText : TextInputEditText {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?): super(context,attrs)

    override fun getAutofillType(): Int {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            //禁止EditText自动填充
            AUTOFILL_TYPE_NONE
        } else {
            super.getAutofillType()
        }

    }
}