package com.socialplay.gpark.ui.gamereview.dialog

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.gamereview.GameAppraiseData
import com.socialplay.gpark.databinding.ItemGameReviewBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.view.UserLabelListener
import com.socialplay.gpark.util.DateUtil.formatWholeDate
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import timber.log.Timber

/**
 * Created by liujiang
 * Date: 2023/6/28 17:10
 * Desc:
 * Version:
 */
//todo 二级评论弃用
class SelfReviewAdapter(
    private val glide: RequestManager,
    private val listener: UserLabelListener
) :
    BaseAdapter<GameAppraiseData, ItemGameReviewBinding>() {

    private var moreCallback: ((view: View, gameReview: GameAppraiseData) -> Unit)? =
        null

    fun setReviewMoreCallback(listener: (view: View, gameReview: GameAppraiseData) -> Unit) {
        this.moreCallback = listener
    }

    private var likeCallback: ((view: View, gameReview: GameAppraiseData) -> Unit)? =
        null

    fun setLikeReviewCallback(listener: (view: View, gameReview: GameAppraiseData) -> Unit) {
        this.likeCallback = listener
    }

    private var sendEventCallback: ((commentId: String) -> Unit)? =
        null

    fun setSendEventCallback(listener: (commentId: String) -> Unit) {
        this.sendEventCallback = listener
    }

    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): ItemGameReviewBinding {
        return ItemGameReviewBinding.inflate(LayoutInflater.from(context), parent, false)
    }

    override fun convert(
        holder: BindingViewHolder<ItemGameReviewBinding>,
        item: GameAppraiseData,
        position: Int
    ) {
        val binding = holder.binding
        item.isShow = true
        binding.root.background = ContextCompat.getDrawable(context, R.drawable.bg_white_16)
        glide.load(item.avatar).placeholder(R.drawable.placeholder_white_round)
            .into(binding.ivGameReviewAvatar)
        binding.tvGameReviewName.text = item.nickname
        binding.tvLike.text = item.likeCount.toString()
        likeItemConvert(binding, item.isLike())
        binding.tvGameReviewContent.text = item.content
        binding.tvPostTime.text = item.commentTime.formatWholeDate()
        binding.ivMyReviewMore.isVisible = true
        binding.ratingbar.rating = item.score.toFloat()
        binding.ivMyReviewMore.setOnAntiViolenceClickListener {
            moreCallback?.invoke(binding.ivMyReviewMore, item)
        }
        binding.imgLike.setOnAntiViolenceClickListener {
            likeItemConvert(binding, !item.isLike())
            likeCallback?.invoke(binding.imgLike, item)
        }
        val canSend = !item.isSendEvent && item.isShow
        Timber.d("canSend=$canSend  item.isSendEvent=${item.isSendEvent} item.isShow=${item.isShow}")
        Timber.d("canSend=$canSend  content= ${item.content}")
        if (canSend) {
            item.isSendEvent = true
            sendEventCallback?.invoke(item.commentId)
        }
        binding.labelGroup.show(item.tagIds, item.userLabelInfo, isMe = true)
    }

    private fun likeItemConvert(binding: ItemGameReviewBinding, isLike: Boolean) {
        if (isLike) {
            binding.imgLike.setImageResource(R.drawable.icon_like_yes_1a)
            binding.tvLike.setTextColorByRes(R.color.color_1A1A1A)
        } else {
            binding.imgLike.setImageResource(R.drawable.icon_like_no_b3)
            binding.tvLike.setTextColorByRes(R.color.color_B3B3B3)
        }
    }

    override fun onViewAttachedToWindow(holder: BindingViewHolder<ItemGameReviewBinding>) {
        super.onViewAttachedToWindow(holder)
        holder.binding.labelGroup.setListener(listener)
    }

    override fun onViewDetachedFromWindow(holder: BindingViewHolder<ItemGameReviewBinding>) {
        holder.binding.labelGroup.setListener(null)
        super.onViewDetachedFromWindow(holder)
    }
}