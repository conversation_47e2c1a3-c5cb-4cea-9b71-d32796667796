package com.socialplay.gpark.ui.dialog

import android.content.DialogInterface
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.annotation.DrawableRes
import androidx.fragment.app.Fragment
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.SimpleListData
import com.socialplay.gpark.databinding.DialogSimpleListBinding
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding

/**
 * create by: bin on 2023/3/8
 */
class ListDialog : BaseDialogFragment() {
    override val binding by viewBinding(DialogSimpleListBinding::inflate)
    private val adapter: SimpleButtonAdapter = SimpleButtonAdapter()
    private var isClickedOutside = true
    private var isOutsideDismiss = true
    private var isBackPressedDismiss = true

    private var title: String? = null
    private var content: String? = null
    @DrawableRes
    private var imageResource: Int = 0
    private var onViewCreateCallback: ((DialogSimpleListBinding) -> Unit)? = null
    private var list = emptyList<SimpleListData>()
    private var clickCallback: ((SimpleListData?) -> Unit)? = null

    /**
     * 当 #clickOutside 关闭弹框时, 会回调null
     *
     * @see ListDialog.onDismiss
     */
    fun clickCallback(clickCallback: ((SimpleListData?) -> Unit)? = null): ListDialog {
        this.clickCallback = clickCallback
        return this
    }

    /**
     * @see ListDialog.onViewCreated 时回调, 此时可自定义改变view
     */
    fun onViewCreateCallback(onViewCreateCallback: ((DialogSimpleListBinding) -> Unit)? = null): ListDialog {
        this.onViewCreateCallback = onViewCreateCallback
        return this
    }

    fun list(list: List<SimpleListData>): ListDialog {
        this.list = list
        return this
    }

    fun title(title: String?): ListDialog {
        this.title = title
        return this
    }

    fun setOutsideDismiss(outsideDismiss:Boolean): ListDialog {
        this.isOutsideDismiss = outsideDismiss
        return this
    }

    fun setBackPressedDismiss(backPressedDismiss:Boolean): ListDialog {
        this.isBackPressedDismiss = backPressedDismiss
        return this
    }

    fun content(content: String?): ListDialog {
        this.content = content
        return this
    }

    fun image(@DrawableRes image: Int): ListDialog {
        this.imageResource = image
        return this
    }

    override fun isClickOutsideDismiss(): Boolean {
        return isOutsideDismiss
    }

    override fun isBackPressedDismiss(): Boolean {
        return isBackPressedDismiss
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        onViewCreateCallback?.invoke(binding)
    }

    override fun init() {
        binding.rv.adapter = adapter
        adapter.setList(list)
        adapter.setOnItemClickListener { view, position ->
            val item = adapter.getItem(position)
            isClickedOutside = false
            clickCallback?.invoke(item)
            dismissAllowingStateLoss()
        }
        binding.title.visible(!title.isNullOrEmpty())
        binding.content.visible(!content.isNullOrEmpty())
        binding.iv.visible(imageResource != 0)
        binding.rv.visible(list.isNotEmpty())

        binding.title.text = title
        binding.content.text = content
        binding.iv.setImageResource(imageResource)
        if (title.isNullOrEmpty() && content.isNullOrEmpty() && imageResource == 0) {
            binding.rv.setMargin(0, 4.dp, 0, 0)
        }
        if (title.isNullOrEmpty() && !content.isNullOrEmpty() && imageResource == 0) {
            binding.content.setMargin(16.dp, 20.dp, 16.dp, 0)
        } else if (title.isNullOrEmpty() && !content.isNullOrEmpty() && imageResource != 0) {
            binding.content.setMargin(16.dp, 16.dp, 16.dp, 0)
        }

        if (isOutsideDismiss) {
            isClickedOutside = true
            binding.root.setOnClickListener { dismissAllowingStateLoss() }
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        if (isClickedOutside) {
            clickCallback?.invoke(null)
        }
        super.onDismiss(dialog)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        if (clickCallback == null && list.isEmpty()) {
            dismissAllowingStateLoss()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        clickCallback = null
        onViewCreateCallback = null
    }

    override fun loadFirstData() {
    }

    override fun getStyle() = R.style.DialogStyleNonFullScreen

    override fun isFullScreen(): Boolean {
        return true
    }

    override fun isHideNavigation(): Boolean {
        return true
    }

    override fun windowHeight(): Int {
        return WindowManager.LayoutParams.MATCH_PARENT
    }
}