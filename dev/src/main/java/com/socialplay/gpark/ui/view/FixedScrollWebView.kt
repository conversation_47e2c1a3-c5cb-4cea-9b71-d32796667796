package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewParent
import android.webkit.WebView
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeApi
import timber.log.Timber

/**
 * @des:解決viewpager2和Webview中有滑动的冲突
 * @author: lijunjia
 * @date: 2022/10/28 17:56
 */

class FixedScrollWebView : WebView {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    private var handleAllEvent = false
    private var parentScrollView: ViewParent? = null
    private var bridgeApi: JsBridgeApi? = null

    fun getJsBridgeApi(): JsBridgeApi? {
        return bridgeApi
    }

    fun addJsBridgeApi(api: JsBridge<PERSON><PERSON>, name: String) {
        bridgeApi = api
        addJavascriptInterface(api, name)
    }

    override fun removeJavascriptInterface(name: String) {
        bridgeApi = null
        super.removeJavascriptInterface(name)
    }

    fun setHandleAllEvent(handleAll: Boolean) {
        this.handleAllEvent = handleAll
    }

    private fun getViewParent(): ViewParent? {
        if (parentScrollView == null) {
            parentScrollView = findViewParentIfNeeds(this, MAX_DEPTH)
        }
        return parentScrollView
    }


    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                getViewParent()?.requestDisallowInterceptTouchEvent(true)
            }

            MotionEvent.ACTION_CANCEL, MotionEvent.ACTION_UP -> {
                getViewParent()?.requestDisallowInterceptTouchEvent(false)
                parentScrollView = null
            }
        }
        return super.onTouchEvent(event)
    }

    /**
     * 查找父布局是是否是可滑动的View
     * @param tag
     * @param depth 最大递归深度，防止出现死循环
     * @return
     */
    private fun findViewParentIfNeeds(tag: View, depth: Int): ViewParent? {
        if (depth < 0) {
            return null
        }
        val parent = tag.parent ?: return null
        if (parent is ViewGroup) {
            return if (canScrollHorizontally(parent as View) || canScrollVertically(parent as View)) {
                parent
            } else {
                // 增加最大递归深度判断，防止出现ANR或者异常
                findViewParentIfNeeds(parent, depth - 1)
            }
        }
        return null
    }

    /**
     * 是否可以横向滑动
     */
    private fun canScrollHorizontally(view: View): Boolean {
        return view.canScrollHorizontally(100) || view.canScrollHorizontally(-100)
    }

    /**
     * 是否可以纵向滑动
     */
    private fun canScrollVertically(view: View): Boolean {
        return view.canScrollVertically(100) || view.canScrollVertically(-100)
    }


    override fun onOverScrolled(scrollX: Int, scrollY: Int, clampedX: Boolean, clampedY: Boolean) {
        Timber.e("scrollX=$scrollX;scrollY=$scrollY;clampedX=$clampedX;clampedY=$clampedY")
        if ((clampedX || clampedY) && !handleAllEvent) {
            getViewParent()?.requestDisallowInterceptTouchEvent(false)
        }

        super.onOverScrolled(scrollX, scrollY, clampedX, clampedY)
    }

    companion object {
        const val MAX_DEPTH = 5
    }
}