package com.socialplay.gpark.ui.main

import android.util.SparseArray
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.core.util.containsKey
import androidx.core.util.forEach
import androidx.fragment.app.Fragment
import com.airbnb.mvrx.asMavericksArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.editor.create.EditorCreateFragment
import com.socialplay.gpark.ui.editor.create.EditorCreateFragmentArgs
import com.socialplay.gpark.ui.editor.create.EditorCreateV2Fragment
import com.socialplay.gpark.ui.editor.create.EditorCreateV2FragmentArgs
import com.socialplay.gpark.ui.editor.home.v2.EditorHomeFragmentV3
import com.socialplay.gpark.ui.editor.home.v2.EditorHomeFragmentV3Args
import com.socialplay.gpark.ui.im.ConversationListFragment
import com.socialplay.gpark.ui.im.ConversationListFragmentArgs
import com.socialplay.gpark.ui.kol.KolCreatorFragment
import com.socialplay.gpark.ui.main.maps.HomeMapsFragment
import com.socialplay.gpark.ui.outfit.UgcDesignFeedFragment
import com.socialplay.gpark.ui.post.tab.CommunityFeedTabFragment
import com.socialplay.gpark.ui.profile.MeProfileFragment
import com.socialplay.gpark.ui.profile.MeProfileFragmentArgs
import com.socialplay.gpark.ui.profile.home.ProfileArgs
import com.socialplay.gpark.ui.profile.home.ProfileFragment
import com.socialplay.gpark.ui.recommend.RecommendFragment

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/05/27
 * desc   :
 * </pre>
 */

typealias FragmentFactory = () -> Fragment

interface OnBottomNavItemReselected {
    fun onBottomNavReselected(item: MainBottomNavigationItem)
}

data class MainBottomNavigationItem private constructor(
    val itemId: Int,
    @StringRes
    val titleRes: Int = -1,

    @DrawableRes
    val iconRes: Int,

    @DrawableRes
    val iconResDark: Int,

    val clickSource: String? = null,
    val factory: FragmentFactory,
) {

    init {
        check(!map.containsKey(itemId)) { "must unique item ID" }
        map.put(itemId, this)
    }

    companion object {
        const val EDITOR_HOME_ITEM_ID = 1
        var hasProfileTab = false
            private set
        var hasCreateTab = false
            private set
        var hasAssetTab = false
            private set

        private val map = SparseArray<MainBottomNavigationItem>()

        fun fetchDevTabDesc(): String {
            val sb = StringBuilder()
            map.forEach { key, value ->
                sb.append(key)
                sb.append("：${value.clickSource}\n")
            }
            sb.append("default：${PandoraToggle.BOTTOM_TAB_TOGGLE_DEFAULT}")
            return sb.toString()
        }

        val EDITOR_HOME = MainBottomNavigationItem(
            EDITOR_HOME_ITEM_ID,
            titleRes = R.string.edit_profile_title,
            iconRes = R.drawable.icon_bottom_navigation_avatar,
            iconResDark = R.drawable.icon_bottom_navigation_profile,
            clickSource = PageNameConstants.FRAGMENT_NAME_PROFILE_TAB
        ) {
            ProfileFragment().apply {
                arguments = ProfileArgs(
                    uuid = "",
                    from = "tab",
                    checkFollow = false,
                    isMe = true,
                    isFromBottom = true,
                    entry = -1
                ).asMavericksArgs()
            }
        }

        val PARTY = MainBottomNavigationItem(
            2,
            titleRes = R.string.home_tab_create_maps,
            iconRes = R.drawable.icon_bottom_navigation_party,
            iconResDark = R.drawable.icon_bottom_navigation_party_dark,
            clickSource = PageNameConstants.FRAGMENT_PARTY
        ) {
//            if (PandoraToggle.isShowNewGames) {
            HomeMapsFragment()
//            } else if (PandoraToggle.isHomeRecommend) {
//                RecommendFragment()
//            } else if (PandoraToggle.isParty) {
//                PartyFragment()
//            } else {
//                ChoiceHomeFragment()
//            }
        }

        val MESSAGE = MainBottomNavigationItem(
            3,
            titleRes = R.string.chat_cap,
            iconRes = R.drawable.icon_bottom_navigation_friend,
            iconResDark = R.drawable.icon_bottom_navigation_friend_dark,
            clickSource = PageNameConstants.FRAGMENT_NAME_FRIEND_TAB
        ) {
            ConversationListFragment().apply {
                arguments = ConversationListFragmentArgs(isFromBottom = true).toBundle()
            }
        }

        val CREATE = MainBottomNavigationItem(
            4,
            titleRes = R.string.create_title,
            iconRes = R.drawable.icon_bottom_navigation_create,
            iconResDark = R.drawable.icon_bottom_navigation_create_dark,
            clickSource = PageNameConstants.FRAGMENT_NAME_CREATE_TAB
        ) {
            if (PandoraToggle.enableUgcBuildV2) {
                EditorCreateV2Fragment().apply {
                    arguments = EditorCreateV2FragmentArgs(fromBottomTab = true).toBundle()
                }
            } else {
                EditorCreateFragment().apply {
                    arguments = EditorCreateFragmentArgs(true).toBundle()
                }
            }
        }

        val PROFILE = MainBottomNavigationItem(
            5,
            titleRes = R.string.edit_profile_title,
            iconRes = R.drawable.icon_bottom_navigation_profile,
            iconResDark = R.drawable.icon_bottom_navigation_profile/*TODO 这个资源还没换，设计图上没有，需要@设计*/,
            clickSource = PageNameConstants.FRAGMENT_NAME_PROFILE_TAB
        ) {
            ProfileFragment().apply {
                arguments = ProfileArgs(
                    uuid = "",
                    from = "tab",
                    checkFollow = false,
                    isMe = true,
                    isFromBottom = true,
                    entry = -1
                ).asMavericksArgs()
            }
        }

        val FEED = MainBottomNavigationItem(
            6,
            titleRes = R.string.feed,
            iconRes = R.drawable.icon_bottom_navigation_feed,
            iconResDark = R.drawable.icon_bottom_navigation_feed_dark,
            clickSource = PageNameConstants.FRAGMENT_NAME_FEED_TAB
        ) {
            CommunityFeedTabFragment()
        }
        val AI_BOT_UGC = MainBottomNavigationItem(
            8,
            titleRes = R.string.recommend_create,
            iconRes = R.drawable.profile_icon,
            iconResDark = R.drawable.profile_icon,
            clickSource = PageNameConstants.FRAGMENT_NAME_AIBOT_TAB
        ) {
            Fragment()
        }

        val CREATOR = MainBottomNavigationItem(
            7,
            titleRes = R.string.kol_bottom_tab,
            iconRes = R.drawable.icon_bottom_navigation_create,
            iconResDark = R.drawable.icon_bottom_navigation_create_dark,
            clickSource = PageNameConstants.FRAGMENT_NAME_CREATOR_TAB
        ) {
            KolCreatorFragment()
        }

        val ADD = MainBottomNavigationItem(
            9,
            titleRes = 0,
            iconRes = 0,
            iconResDark = 0,
            clickSource = "add"
        ) {
            Fragment()
        }

        val UGC_DESIGN = MainBottomNavigationItem(
            10,
            titleRes = R.string.assets,
            iconRes = R.drawable.icon_bottom_navigation_library,
            iconResDark = R.drawable.icon_bottom_navigation_library,
            clickSource = PageNameConstants.FRAGMENT_UGC_DESIGN_FEED
        ) {
            UgcDesignFeedFragment()
        }

        fun getItem(itemId: Int): MainBottomNavigationItem? {
            return map.get(itemId)
        }

        fun initFlags(items: List<MainBottomNavigationItem>) {
            hasCreateTab = false
            hasProfileTab = false
            hasAssetTab = false
            for (item in items) {
                when (item.itemId) {
                    CREATE.itemId -> {
                        hasCreateTab = true
                    }

                    PROFILE.itemId -> {
                        hasProfileTab = true
                    }

                    UGC_DESIGN.itemId -> {
                        hasAssetTab = true
                    }
                }
            }
        }
    }

}