package com.socialplay.gpark.ui.account

import android.app.Application
import androidx.core.util.PatternsCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepositoryWrapper
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.account.EmailScene
import com.socialplay.gpark.data.model.account.EmailSendResult
import com.socialplay.gpark.util.RegexUtils
import com.socialplay.gpark.util.extension.replaceChinese
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext


class EmailVerifyViewModel(val repository: IMetaRepositoryWrapper, val accountInteractor: AccountInteractor) : ViewModel() {

    companion object {
        const val TAG = "Account-EmailVerify"
    }

    private val context: Application = GlobalContext.get().get()

    private val _emailFlow = MutableStateFlow<String?>(null)
    val emailFlow: Flow<String?> = _emailFlow

    private val _verifyCodeFlow = MutableStateFlow<String?>(null)
    val verifyCodeFlow: Flow<String?> = _verifyCodeFlow

    val emailAndVerifyCodeValidFlow: Flow<Boolean> = emailFlow.combine(verifyCodeFlow) { v1, v2 -> isEmailValid(v1) && isVerifyCodeValid(v2) }

    private val _emailAndVerifyCodeCheckResultFlow = MutableSharedFlow<String>()
    val emailAndVerifyCodeCheckResultFlow: Flow<String?> = _emailAndVerifyCodeCheckResultFlow


    private val _emailSendFlow = MutableSharedFlow<EmailSendResult>()
    val emailSendFlow = _emailSendFlow

    private val _emailCheckFlow = MutableSharedFlow<EmailSendResult?>()
    val emailCheckFlow = _emailCheckFlow

    fun postEmailValueChanged(email: String?) {
        _emailFlow.value = email
    }

    fun postPasswordValueChanged(code: String?) {
        _verifyCodeFlow.value = code
    }

    fun isEmailValid(email: String?): Boolean {
        if (EnvConfig.isParty()) {
            return !email.isNullOrBlank() && RegexUtils.isMobileSimple(email)
        }
        return !email.isNullOrBlank() && PatternsCompat.EMAIL_ADDRESS.matcher(email).matches()
    }

    fun isVerifyCodeValid(code: String?): Boolean {
        return !code.isNullOrBlank() && code.length >= 4
    }

    fun sendEmail(email: String?, scene: EmailScene) = viewModelScope.launch {
        if (EnvConfig.isParty()) {
            // 派对找回密码，这里就用短信验证码

            // 验证手机号格式
            if (!RegexUtils.isMobileSimple(email)) {
                _emailAndVerifyCodeCheckResultFlow.emit(context.getString(R.string.phone_login_toast_phone_wrong))
                return@launch
            }
            repository.getPhoneSmsCode(email!!, scene.phoneType).collect {
                var result: EmailSendResult = if (it.succeeded) EmailSendResult.Success(scene) else EmailSendResult.Failed(scene, it.message)
                _emailSendFlow.emit(result)
            }
            return@launch
        }
        if (!checkEmail()) return@launch

        repository.sendEmail(email, scene.key).collect {
            val result: EmailSendResult = if (it.data == true) EmailSendResult.Success(scene) else EmailSendResult.Failed(scene, it.message.replaceChinese(""))
            _emailSendFlow.emit(result)
        }
    }

    fun checkEmail(email: String, code: String, scene: EmailScene) = viewModelScope.launch {
        if (code.isBlank() || code.length < 4) {
            _emailCheckFlow.emit(EmailSendResult.Failed(scene, context.getString(R.string.verify_code_invalid)))
            return@launch
        }
        if (EnvConfig.isParty()) {
            // 派对
            repository.verifyPhoneCode(email, code, scene.phoneType).collect {
                val result: EmailSendResult = if (it.data == true) EmailSendResult.Success(scene) else EmailSendResult.Failed(scene, it.message)
                _emailCheckFlow.emit(result)
            }
            return@launch
        }
        if (!checkEmailAndCode()) return@launch
        repository.checkEmail(email, code, scene.key).collect {
            val result: EmailSendResult = if (it.data == true) EmailSendResult.Success(scene) else EmailSendResult.Failed(scene, it.message)
            _emailCheckFlow.emit(result)
        }
    }

    fun bindEmail(email: String, code: String, scene: EmailScene) = viewModelScope.launch {
        if (!checkEmailAndCode()) return@launch
        accountInteractor.bindEmail(email, code).collect {
            val result: EmailSendResult = if (it.succeeded && it.data == true) EmailSendResult.Success(scene) else EmailSendResult.Failed(scene, it.message)
            _emailCheckFlow.emit(result)
        }
    }

    fun bindEmailChange(newEmail: String, newEmailCode: String, oldEmailCode: String, scene: EmailScene) = viewModelScope.launch {
        if (!checkEmailAndCode()) return@launch
        accountInteractor.bindEmailChange(newEmail, newEmailCode, oldEmailCode).collect {
            val result: EmailSendResult = if (it.succeeded && it.data == true) EmailSendResult.Success(scene) else EmailSendResult.Failed(scene, it.message)
            _emailCheckFlow.emit(result)
        }
    }

    fun bindParentEmail(email: String, code: String, scene: EmailScene) = viewModelScope.launch {
        if (!checkEmailAndCode()) return@launch
        accountInteractor.bindParentEmail(email, code).collect {
            val result: EmailSendResult = if (it.succeeded && it.data == true) EmailSendResult.Success(scene) else EmailSendResult.Failed(scene, it.message)
            _emailCheckFlow.emit(result)
        }
    }

    fun bindParentEmailChange(newEmail: String, newEmailCode: String, oldEmailCode: String, scene: EmailScene) = viewModelScope.launch {
        if (!checkEmailAndCode()) return@launch
        accountInteractor.bindParentEmailChange(newEmail, newEmailCode, oldEmailCode).collect {
            val result: EmailSendResult = if (it.succeeded && it.data == true) EmailSendResult.Success(scene) else EmailSendResult.Failed(scene, it.message)
            _emailCheckFlow.emit(result)
        }
    }

    private suspend fun checkEmail(): Boolean {
        if (_emailFlow.value.isNullOrEmpty()) {
            _emailAndVerifyCodeCheckResultFlow.emit(context.getString(R.string.cannot_empty).format(context.getString(R.string.text_email)))
            return false
        }
        if (!_emailFlow.value!!.contains("@")) {
            _emailAndVerifyCodeCheckResultFlow.emit(context.getString(R.string.verify_code_send_failed))
            return false
        }
        return true
    }

    private suspend fun checkEmailAndCode(): Boolean {
        if (_emailFlow.value.isNullOrEmpty()) {
            _emailAndVerifyCodeCheckResultFlow.emit(context.getString(R.string.cannot_empty).format(context.getString(R.string.text_email)))
            return false
        }
        if (_verifyCodeFlow.value.isNullOrEmpty()) {
            _emailAndVerifyCodeCheckResultFlow.emit(context.getString(R.string.cannot_empty).format(context.getString(R.string.text_verification_code)))
            return false
        }
        return true
    }

}