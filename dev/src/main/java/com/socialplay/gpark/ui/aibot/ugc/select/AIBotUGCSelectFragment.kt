package com.socialplay.gpark.ui.aibot.ugc.select

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.airbnb.mvrx.asMavericksArgs
import com.bumptech.glide.Glide
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.aibot.AiBotSelectResultEvent
import com.socialplay.gpark.data.model.aibot.BotInfo
import com.socialplay.gpark.databinding.FragmentAiBotUgcSelectBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.aibot.ugc.AIBotUGCCreateViewModel.Companion.SELECT_IMAGE
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.plot.chooseimage.PlotClipImageFragment
import com.socialplay.gpark.ui.plot.chooseimage.PlotClipImageFragmentArgs
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/26
 *     desc   :
 *
 */
class AIBotUGCSelectFragment : BaseFragment<FragmentAiBotUgcSelectBinding>() {
    private val viewModel by viewModel<AIBotUGCSelectViewModel>()
    private val accountInteractor: AccountInteractor by inject()
    val args = navArgs<AIBotUGCSelectFragmentArgs>()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentAiBotUgcSelectBinding? {
        return FragmentAiBotUgcSelectBinding.inflate(inflater, container, false)
    }

    override fun init() {
        initData()
        initView()
    }
    private fun initData(){
        viewModel.generateLiveData.observe(viewLifecycleOwner){
            Glide.with(requireContext()).load(it.avatar).into(binding.imgIcon)
            Glide.with(requireContext()).load(it.backgroundImage).into(binding.imgBg)
            Glide.with(requireContext()).load(it.avatar).into(binding.userIconOne)
            Glide.with(requireContext()).load(it.avatar).into(binding.userIconTwo)
            Glide.with(requireContext()).load(accountInteractor.accountLiveData.value?.portrait).into(binding.userIconThree)
        }

    }
    private fun initView(){
        binding.imgBack.setOnAntiViolenceClickListener {
            navigateUp()
        }
        Glide.with(this).load(BuildConfig.CND_AB_BOT_UGC_TOP_BG).into(binding.bgLineAiBotUgcBack)
        binding.imgConvert.setOnAntiViolenceClickListener {
            gotoClip()

        }
        binding.imgIcon.setOnAntiViolenceClickListener {
            gotoClip()
        }
        binding.tvGenerate.setOnAntiViolenceClickListener {
            viewModel.generateLiveData.value?.let {
                val botInfo = BotInfo()
                botInfo.icon = it.avatar
                botInfo.chatImage = it.backgroundImage
                botInfo.cover = it.coverImage
                botInfo.gender = it.gender
                EventBus.getDefault().post(AiBotSelectResultEvent(""))
                this.findNavController().popBackStack()
                MetaRouter.AiBot.gotoAIBotGenerate(
                    this,
                    GsonUtil.safeToJson(botInfo)
                )
            }
            Analytics.track(EventConstants.EVENT_AI_BOT_AVATAR_COMFIRM_CLICK)
        }

    }
    private fun getGenerateType(): String {
        return if (viewModel.generateLiveData.value?.selectType == SELECT_IMAGE) {
            "image 2 image"
        } else {
            "text 2 image"
        }
    }
    private fun gotoClip(){
        val with = ScreenUtil.getScreenWidth(requireContext()) - 32.dp
        viewModel.generateLiveData.value?.backgroundImage?.let { it1 ->
            MetaRouter.AiBot.clipAIBotImage(
                this,
                it1,
                with,
                with
            ) {
                if (!it.isNullOrEmpty()) {
                    viewModel.updateIcon(it)
                }
            }
        }
    }
    override fun loadFirstData() {
        viewModel.initData(args.value.data)
    }

    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_AI_BOT_CREATE_SELECT
}