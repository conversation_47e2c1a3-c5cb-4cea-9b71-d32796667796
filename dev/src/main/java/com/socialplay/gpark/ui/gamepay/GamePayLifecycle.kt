package com.socialplay.gpark.ui.gamepay

import android.app.Activity
import android.view.View
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import com.meta.biz.ugc.model.GetAllMemberInfoMsg
import com.meta.biz.ugc.model.IMWMsg
import com.meta.biz.ugc.model.IPlatformMsg
import com.meta.biz.ugc.model.MWProtocol
import com.meta.biz.ugc.model.MemberRechargeMsg
import com.meta.biz.ugc.model.RechargeArkMsg
import com.meta.biz.ugc.model.RechargeResultMgs
import com.meta.biz.ugc.protocol.UGCProtocolReceiver
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolReceiveConstants
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.meta.biz.ugc.protocol.listener.SingleProtocolListener
import com.meta.lib.mwbiz.MWBizBridge
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.LifecycleInteractor
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.event.CloseRechargeDialogEvent
import com.socialplay.gpark.data.model.event.MakeFloatingTop
import com.socialplay.gpark.databinding.ViewPayConfirmDialogBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.editor.EditorGameInteractHelper
import com.socialplay.gpark.function.intermodal.base.GameViewShowHelper
import com.socialplay.gpark.function.intermodal.base.GameViewShowHelper.VIEW_DEFAULT_PAGE
import com.socialplay.gpark.function.member.MwMemberInteractor
import com.socialplay.gpark.function.mw.lifecycle.MWLifecycle
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.ui.dialog.WebViewDialog
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.ui.pay.RechargeConfirmDialogFragment
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.UnitUtilWrapper
import com.socialplay.gpark.util.extension.filterValueNotNull
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.single
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.lang.ref.WeakReference

/**
 * create by: bin on 2022/4/28
 */
class GamePayLifecycle(val isHost: Boolean) : MWLifecycle() {
    private val payInteractor: IPayInteractor = GlobalContext.get().get()
    private val repository: IMetaRepository = GlobalContext.get().get()
    private var isShowing = false
    private var rechargeDialogRef: WeakReference<RechargeConfirmDialogFragment> = WeakReference(null)
    private val scopeIO = CoroutineScope(Dispatchers.IO)
    private var mPayView: View? = null
    private var webDialog: WebViewDialog? = null

    /**
     * 本次充值的信息, 当充值成功后, 发起代币兑换流程时, 会用到
     * 会在用户手动关闭充值弹窗时, 置为 null
     */
    private var rechargeMessage: RechargeArkMsg? = null
    private var needShowConfirm = true

    init {
        scopeIO.launch {
            needShowConfirm = !(repository.getTTaiConfigById(20305).single().data?.value?.contains(
                MWBizBridge.currentGameId()
            ) ?: false)
        }
        // 收到游戏的信息
        UGCProtocolReceiver.addProtocolObserver(object :
            SingleProtocolListener<GetAllMemberInfoMsg>(
                ProtocolReceiveConstants.PROTOCOL_MEMBER_INFO
            ) {
            override fun handleProtocol(message: GetAllMemberInfoMsg?, messageId: Int) {
                MwMemberInteractor.refreshUserMemberInfo(messageId)
            }
        })
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<MemberRechargeMsg>(
            ProtocolReceiveConstants.PROTOCOL_MEMBER_RECHARGE
        ) {
            override fun handleProtocol(message: MemberRechargeMsg?, messageId: Int) {
                scopeIO.launch(Dispatchers.Main) {
                    if (isHost) {
                        message?.gameId = EditorGameInteractHelper.getRoleGameId()
                    }
                    if (PandoraToggle.IAP_PREMIUM == "0") {
                        Timber.d("IAP_PREMIUM is close")
                        return@launch
                    }
                    val rechargeResultMgs = RechargeResultMgs("1", 200, 0, "", null, messageId)
                    LifecycleInteractor.activityRef?.get()
                        ?.let {
                            MwMemberInteractor.goMemberRecharge(
                                it,
                                message,
                                rechargeResultMgs
                            )
                        }
                }
            }

        })
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<RechargeArkMsg>(
            ProtocolReceiveConstants.PROTOCOL_PAY_START
        ) {
            override fun handleProtocol(message: RechargeArkMsg?, messageId: Int) {
                scopeIO.launch(Dispatchers.Main) {
                    val data = mutableMapOf<String, Any>()
                    data.putAll(message?.rawData?.filterValueNotNull() ?: mutableMapOf())
                    val rechargeResultMgs = RechargeResultMgs("0", 200, 0, "", data, messageId)
                    startPay(message, false, rechargeResultMgs)
                }
            }
        })
        UGCProtocolReceiver.addProtocolObserver(object : SingleProtocolListener<IMWMsg>(
            ProtocolReceiveConstants.PROTOCOL_PAY_GET_ARK
        ) {
            override fun handleProtocol(message: IMWMsg?, messageId: Int) {
                getArk(messageId)
            }
        })
        EventBus.getDefault().register(this)
    }

    private fun getArk(messageId: Int) {
        scopeIO.launch {
            payInteractor.getBalance(messageId)
        }
    }


    private fun startPay(
        msg: RechargeArkMsg?,
        mustEnoughMoney: Boolean = false,
        rechargeResultMgs: RechargeResultMgs? = null
    ) {
        if (msg == null) {
            send2Ue(DataResult.Error(502, "msg is null", data = null), "")
            return
        }
        scopeIO.launch {
            getArkNum(msg, mustEnoughMoney, rechargeResultMgs)
        }
    }

    private suspend fun getArkNum(
        msg: RechargeArkMsg,
        mustEnoughMoney: Boolean,
        rechargeResultMgs: RechargeResultMgs? = null
    ) {
        val gameDetailInfo = repository.getMgsGameInfoByGameId(msg.gameId)
        val cur = System.currentTimeMillis()
        payInteractor.getBalance { ark, point ->
            Analytics.track(
                EventConstants.EVENT_IAP_TIME_PAY_GET_BALANCE,
                "time" to System.currentTimeMillis() - cur,
                "pay_order_id" to msg.cpOrderId.toString(),
                "gamecode" to msg.gameId,
            )
            withContext(Dispatchers.Main) {
                showPayView(msg, ark, point, mustEnoughMoney, rechargeResultMgs, cur, gameDetailInfo)
            }
        }
    }

    private fun showPayView(
        msg: RechargeArkMsg,
        balance: Long,
        point: Long,
        mustEnoughMoney: Boolean,
        rechargeResultMgs: RechargeResultMgs? = null,
        startTime: Long,
        gameDetailInfo: GameDetailInfo? = null
    ) {
        payInteractor.preProcessRechargeArkMsg(msg)
        val price: Long = runCatching { (msg.payAmount ?: "0").toLong() }.getOrElse {
            send2Ue(
                DataResult.Error(503, "amount is wrong data ${msg.payAmount}", data = null),
                msg.cpOrderId ?: ""
            )
            it.printStackTrace()
            return
        }
        msg.rawData["payTunnel"] = 1
        val isDebug = msg.rawData["isDebug"]
        val payType = kotlin.runCatching { msg.rawData["payType"].toString().toInt() }.getOrNull()
        val payChannel = payInteractor.getPayChannel(payType)
        if (payChannel != null) {
            msg.rawData["payChannel"] = payChannel
        }

        if (payType == 1 || payType == null) {
            // 代币支付
            arkPay(balance, price, msg, mustEnoughMoney, rechargeResultMgs, startTime, gameDetailInfo)
        } else if (payType == 2 && payInteractor.supportPointsPayment) {
            // 积分支付
            scopeIO.launch {
                mwPay(msg, null, price)
            }
        } else {
            send2Ue(
                DataResult.Error(506, "payType is wrong data $payType", data = null),
                msg.cpOrderId.toString(),
            )
        }
    }

    /**
     * 平台币支付
     */
    private fun arkPay(
        it: Long,
        price: Long,
        msg: RechargeArkMsg,
        mustEnoughMoney: Boolean,
        rechargeResultMgs: RechargeResultMgs?,
        startTime: Long,
        gameDetailInfo: GameDetailInfo? = null
    ) {
        val cur = System.currentTimeMillis()
        if (it >= price || msg.rawData["isDebug"]?.toString() == "true") {// 够钱 弹购买弹窗
            if (needShowConfirm) {
                Analytics.track(
                    EventConstants.EVENT_GPARK_PAY_COIN_SHOW,
                    "gamecode" to msg.gameId,
                    "commodityid" to (msg.productCode ?: ""),
                    "propprice" to price.toString(),
                    "balance" to it.toString()
                )
                val curActivity = LifecycleInteractor.activityRef?.get() ?: return
                val payView = ViewPayConfirmDialogBinding.inflate(curActivity.layoutInflater)
                mPayView = payView.root
                payView.ivCancel.setOnAntiViolenceClickListener { _ ->
                    Analytics.track(
                        EventConstants.EVENT_GPARK_PAY_COIN_CLICK,
                        "gamecode" to msg.gameId,
                        "commodityid" to (msg.productCode ?: ""),
                        "choice" to "cancel",
                    )
                    send2Ue(
                        DataResult.Error(504, "cancel by user", data = null),
                        msg.cpOrderId ?: ""
                    )
                    closePayView()
                    rechargeMessage = null
                }
                payView.tvConfirmSandbox.isVisible = msg.rawData["isDebug"]?.toString() == "true"
                payView.tvPayDescSandbox.isVisible = msg.rawData["isDebug"]?.toString() == "true"
                payView.tvNeedPay.text = UnitUtilWrapper.formatCoinCont(price)
                payView.tvBalance.text = UnitUtilWrapper.formatCoinCont(it)
                payView.tvConfirm.setOnAntiViolenceClickListener { _ ->
                    Analytics.track(
                        EventConstants.EVENT_GPARK_PAY_COIN_CLICK,
                        "gamecode" to msg.gameId,
                        "commodityid" to (msg.productCode ?: ""),
                        "choice" to "confirm",
                    )
                    // 确认支付
                    payView.loading.showLoading()
                    scopeIO.launch {
                        mwPay(msg, payView, price)
                    }
                }
                if (isShowing) return
                isShowing = true
                GameViewShowHelper.getInstance()
                    .displayView(curActivity, curActivity, payView.root, VIEW_DEFAULT_PAGE)
            } else {
                // 确认支付
                scopeIO.launch {
                    mwPay(msg, null, price)
                }
            }
        } else if (!mustEnoughMoney) {// 没钱 弹充值弹窗
//            val curActivity = LifecycleInteractor.activityRef?.get() ?: return
//            (curActivity as? FragmentActivity)?.apply {
//                val url = GlobalContext.get().get<H5PageConfigInteractor>()
//                    .getH5PageUrl(H5PageConfigInteractor.BALANCE).toHttpUrl()
//                val newUrl = url.newBuilder()
//                    .addQueryParameter("product", Uri.encode(msg.productName.toString()))
//                    .addQueryParameter("price", price.toString())
//                    .addQueryParameter("needNum", (price - it).toString())
//                    .addQueryParameter("gid", msg.gameId)
//                    .addQueryParameter("productCode", msg.productCode ?: "")
//                    .addQueryParameter("source", "ts_game")
//                    .addQueryParameter("startTime", startTime.toString())
//                    .addQueryParameter("sceneCode", msg.rawData["sceneCode"].toString())
//                    .build().toString()
//                payInteractor.checkConnect()
//                webDialog = MetaRouter.Web.showDialog(
//                    this,
//                    newUrl,
//                    rechargeResultMgs?.let { it1 -> GsonUtil.safeToJson(it1) })
//            }
//
//            this.rechargeResultMgs = rechargeResultMgs
//            rechargeMessage = msg

            rechargeByProductPrice(it, price, msg, gameDetailInfo)
            send2Ue(DataResult.Error(501, "insufficient balance", data = null), msg.cpOrderId ?: "")
        }
        Analytics.track(
            EventConstants.EVENT_IAP_TIME_SHOW_DIALOG,
            "time" to System.currentTimeMillis() - cur,
            "pay_order_id" to msg.cpOrderId.toString(),
            "gamecode" to msg.gameId,
        )
        scopeIO.launch {
            delay(500)
            EventBus.getDefault().post(MakeFloatingTop())
        }
    }

    private fun rechargeByProductPrice(
        balance: Long,
        price: Long,
        msg: RechargeArkMsg,
        gameDetailInfo: GameDetailInfo? = null
    ) {
        rechargeMessage = msg
        val aty = LifecycleInteractor.activityRef?.get() ?: return
        val fragmentActivity = (aty as? FragmentActivity) ?: return
        val fragmentManager = if (fragmentActivity is MainActivity) {
            val navHostFragment = fragmentActivity.findNavHostFragment() ?: return
            if (!navHostFragment.isAdded) return
            val f =
                navHostFragment.childFragmentManager.fragments.firstOrNull { it.isResumed && it.isVisible }
                    ?: navHostFragment.childFragmentManager.fragments.firstOrNull { it.isVisible }
                    ?: navHostFragment
            f.childFragmentManager
        } else {
            fragmentActivity.supportFragmentManager
        }
        val lastRechargeDialog = rechargeDialogRef.get()
        if (lastRechargeDialog != null && !lastRechargeDialog.isDestroy) {
            return
        }
        val rechargeDialog= RechargeConfirmDialogFragment.show(fragmentManager, balance, price, msg, gameDetailInfo) { paySuccess ->
            if (paySuccess) {
                val rechargeMessageTemp = rechargeMessage
                rechargeMessage = null
                // PG 币充值成功后, 再次调起支付流程
                startPay(rechargeMessageTemp, false)
            }
        }
        rechargeDialogRef = WeakReference(rechargeDialog)
    }

    private suspend fun mwPay(
        msg: RechargeArkMsg,
        payView: ViewPayConfirmDialogBinding?,
        price: Long
    ) {
        msg.rawData["payTunnel"] = 1
        msg.rawData["gameCode"] = msg.gameId
        msg.rawData.remove("gameId")
        payInteractor.mwPay(msg.rawData) { result ->
            withContext(Dispatchers.Main) {
                val curActivity = LifecycleInteractor.activityRef?.get() ?: return@withContext
                ToastUtil.showShort(
                    curActivity,
                    if (result.succeeded) curActivity.getString(R.string.iap_purchase_success) else result.message
                )
                // 回调给游戏
                send2Ue(result, msg.cpOrderId ?: "")
                payInteractor.getBalance { balance, point ->
                    val hash = hashMapOf(
                        "is_debug" to (msg.rawData["isDebug"]?.toString() ?: ""),
                        "gamecode" to msg.gameId,
                        "commodityid" to (msg.productCode ?: ""),
                        "propprice" to price.toString(),
                        "balance" to balance.toString(),
                        "propname" to msg.productName.toString(),
                        "proporderid" to (msg.cpOrderId ?: ""),
                        "orderid" to result.data.toString(),
                        "scenecode" to msg.sceneCode.toString(),
                        "result" to if (result.code == 200) "1" else "2",
                        "failedcode" to result.code.toString(),
                        "failedmsg" to result.message.toString(),
                    )
                    Analytics.track(EventConstants.EVENT_GPARK_PAY_COIN_SUCCESS, hash)
                }
                payView?.let {
                    it.loading.hide()
                    it.root.gone()
                    closePayView()
                }
            }
        }
    }

    private fun closePayView() {
        val payView = mPayView
        mPayView = null
        isShowing = false
        payView ?: return
        val curActivity = LifecycleInteractor.activityRef?.get() ?: return
        GameViewShowHelper.getInstance().dismissView(curActivity, payView)
    }

    private fun send2Ue(it: DataResult<*>, cpOrderId: String) {
        UGCProtocolSender.sendProtocol(
            ProtocolSendConstant.PROTOCOL_PAY_CALLBACK,
            0,
            HashMap<String, Any>().apply {
                this["code"] = it.code ?: 0
                this["message"] = it.message ?: ""
                this["cpOrderId"] = cpOrderId
            }
        )
    }

    override fun onActivityResumed(activity: Activity) {
        super.onActivityResumed(activity)
        Timber.d("curActivity: $activity")
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: CloseRechargeDialogEvent) {
        runCatching {
            closePayView()
            if (webDialog != null) webDialog?.dismissAllowingStateLoss()
        }
    }

    companion object {
        val IAP_TS_PAY_RECHARGE = MWProtocol("iap.ts.pay.recharge", "iap直接跳转充值派对币")

        /**
         * 充值结果回调
         */
        fun sendRechargeResult2UE(data: RechargeResultMgs) {
            UGCProtocolSender.sendProtocol(
                ProtocolSendConstant.PROTOCOL_RECHARGE_CALL_BACK,
                data.messageId,
                object : IPlatformMsg() {
                    override fun addJsonData(map: MutableMap<String, Any>) {
                        data.extra?.filterValueNotNull()?.let {
                            map.putAll(it)
                        }
                        map["source"] = data.source
                        map["code"] = data.code
                        // 实际支付金额，单位：分
                        map["amount"] = data.amount
                        if (data.message != null) map["message"] = data.message ?: ""
                    }
                }
            )
        }
    }
}