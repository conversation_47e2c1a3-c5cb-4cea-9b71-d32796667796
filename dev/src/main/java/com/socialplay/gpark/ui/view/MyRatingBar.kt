package com.socialplay.gpark.ui.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.*
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import com.socialplay.gpark.R
import kotlin.math.ceil
import kotlin.math.roundToInt

/**　
 * @author: <EMAIL>
 * @date: 2021/06/11 11:47
 *  Created by 蛟神 on 2018/6/11.
 */
class MyRatingBar : View {
    //星星间距
    private var starDistance = 0

    //星星个数
    private var starCount = 5

    //星星高度大小，星星一般正方形，宽度等于高度
    private var starSize = 0

    //评分星星
    var rating = 0.0f
        set(value) {
            field = if (integerMark) {
                ceil(value.toDouble()).toFloat()
            } else {
                (value * 10).roundToInt() * 1.0f / 10
            }
            //调用监听接口
            onStarChangeListener?.invoke(value)
            invalidate()
        }

    //亮星星
    private var starFillBitmap: Bitmap? = null

    //暗星星
    private var starEmptyDrawable: Drawable? = null

    //监听星星变化接口
    private val onStarChangeListener: ((mark: Float) -> Unit)? = null

    //绘制星星画笔
    private var paint: Paint = Paint()

    private val integerMark = false

    //是否可以触摸改变星
    private val isCanTouchChange = false

    constructor(context: Context?, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        isClickable = true
        val mTypedArray = context!!.obtainStyledAttributes(attrs, R.styleable.MyRatingBar)
        starDistance = mTypedArray.getDimension(R.styleable.MyRatingBar_starDistance, 0f).toInt()
        starSize = mTypedArray.getDimension(R.styleable.MyRatingBar_starSize, 20f).toInt()
        starCount = mTypedArray.getInteger(R.styleable.MyRatingBar_starCount, 5)
        starEmptyDrawable = mTypedArray.getDrawable(R.styleable.MyRatingBar_starEmpty)
        starFillBitmap = drawableToBitmap(mTypedArray.getDrawable(R.styleable.MyRatingBar_starFill))
        mTypedArray.recycle()

        paint.isAntiAlias = true
        starFillBitmap?.let { bitmap ->
            paint.shader = BitmapShader(
                bitmap,
                Shader.TileMode.CLAMP,
                Shader.TileMode.CLAMP
            )
        }

    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        setMeasuredDimension(starSize * starCount + starDistance * (starCount - 1), starSize)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        for (i in 0 until starCount) {
            starEmptyDrawable?.setBounds(
                (starDistance + starSize) * i,
                0,
                (starDistance + starSize) * i + starSize,
                starSize
            )
            starEmptyDrawable?.draw(canvas)
        }
        if (rating > 1) {
            canvas.drawRect(0f, 0f, starSize.toFloat(), starSize.toFloat(), paint)
            if (rating - rating.toInt() == 0f) {
                var i = 1
                while (i < rating) {
                    canvas.translate((starDistance + starSize).toFloat(), 0f)
                    canvas.drawRect(0f, 0f, starSize.toFloat(), starSize.toFloat(), paint)
                    i++
                }
            } else {
                var i = 1
                while (i < rating - 1) {
                    canvas.translate((starDistance + starSize).toFloat(), 0f)
                    canvas.drawRect(0f, 0f, starSize.toFloat(), starSize.toFloat(), paint)
                    i++
                }
                canvas.translate((starDistance + starSize).toFloat(), 0f)
                canvas.drawRect(
                    0f,
                    0f,
                    starSize * (((rating - rating.toInt()) * 10).roundToInt() * 1.0f / 10),
                    starSize.toFloat(),
                    paint
                )
            }
        } else {
            canvas.drawRect(0f, 0f, starSize * rating, starSize.toFloat(), paint)
        }
    }

    private fun drawableToBitmap(drawable: Drawable?): Bitmap? {
        drawable?.let {
            val bitmap = Bitmap.createBitmap(starSize, starSize, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bitmap)
            it.setBounds(0, 0, starSize, starSize)
            it.draw(canvas)
            return bitmap
        } ?: run {
            return null
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (isCanTouchChange) {
            var x = event.x.toInt()
            if (x < 0) {
                x = 0
            }
            if (x > measuredWidth) {
                x = measuredWidth
            }
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    rating = (x * 1.0f / (measuredWidth * 1.0f / starCount))
                }
                MotionEvent.ACTION_MOVE -> {
                    rating = (x * 1.0f / (measuredWidth * 1.0f / starCount))
                }
                MotionEvent.ACTION_UP   -> {
                }
            }
            invalidate()
        }
        return super.onTouchEvent(event)
    }

}