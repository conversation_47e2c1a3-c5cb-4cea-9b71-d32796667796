package com.socialplay.gpark.ui.core

import android.app.Dialog
import android.os.Bundle
import android.view.View
import androidx.annotation.CallSuper
import androidx.annotation.LayoutRes
import androidx.viewbinding.ViewBinding
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialog

/**
 * Created by bo.li
 * Date: 2023/9/21
 * Desc:
 */
abstract class BaseBottomSheetDialogFragment :
    com.socialplay.gpark.ui.base.BaseBottomSheetDialogFragment(), MavericksViewEx {

    override fun onCreateDialog(savedInstanceState: Bundle?): BottomSheetDialog {
        return super.onCreateDialog(savedInstanceState) as BottomSheetDialog
    }

    @Deprecated("use init")
    override fun loadFirstData() {
    }

    override fun invalidate() {

    }
}

abstract class BaseRVBottomSheetDialogFragment :
    BaseBottomSheetDialogFragment() {

    protected abstract val recyclerView: EpoxyRecyclerView

    protected val epoxyController by lazy { epoxyController() }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        epoxyController.onRestoreInstanceState(savedInstanceState)
    }

    @CallSuper
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView.setController(epoxyController)
    }

    abstract fun epoxyController(): EpoxyController

    override fun invalidate() {
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        epoxyController.onSaveInstanceState(outState)
    }

}