package com.socialplay.gpark.ui.editor.banner

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.databinding.AdapterBannerUgcBinding
import com.youth.banner.adapter.BannerAdapter

class UgcBannerAdapter(data: List<UniJumpConfig>?) : BannerAdapter<UniJumpConfig, UgcBannerAdapter.ViewHolder>(data) {
    //更新数据
    fun updateData(data: List<UniJumpConfig>?) {
        //这里的代码自己发挥，比如如下的写法等等
        mDatas.clear()
        data?.let { mDatas.addAll(it) }
        notifyDataSetChanged()
    }

    override fun onCreateHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = AdapterBannerUgcBinding.inflate(LayoutInflater.from(parent.context),
            parent, false)
        binding.ivPosterImg.layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.MATCH_PARENT)
        binding.ivPosterImg.scaleType = ImageView.ScaleType.CENTER_CROP
        return ViewHolder(binding)
    }

    override fun onBindView(holder: ViewHolder, data: UniJumpConfig, position: Int, size: Int) {
        holder.bind(data, position)
    }

    class ViewHolder(private val binding: AdapterBannerUgcBinding) : RecyclerView.ViewHolder(binding.root) {
        var curPosition: Int =  0
        fun bind(info: UniJumpConfig, position: Int) {
            curPosition = position
            Glide.with(itemView)
                .load(info.iconUrl)
                .placeholder(R.color.color_placeholder)
                .transform(CenterCrop())
                .into(binding.ivPosterImg)
        }
    }
}