package com.socialplay.gpark.ui.base

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.FrameLayout
import androidx.annotation.StyleRes
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.viewbinding.ViewBinding
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.socialplay.gpark.R
import com.socialplay.gpark.function.analytics.observer.LifecycleObserver
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.navColor
import com.socialplay.gpark.util.property.ViewBindingLifecycleOwner
import com.socialplay.gpark.util.property.ViewBindingLifecycleOwnerProvider

/**
 * Created by bo.li
 * Date: 2022/11/21
 * Desc:
 */
abstract class BaseBottomSheetDialogFragment: BottomSheetDialogFragment(), ViewBindingLifecycleOwnerProvider {

    protected abstract val binding: ViewBinding
    private var viewBindingLifecycleOwner: ViewBindingLifecycleOwner? = null
    private var isLoadFirstData = false

    protected open var navColorRes: Int = R.color.white
    private var nextNavColor: Int = 0

    protected var glide: RequestManager? = null
        get() {
            if (field == null && enableGlide) {
                field = Glide.with(this)
            }
            return field
        }
    private var enableGlide: Boolean = false

    fun isBindingAvailable(): Boolean {
        return viewBindingLifecycleOwner != null
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (needCountTime()) {
            LifecycleObserver(this, getPageName())
        }
        setStyle(DialogFragment.STYLE_NO_TITLE, getStyle())
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        viewBindingLifecycleOwner = ViewBindingLifecycleOwner()
        return binding.root
    }

    override fun onStart() {
        kotlin.runCatching {
            super.onStart()
        }.getOrElse {
            dismissAllowingStateLoss()
        }
    }

    override fun onResume() {
        super.onResume()
        setStatusBarTextColor(isStatusBarTextDark())
        dialog?.window?.let {
            it.navColor = nextNavColor
        }
    }

    open var heightPercent = 0.8F
    open var heightPercentOffset = 0

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        enableGlide = true
        dialog?.run {
            window?.run {
                binding.root.background = ColorDrawable(Color.TRANSPARENT)
                getBottomSheet()?.let {
                    val lp = it.layoutParams as CoordinatorLayout.LayoutParams
                    if(heightPercent!=0f){
                        lp.height = (ScreenUtil.screenHeight * heightPercent).toInt() - heightPercentOffset
                    }
                    behavior.state = BottomSheetBehavior.STATE_EXPANDED
                    it.layoutParams = lp
                }
                val attr = attributes
                attr.flags = attr.flags or WindowManager.LayoutParams.FLAG_DIM_BEHIND
                attr.dimAmount = dimAmount()
                attributes = attr
            }
            val clickOutsideDismiss = isClickOutsideDismiss()
            val backPressedDismiss = isBackPressedDismiss()
            if (backPressedDismiss) {
                setCancelable(true)
            }
            setCanceledOnTouchOutside(clickOutsideDismiss)

            setOnKeyListener { _, keyCode, event ->
                if (keyCode == KeyEvent.KEYCODE_BACK && event.repeatCount == 0) {
                    return@setOnKeyListener <EMAIL>() || !backPressedDismiss
                }
                return@setOnKeyListener false
            }
        }
        init()
        if (!isLoadFirstData) {
            isLoadFirstData = true
            loadFirstData()
        }
        if (nextNavColor == 0) {
            nextNavColor = getColorByRes(navColorRes)
        }
    }

    override fun dismiss() {
        dismissAllowingStateLoss()
    }

    override fun show(manager: FragmentManager, tag: String?) {
        if (!manager.isStateSaved && !isStateSaved) {
            super.show(manager, tag)
        }
    }

    @StyleRes
    open fun getStyle() = R.style.BottomDialogStyle

    open fun isClickOutsideDismiss() = true

    open fun isBackPressedDismiss() = true

    open fun isStatusBarTextDark(): Boolean {
        return true
    }

    open fun setStatusBarTextColor(isDark: Boolean) {
        dialog?.window?.let {
            if (isDark) {
                StatusBarUtil.setLightMode(it)
            } else {
                StatusBarUtil.setDarkMode(it)
            }
        }
    }

    open fun dimAmount() = 0.7F

    open fun gravity() = Gravity.BOTTOM

    abstract fun init()

    abstract fun loadFirstData()

    override fun onDetach() {
        super.onDetach()
        isLoadFirstData = false
    }

    override fun onDestroyView() {
        enableGlide = false
        glide = null
        super.onDestroyView()
        viewBindingLifecycleOwner?.onDestroyView()
        viewBindingLifecycleOwner = null
    }

    override fun viewBindingLayoutInflater(): LayoutInflater {
        return layoutInflater
    }

    override fun viewBindingLifecycleOwner(): ViewBindingLifecycleOwner {
        return viewBindingLifecycleOwner ?: error("view not create or destroy")
    }

    open fun onBackPressed(): Boolean = false

    /**
     * 发送app_time与event_show_page埋点
     */
    abstract fun needCountTime(): Boolean

    abstract fun getPageName(): String

    override fun getDialog(): BottomSheetDialog? {
        return super.getDialog() as? BottomSheetDialog
    }

    fun getWindow(): Window? {
        return dialog?.window
    }

    fun getOutside(): View? {
        return dialog?.delegate?.findViewById(com.google.android.material.R.id.touch_outside)
    }

    fun getBehavior(): BottomSheetBehavior<FrameLayout>? {
        return dialog?.behavior ?: getBottomSheet()?.let { BottomSheetBehavior.from(it) }
    }

    fun getBottomSheet(): FrameLayout? {
        return dialog?.delegate?.findViewById(com.google.android.material.R.id.design_bottom_sheet)
    }

    fun setPeekHeight(height: Int) {
        getBehavior()?.peekHeight = height
    }

    fun setPeekHeight(percent: Float = 0F, offset: Int = -1) {
        val actualPercent = if (percent == 0F) heightPercent else percent
        val actualOffset = if (offset == -1) heightPercentOffset else offset
        setPeekHeight((ScreenUtil.screenHeight * actualPercent).toInt() - actualOffset)
    }

    fun skipCollapsed() {
        getBehavior()?.skipCollapsed = true
    }

    fun enableDrag(enable: Boolean) {
        getBehavior()?.isDraggable = enable
    }

    fun enableHide(enable: Boolean) {
        getBehavior()?.isHideable = enable
    }
}