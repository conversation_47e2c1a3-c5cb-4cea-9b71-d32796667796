package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.RelativeLayout
import androidx.core.view.isGone
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.im.HomeOperationNotification
import com.socialplay.gpark.databinding.ViewHomeNoticeBinding
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

/**
 * Created by bo.li
 * Date: 2022/9/19
 * Desc:
 */
class HomeNoticeView: RelativeLayout {
    lateinit var bind: ViewHomeNoticeBinding
    private var notice: HomeOperationNotification? = null
    private var onCloseNoticeListener: ((HomeOperationNotification?) -> Unit)? = null
    private var onJumpWebListener: ((HomeOperationNotification?) -> Unit)? = null

    constructor(context: Context) : super(context) {
        init(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        init(context, attrs)
    }

    private fun init(context: Context, attrs: AttributeSet?) {
        bind = ViewHomeNoticeBinding.inflate(LayoutInflater.from(context), this, true)
        bind.ivNoticeClose.setOnAntiViolenceClickListener {
            onCloseNoticeListener?.invoke(notice)
        }
        bind.ivNoticeGoWeb.setOnAntiViolenceClickListener {
            onJumpWebListener?.invoke(notice)
        }
        updateNotice()
    }

    private fun updateNotice() {
        Glide.with(this).load(notice?.imgUrl).placeholder(R.drawable.icon_home_notice).into(bind.ivHomeNotice)
        bind.tvHomeNotice.text = notice?.name
        bind.ivNoticeGoWeb.isVisible = notice?.isJumpWeb() == true
    }

    fun setNoticeData(data: HomeOperationNotification?) {
        notice = data
        updateNotice()
    }

    fun setOnCloseListener(listener: ((HomeOperationNotification?) -> Unit)?) {
        onCloseNoticeListener = listener
    }

    fun setOnJumpWebListener(listener: ((HomeOperationNotification?) -> Unit)?) {
        onJumpWebListener = listener
    }
}