package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewConfiguration
import androidx.viewpager2.widget.ViewPager2
import com.youth.banner.adapter.BannerAdapter
import kotlin.math.abs

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/12/25
 *     desc   :
 * </pre>
 */
class WrapEpoxyBanner<T, BA : BannerAdapter<T, *>> @JvmOverloads constructor(
    context: Context,
    attributeSet: AttributeSet? = null,
    defaultStyleAttr: Int = 0
) : EpoxyBanner<T, BA>(context, attributeSet, defaultStyleAttr) {

    private val mTouchSlop: Int = ViewConfiguration.get(context).scaledTouchSlop
    private var mStartX = 0f
    private var mStartY = 0f
    private var hasIntercepted = false

    override fun onInterceptTouchEvent(event: MotionEvent): Boolean {
        if (!viewPager2.isUserInputEnabled) {
            return super.onInterceptTouchEvent(event)
        }
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                mStartX = event.x
                mStartY = event.y
                parent.requestDisallowInterceptTouchEvent(true)
                hasIntercepted = false
            }

            MotionEvent.ACTION_MOVE -> if (!hasIntercepted) {
                val endX = event.x
                val endY = event.y
                val distanceX = abs((endX - mStartX).toDouble()).toFloat()
                val distanceY = abs((endY - mStartY).toDouble()).toFloat()

                if (distanceX > mTouchSlop || distanceY > mTouchSlop) {
                    if (viewPager2.orientation == ViewPager2.ORIENTATION_HORIZONTAL) {
                        if (distanceX < mTouchSlop) {
                            parent.requestDisallowInterceptTouchEvent(false)
                        }
                    } else {
                        if (distanceY < mTouchSlop) {
                            parent.requestDisallowInterceptTouchEvent(false)
                        }
                    }
                    hasIntercepted = true
                }
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> parent.requestDisallowInterceptTouchEvent(
                false
            )
        }
        return false
    }
}