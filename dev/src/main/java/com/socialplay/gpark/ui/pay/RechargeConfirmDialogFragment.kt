package com.socialplay.gpark.ui.pay

import android.view.Gravity
import android.view.animation.AnimationUtils
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.withResumed
import com.meta.biz.ugc.model.RechargeArkMsg
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.LifecycleInteractor
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.pay.CommonPayParams
import com.socialplay.gpark.data.model.pay.IAPConstants
import com.socialplay.gpark.databinding.ViewPayRechargeConfirmDialogBinding
import com.socialplay.gpark.function.MetaRouterWrapper
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.pay.RechargeViewData
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.UnitUtilWrapper
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext

class RechargeConfirmDialogFragment : BaseDialogFragment() {
    var callback: (Boolean) -> Unit = {}
    var balance: Long = 0L
    var price: Long = 0L
    var gameDetailInfo: GameDetailInfo? = null
    private var rechargeMsg: RechargeArkMsg? = null
    private var mViewData: RechargeViewData? = null

    companion object {
        fun show(
            fragmentManager: FragmentManager,
            balance: Long,
            price: Long,
            rechargeMsg: RechargeArkMsg,
            gameDetailInfo: GameDetailInfo? = null,
            callback: (Boolean) -> Unit
        ): RechargeConfirmDialogFragment {
            val dialogFragment = RechargeConfirmDialogFragment().apply {
                this.balance = balance
                this.price = price
                this.rechargeMsg = rechargeMsg
                this.gameDetailInfo = gameDetailInfo
                this.callback = callback
            }
            dialogFragment.show(fragmentManager, PageNameConstants.DIALOG_RECHARGE_CONFIRM)
            return dialogFragment
        }
    }

    override val binding: ViewPayRechargeConfirmDialogBinding by viewBinding(
        ViewPayRechargeConfirmDialogBinding::inflate
    )
    private val payInteractor: IPayInteractor = GlobalContext.get().get()
    private val scopeIO = CoroutineScope(Dispatchers.IO)

    private var isRechargeLoading = false
    override fun gravity(): Int {
        return Gravity.CENTER
    }

    override fun isBackPressedDismiss() = false
    override fun isClickOutsideDismiss() = false

    override fun getFragmentName() = PageNameConstants.DIALOG_RECHARGE_CONFIRM

    override fun init() {
        mViewData = null
        val msg = rechargeMsg
        if (!isBindingAvailable() || msg == null) {
            dismissWithResult(false)
            return
        }
        binding.loading.showLoading()
        payInteractor.getRechargeViewData(balance, price, msg) { viewDataResult ->
            if (!isVisible || isDestroy || isDetached || !isBindingAvailable()) {
                dismissWithResult(false)
                return@getRechargeViewData
            }
            if (viewDataResult.succeeded) {
                val viewData = viewDataResult.data ?: return@getRechargeViewData
                mViewData = viewData
                Analytics.track(
                    EventConstants.EVENT_NO_ENOUGH_COIN_PAY_VIEW_SHOW,
                    "gamecode" to msg.gameId,
                    "commodityid" to (msg.productCode ?: ""),
                    "propprice" to price.toString(),
                    "balance" to balance.toString(),
                    "productid" to viewData.productId,
                    "product" to (msg.productName ?: ""),
                    "currencycode" to viewData.currencyCode,
                    "price" to viewData.price.toString(),
                    // 是否有premium优惠(只看是不是会员, 即便会员赠送为0), 没有0, 有1
                    "page_style" to if (viewData.isMember) "1" else "0",
                )
                val awardValue = viewData.awardCoinNum + viewData.memberRewardCoinNum
                scopeIO.launch(Dispatchers.Main) {
                    if (!isBindingAvailable()) {
                        return@launch
                    }
                    // 数据请求成功了, 隐藏loading
                    binding.loading.hide()

                    binding.ivCancel.setOnAntiViolenceClickListener {
                        handleCloseDialog(msg)
                    }
                    binding.tvConfirmSandbox.isVisible =
                        msg.rawData["isDebug"]?.toString() == "true"
                    binding.tvPayDescSandbox.isVisible =
                        msg.rawData["isDebug"]?.toString() == "true"
                    binding.tvNeedPay.text = UnitUtilWrapper.formatCoinCont(price)
                    binding.tvNeedAdditional.text =
                        UnitUtilWrapper.formatCoinCont(viewData.needAdditional)
                    binding.coinAmountTv.text =
                        UnitUtilWrapper.formatCoinCont(viewData.baseCoinNum + awardValue)
                    binding.memberFlagTv.isVisible = viewData.isMember
                    val curActivity =
                        LifecycleInteractor.activityRef?.get() ?: return@launch
                    if (awardValue > 0) {
                        binding.coinAwardTv.text = curActivity.getString(
                            R.string.iap_award_text,
                            UnitUtilWrapper.formatCoinCont(viewData.baseCoinNum),
                            UnitUtilWrapper.formatCoinCont(awardValue)
                        )
                    }
                    binding.coinAwardTv.isVisible = awardValue > 0
                    binding.payPriceTv.text = viewData.currencyCodePrice
                    binding.tvConfirm.text = curActivity.getString(
                        R.string.iap_pay_button_text,
                        viewData.currencyCodePrice
                    )

                    binding.tvConfirm.setOnAntiViolenceClickListener {
                        if (payInteractor.needRealName()) {
                            send2Ue(
                                DataResult.Error(507, "need real name", data = null),
                                msg.cpOrderId ?: ""
                            )
                            dismissWithResult(false)
                            payInteractor.gotoRealNamePage()
                            return@setOnAntiViolenceClickListener
                        }
                        isRechargeLoading = true
                        // 显示充值loading
                        binding.rechargeLoading.isVisible = true
                        binding.rechargeLoading.isClickable = true
                        binding.rechargeLoadingIv.startAnimation(
                            AnimationUtils.loadAnimation(
                                curActivity,
                                R.anim.anim_rotation_linear_2
                            )
                        )

                        Analytics.track(
                            EventConstants.EVENT_NO_ENOUGH_COIN_PAY_VIEW_CLICK,
                            "gamecode" to msg.gameId,
                            "commodityid" to (msg.productCode ?: ""),
                            "propprice" to price.toString(),
                            "balance" to balance.toString(),
                            "productid" to viewData.productId,
                            "product" to (msg.productName ?: ""),
                            "currencycode" to viewData.currencyCode,
                            "price" to viewData.price.toString(),
                            "choice" to "confirm",
                            // 是否有premium优惠(只看是不是会员, 即便会员赠送为0), 没有0, 有1
                            "page_style" to if (viewData.isMember) "1" else "0",
                        )
                        payInteractor.startPay(
                            curActivity,
                            CommonPayParams(
                                ourProductId = viewData.ourProductId,
                                parentProductId = viewData.parentProductId,
                                source = null,
                                gameId = msg.gameId,
                                scene = IAPConstants.IAP_SCENE_PG_COIN,
                                productId = viewData.productId,
                                price = viewData.price,
                                sceneCode = IAPConstants.IAP_SCENE_CODE_PG_COIN,
                                currencyCode = viewData.currencyCode,
                            ),
                            viewData.extra,
                            getFragmentName()
                        ) { payResult ->
                            when (payResult.iapScene) {
                                IAPConstants.IAP_SCENE_PG_COIN -> {
                                    scopeIO.launch(Dispatchers.Main) {
                                        // Android不允许自定义View的Toast在后台弹出
                                        // 微信支付的回调较快, 得到支付结果时, App还处在后台, Toast无法显示, 所以需要在onResumed时执行
                                        withResumed {
                                            scopeIO.launch(Dispatchers.Main) {
                                                // 延迟100ms, 让用户看到当前的弹窗
                                                delay(100)
                                                if (isRechargeLoading && isVisible && !isDetached) {
                                                    isRechargeLoading = false
                                                    if (isBindingAvailable()) {
                                                        binding.rechargeLoading.isVisible = false
                                                    }
                                                    if (payResult.isSuccess) {
                                                        dismissWithResult(true)
                                                        ToastUtil.showWithIcon(
                                                            R.drawable.icon_toast_recharge_success,
                                                            R.string.iap_recharge_success
                                                        )
                                                    } else {
                                                        if (payResult.code == IPayInteractor.FAIL_CANCEL) {
                                                            ToastUtil.showWithIcon(
                                                                R.drawable.icon_toast_recharge_failed,
                                                                getString(
                                                                    R.string.iap_recharge_failed
                                                                )
                                                            )
                                                        } else {
                                                            ToastUtil.showWithIcon(
                                                                R.drawable.icon_toast_recharge_failed,
                                                                payResult.reason.ifEmpty {
                                                                    getString(
                                                                        R.string.iap_recharge_failed
                                                                    )
                                                                }
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    binding.rechargePrivacyLayout.isVisible = viewData.showPrivacy
                    if (viewData.showPrivacy) {
                        binding.rechargePrivacy.setOnAntiViolenceClickListener {
                            MetaRouterWrapper.Web.goProtocolWeb(
                                curActivity,
                                viewData.rechargePrivacyUrl
                            )
                        }
                    }
                }
            } else {
                ToastUtil.showShort(viewDataResult.message ?: "network error")
                send2Ue(
                    DataResult.Error(507, "network error", data = null),
                    msg.cpOrderId ?: ""
                )
                dismissWithResult(false)
            }
        }
    }

    override fun loadFirstData() {
    }

    private fun dismissWithResult(isSuccess: Boolean) {
        mViewData = null
        isRechargeLoading = false
        callback.invoke(isSuccess)
        dismissAllowingStateLoss()
    }

    private fun handleCloseDialog(msg: RechargeArkMsg?) {
        if (msg != null) {
            Analytics.track(
                EventConstants.EVENT_NO_ENOUGH_COIN_PAY_VIEW_CLICK,
                // 游戏code码，如果没有则为null
                "gamecode" to msg.gameId,
                // 游戏内道具的ID，多个时用","分隔上传
                "commodityid" to (msg.productCode ?: ""),
                // 消耗订单支付代币的价格，对应红军订单的paymount，比如游戏里面有个道具 400 代币，这里就是 400
                "propprice" to price.toString(),
                // 现有G$的账户余额
                "balance" to balance.toString(),
                // 推荐的内购商品ID（google/ios配置的id）
                "productid" to (mViewData?.productId ?: ""),
                // google读出来的商品名称
                "product" to (msg.productName ?: ""),
                // 货币类型：美元、人民币等
                "currencycode" to (mViewData?.currencyCode ?: ""),
                // 谷歌那边读到的价格（单位：分）
                "price" to (mViewData?.price ?: "0").toString(),
                // 确认：confirm；取消：cancel
                "choice" to "cancel",
                // 是否有premium优惠(只看是不是会员, 即便会员赠送为0), 没有0, 有1
                "page_style" to if (mViewData?.isMember == true) "1" else "0",
            )
        }
        send2Ue(
            DataResult.Error(504, "cancel by user", data = null),
            msg?.cpOrderId ?: ""
        )
        dismissWithResult(false)
    }

    override fun onBackPressed(): Boolean {
        if (isRechargeLoading && isVisible && !isDetached) {
            payInteractor.cancelPay()
        }
        handleCloseDialog(rechargeMsg)
        return super.onBackPressed()
    }

    var isDestroy = false
    override fun onDestroy() {
        isDestroy = true
        super.onDestroy()
    }

    private fun send2Ue(it: DataResult<*>, cpOrderId: String) {
        UGCProtocolSender.sendProtocol(
            ProtocolSendConstant.PROTOCOL_PAY_CALLBACK,
            0,
            HashMap<String, Any>().apply {
                this["code"] = it.code ?: 0
                this["message"] = it.message ?: ""
                this["cpOrderId"] = cpOrderId
            }
        )
    }
}