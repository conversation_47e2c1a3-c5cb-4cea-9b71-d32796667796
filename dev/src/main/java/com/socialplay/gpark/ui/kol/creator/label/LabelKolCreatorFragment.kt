package com.socialplay.gpark.ui.kol.creator.label

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.label.CommonLabelInfo
import com.socialplay.gpark.data.model.creator.label.KolCreatorLabel
import com.socialplay.gpark.databinding.FragmentLabelKolCreatorBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.kol.creator.IKolMoreCreatorAction
import com.socialplay.gpark.ui.kol.creator.kolMoreCreatorItem
import com.socialplay.gpark.ui.kol.list.adapter.KolCreatorLabelAdapter
import com.socialplay.gpark.ui.kol.popup.KolGameLabelDialog
import com.socialplay.gpark.ui.view.center.CenterLayoutManager
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getCurrentOffset
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.scrollWithOffset
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import kotlinx.parcelize.Parcelize


/**
 * @param labelList 标签列表
 */
@Parcelize
data class LabelKolCreatorFragmentArgs(val labelList: List<KolCreatorLabel>) : Parcelable

/**
 * Created by bo.li
 * Date: 2024/8/9
 * Desc: kol 标签创作者
 */
class LabelKolCreatorFragment :
    BaseRecyclerViewFragment<FragmentLabelKolCreatorBinding>(R.layout.fragment_label_kol_creator) {

    private val viewModel: LabelKolCreatorViewModel by fragmentViewModel()

    private var listListener: IKolMoreCreatorAction? = null
    private lateinit var epoxyVisibilityTracker: EpoxyVisibilityTracker
    private val labelAdapter by lazy { KolCreatorLabelAdapter(Glide.with(this)) }

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rvMoreCreator

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentLabelKolCreatorBinding? {
        return FragmentLabelKolCreatorBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        listListener = getInitListener()
        epoxyVisibilityTracker = EpoxyVisibilityTracker().apply {
            attach(recyclerView)
        }
        binding.title.setOnBackAntiViolenceClickedListener {
            navigateUp()
        }
        initLabel()
        initData()
    }

    private fun initData() {
        viewModel.onEach(LabelKolCreatorModelState::labelList) {
            val showLabel = it.size > 3
            binding.includeLabel.ivMoreLabel.isVisible = showLabel
            binding.includeLabel.vMoreLabel.isVisible = showLabel
            labelAdapter.setList(it)
        }
        viewModel.onEach(LabelKolCreatorModelState::selectedLabelId) {
            val (ok, pos, offset) = binding.includeLabel.rvLabel.getCurrentOffset()
            if (ok) {
                binding.includeLabel.rvLabel.scrollWithOffset(pos, offset - 16.dp)
            }
        }
        viewModel.registerAsyncErrorToast(LabelKolCreatorModelState::asyncCreatorList)
        viewModel.registerAsyncErrorToast(LabelKolCreatorModelState::followResult)
        viewModel.setupRefreshLoading(
            LabelKolCreatorModelState::asyncCreatorList,
            binding.loadingMoreCreator,
            binding.refresh
        ) {
            viewModel.refreshCreatorList()
        }
    }

    private fun initLabel() {
        labelAdapter.setOnItemClickListener { _, view, position ->
            val item = labelAdapter.getItem(position)
            Analytics.track(
                EventConstants.EVENT_STAR_CREATOR_TAG_CLICK,
                EventParamConstants.KEY_TAGID to "${item.tagId}",
                EventParamConstants.KEY_TYPE to EventConstants.Type.TYPE_STAR_CREATOR_LABEL_MORE_BRIEF,
            )
            viewModel.selectLabel(item.tagId)
        }
        binding.includeLabel.rvLabel.layoutManager =
            CenterLayoutManager(context, RecyclerView.HORIZONTAL, false)
        binding.includeLabel.rvLabel.setPaddingEx(16.dp)
        binding.includeLabel.rvLabel.adapter = labelAdapter

        binding.includeLabel.ivMoreLabel.setOnAntiViolenceClickListener {
            showCreatorLabelDialog()
        }
    }

    private fun showCreatorLabelDialog() {
        withState(viewModel) {
            MetaRouter.Kol.showAllLabelDialog(
                this,
                CommonLabelInfo.buildByCreatorLabel(it.labelList),
                KolGameLabelDialog.KEY_REQUEST_LABEL_CREATOR
            ) {
                val tagId = it?.id?.toIntOrNull() ?: 0
                Analytics.track(
                    EventConstants.EVENT_STAR_CREATOR_TAG_CLICK,
                    EventParamConstants.KEY_TAGID to "${tagId}",
                    EventParamConstants.KEY_TYPE to EventConstants.Type.TYPE_STAR_CREATOR_LABEL_MORE_ALL,
                )
                viewModel.selectLabel(tagId)
            }
        }
    }


    private fun getInitListener() = object : IKolMoreCreatorAction {
        override fun goProfile(uuid: String) {
            MetaRouter.Profile.other(
                this@LabelKolCreatorFragment,
                uuid,
                EventConstants.From.FROM_KOL_CREATOR_MORE_STAR
            )
        }

        override fun changeFollow(uuid: String, toFollow: Boolean) {
            viewModel.changeFollow(uuid, toFollow)
        }

        override fun onItemShow(uuid: String) {
            withState(viewModel) {
                Analytics.track(
                    EventConstants.EVENT_STAR_CREATOR_SHOW,
                    EventParamConstants.KEY_USERID to uuid,
                    EventParamConstants.KEY_TAGID to it.selectedLabelId.toString(),
                    EventParamConstants.KEY_TYPE to EventConstants.Type.TYPE_KOL_CREATOR_MORE_STAR,
                )
            }
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    override fun epoxyController(): EpoxyController = simpleController(
        viewModel,
        LabelKolCreatorModelState::creatorList,
        LabelKolCreatorModelState::creatorLoadMore
    ) { list, loadMore ->
        // 列表
        list.forEachIndexed { index, user ->
            kolMoreCreatorItem(
                getPageName(),
                user,
                true,
                listListener
            )
        }
        // 加载更多
        if (list.isNotEmpty()) {
            loadMoreFooter(loadMore) {
                viewModel.loadMoreCreatorList()
            }
        }
    }

    override fun onDestroyView() {
        epoxyVisibilityTracker.detach(recyclerView)
        binding.includeLabel.rvLabel.adapter = null
        super.onDestroyView()
    }

    override fun getPageName(): String =
        PageNameConstants.FRAGMENT_NAME_KOL_MORE_CREATOR_STAR
}