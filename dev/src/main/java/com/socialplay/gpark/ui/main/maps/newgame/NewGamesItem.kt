package com.socialplay.gpark.ui.main.maps.newgame

import android.view.View
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.Priority
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.MapsNewestList
import com.socialplay.gpark.databinding.ItemNewGameBinding
import com.socialplay.gpark.ui.core.GlideGetter
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.util.DateUtil.formatAgoStyleForChat
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visibleList
import com.socialplay.gpark.util.getStringByGlobal


fun EpoxyController.addGameItem(
    index: Int,
    item: MapsNewestList,
    onShow: () -> Unit,
    onClick: () -> Unit,
    getGlide: GlideGetter
) {
    add(GameItem(index, item, onShow, onClick, getGlide).apply {
        id("GameItem ${item.ugid}")
        spanSizeOverride { _, _, _ -> 1 }
    })
}


data class GameItem(
    val index: Int,
    val item: MapsNewestList,
    val onShow: () -> Unit,
    val onClick: () -> Unit,
    val getGlide: GlideGetter
) : ViewBindingItemModel<ItemNewGameBinding>(
    R.layout.item_new_game,
    ItemNewGameBinding::bind
) {

    override fun ItemNewGameBinding.onBind() {
        getGlide()?.run {
            load(item.banner).priority(Priority.HIGH)
                .placeholder(R.drawable.placeholder_corner_12)
                .transform(CenterCrop(), RoundedCorners(12.dp))
                .into(ivIcon)
        }
        tvName.text = item.ugcGameName
        if (item.showTimeType == 1) {
            // 展示发布时间
            tvTime.text = getStringByGlobal(R.string.posted_time, item.releaseTime?.formatAgoStyleForChat() ?: "")
        } else {
            // 展示更新时间
            tvTime.text = getStringByGlobal(R.string.updated_time, item.updateTime?.formatAgoStyleForChat() ?: "")
        }
        visibleList(ivTagBg, ivTag, visible = item.isStrongInsertionGame == true)
        root.setOnAntiViolenceClickListener {
            onClick.invoke()
        }
    }

    override fun onViewAttachedToWindow(view: View) {
        super.onViewAttachedToWindow(view)
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE) {
            onShow.invoke()
        }
    }

    override fun ItemNewGameBinding.onUnbind() {
        root.unsetOnClick()
    }

}