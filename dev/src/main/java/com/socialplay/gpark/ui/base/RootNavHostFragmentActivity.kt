package com.socialplay.gpark.ui.base

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.annotation.IdRes
import androidx.core.view.LayoutInflaterCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.commitNow
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavHostController
import androidx.navigation.Navigation
import androidx.navigation.Navigator
import androidx.navigation.findNavController
import androidx.navigation.fragment.FragmentNavigator
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ActivityFragmentNavHostBinding
import com.socialplay.gpark.function.apm.page.view.PageMonitorLayoutInflaterFactory2
import com.socialplay.gpark.ui.view.MetaTextView
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.property.viewBinding
import timber.log.Timber

class RootNavHostFragmentHorizontalActivity : BaseRootNavHostFragmentActivity() {

    companion object {

        fun start(context: Context, @IdRes navDestId: Int, extras: Bundle? = null) {
            start(
                RootNavHostFragmentHorizontalActivity::class.java,
                context,
                navDestId,
                extras
            )
        }
    }
}

class RootNavHostFragmentActivity : BaseRootNavHostFragmentActivity() {

    companion object {

        fun start(context: Context, @IdRes navDestId: Int, extras: Bundle? = null) {
            start(
                RootNavHostFragmentActivity::class.java,
                context,
                navDestId,
                extras
            )
        }
    }
}

abstract class BaseRootNavHostFragmentActivity: BaseActivity() {

    companion object {
        const val ARG_NAV_DEST_ID = "nav_dest_id"

        fun start(cls: Class<*>, context: Context, @IdRes navDestId: Int, extras: Bundle? = null) {
            val intent = Intent(context, cls)
            intent.putExtra(ARG_NAV_DEST_ID, navDestId)
            if (context !is Activity) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            extras?.let { intent.putExtras(it) }
            context.startActivity(intent)
        }
    }

    override val binding by viewBinding(ActivityFragmentNavHostBinding::inflate)

    private var navDestId = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        if (layoutInflater.factory == null && layoutInflater.factory2 == null) {
            LayoutInflaterCompat.setFactory2(layoutInflater, object : LayoutInflater.Factory2 {
                override fun onCreateView(parent: View?, name: String, context: Context, attrs: AttributeSet): View? {
                    val mName = if (name == "TextView" || name == "androidx.appcompat.widget.AppCompatTextView") {
                        MetaTextView::class.java.name
                    } else {
                        name
                    }
                    return PageMonitorLayoutInflaterFactory2.onCreateView(name, context, attrs)
                        ?: delegate.createView(parent, mName, context, attrs)
                }

                override fun onCreateView(name: String, context: Context, attrs: AttributeSet): View? {
                    return null
                }
            })
        }
        super.onCreate(savedInstanceState)
        StatusBarUtil.setTransparent(this)
        navDestId = intent.getIntExtra("nav_dest_id", 0)
        Timber.d("onCreate navDestId %s", navDestId)
        if (navDestId == 0) {
            finish()
            return
        }
        initNavHostFragment()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        navDestId = intent?.getIntExtra("nav_dest_id", 0) ?: 0
        Timber.d("onNewIntent navDestId %s", navDestId)
        if (navDestId == 0) {
            finish()
            return
        }
        findNavController(R.id.nav_host_fragment).let {
            it.navigatorProvider.getNavigator(AutoFinishFragmentNavigator::class.java).finishId =
                navDestId
            it.navigate(navDestId, intent?.extras)
        }
    }

    private fun initNavHostFragment() {
        val existHostFragment =
            supportFragmentManager.findFragmentById(R.id.nav_host_fragment) as? AutoFinishNavHostFragment
        val hostFragment = existHostFragment ?: AutoFinishNavHostFragment()

        hostFragment.arguments = intent.extras
        supportFragmentManager.commitNow(true) {
            if (existHostFragment == hostFragment) { //Activity恢复实例的情况
                show(hostFragment)
            } else {
                add(R.id.nav_host_fragment, hostFragment)
            }
            setPrimaryNavigationFragment(hostFragment)
        }
        Navigation.setViewNavController(binding.navHostFragment, hostFragment.navController)

        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val curDestinationId = hostFragment.navController.currentBackStackEntry?.destination?.id
                val preDestinationId = hostFragment.navController.previousBackStackEntry?.destination?.id
                when {
                    curDestinationId == navDestId && preDestinationId == R.id.stub_start -> {
                        finish()
                    }

                    curDestinationId == R.id.stub_start && preDestinationId == null -> {
                        finish()
                    }

                    else -> {
                        hostFragment.navController.popBackStack()
                    }
                }
            }
        }
        onBackPressedDispatcher.addCallback(this, callback)
    }

    override fun navigateUpTo(upIntent: Intent?): Boolean {
        return findNavController(R.id.nav_host_fragment).navigateUp()
    }
}


@Navigator.Name("fragment")
private class AutoFinishFragmentNavigator(
    private val context: Context,
    private val fragmentManager: FragmentManager,
    private val containerId: Int,
) : FragmentNavigator(context, fragmentManager, containerId) {

    internal var finishId = 0

    override fun popBackStack(popUpTo: NavBackStackEntry, savedState: Boolean) {

        if (finishId != 0 && popUpTo.destination.id == finishId && popUpTo.arguments?.containsKey(BaseRootNavHostFragmentActivity.ARG_NAV_DEST_ID) == true) {
            (context as? Activity)?.finish()
            return
        }
        super.popBackStack(popUpTo, savedState)
    }

}

class AutoFinishNavHostFragment : NavHostFragment() {


    override fun onCreate(savedInstanceState: Bundle?) {
        kotlin.runCatching {
            navController.graph
            error("graph should be null ${navController.graph}")
        }
        val navDestId = arguments?.getInt("nav_dest_id") ?: 0
        Timber.d("StubStartFragment navDestId %s", navDestId)
        if (navDestId == 0) {
            requireActivity().finish()
            return
        }
        navController.navigatorProvider.getNavigator(AutoFinishFragmentNavigator::class.java).finishId = navDestId
        val graphInflater = navController.navInflater
        val navGraph = graphInflater.inflate(R.navigation.root)
        navGraph.setStartDestination(R.id.stub_start)
        navController.setGraph(navGraph, arguments)

        super.onCreate(savedInstanceState)
    }

    override fun onCreateNavHostController(navHostController: NavHostController) {
        super.onCreateNavHostController(navHostController)
        navHostController.navigatorProvider.addNavigator(
            AutoFinishFragmentNavigator(
                requireContext(),
                childFragmentManager,
                id,
            )
        )
    }
}

class StubStartFragment : Fragment() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val navDestId = arguments?.getInt("nav_dest_id") ?: 0
        Timber.d("StubStartFragment navDestId %s", navDestId)
        if (navDestId == 0) {
            requireActivity().finish()
            return
        }
        findNavController().navigate(navDestId, arguments)
    }
}