package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import com.socialplay.gpark.util.StatusBarUtil

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2021/06/08
 *     desc   : 状态栏占位视图，就是一个高度为状态栏高的视图
 */
class StatusBarPlaceHolderView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : View(context, attrs, defStyleAttr) {

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val statusBarHeight = StatusBarUtil.getStatusBarHeight(context)
        setMeasuredDimension(MeasureSpec.getSize(widthMeasureSpec), statusBarHeight)
    }
}