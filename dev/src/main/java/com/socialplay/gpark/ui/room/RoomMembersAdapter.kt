package com.socialplay.gpark.ui.room

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bumptech.glide.Glide
import com.socialplay.gpark.data.model.room.RoomMemberInfo
import com.socialplay.gpark.databinding.AdapterMembersBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder

/**
 * create by: bin on 2023/7/12
 */
class RoomMembersAdapter:BaseAdapter<RoomMemberInfo,AdapterMembersBinding>() {
    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int,
    ): AdapterMembersBinding {
        return AdapterMembersBinding.inflate(LayoutInflater.from(parent.context))
    }

    override fun convert(
        holder: BindingViewHolder<AdapterMembersBinding>,
        item: RoomMemberInfo,
        position: Int,
    ) {
        Glide.with(holder.binding.iv).load(item.avatar).circleCrop().into(holder.binding.iv)
    }
}