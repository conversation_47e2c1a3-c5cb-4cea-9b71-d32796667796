package com.socialplay.gpark.ui.view.pickerview.adapter;


import android.content.Context;

import com.socialplay.gpark.R;
import com.socialplay.gpark.ui.view.wheelview.adapter.WheelAdapter;

import org.koin.core.context.GlobalContext;

/**
 * Numeric Wheel adapter.
 */
public class NumericMonthWheelAdapter implements WheelAdapter {

	private final int minValue;
	private final int maxValue;

	String[] getMonthArray(Context context) {
		return new String[]{
				context.getString(R.string.january_shorthand),
				context.getString(R.string.february_shorthand),
				context.getString(R.string.march_shorthand),
				context.getString(R.string.april_shorthand),
				context.getString(R.string.may_shorthand),
				context.getString(R.string.june_shorthand),
				context.getString(R.string.july_shorthand),
				context.getString(R.string.august_shorthand),
				context.getString(R.string.september_shorthand),
				context.getString(R.string.october_shorthand),
				context.getString(R.string.november_shorthand),
				context.getString(R.string.december_shorthand),
		};
	}

	/**
	 * Constructor
	 * @param minValue the wheel min value
	 * @param maxValue the wheel max value
	 */
	public NumericMonthWheelAdapter(int minValue, int maxValue) {
		this.minValue = minValue;
		this.maxValue = maxValue;
	}

	@Override
	public Object getItem(int index, Context context) {
		String[] monthArray = getMonthArray(context);
		if (index >= 0 && index < getItemsCount()) {
			return monthArray[index];
		}
		return 0;
	}

	@Override
	public int getItemsCount() {
		return maxValue - minValue + 1;
	}
	
	@Override
	public int indexOf(Object o){
		try {
			return (int)o - minValue;
		} catch (Exception e) {
			return -1;
		}

	}
}
