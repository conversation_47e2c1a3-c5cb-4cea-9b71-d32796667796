package com.socialplay.gpark.ui.gamedetail.unify

import android.animation.ValueAnimator
import android.graphics.Rect
import android.os.Bundle
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.CallSuper
import androidx.core.text.set
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.PagingApiResult
import com.socialplay.gpark.data.model.feedback.SubmitNewFeedbackRequest
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostCommentListRequestBody
import com.socialplay.gpark.data.model.share.ShareFeature
import com.socialplay.gpark.databinding.FragmentGameDetailCommonBinding
import com.socialplay.gpark.databinding.PopUpGameDetailCommonBinding
import com.socialplay.gpark.databinding.PopUpGameDetailCommonCommentBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.share.IGlobalShareCallback
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.core.views.divider
import com.socialplay.gpark.ui.core.views.empty
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.editor.detail.comment.IUgcCommentListener
import com.socialplay.gpark.ui.editor.detail.comment.ugcCommentExpandItem
import com.socialplay.gpark.ui.editor.detail.comment.ugcCommentItem
import com.socialplay.gpark.ui.editor.detail.comment.ugcCommentLoading
import com.socialplay.gpark.ui.editor.detail.comment.ugcReplyItem
import com.socialplay.gpark.ui.feedback.FeedbackTypeWrapper
import com.socialplay.gpark.ui.gamedetail.lightup.SparkDialog
import com.socialplay.gpark.ui.gamedetail.lightup.SparkState
import com.socialplay.gpark.ui.gamedetail.lightup.SparkViewModel
import com.socialplay.gpark.ui.view.PopupWindowCompat
import com.socialplay.gpark.ui.view.center.CenterImageSpan
import com.socialplay.gpark.util.DateUtil.formatUpdateDate
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.addListener
import com.socialplay.gpark.util.extension.addModelBuildListener
import com.socialplay.gpark.util.extension.addOnOffsetChangedListener
import com.socialplay.gpark.util.extension.addUpdateListener
import com.socialplay.gpark.util.extension.attachV2
import com.socialplay.gpark.util.extension.clearCompoundDrawables
import com.socialplay.gpark.util.extension.compoundDrawables
import com.socialplay.gpark.util.extension.doOnLayoutChanged
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.getDrawableByRes
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setDraggableForever
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visibleList
import kotlin.math.abs

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/22
 *     desc   :
 * </pre>
 */
abstract class BaseGameDetailCommonFragment :
    BaseRecyclerViewFragment<FragmentGameDetailCommonBinding>(R.layout.fragment_game_detail_common),
    IGlobalShareCallback {

    companion object {
        const val GAME_TYPE_PGC = 1
        const val GAME_TYPE_UGC = 2
        const val GAME_TYPE_UGC_DESIGN = 3

        const val FEAT_REPORT = 1
    }

    private var offset = 0

    private var gameNameTitleBarShow = false
    private var gameNameHeight = 0

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rvComment

    protected val sparkVm: SparkViewModel by fragmentViewModel()

    protected var scrollToTop = false
    protected var needLocate = false
    protected var locatePosition = 0
    protected var likeAnim = false

    protected lateinit var popupWindow: PopupWindowCompat
    protected val popupBinding by lazy { PopUpGameDetailCommonBinding.inflate(layoutInflater) }

    protected lateinit var popupWindowComment: PopupWindowCompat
    protected val popupBindingComment by lazy { PopUpGameDetailCommonCommentBinding.inflate(layoutInflater) }

    protected var pendingRefreshAnim: ValueAnimator? = null

    protected var trackGameReviewShow = true

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentGameDetailCommonBinding? {
        return FragmentGameDetailCommonBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        childFragmentManager.setFragmentResultListener(
            GameDetailMoreDialog.TAG,
            viewLifecycleOwner
        ) { _, bundle ->
            val function = bundle.getInt(GameDetailMoreDialog.KEY_FUNCTION)
            when (function) {
                GameDetailMoreDialog.FUNCTION_FEEDBACK -> {
                    MetaRouter.Feedback.feedback(
                        this,
                        gameId,
                        SubmitNewFeedbackRequest.SOURCE_GAME_NUMBER,
                        FeedbackTypeWrapper.experience.id,
                        false,
                        needBackGame = false,
                        fromGameId = null
                    )
                }
            }
        }

        likeAnim = false
        trackGameReviewShow = true

        binding.lavLikeAnim.setMinAndMaxProgress(0.0f, 1.0f)

        binding.tbl.setOnBackAntiViolenceClickedListener {
            navigateUp()
        }
        binding.tvBuildBtn.setOnAntiViolenceClickListener {
            MetaRouter.MobileEditor.creation(this, source = "build")
        }
        binding.ivMoreBtn.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.EVENT_FEEDBACK_SHOW_PAGE) {
                put("gameid", gameId)
                put("source", SubmitNewFeedbackRequest.SOURCE_GAME_NUMBER)
            }
            if (enableShare) {
                onMoreClick()
            } else {
                GameDetailMoreDialog.show(this, false)
            }
        }
        binding.layerLightUp.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.UGC_LIGHT_UP_CLICK)
            SparkDialog().show(childFragmentManager, "SparkDialog")
        }

        gameNameTitleBarShow = false
        binding.tvGameName.doOnLayoutChanged(viewLifecycleOwner) {
            gameNameHeight = it.bottom
            if (!gameNameTitleBarShow && offset >= gameNameHeight) {
                gameNameTitleBarShow = true
                binding.tvTitleBarGameName.translationY = 0.0f
            }
        }
        binding.abl.addOnOffsetChangedListener(viewLifecycleOwner) { _, offset ->
            val absOffset = abs(offset)
            this.offset = absOffset
            if (gameNameHeight > 0) {
                if (absOffset >= gameNameHeight) {
                    if (!gameNameTitleBarShow) {
                        gameNameTitleBarShow = true
                        binding.tvTitleBarGameName
                            .animate()
                            .cancel()
                        binding.tvTitleBarGameName
                            .animate()
                            .alpha(1.0f)
                            .translationY(0.0f)
                            .setDuration(200)
                    }
                } else {
                    if (gameNameTitleBarShow) {
                        gameNameTitleBarShow = false
                        binding.tvTitleBarGameName
                            .animate()
                            .cancel()
                        binding.tvTitleBarGameName
                            .animate()
                            .alpha(0.0f)
                            .translationY(dp(12).toFloat())
                            .setDuration(200)
                    }
                }
            }
        }
        binding.abl.setDraggableForever(viewLifecycleOwner)
        epoxyController.addModelBuildListener(viewLifecycleOwner) {
            if (needLocate) {
                needLocate = false
                scrollToTop = false
                binding.abl.setExpanded(false, true)
                binding.rvComment.smoothScrollToPosition(
                    locatePosition.coerceIn(
                        0,
                        epoxyController.adapter.itemCount - 1
                    )
                )
            } else if (scrollToTop) {
                scrollToTop = false
                binding.rvComment.scrollToPosition(0)
            }
        }
        binding.rvNotice.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
        EpoxyVisibilityTracker().attachV2(viewLifecycleOwner, binding.rvComment)
        initPopup()

        sparkVm.onEach(
            SparkState::sparkTotal2Work,
            SparkState::sparkCount2Work,
        ) { sparkTotal2Work, sparkCount2Work ->
            if (!PandoraToggle.enableLightUp || sparkTotal2Work == null || sparkCount2Work == null) {
                return@onEach
            }
            visibleList(
                binding.ivLightUp,
                binding.tvLightUp,
                binding.layerLightUp,
                visible = true
            )
            if (sparkCount2Work > 0) {
                binding.ivLightUp.setImageResource(R.drawable.ic_light_up_red)
                binding.tvLightUp.setTextColor(getColorByRes(R.color.color_FF5F42))
            } else {
                binding.ivLightUp.setImageResource(R.drawable.ic_light_up)
                binding.tvLightUp.setTextColor(getColorByRes(R.color.color_1A1A1A))
            }
            binding.tvLightUp.text = UnitUtil.formatKMCount(sparkTotal2Work)

        }
        sparkVm.onAsync(
            SparkState::lightUpResult,
            deliveryMode = uniqueOnly()
        ) {
            val (count, msg) = it
            if (count > 0L) {
                toast(R.string.light_up_succeeded)
            } else {
                toast(msg ?: getString(R.string.common_failed))
            }
        }
        sparkVm.registerToast(SparkState::toast)
    }

    private fun initPopup() {
        popupWindow = PopupWindowCompat(
            popupBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
            animationStyle = R.style.PopupAnimationGameDetailCommon
        }
        popupBinding.root.setOnClickListener {
            popupWindow.dismiss()
        }
        popupBinding.vDefaultClick.setOnAntiViolenceClickListener {
            updateSortType(PostCommentListRequestBody.QUERY_TYPE_DEFAULT)
            popupWindow.dismiss()
        }
        popupBinding.vNewestClick.setOnAntiViolenceClickListener {
            updateSortType(PostCommentListRequestBody.QUERY_TYPE_TOP)
            popupWindow.dismiss()
        }
        popupBinding.vHotClick.setOnAntiViolenceClickListener {
            updateSortType(PostCommentListRequestBody.QUERY_TYPE_LIKE)
            popupWindow.dismiss()
        }
        popupBinding.vAuthorOnlyClick.setOnAntiViolenceClickListener {
            updateFilterType(PostCommentListRequestBody.FILTER_AUTHOR)
            popupWindow.dismiss()
        }
        popupBinding.vSelfOnlyClick.setOnAntiViolenceClickListener {
            updateFilterType(PostCommentListRequestBody.FILTER_SELF)
            popupWindow.dismiss()
        }
        binding.tvSortBtn.setOnAntiViolenceClickListener {
            popupBinding.cv.measure(
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )
            val x = -popupBinding.cv.measuredWidth + it.measuredWidth - dp(32)
            val rawY = -dp(26)
            val rect = Rect()
            it.getGlobalVisibleRect(rect)
            val rawBottom = rect.bottom + popupBinding.cv.measuredHeight + rawY
            binding.root.getGlobalVisibleRect(rect)
            val y = if (rawBottom > rect.bottom) {
                -popupBinding.cv.measuredHeight - it.measuredHeight - dp(6)
            } else {
                rawY
            }
            popupWindow.showAsDropDownByLocation(it, x, y, autoHeight = false)
        }

         popupWindowComment = PopupWindowCompat(
            popupBindingComment.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
            animationStyle = 0
        }
        popupBindingComment.root.setOnClickListener {
            popupWindowComment.dismiss()
        }
        popupWindowComment.setOnDismissListener {
            popupBindingComment.vCopyClick.unsetOnClick()
            popupBindingComment.vPinClick.unsetOnClick()
            popupBindingComment.vUnpinClick.unsetOnClick()
            popupBindingComment.vReportClick.unsetOnClick()
            popupBindingComment.vDeleteClick.unsetOnClick()
        }
    }

    protected fun updateSortTypeText(sortType: Int, filterType: Int?) {
        val resId = when (filterType) {
            PostCommentListRequestBody.FILTER_AUTHOR -> {
                R.string.sort_author_only
            }

            PostCommentListRequestBody.FILTER_SELF -> {
                R.string.sort_self_only
            }

            else -> {
                when (sortType) {
                    PostCommentListRequestBody.QUERY_TYPE_DEFAULT -> {
                        R.string.sort_hot
                    }

                    PostCommentListRequestBody.QUERY_TYPE_TOP -> {
                        R.string.sort_newest
                    }

                    PostCommentListRequestBody.QUERY_TYPE_LIKE -> {
                        R.string.sort_hottest
                    }

                    else -> {
                        return
                    }
                }
            }
        }
        binding.tvSortBtn.setText(resId)
    }

    protected fun animateCommentRefresh(height: Int) {
        pendingRefreshAnim?.cancel()
        pendingRefreshAnim = ValueAnimator.ofInt(binding.llCommentRefresh.height, height).apply {
            duration = 100L
            addUpdateListener(viewLifecycleOwner) {
                if (isBindingAvailable()) {
                    binding.llCommentRefresh.setHeight(it.animatedValue as Int)
                }
            }
            addListener(viewLifecycleOwner, onEnd = {
                pendingRefreshAnim = null
            })
            start()
        }
    }

    protected fun updatePv(pv: Long) {
        binding.tvPv.setTextWithArgs(R.string.played_x_times, UnitUtil.formatKMCount(pv))
    }

    protected fun updateTime(ts: Long) {
        val updateTime = getString(
            R.string.detail_update_time,
            ts.formatUpdateDate(requireContext())
        )
        binding.tvUpdateTime.text = updateTime
    }

    protected fun updateMwCompatibilityTips(mwTips: String?) {
        //MW引擎不兼容提示
        binding.tvMwNotCompatible.isVisible = !mwTips.isNullOrEmpty()
        if (!mwTips.isNullOrEmpty()) {
            binding.tvMwNotCompatible.text =
                binding.getDrawableByRes(R.drawable.ic_warn_engine_not_compatible)?.let {
                    it.setBounds(0, 0, dp(14), dp(14))
                    val tips = SpannableString("_$mwTips")
                    tips[0, 1] = CenterImageSpan(it, right = dp(5))
                    tips
                } ?: mwTips
        }
    }

    protected fun updateFollow(isFollow: Boolean) {
        if (isFollow) {
            binding.tvFollowBtn.setBackgroundResource(R.drawable.bg_corner_s100_stroke_c999999_s05)
            binding.tvFollowBtn.clearCompoundDrawables(left = true)
            binding.tvFollowBtn.setTextColorByRes(R.color.color_999999)
            binding.tvFollowBtn.setText(R.string.following_cap)
        } else {
            binding.tvFollowBtn.setBackgroundResource(R.drawable.bg_corner_s100_stroke_c1a1a1a_s05)
            binding.tvFollowBtn.compoundDrawables(left = R.drawable.ic_add_1a1a1a_s12)
            binding.tvFollowBtn.setTextColorByRes(R.color.color_1A1A1A)
            binding.tvFollowBtn.setText(R.string.follow)
        }
//        if (isFollow) {
//            binding.tvFollowBtn.setBackgroundResource(R.drawable.shape_f5f5f5_corner_360)
//            binding.tvFollowBtn.setTextColorByRes(R.color.color_757575)
//            binding.tvFollowBtn.setText(R.string.following_cap)
//        } else {
//            binding.tvFollowBtn.setBackgroundResource(R.drawable.bg_424242_corner_360)
//            binding.tvFollowBtn.setTextColorByRes(R.color.white)
//            binding.tvFollowBtn.setText(R.string.follow)
//        }
    }

    protected fun updateMyAvatar(avatar: String?) {
        glide?.run {
            load(avatar).placeholder(R.drawable.icon_default_avatar)
                .circleCrop()
                .into(binding.ivMyAvatar)
        }
    }

    protected fun updateCommentStatus(
        commentList: Async<PagingApiResult<PostComment>>,
        loadMore: Async<LoadMoreState>
    ) {
        if (commentList is Success && loadMore is Success) {
            visibleList(
                binding.ivMyAvatar,
                binding.tvReplyHint,
                binding.ivEmojiBtn,
                binding.ivImageBtn,
                binding.tvSortBtn,
                visible = true
            )
            val commentCount = UnitUtil.formatKMCount(commentList.invoke().total)
            binding.tvComment.text = commentCount
            binding.tvCommentCount.text = commentCount
        }
    }

    @CallSuper
    override fun invokeShareFeature(feature: ShareFeature) {
        when (feature.featureId) {
            FEAT_REPORT -> {
                MetaRouter.Feedback.feedback(
                    this,
                    gameId,
                    SubmitNewFeedbackRequest.SOURCE_GAME_NUMBER,
                    FeedbackTypeWrapper.experience.id,
                    false,
                    needBackGame = false,
                    fromGameId = null
                )
            }
        }
    }

    protected fun MetaEpoxyController.buildCommentController(
        comments: Async<PagingApiResult<PostComment>>,
        loadMore: Async<LoadMoreState>,
        uniqueTag: Int,
        firstPin: Boolean
    ) {
        when (comments) {
            is Success -> {
                val list = comments().dataList
                if (list.isNullOrEmpty()) {
                    empty(
                        iconRes = R.drawable.icon_no_recent_activity,
                        top = dp(32)
                    ) {
                        getCommentList(true)
                    }
                } else {
                    val dp05 = dp(0.5)
                    val dp8 = dp(8)
                    val dp62 = dp(62)
                    val dp16 = dp(16)
                    val commentContentWidth = screenWidth - 78.dp
                    val replyContentWidth = screenWidth - 112.dp
                    val atColor = getColorByRes(R.color.color_0083FA)
                    list.forEachIndexed { commentPosition, comment ->
                        if (comment.isNewAdd) {
                            comment.isNewAdd = false
                            scrollToTop = true
                        }
                        ugcCommentItem(
                            uniqueTag,
                            comment,
                            commentPosition,
                            commentContentWidth,
                            true,
                            firstPin,
                            itemListener
                        )
                        if (comment.needLocate) {
                            comment.needLocate = false
                            needLocate = true
                            locatePosition = buildItemIndex
                        }
                        var showReplyItem = false
                        if (!comment.collapse) {
                            showReplyItem = (comment.authorReply?.size?: 0) + (comment.replyCommonPage?.dataList?.size ?: 0) > 0
                            comment.authorReply?.forEachIndexed { replyPosition, reply ->
                                ugcReplyItem(
                                    uniqueTag,
                                    reply,
                                    replyPosition,
                                    commentPosition,
                                    true,
                                    atColor,
                                    replyContentWidth,
                                    true,
                                    itemListener
                                )
                                if (reply.needLocate) {
                                    reply.needLocate = false
                                    needLocate = true
                                    locatePosition = buildItemIndex
                                }
                            }
                            comment.replyCommonPage?.dataList?.forEachIndexed { replyPosition, reply ->
                                ugcReplyItem(
                                    uniqueTag,
                                    reply,
                                    replyPosition,
                                    commentPosition,
                                    false,
                                    atColor,
                                    replyContentWidth,
                                    true,
                                    itemListener
                                )
                                if (reply.needLocate) {
                                    reply.needLocate = false
                                    needLocate = true
                                    locatePosition = buildItemIndex
                                }
                            }
                        } else {
                            comment.replyCommonPage?.dataList?.forEachIndexed { replyPosition, reply ->
                                if (reply.forceShow) {
                                    showReplyItem = true
                                    ugcReplyItem(
                                        uniqueTag,
                                        reply,
                                        replyPosition,
                                        commentPosition,
                                        false,
                                        atColor,
                                        replyContentWidth,
                                        true,
                                        itemListener
                                    )
                                    if (reply.needLocate) {
                                        reply.needLocate = false
                                        needLocate = true
                                        locatePosition = buildItemIndex
                                    }
                                }
                            }
                        }
                        val showReplyButtons = comment.showReplyButtons
                        if (comment.loading) {
                            ugcCommentLoading(
                                uniqueTag,
                                comment
                            )
                        } else if (showReplyButtons) {
                            ugcCommentExpandItem(
                                uniqueTag,
                                comment,
                                commentPosition,
                                showReplyItem,
                                itemListener
                            )
                        }
                        divider(
                            height = dp05,
                            colorRes = R.color.color_E6E6E6,
                            marginLeft = dp62,
                            marginTop = dp16,
                            marginRight = dp16,
                            idStr = "GameCommentDivider-${uniqueTag}-${comment.commentId}"
                        )
                    }
                    loadMoreFooter(
                        loadMore,
                        idStr = "GameCommentFooter-${uniqueTag}",
                        endText = getString(R.string.community_article_comment_empty),
                        endTextColorRes = R.color.textColorSecondary
                    ) {
                        getCommentList(false)
                    }
                }
            }

            is Loading -> {
                loadMoreFooter(idStr = "GameCommentFooterLoading") {}
            }

            is Fail -> {
                empty(
                    iconRes = R.drawable.icon_no_recent_activity,
                    descRes = R.string.footer_load_failed,
                    top = dp(32)
                ) {
                    initCommentList()
                }
            }

            else -> {}
        }
    }

    override fun onDestroyView() {
        if (::popupWindow.isInitialized) {
            popupWindow.dismiss()
        }
        if (::popupWindowComment.isInitialized) {
            popupWindowComment.dismiss()
        }
        super.onDestroyView()
    }

    protected abstract val gameId: String
    protected abstract val gameType: Int
    protected abstract val enableShare: Boolean
    protected abstract val itemListener: IGameDetailCommonListener

    protected abstract fun getCommentList(isRefresh: Boolean)
    protected abstract fun initCommentList()
    protected abstract fun onMoreClick()

    @CallSuper
    protected open fun updateSortType(sortType: Int) {
        Analytics.track(
            EventConstants.GAME_REVIEW_TYPE_CHOOSE_CLICK,
            "type" to when (sortType) {
                PostCommentListRequestBody.QUERY_TYPE_DEFAULT -> {
                    1
                }

                PostCommentListRequestBody.QUERY_TYPE_LIKE -> {
                    2
                }

                PostCommentListRequestBody.QUERY_TYPE_TOP -> {
                    3
                }

                else -> {
                    return
                }
            },
            "creatortype" to gameType,
            "gameid" to gameId
        )
    }

    @CallSuper
    protected open fun updateFilterType(filterType: Int) {
        Analytics.track(
            EventConstants.GAME_REVIEW_TYPE_CHOOSE_CLICK,
            "type" to when (filterType) {
                PostCommentListRequestBody.FILTER_AUTHOR -> {
                    4
                }

                PostCommentListRequestBody.FILTER_SELF -> {
                    5
                }

                else -> {
                    return
                }
            },
            "creatortype" to gameType,
            "gameid" to gameId
        )
    }

    protected interface IGameDetailCommonListener : IUgcCommentListener, IGameOperationListener
}