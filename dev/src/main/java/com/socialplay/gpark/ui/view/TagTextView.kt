package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.text.InputFilter
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import com.socialplay.gpark.R

/**
 * xingxiu.hou
 * 2021/6/4
 */
class TagTextView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    private var paintColor = Color.TRANSPARENT
    private var radius = 0F
    private var round = RectF()

    private val paint = Paint().apply {
        isAntiAlias = true
    }

    init {
        val attributes = context.obtainStyledAttributes(attrs, R.styleable.TagTextView)
        try {
            paintColor = attributes.getColor(R.styleable.TagTextView_tagColor, Color.TRANSPARENT)
            radius = attributes.getDimension(R.styleable.TagTextView_tagRadius, 0F)
        } finally {
            attributes.recycle()
        }
        paint.color = paintColor
    }

    class Option {
        var visibility: Int = VISIBLE
        var text: String = ""
        var bgColor: Int = 0
        var fontColor: Int = 0
        var textMaxLength: Int = 0
    }

    fun setOption(option: Option) {
        visibility = option.visibility
        paint.color = option.bgColor
        filters = arrayOf(InputFilter.LengthFilter(option.textMaxLength))
        setTextColor(option.fontColor)
        text = option.text
        postInvalidate()
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        round.set(0F, 0F, width.toFloat(), height.toFloat())
    }

    override fun onDraw(canvas: Canvas) {
        canvas.drawRoundRect(round, radius, radius, paint)
        super.onDraw(canvas)
    }
}