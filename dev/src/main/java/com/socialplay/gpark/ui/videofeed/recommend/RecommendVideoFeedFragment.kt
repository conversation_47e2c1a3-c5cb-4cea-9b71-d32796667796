package com.socialplay.gpark.ui.videofeed.recommend

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.videofeed.VideoFeedArgs
import com.socialplay.gpark.databinding.FragmentRecommendVideoFeedBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.core.statusbar.StatusBarStateProvider
import com.socialplay.gpark.ui.videofeed.VideoFeedCoordinateHelper
import com.socialplay.gpark.ui.videofeed.VideoFeedFragment
import com.socialplay.gpark.ui.videofeed.publish.VideoPublishGuideHelper
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.extension.navColor
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible

class RecommendVideoFeedFragment :
    BaseFragment<FragmentRecommendVideoFeedBinding>(R.layout.fragment_recommend_video_feed),
    StatusBarStateProvider {

    private val viewModel by fragmentViewModel(RecommendVideoFeedViewModel::class)
    private val videoPublishGuideViewModel by fragmentViewModel(VideoPublishGuideViewModel::class)

    private var navigationBarColorBackup = 0

    override fun getPageName() = PageNameConstants.FRAGMENT_VIDEO_FEED_WRAPPER

    override fun invalidate() {}

    override fun isEnableTrackPageExposure(): Boolean = false

    override fun isStatusBarDarkText() = false

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentRecommendVideoFeedBinding? {
        return FragmentRecommendVideoFeedBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val fm = childFragmentManager
        val id = binding.fragmentVideoFeedContainer.id
        val existingFragment: Fragment? = fm.findFragmentById(id)

        if (existingFragment == null) {
            if (id == View.NO_ID) {
                throw IllegalStateException(
                    "Fragment container view must have an android:id to add Fragment"
                )
            }

            withState(viewModel) {

                val fragment = VideoFeedFragment().apply {
                    arguments = VideoFeedArgs(
                        resId = it.args.resId,
                        videoId = it.args.videoId,
                        style = VideoFeedArgs.STYLE_RECOMMEND,
                    ).asMavericksArgs()
                }

                fm.beginTransaction()
                    .setReorderingAllowed(true)
                    .add(id, fragment, "")
                    .commitNowAllowingStateLoss()
            }
        }

        binding.tblTitleBar.setOnBackClickedListener {
            val videoFeedCoordinateHelper = getVideoFeedCoordinatorHelper()
            videoFeedCoordinateHelper?.let {
                Analytics.track(EventConstants.COMMUNITY_VIDEO_FEED_BACK,
                    mapOf(
                        "show_param1" to videoFeedCoordinateHelper.currentPosition + 1,
                        "postId" to (videoFeedCoordinateHelper.currentVideoFeedItem?.videoFeedItem?.videoId ?: ""),
                    )
                )
            }
            navigateUp()
        }

        binding.ivPublishVideo.visible(PandoraToggle.isOpenVideoPublish)

        binding.ivPublishVideo.setOnAntiViolenceClickListener {
            withState(viewModel) {
                val videoFeedCoordinateHelper = getVideoFeedCoordinatorHelper()
                val reqId = videoFeedCoordinateHelper?.currentVideoFeedItem?.reqId

                val resIdBean = ResIdBean
                    .newInstance(it.args.resId)
                    .setReqId(reqId ?: "")

                val analyticsMap = ResIdUtils.getAnalyticsMap(resIdBean)
                Analytics.track(EventConstants.INTERNAL_VIDEO_PUBLISH_CLICK, analyticsMap)

                MetaRouter.Video.publish(this, resIdBean)
            }
        }

        videoPublishGuideViewModel.onAsync(
            VideoPublishGuideViewModelState::showVideoPublishGuide,
            deliveryMode = uniqueOnly(),
            onSuccess = {
                if (it) {
                    VideoPublishGuideHelper(this).show(binding.ivPublishVideo)
                    videoPublishGuideViewModel.setVideoPublishGuideShown()
                }
            })
    }

    private fun getVideoFeedCoordinatorHelper(): VideoFeedCoordinateHelper? {
        val fm = childFragmentManager
        val id = binding.fragmentVideoFeedContainer.id
        val existingFragment: Fragment? = fm.findFragmentById(id)
        return existingFragment as? VideoFeedCoordinateHelper
    }

    override fun onResume() {
        super.onResume()
        navigationBarColorBackup = requireActivity().window.navigationBarColor
        StatusBarUtil.setDarkText(this, false)
        requireActivity().window.navColor = Color.BLACK
        if(PandoraToggle.isOpenVideoPublish){
            videoPublishGuideViewModel.checkVideoPublishGuideStatus()
        }
    }

    override fun onPause() {
        super.onPause()
        requireActivity().window.navColor = navigationBarColorBackup
    }
}