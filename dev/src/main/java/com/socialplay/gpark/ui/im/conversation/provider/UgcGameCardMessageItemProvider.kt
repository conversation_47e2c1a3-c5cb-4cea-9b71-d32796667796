package com.socialplay.gpark.ui.im.conversation.provider

import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.bumptech.glide.Glide
import com.ly123.tes.mgs.im.base.IContainerItemProvider
import com.ly123.tes.mgs.im.base.MessageListAdapter
import com.ly123.tes.mgs.im.model.ProviderTag
import com.ly123.tes.mgs.im.model.UIMessage
import com.ly123.tes.mgs.metacloud.message.OverseaUgcGameCardMessage
import com.ly123.tes.mgs.metacloud.model.Message
import com.socialplay.gpark.R
import com.socialplay.gpark.util.ThreadHelper
import com.socialplay.gpark.util.extension.setTextColorByRes

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/29
 *     desc   :
 * </pre>
 */
@ProviderTag(
    messageContent = OverseaUgcGameCardMessage::class,
    showReadState = false,
    showWarning = true,
    showSenderPortrait = true
)
class UgcGameCardMessageItemProvider :
    IContainerItemProvider.MessageProvider<OverseaUgcGameCardMessage>() {

    override fun newView(context: Context?, parent: ViewGroup?): View {
        val view =
            LayoutInflater.from(context).inflate(R.layout.item_im_share_pgc, null as ViewGroup?)
        view.tag = ViewHolder(view)
        return view
    }

    override fun bindView(
        view: View,
        position: Int,
        content: OverseaUgcGameCardMessage?,
        message: UIMessage?,
        messageClickListener: MessageListAdapter.OnMessageClickListener?
    ) {
        val holder: ViewHolder = view.tag as ViewHolder
        val data = content?.getUgcCardInfo()
        holder.tvTitle.text = data?.ugcGameName
        holder.tvDesc.text = data?.ugcGameDesc
        ThreadHelper.runOnUiThread {
            Glide.with(view)
                .load(data?.banner)
                .placeholder(R.mipmap.ic_launcher)
                .centerCrop()
                .into(holder.ivIcon)
        }
        if (message?.messageDirection == Message.MessageDirection.SEND) {
            holder.root.setBackgroundResource(R.drawable.bg_chat_message_send)
            holder.tvTitle.setTextColorByRes(R.color.textColorPrimary)
            holder.tvDesc.setTextColorByRes(R.color.textColorPrimary)
        } else {
            holder.root.setBackgroundResource(R.drawable.bg_chat_message_received)
            holder.tvTitle.setTextColorByRes(R.color.textColorPrimary)
            holder.tvDesc.setTextColorByRes(R.color.textColorPrimary)
        }
        view.setOnClickListener {
            data?.let {
                messageClickListener?.clickOverseaUgcCard(it)
            }
        }
    }

    override fun getContentSummary(context: Context?, data: OverseaUgcGameCardMessage?): Spannable {
        return SpannableString(context?.getString(R.string.share_ugc_content))
    }

    override fun onItemClick(
        p0: View?,
        p1: Int,
        p2: OverseaUgcGameCardMessage?,
        p3: UIMessage?,
        p4: MessageListAdapter.OnMessageClickListener?
    ) {
    }

    private class ViewHolder(view: View) {
        var ivIcon: ImageView = view.findViewById(R.id.iv_icon)
        var tvTitle: TextView = view.findViewById(R.id.tv_title)
        var tvDesc: TextView = view.findViewById(R.id.tv_desc)
        var root: ConstraintLayout = view.findViewById(R.id.cl_root)
    }
}