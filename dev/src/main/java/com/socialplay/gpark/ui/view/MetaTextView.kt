package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import androidx.annotation.AttrRes
import androidx.appcompat.widget.AppCompatTextView
import com.socialplay.gpark.R
import kotlin.math.roundToInt

/**
 * Created by bo.li
 * Date: 2022/7/14
 * Desc:
 */
open class MetaTextView : AppCompatTextView {

    var mUiLineHeight = 0

    constructor(context: Context) : super(context) {
        inflateLayout(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        inflateLayout(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, @AttrRes defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        inflateLayout(context, attrs)
    }

    protected open fun inflateLayout(context: Context, attrs: AttributeSet?) {
        runCatching {
            val a = context.obtainStyledAttributes(attrs, R.styleable.MetaTextView)
            mUiLineHeight = a.getDimension(R.styleable.MetaTextView_uiLineHeight, 0F).toInt()
            includeFontPadding = false
            a.recycle()
        }
        initView()
    }

    private fun initView() {
        val fontHeight: Int = paint.getFontMetricsInt(null)
        if (mUiLineHeight == 0) {
            mUiLineHeight = fontHeight
        }
        lineHeight = mUiLineHeight
        val haveSetMinLines = minLines > 1
        if (minHeight < mUiLineHeight && !haveSetMinLines) {
            minHeight = mUiLineHeight
        }
    }

    fun measureString() = paint.measureText(text.toString()).roundToInt()
}