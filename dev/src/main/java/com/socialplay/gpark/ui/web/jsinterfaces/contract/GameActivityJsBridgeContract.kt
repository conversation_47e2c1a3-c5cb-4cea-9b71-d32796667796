package com.socialplay.gpark.ui.web.jsinterfaces.contract

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.data.model.event.PayResult
import com.socialplay.gpark.ui.web.jsinterfaces.IJsBridgeContract

class GameActivityJsBridgeContract(private val host: FragmentActivity) : IJsBridgeContract {

    private var proxy: IJsBridgeContract? = null

    fun setProxyContract(contract: IJsBridgeContract?) {
        this.proxy = contract
    }

    override val context: Context?
        get() = proxy?.context ?: host

    override val viewLifecycleOwner: LifecycleOwner
        get() = proxy?.viewLifecycleOwner ?: host

    override val lifecycle: Lifecycle
        get() = proxy?.lifecycle ?: host.lifecycle

    override val lifecycleScope: LifecycleCoroutineScope
        get() = proxy?.lifecycleScope ?: host.lifecycleScope

    override val lifecycleOwner: LifecycleOwner
        get() = proxy?.lifecycleOwner ?: host

    override val activity: Activity?
        get() = proxy?.activity ?: host

    override fun requireContext(): Context {
        return proxy?.requireContext() ?: host
    }

    override fun requireActivity(): FragmentActivity {
        return proxy?.requireActivity() ?: host
    }

    override fun isFromGame(): Boolean {
        return proxy?.isFromGame() ?: false
    }


    override fun isToolbarVisible(): Boolean? {
        return proxy?.isToolbarVisible() ?: false
    }

    override fun setStatusBarVisible(visible: Boolean) {
        proxy?.setStatusBarVisible(visible)
    }

    override fun setToolbarVisible(visible: Boolean) {
        proxy?.setToolbarVisible(visible)
    }

    override fun setStatusBarColor(colorStr: String): Boolean? {
        return proxy?.setStatusBarColor(colorStr)
    }

    override fun setStatusBarTextColor(dark: Boolean) {
        proxy?.setStatusBarTextColor(dark)
    }

    override fun routerToLogin(source: String) {
        proxy?.routerToLogin(source)
    }

    override fun gameToLogin(source: String) {
        proxy?.gameToLogin(source)
    }

    override fun hasFragment(): Boolean {
        return proxy?.hasFragment() ?: false
    }

    override fun isWebFragment(): Boolean {
        return proxy?.isWebFragment() ?: false
    }

    override fun goBack(): Boolean {
        return proxy?.goBack() ?: false
    }

    override fun closeWebView(removeWebView: Boolean) {
        proxy?.closeWebView(removeWebView)
    }


    override fun requireFragment(): Fragment {
        return proxy?.requireFragment() ?: throw IllegalStateException("not support")
    }

    override fun startActivity(createChooser: Intent) {
        proxy?.startActivity(createChooser) ?: host.startActivity(createChooser)
    }

    override fun isWebViewDialog(): Boolean {
        return proxy?.isWebViewDialog() ?: false
    }

    override fun toast(res: Int) {
        proxy?.toast(res)
    }

    override fun fragmentManager(): FragmentManager {
        return proxy?.fragmentManager() ?: host.supportFragmentManager
    }

    override fun onPayResultToGame(payResult: PayResult) {
        proxy?.onPayResultToGame(payResult)
    }
}