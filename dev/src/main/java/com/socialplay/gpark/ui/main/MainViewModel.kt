package com.socialplay.gpark.ui.main

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import com.meta.box.biz.friend.model.FriendInfo
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.DeviceInteractor
import com.socialplay.gpark.data.interactor.EditorInteractor
import com.socialplay.gpark.data.interactor.FriendInteractor
import com.socialplay.gpark.data.interactor.ImInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.interactor.UpdateAppInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.DarkModeConfig
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.RealNameLockConfig
import com.socialplay.gpark.data.model.TabBarStatus
import com.socialplay.gpark.data.model.TabPendingConsumeData
import com.socialplay.gpark.data.model.TabReSelectedData
import com.socialplay.gpark.data.model.VersionRuleUtil
import com.socialplay.gpark.data.model.aibot.AIBotCount
import com.socialplay.gpark.data.model.startup.GuideInfo
import com.socialplay.gpark.data.model.startup.SelectModeGameInfo
import com.socialplay.gpark.data.model.user.ContinueAccountInfo
import com.socialplay.gpark.function.analytics.kernel.PandoraInit
import com.socialplay.gpark.function.deeplink.TabSelector
import com.socialplay.gpark.function.editor.RoleGameTryOn
import com.socialplay.gpark.function.im.RongImHelper
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.LifecycleCallback
import com.socialplay.gpark.util.extension.collectWithTimeout
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/10
 * desc   :
 * </pre>
 */


class MainViewModel(
    private val metaKV: MetaKV,
    private val imInteractor: ImInteractor,
    private val friendInteractor: FriendInteractor,
    private val editorInteractor: EditorInteractor,
    val accountInteractor: AccountInteractor,
    private val repository: IMetaRepository,
    private val tTaiInteractor: TTaiInteractor,
    private val deviceInteractor: DeviceInteractor,
    private val updateAppInteractor: UpdateAppInteractor
) : ViewModel(), TabSelector {

    private val _mainItems = MutableLiveData<ArrayList<MainBottomNavigationItem>>()
    val mainItems: LiveData<ArrayList<MainBottomNavigationItem>> get() = _mainItems

    private val _continueAccountInfo = MutableLiveData<ContinueAccountInfo?>()
    val continueAccountInfo: LiveData<ContinueAccountInfo?> get() = _continueAccountInfo

    private val _selectedItemLiveData = MutableLiveData<MainBottomNavigationItem?>()
    val selectedItemLiveData: LiveData<MainBottomNavigationItem?> get() = _selectedItemLiveData

    val msgUnReadCountFlow: Flow<Int> = imInteractor.imUnReadCount.asFlow()
        .combine(friendInteractor.friendRequestUnreadCount.asFlow()) { a, b -> a + b }
        .combine(imInteractor.sysUnReadCount.asFlow()) { a, b -> a + b }
        .combine(imInteractor.showCreateGroupChatRedDot) { a, b ->
            a + if (b) {
                1
            } else {
                0
            }
        }
    val homeVisitorCountFlow: Flow<Boolean> = editorInteractor.homeVisitorCountLiveData.asFlow()
        .combine(accountInteractor.badgeLiveData.asFlow()) { a, b -> (a.isNotEmpty() || (b?.dressResource?.hasNew == true || b?.newFollower?.hasNew == true)) }
    val avatarV3CountFlow: Flow<Boolean> = accountInteractor.badgeLiveData.asFlow().map {
        it?.dressResource?.hasNew == true || it?.newFollower?.hasNew == true
    }

    private val _abTestLiveData = MutableLiveData<Boolean>()
    val abTestLiveData: LiveData<Boolean> = _abTestLiveData

    private val _tabBarStatus = MutableStateFlow(TabBarStatus(isShow = true, isAnim = false))
    val tabBarStatusChangedEvent: Flow<TabBarStatus> =
        _tabBarStatus.shareIn(viewModelScope, SharingStarted.Eagerly)

    private val _darkMode = MutableStateFlow(DarkModeConfig(0F))
    private val _realNameLock = MutableLiveData<Boolean>()
    val darkMode: StateFlow<DarkModeConfig> get() = _darkMode
    val realNameLock: LiveData<Boolean> get() = _realNameLock

    var mode: Int = 0
        get() {
            val prevMode = field
            field = 0
            return prevMode
        }

    var buildMode: Int = 0
        get() {
            val prevMode = field
            field = 0
            return prevMode
        }

    private val _selectModeLiveData = MutableLiveData<SelectModeGameInfo>()
    val selectModeLiveData: LiveData<SelectModeGameInfo> = _selectModeLiveData
    private val _modeGameLiveData = MutableLiveData<GameDetailInfo?>()
    val modeGameLiveData: LiveData<GameDetailInfo?> = _modeGameLiveData

    private val _gameCover = MutableLiveData<Boolean>()
    val gameCover: LiveData<Boolean> = _gameCover


    // 1、挂载data 到 TabPendingConsumeData 上
    // 2、跳转指定Tab
    // 3、各个Tab对数据进行消费，消费后清除掉PendingData
    private val _tabPendingConsumeDataFlow = MutableStateFlow<TabPendingConsumeData?>(null)
    val tabPendingConsumeDataFlow: Flow<TabPendingConsumeData> =
        _tabPendingConsumeDataFlow.filterNotNull()


    /*
        .combine(systemNoticeInteractor.noticeUnreadNumFlow) { a, b -> a + b }
        .combine(editorInteractor.noticeNumLiveData.asFlow()) { a, b -> a + b }
        .combine(editorInteractor.postNoticeLiveData.asFlow()) { a, b -> a + b }*/
    private val friendsObserver = Observer<List<FriendInfo>> {
        if (it.isNotEmpty()) RongImHelper.needConnect()
    }

    private val _tabReSelectedEvent = MutableSharedFlow<TabReSelectedData>(replay = 0)
    val tabReSelectedEvent: Flow<TabReSelectedData> get() = _tabReSelectedEvent

    val needEnterFullAvatarCallback: LifecycleCallback<(RoleGameTryOn?) -> Unit> = LifecycleCallback()

    val guideCallback: LifecycleCallback<(GuideDialog, GuideInfo, onDismissCallback: ((Boolean) -> Unit)?) -> Unit> = LifecycleCallback()

    private val _ugcAssetRookieTabRedDotLiveData = MutableLiveData<Boolean>(false)
    val ugcAssetRookieTabRedDotLiveData: LiveData<Boolean> = _ugcAssetRookieTabRedDotLiveData

    init {
        configFragments()
        checkAbTst()
        friendInteractor.friendList.observeForever(friendsObserver)
    }

    fun configFragments() = viewModelScope.launch {
        val items = ArrayList<MainBottomNavigationItem>()
        val tabIdsList: List<String> = PandoraToggle.getTabIdList()
        if (tabIdsList.isNullOrEmpty()) {
            if (com.socialplay.gpark.BuildConfig.DEBUG) {
                throw RuntimeException("The bottom bar is misconfigured, please check")
            }
            initNormalConfig()
            return@launch
        }
        for (id in tabIdsList) {
            val idInt = id.toInt()
            val tabItem: MainBottomNavigationItem = MainBottomNavigationItem.getItem(idInt) ?: continue
            items.add(tabItem)
            if (idInt == MainBottomNavigationItem.UGC_DESIGN.itemId) {
                val showRedDot = metaKV.account.showRedDotOnAssetRookieTab
                _ugcAssetRookieTabRedDotLiveData.value = showRedDot
            }
        }
        MainBottomNavigationItem.initFlags(items)

        _mainItems.value = items
        setSelectedItem(
            if (items.contains(MainBottomNavigationItem.EDITOR_HOME) && editorInteractor.openWithEditor) {
                // 锚定探索底栏
                MainBottomNavigationItem.EDITOR_HOME.itemId
            } else if (items.contains(MainBottomNavigationItem.PARTY)) {
                // 正常锚定在派对页
                MainBottomNavigationItem.PARTY.itemId
            } else {
                items.first().itemId
            }
        )
    }

    /**
     * 正常
     */
    private fun initNormalConfig(isYouths: Boolean = false) { // 默认 锁区 buildconfig 开关配置底栏
        val items = ArrayList<MainBottomNavigationItem>()
        items.add(MainBottomNavigationItem.PARTY)
        items.add(MainBottomNavigationItem.MESSAGE)
        _mainItems.value = items
    }

    override fun setSelectedItem(itemId: Int, tabPendingConsumeData: TabPendingConsumeData?): Boolean {
        tabPendingConsumeData?.let { _tabPendingConsumeDataFlow.value = it }

        if (_selectedItemLiveData.value?.itemId != itemId) {
            val selectItem = _mainItems.value?.find { it.itemId == itemId } ?: _mainItems.value?.first() ?: return false
            Timber.d("setCurrentSelectedItem %d", selectItem.itemId)
            if (selectItem.itemId == MainBottomNavigationItem.MESSAGE.itemId) {
                //选中好友tab时需要连接融云
                RongImHelper.needConnect()
            }
            _selectedItemLiveData.value = selectItem
        }
        return true
    }

    fun saveLastBottomTab(tabId: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            metaKV.appKV.lastTab = tabId
        }
    }

    fun hasTab(tabItem: MainBottomNavigationItem): Boolean {
        return _mainItems.value?.any { it.itemId == tabItem.itemId } == true
    }

    fun getPostUnread() = editorInteractor.getPostUnread()

    fun getSysUnread() = imInteractor.getSysUnread()

    fun setHomeVisitorCount(count: Int) {
        editorInteractor.setHomeVisitorCount(count)
    }

    fun checkAbTst() = viewModelScope.launch {
        // 产品定超时1s
        _abTestLiveData.value = if (PandoraInit.abTestConfigFetchedLiveData.value == true) {
            true
        } else {
            PandoraInit.abTestConfigFetchedLiveData.asFlow().collectWithTimeout(1_000L)
        }
    }

    fun fireItemReSelectedEvent(item: MainBottomNavigationItem) {
        viewModelScope.launch {
            _tabReSelectedEvent.emit(TabReSelectedData(item, System.currentTimeMillis()))
        }
    }

    override fun onCleared() {
        super.onCleared()
        friendInteractor.friendList.removeObserver(friendsObserver)
    }

    fun showTabBar(
        anim: Boolean,
        animDuration: Long = if (anim) 80 else 0,
        animDelay: Long = 0
    ) {
        this._tabBarStatus.value = TabBarStatus(
            isShow = true,
            isAnim = anim,
            animDuration = animDuration,
            animDelay = animDelay
        )
    }

    fun hideTabBar(
        anim: Boolean,
        animDuration: Long = if (anim) 80 else 0,
        animDelay: Long = 0
    ) {
        this._tabBarStatus.value = TabBarStatus(
            isShow = false,
            isAnim = anim,
            animDuration = animDuration,
            animDelay = animDelay
        )
    }

    fun isTabBarShowing(): Boolean {
        return this._tabBarStatus.value.isShow
    }


    fun setDarkMode(fraction: Float = 1F) {
        this._darkMode.value = DarkModeConfig(fraction)
    }

    fun fetchModeGameInfo() = viewModelScope.launch {
        val modeGameInfo = _selectModeLiveData.value
            ?: GsonUtil.gsonSafeParseCollection<SelectModeGameInfo>(metaKV.tTaiKV.selectModeGameInfo.ifBlank {
                repository.getTTaiConfigById(TTaiKV.ID_SELECT_MODE_GAME_INFO)
                    .singleOrNull()?.data?.value
            }) ?: SelectModeGameInfo(
                BuildConfig.SELECT_MODE_GAME_ID,
                null,
                null,
                null,
                type = SelectModeGameInfo.TYPE_DEFAULT
            )
        if (_selectModeLiveData.value == null) {
            _selectModeLiveData.value = modeGameInfo
        }
        _modeGameLiveData.value = repository
            .fetchGameInfoByIdFromRemoteWithCache(modeGameInfo.id)
            .map { it.data }
            .firstOrNull()
    }

    fun resetModeGameInfo() {
        _modeGameLiveData.value = null
    }

    fun showGameCover() {
        _gameCover.value = true
    }

    fun hideGameCover() = viewModelScope.launch {
        delay(3_000)
        _gameCover.value = false
    }


    fun setTabPendingConsumeData(data: TabPendingConsumeData) {
        _tabPendingConsumeDataFlow.value = data
    }

    fun clearTabPendingConsumeData() {
        _tabPendingConsumeDataFlow.value = null
    }

    fun isEnoughAiBotCreate(): Boolean {
        val lastTime = metaKV.account.getAiCreateTime()
        val count = metaKV.account.getAiCreateTodayCount()
        if (DateUtil.getToday() > lastTime) {
            metaKV.account.setAICreateTodayCount(0)
            metaKV.account.saveAiCreateTime()
            return true
        } else {
            val value = tTaiInteractor.configs.value[TTaiKV.ID_KEY_AI_BOT_CREATE_COUNT]?.value
            val data = GsonUtil.gsonSafeParse<AIBotCount>(value)
            return count < (data?.count ?: 1)
        }
    }

    fun enterFullAvatar(tryOnData: RoleGameTryOn?) {
        needEnterFullAvatarCallback.dispatchOnMainThread { invoke(tryOnData) }
    }

    fun dispatchGuideInfo(guideDialog: GuideDialog, guideInfo: GuideInfo, onDismissCallback: ((Boolean) -> Unit)?) {
        guideCallback.dispatchOnMainThread { invoke(guideDialog, guideInfo, onDismissCallback) }
    }

    fun checkRealNameLock() = viewModelScope.launch {
        tTaiInteractor.getTTaiWithTypeV3<List<RealNameLockConfig>>(TTaiKV.ID_REALNAME_APP_LOCK).collect {
            Timber.d("checkRealNameLock data: $it")
            if (it == null || it.isEmpty() == true) {
                _realNameLock.value = false
                return@collect
            }
            val apkChannelId = deviceInteractor.apkChannelId
            var isContains = false
            for (it in it) {
                if (it.channelId?.contains(apkChannelId) == true) {
                    // 渠道号可以匹配上，继续匹配版本号
                    val isContain = VersionRuleUtil.isContain(it.versionCode ?: "", BuildConfig.VERSION_CODE)
                    Timber.d("checkRealNameLock contains channelId: $apkChannelId isContain:$isContain")
                    if (isContain) {
                        isContains = true
                        break
                    }
                } else {
                    Timber.d("checkRealNameLock not contains channelId: $apkChannelId")
                }
            }
            _realNameLock.value = isContains
        }

    }

    fun clearUgcAssetRookieTabRedDot() {
        if (_ugcAssetRookieTabRedDotLiveData.value == true) {
            _ugcAssetRookieTabRedDotLiveData.value = false
            metaKV.account.showRedDotOnAssetRookieTab = false
        }
    }
}