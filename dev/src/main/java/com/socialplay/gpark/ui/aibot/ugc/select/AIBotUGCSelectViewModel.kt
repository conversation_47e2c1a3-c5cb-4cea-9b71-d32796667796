package com.socialplay.gpark.ui.aibot.ugc.select

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.UploadFileInteractor
import com.socialplay.gpark.data.model.aibot.AIBotCreateImageInfo
import com.socialplay.gpark.util.GsonUtil

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/26
 *     desc   :
 *
 */
class AIBotUGCSelectViewModel(
    val metaRepository: IMetaRepository,
    private val uploadFileInteractor: UploadFileInteractor
) : ViewModel() {
    private val _generateLiveData = MutableLiveData<AIBotCreateImageInfo>()
    val generateLiveData = _generateLiveData
    fun initData(data: String) {
        _generateLiveData.value =GsonUtil.gsonSafeParse(data)
    }

    fun updateIcon(it: String) {
        val data = _generateLiveData.value ?: return
        data.avatar = it
        _generateLiveData.value = data
    }

}