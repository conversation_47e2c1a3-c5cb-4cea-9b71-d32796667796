package com.socialplay.gpark.ui.main

import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.startup.GuideInfo
import com.socialplay.gpark.databinding.ItemGuideCardBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.unsetOnClick

interface IGuideListener : IBaseEpoxyItemListener {
    fun click(item: GuideInfo)
}

fun MetaEpoxyController.guideCardItem(
    item: GuideInfo,
    position: Int,
    listener: IGuideListener
) {
    add {
        GuideCardItem(item, position, listener).id("GuideCardItem-${it}")
    }
}

data class GuideCardItem(
    val item: GuideInfo,
    val position: Int,
    val listener: IGuideListener
) : ViewBindingItemModel<ItemGuideCardBinding>(
    R.layout.item_guide_card,
    ItemGuideCardBinding::bind
) {
    override fun ItemGuideCardBinding.onBind() {
        tvTitle.text = item.title
        tvContent.text = item.desc
        listener.getGlideOrNull()?.run {
            load(item.imgUrl).placeholder(R.drawable.placeholder)
                .into(ivBanner)
        }
        when (position % 4) {
            0 -> {
                cvBg.setCardBackgroundColor(getColorByRes(R.color.color_FFEF30))
            }

            1 -> {
                cvBg.setCardBackgroundColor(getColorByRes(R.color.color_B1FF45))
            }

            2 -> {
                cvBg.setCardBackgroundColor(getColorByRes(R.color.color_B884FF))
            }

            3  -> {
                cvBg.setCardBackgroundColor(getColorByRes(R.color.color_4AB4FF))
            }
        }
        root.setOnAntiViolenceClickListener {
            listener.click(item)
        }
    }

    override fun ItemGuideCardBinding.onUnbind() {
        root.unsetOnClick()
    }
}