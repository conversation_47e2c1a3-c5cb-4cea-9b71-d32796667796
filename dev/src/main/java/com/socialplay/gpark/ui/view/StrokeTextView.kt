package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.text.TextPaint
import android.util.AttributeSet
import android.view.ViewGroup
import android.widget.TextView
import com.socialplay.gpark.R


/**
 * Created by bo.li
 * Date: 2022/9/26
 * Desc: 可以设置文字描边颜色、粗细的textView
 */
class StrokeTextView : MetaTextView {
    private lateinit var outlineTextView: TextView
    private var mStrokeColor = Color.WHITE
    private var mStrokeWidth = 0f

    constructor(context: Context) : super(context) {
        init(context, null, 0)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context, attrs, 0)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle) {
        init(context, attrs, defStyle)
    }

    fun init(context: Context, attrs: AttributeSet?, defStyle: Int) {
        if (attrs != null) {
            val typedArray = context.obtainStyledAttributes(attrs, R.styleable.StrokeTextView)
            mStrokeColor = typedArray.getColor(R.styleable.StrokeTextView_textStrokeColor, Color.WHITE)
            mStrokeWidth = typedArray.getDimension(R.styleable.StrokeTextView_textStrokeWidth, 0f)
            typedArray.recycle()
        }
        outlineTextView = TextView(context, attrs, defStyle)
    }

    fun setStrokeWidth(value: Float) {
        mStrokeWidth = value
        invalidate()
    }
    fun setStrokeColor(color:Int){
        mStrokeColor = color
        invalidate()
    }

    override fun setLayoutParams(params: ViewGroup.LayoutParams) {
        super.setLayoutParams(params)
        outlineTextView.layoutParams = params
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val tt = outlineTextView.text

        // 两个TextView上的内容必须一致
        if (tt == null || tt != this.text) {
            outlineTextView.text = text
            this.postInvalidate()
        }
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        outlineTextView.measure(widthMeasureSpec, heightMeasureSpec)
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        outlineTextView.layout(left, top, right, bottom)
        super.onLayout(changed, left, top, right, bottom)

    }

    override fun onDraw(canvas: Canvas) {
        init()
        outlineTextView.draw(canvas)
        super.onDraw(canvas)
    }
    private fun init(){
        val tp1: TextPaint = outlineTextView.paint
        //设置描边宽度
        tp1.strokeWidth = mStrokeWidth
        //背景描边并填充全部
        tp1.style = Paint.Style.STROKE
        //设置描边颜色
        outlineTextView.setTextColor(mStrokeColor)
        //将背景的文字对齐方式做同步
        outlineTextView.gravity = gravity
    }
}