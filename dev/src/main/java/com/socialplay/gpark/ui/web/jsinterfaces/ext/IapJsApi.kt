package com.socialplay.gpark.ui.web.jsinterfaces.ext

import android.webkit.JavascriptInterface
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.data.model.event.PayResult
import com.socialplay.gpark.data.model.pay.CommonPayParams
import com.socialplay.gpark.data.model.pay.IAPConstants
import com.socialplay.gpark.data.model.pay.PayExtraInfo
import com.socialplay.gpark.data.model.pay.SubsData
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeApi
import com.socialplay.gpark.util.GsonUtil
import org.greenrobot.eventbus.EventBus
import org.json.JSONArray

/**
 * 支付相关js接口
 * <AUTHOR>
 * @date 2021/06/09
 */
@Deprecated("replace by BuyCoinsFragment")
@JavascriptInterface
fun JsBridgeApi.getProductList(paramArray: JSONArray): String {
    helper.contract.viewLifecycleOwner.lifecycleScope.launchWhenResumed {
        var scene = paramArray.optString(0)
        var source = paramArray.optString(1)
        var gameId = paramArray.optString(2)
        val extra = paramArray.optJSONObject(3)
        val list = if (extra != null) {
            val payExtraInfo = GsonUtil.gsonSafeParse<PayExtraInfo>(extra.toString())
            payExtraInfo?.productIdList?.map { SubsData(it, null) }
        } else {
            emptyList()
        }
        payInteractor.loadProducts(scene, list) {}
    }
    return createSuccessResult()
}

@JavascriptInterface
fun JsBridgeApi.getSubscribeProductList(param: JSONArray): String {
    try {
        helper.contract.viewLifecycleOwner.lifecycleScope.launchWhenResumed {
            val extra = param.optJSONArray(0).toString()
            val list = GsonUtil.gsonSafeParseCollection<ArrayList<SubsData>>(extra)
            payInteractor.loadProducts(IAPConstants.IAP_SCENE_VIP_PLUS, list) {}
        }
        return createSuccessResult()
    } catch (e: Exception) {
        return createErrorResult()
    }
}


/**
 * web调用充值的通用埋点
 * https://meta.feishu.cn/wiki/wikcnKJURHE8mhDlfhpWpWS2gte
 */
@JavascriptInterface
fun JsBridgeApi.startPay(paramArray: JSONArray): String {
    val commonPayParams = paramArray.optString(0)
    try {
        val data = GsonUtil.gsonSafeParse<CommonPayParams>(commonPayParams)
        helper.lifecycleScope.launchWhenResumed {
            data?.let {
                payInteractor.startPay(
                    helper.contract.requireActivity(),
                    it,
                    null,
                    data.source,
                ) { payResult: PayResult ->
                    EventBus.getDefault().post(payResult)
                }
            }
        }
    } catch (e: Exception) {
        return createErrorResult()
    }
    return createSuccessResult()
}
/*
JsBridgeApi.startPay(paramArray: JSONArray) 方法参数内容:
[ {
  "productId" : "gp_test_99",
  "source" : "ts_game",
  "gameId" : "1VYOldkmkLh6CAoFK3Na",
  "scene" : "PG_COIN"
} ]
*/