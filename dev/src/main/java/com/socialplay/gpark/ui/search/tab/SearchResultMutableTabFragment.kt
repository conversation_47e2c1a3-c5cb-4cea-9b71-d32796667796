package com.socialplay.gpark.ui.search.tab

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.search.SearchTabItem
import com.socialplay.gpark.databinding.FragmentSearchResultMutableTabBinding
import com.socialplay.gpark.databinding.ViewSearchTabBinding
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.search.SearchViewModel
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible

class SearchResultMutableTabFragment: BaseRecyclerViewFragment<FragmentSearchResultMutableTabBinding>(
    R.layout.fragment_search_result_mutable_tab) {

    private val tabCallback = object : TabLayout.OnTabSelectedListener {
        override fun onTabSelected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, true)
        }

        override fun onTabUnselected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, false)
        }

        override fun onTabReselected(tab: TabLayout.Tab) {}
    }

    private val viewModel: SearchTabViewModel by fragmentViewModel()
    val searchVM: SearchViewModel by viewModels({ requireParentFragment() })

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rv

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentSearchResultMutableTabBinding? {
        return FragmentSearchResultMutableTabBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.tabLayout.addOnTabSelectedListener(tabCallback)
        binding.vp.registerOnPageChangeCallback(object : OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                binding.ivFilter.visible(viewModel.oldState.tabList[position].isCommunity()&&PandoraToggle.isSearchFilterShow)
                binding.llFilter.gone()
                searchVM.curTab = position
            }
        })
        binding.ivFilter.setOnAntiViolenceClickListener {
            binding.llFilter.visible(!binding.llFilter.isVisible)
        }
        binding.llFilter.setOnAntiViolenceClickListener {
            binding.llFilter.gone()
        }
        viewModel.onAsync(SearchTabState::refresh, onSuccess = {
            val hasSelected = it.firstOrNull {
                it.items?.firstOrNull { it.isSelected } != null
            } != null
            binding.ivFilter.setImageResource(if (hasSelected) R.drawable.search_icon_filter2 else R.drawable.search_icon_filter)
        })
        if (viewModel.oldState.tabList.isEmpty()){
            viewModel.onAsync(SearchTabState::tabs, onSuccess = {
                initTab(it)
            })
        }else{
            initTab(viewModel.oldState.tabList)
        }
    }

    private fun initTab(it: List<SearchTabItem>) {
        binding.vp.adapterAllowStateLoss =
            object : FragmentStateAdapter(childFragmentManager, lifecycle) {
                override fun getItemCount() = it.size

                override fun createFragment(position: Int): Fragment {
                    return when {
                        // todo
//                        it[position].isCommunity() -> {
//                            SearchPostFragment()
//                        }

                        else -> {
                            SearchGameFragment()
                        }
                    }
                }
            }
        val tabLayoutMediator = TabLayoutMediator(binding.tabLayout, binding.vp) { tab, position ->
            val tabBinding = ViewSearchTabBinding.inflate(layoutInflater)
            val title = it[position].tabName

            tabBinding.tvNormal.text = title
            tabBinding.tvSelected.text = title

            tab.customView = tabBinding.root
            tab.tag = it[position].tabName
        }
        tabLayoutMediator.attach()
        if (it.size > searchVM.curTab) {
            binding.vp.setCurrentItem(searchVM.curTab, false)
        }
    }

    override fun epoxyController() = simpleController(viewModel, SearchTabState::list) { list ->
        list.forEach {
            addFilterItem(it) {
                viewModel.onSelected(it)
            }
        }
    }

    override fun invalidate() {
    }

    private fun setTabSelectUI(tab: TabLayout.Tab, select: Boolean) {
        val cv = tab.customView ?: return
        cv.findViewById<TextView>(R.id.tv_normal)?.invisible(select)
        cv.findViewById<TextView>(R.id.tv_selected)?.invisible(!select)
    }

    override fun getPageName(): String {
        return "SearchResultMutableTabFragment"
    }

}