package com.socialplay.gpark.ui.view

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.*
import android.os.Build
import android.os.Parcel
import android.os.Parcelable
import android.util.AttributeSet
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.annotation.RequiresApi
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.res.ResourcesCompat
import com.socialplay.gpark.R
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.dp
import java.text.DecimalFormat

/**
 * Created by hiw<PERSON><PERSON> on 17-2-20.
 */
class DownloadProgressButton : AppCompatTextView {
    //背景画笔
    private lateinit var mBackgroundPaint: Paint

    //按钮文字画笔
    private lateinit var mTextPaint: Paint

    //按钮图标画笔
    private lateinit var mIconPaint: Paint

    //不可用状态画笔
    private val mStatusPaint: Paint by lazy {
        Paint().apply {
            color = Color.WHITE
            alpha = 150
        }
    }

    // 下载进度后面小数点位数 默认1位 0.0%
    private var progressDotSize = 1

    //背景颜色
    @ColorInt
    var mBackgroundColor = 0

    //背景颜色前渐变色
    @ColorInt
    var mBackgroundStartColor = 0

    //背景颜色后渐变色
    @ColorInt
    var mBackgroundEndColor = 0

    //下载中后半部分后面背景颜色
    @ColorInt
    var mBackgroundSecondColor = 0

    //文字颜色
    private var mTextColor = 0

    //覆盖后颜色
    private var textCoverColor = 0

    //按钮弧度
    private var buttonRadius = 0f

    //边框宽度
    var borderWidth = 0f

    var progress = -1f
        private set

    private var mToProgress = 0f
    private var mProgressPercent = 0f
    private var mTextRightBorder = 0f
    private var mTextBottomBorder = 0f

    //是否显示边框
    var isShowBorder = false
    private lateinit var mBackgroundBounds: RectF
    private var mProgressTextGradient: LinearGradient? = null

    //下载平滑动画
    private lateinit var mProgressAnimation: ValueAnimator

    //记录当前文字
    var mCurrentText: CharSequence? = null

    //字体大小
    private var mTextSize = 0

    // icon
    @DrawableRes
    private var mIconRes = 0

    // 文字icon的bitmap
    private var bitmap: Bitmap? = null

    // icon右padding
    private var mIconPaddingEnd = 0f

    // 当前状态
    private var mState = 0

    /**
     * 设置/获取当前按钮状态
     */
    var state: Int
        get() = mState
        set(value) {
            if (mState != value) {
                mState = value
                invalidate()
            }
        }

    companion object {
        const val STATE_UNAVAILABLE = -1 //不可用
        const val STATE_NORMAL = 0 //准备下载
        const val STATE_DOWNLOADING = 1 //下载之中
        const val STATE_PAUSE = 2 //暂停下载
        const val STATE_DOWNLOAD_ERROR = 6 //下载错误
        const val STATE_CDN_ERROR = 7 //获取信息错误

        const val MAX_PROGRESS = 100F
        const val MIN_PROGRESS = 0F
    }

    constructor(context: Context) : super(context) {
        initAttrs(context, null)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        initAttrs(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        initAttrs(context, attrs)
    }

    private fun initAttrs(context: Context, attrs: AttributeSet?) {
        val a = context.obtainStyledAttributes(attrs, R.styleable.DownloadProgressButton)
        try {
            mBackgroundColor = a.getColor(R.styleable.DownloadProgressButton_progress_btn_background_color, Color.parseColor("#3385FF"))
            mBackgroundStartColor = a.getColor(R.styleable.DownloadProgressButton_progress_btn_background_color_start, 0)
            mBackgroundEndColor = a.getColor(R.styleable.DownloadProgressButton_progress_btn_background_color_end, 0)
            mBackgroundSecondColor = a.getColor(R.styleable.DownloadProgressButton_progress_btn_background_second_color, Color.parseColor("#E8E8E8"))
            buttonRadius = a.getDimension(R.styleable.DownloadProgressButton_progress_btn_radius, 0f)
            mTextColor = a.getColor(R.styleable.DownloadProgressButton_progress_btn_text_color, mBackgroundColor)
            textCoverColor = a.getColor(R.styleable.DownloadProgressButton_progress_btn_text_cover_color, Color.WHITE)
            borderWidth = a.getDimension(R.styleable.DownloadProgressButton_progress_btn_border_width, 2.dp.toFloat())
            mTextSize = a.getInt(R.styleable.DownloadProgressButton_progress_btn_text_size, 15)
            isShowBorder = a.getBoolean(R.styleable.DownloadProgressButton_progress_btn_show_bolder, true)
            mIconRes = a.getResourceId(R.styleable.DownloadProgressButton_progress_btn_text_drawable, -1)
            mCurrentText = a.getString(R.styleable.DownloadProgressButton_progress_btn_current_text)
            mIconPaddingEnd = a.getDimension(R.styleable.DownloadProgressButton_progress_btn_text_drawable_padding_end, 4.dp.toFloat())
            progressDotSize = a.getInt(R.styleable.DownloadProgressButton_progress_btn_progress_dot_size, progressDotSize)
        } finally {
            a.recycle()
        }
        init()
        setupAnimations()
    }

    private fun init() {
        progress = 0f

        //设置背景画笔
        mBackgroundPaint = Paint()
        mBackgroundPaint.isAntiAlias = true
        mBackgroundPaint.style = Paint.Style.FILL

        //设置文字画笔
        mTextPaint = Paint()
        mTextPaint.isAntiAlias = true
        runCatching {
            // 处理字体资源找不到崩溃
            mTextPaint.typeface = typeface ?: ResourcesCompat.getFont(context, R.font.poppins_black_900)
        }
        mTextPaint.textSize = ScreenUtil.dp2px(context, mTextSize.toFloat()).toFloat() //解决文字有时候画不出问题
        setLayerType(LAYER_TYPE_SOFTWARE, mTextPaint)

        //设置icon画笔
        mIconPaint = Paint()
        mIconPaint.isAntiAlias = true
        mIconPaint.style = Paint.Style.FILL

        //初始化状态设为NORMAL
        mState = STATE_NORMAL
        invalidate()
        if (mIconRes != -1) {
            bitmap = BitmapFactory.decodeResource(resources, mIconRes)
        }
    }

    private fun setupAnimations() {
        // ProgressBar的动画
        mProgressAnimation = ValueAnimator.ofFloat(0f, 1f).setDuration(500)
        mProgressAnimation.addUpdateListener { animation ->
            val timePercent = animation.animatedValue as Float
            progress += (mToProgress - progress) * timePercent
            invalidate()
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        drawBackground(canvas)
        drawTextAbove(canvas)
        drawMask(canvas)
    }

    private fun drawBackground(canvas: Canvas) {
        mBackgroundBounds = RectF() //根据Border宽度得到Button的显示区域
        val width = if (isShowBorder) borderWidth else 0F
        mBackgroundBounds.left = width
        mBackgroundBounds.top = width
        mBackgroundBounds.right = measuredWidth - width
        mBackgroundBounds.bottom = measuredHeight - width
        if (isShowBorder) {
            mBackgroundPaint.style = Paint.Style.STROKE
            mBackgroundPaint.color = mBackgroundColor
            mBackgroundPaint.strokeWidth = borderWidth
            canvas.drawRoundRect(mBackgroundBounds, buttonRadius, buttonRadius, mBackgroundPaint)
        }
        mBackgroundPaint.style = Paint.Style.FILL
        when (mState) {
            STATE_UNAVAILABLE, STATE_NORMAL, STATE_DOWNLOAD_ERROR, STATE_CDN_ERROR            -> {
                mBackgroundPaint.color = mBackgroundColor
                if (mBackgroundStartColor != 0) {
                    mBackgroundPaint.shader = LinearGradient(width, width, mBackgroundBounds.right, mBackgroundBounds.bottom, mBackgroundStartColor, mBackgroundEndColor, Shader.TileMode.CLAMP)
                }
                canvas.drawRoundRect(mBackgroundBounds, buttonRadius, buttonRadius, mBackgroundPaint)
            }
            STATE_PAUSE, STATE_DOWNLOADING -> { //计算当前的进度
                mBackgroundPaint.shader = null
                mProgressPercent = progress / (MAX_PROGRESS + 0f)
                mBackgroundPaint.color = mBackgroundSecondColor
                canvas.save() //画出dst图层
                canvas.drawRoundRect(mBackgroundBounds, buttonRadius, buttonRadius, mBackgroundPaint) //设置图层显示模式为 SRC_ATOP
                val porterDuffXfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_ATOP)
                mBackgroundPaint.color = mBackgroundColor
                mBackgroundPaint.xfermode = porterDuffXfermode //计算 src 矩形的右边界
                val right = mBackgroundBounds.right * mProgressPercent //在dst画出src矩形
                if (mBackgroundStartColor != 0) {
                    mBackgroundPaint.shader = LinearGradient(mBackgroundBounds.left, mBackgroundBounds.top, mBackgroundBounds.right, mBackgroundBounds.bottom, mBackgroundStartColor, mBackgroundEndColor, Shader.TileMode.CLAMP)
                }
                canvas.drawRect(mBackgroundBounds.left, mBackgroundBounds.top, right, mBackgroundBounds.bottom, mBackgroundPaint)
                canvas.restore()
                mBackgroundPaint.xfermode = null
            }
        }
    }

    private fun drawTextAbove(canvas: Canvas) { //计算文字的Baseline绘制的Y坐标
        val y = canvas.height / 2 - (mTextPaint.descent() / 2 + mTextPaint.ascent() / 2)
        var picY = 0f
        var bitmapWidth = 0f
        if (mIconRes != -1) {
            bitmapWidth = bitmap!!.width.toFloat() //计算icon的Baseline绘制的Y坐标
            picY = ((canvas.height - bitmap!!.height) / 2).toFloat()
        }
        if (mCurrentText == null) {
            mCurrentText = ""
        }
        val textWidth = mTextPaint.measureText(mCurrentText.toString())
        val iconWidth = bitmapWidth
        val iconX = (measuredWidth - iconWidth - mIconPaddingEnd - textWidth) / 2
        val textX = (measuredWidth - iconWidth - mIconPaddingEnd - textWidth) / 2 + mIconPaddingEnd + iconWidth
        mTextBottomBorder = y
        mTextRightBorder = (measuredWidth + textWidth) / 2
        when (mState) {
            STATE_UNAVAILABLE, STATE_NORMAL, STATE_DOWNLOAD_ERROR, STATE_CDN_ERROR -> {
                mTextPaint.shader = null
                mTextPaint.color = textCoverColor
                val paint = Paint()
                runCatching {
                    // 处理字体资源找不到崩溃
                    mTextPaint.typeface = typeface ?: ResourcesCompat.getFont(context, R.font.poppins_black_900)
                }
                paint.color = Color.RED
                paint.strokeWidth = 3f
                if (bitmap != null) {
                    canvas.drawBitmap(bitmap!!, iconX, picY, paint)
                }
                canvas.drawText(mCurrentText.toString(), textX, y, mTextPaint)
            }
            STATE_PAUSE, STATE_DOWNLOADING -> {

                //进度条压过距离
                val coverLength = measuredWidth * mProgressPercent //开始渐变指示器
                val indicator1 = measuredWidth / 2 - textWidth / 2 //结束渐变指示器
                val indicator2 = measuredWidth / 2 + textWidth / 2 //文字变色部分的距离
                val coverTextLength = textWidth / 2 - measuredWidth / 2 + coverLength
                val textProgress = coverTextLength / textWidth
                if (coverLength <= indicator1) {
                    mTextPaint.shader = null
                    mTextPaint.color = mTextColor
                } else if (indicator1 < coverLength && coverLength <= indicator2) { //设置变色效果
                    mProgressTextGradient = LinearGradient((measuredWidth - textWidth) / 2, 0F, (measuredWidth + textWidth) / 2, 0F, intArrayOf(textCoverColor, mTextColor), floatArrayOf(textProgress, textProgress + 0.001f), Shader.TileMode.CLAMP)
                    mTextPaint.color = mTextColor
                    mTextPaint.shader = mProgressTextGradient
                } else {
                    mTextPaint.shader = null
                    mTextPaint.color = textCoverColor
                }
                canvas.drawText(mCurrentText.toString(), (measuredWidth - textWidth) / 2, y, mTextPaint)
            }
        }
    }

    private fun drawMask(canvas: Canvas) {
        when (mState) {
            STATE_UNAVAILABLE -> {
                canvas.drawRoundRect(mBackgroundBounds, buttonRadius, buttonRadius, mStatusPaint)
            }
        }
    }

    /**
     * 设置当前按钮文字
     * 目前用不到
     */
//    fun setCurrentTextAndIcon(charSequence: CharSequence?, @DrawableRes iconRes: Int = -1) {
//        mCurrentText = charSequence
//        mIconRes = iconRes
//        if (mIconRes != -1) {
//            bitmap = BitmapFactory.decodeResource(resources, mIconRes)
//        } else {
//            bitmap = null
//        }
//        invalidate()
//    }

    /**
     * 设置当前按钮文字
     */
    fun setCurrentText(charSequence: CharSequence?) {
        mCurrentText = charSequence
        bitmap = null
        invalidate()
    }

    /**
     * 初始化自身progress值，无UI效果
     */
    fun initProgress(progress: Float) {
        this.progress = progress
    }

    /**
     * 设置下载进度
     */
    fun setProgress(progress: Float) {
        setProgressText("", progress, "")
    }

    /**
     * 设置下载中状态的文字
     */
    fun setDownloadingText(text: String) {
        setProgressText(text, progress, "", false)
    }

    /**
     * 设置带下载进度的文字
     */
    fun setProgressText(text: String, progress: Float, suffixTxt: String, needShowProgress: Boolean = true) {
        if (progress in MIN_PROGRESS..MAX_PROGRESS) {
            val format = if (progressDotSize == 0) DecimalFormat("##0") else DecimalFormat("##0.0")
            val progressText = if (needShowProgress) {
                format.format(progress.toDouble()) + "%"
            } else {
                ""
            }
            mCurrentText = text + progressText + suffixTxt
            if (mProgressAnimation.isRunning && this.progress != mToProgress) { //解决动画更不是进度，导致最终文字进度在100%而进度条未达到100%
                this.progress = mToProgress
            }
            mToProgress = progress
            if (mProgressAnimation.isRunning) {
                mProgressAnimation.resume()
                mProgressAnimation.start()
            } else {
                mProgressAnimation.start()
            }
        } else if (progress < MIN_PROGRESS) {
            this.progress = 0f
        } else if (progress > MAX_PROGRESS) {
            this.progress = 100f
            mCurrentText = "$text$progress%$suffixTxt"
            invalidate()
        }
    }

    fun setTargetProgressText(progress: Float) {
        if (progress in MIN_PROGRESS..MAX_PROGRESS) {
            val format = DecimalFormat("##0.0")
            mCurrentText = format.format(progress.toDouble()) + "%"
            mToProgress = progress
            if (mProgressAnimation.isRunning) {
                mProgressAnimation.resume()
                mProgressAnimation.start()
            } else {
                mProgressAnimation.start()
            }
        } else if (progress < MIN_PROGRESS) {
            this.progress = 0f
        } else if (progress > MAX_PROGRESS) {
            this.progress = 100f
            mCurrentText = "$progress%"
            invalidate()
        }
    }

    override fun setTextColor(textColor: Int) {
        mTextColor = textColor
    }

    override fun onRestoreInstanceState(state: Parcelable?) {
        // 6.0以下调用super会崩溃, 但是不调用也会崩溃, 所以需要针对性catch
        kotlin.runCatching { super.onRestoreInstanceState(state) }
        progress = (state as SavedState).downloadProgress
        this.state = state.downloadState
    }

    override fun onSaveInstanceState(): Parcelable {
        val superState = super.onSaveInstanceState()
        val ss = SavedState(superState)
        ss.downloadProgress = progress
        ss.downloadState = state
        return ss
    }


    internal class SavedState : BaseSavedState {

        var downloadState: Int = STATE_NORMAL

        var downloadProgress: Float = 0.0F

        @RequiresApi(Build.VERSION_CODES.N)
        constructor(source: Parcel?, loader: ClassLoader?) : super(source, loader) {
            downloadState = source?.readInt() ?: STATE_NORMAL
            downloadProgress = source?.readFloat() ?: 0.0F
        }

        constructor(source: Parcel?) : super(source) {
            downloadState = source?.readInt() ?: STATE_NORMAL
            downloadProgress = source?.readFloat() ?: 0.0F
        }

        constructor(superState: Parcelable?) : super(superState)

        override fun writeToParcel(out: Parcel, flags: Int) {
            super.writeToParcel(out, flags)
            out.writeInt(downloadState)
            out.writeFloat(downloadProgress)
        }

        companion object CREATOR : Parcelable.ClassLoaderCreator<SavedState> {

            override fun createFromParcel(source: Parcel?, loader: ClassLoader?): SavedState {
                return if (Build.VERSION.SDK_INT >= 24) SavedState(source, loader) else SavedState(
                    source
                )
            }

            override fun createFromParcel(source: Parcel?): SavedState {
                return SavedState(source)
            }

            override fun newArray(size: Int): Array<SavedState?> {
                return arrayOfNulls(size)
            }
        }
    }
}