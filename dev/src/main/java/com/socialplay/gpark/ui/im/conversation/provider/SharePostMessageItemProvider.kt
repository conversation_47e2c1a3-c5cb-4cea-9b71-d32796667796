package com.socialplay.gpark.ui.im.conversation.provider

import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.ly123.tes.mgs.im.base.IContainerItemProvider
import com.ly123.tes.mgs.im.base.MessageListAdapter
import com.ly123.tes.mgs.im.model.ProviderTag
import com.ly123.tes.mgs.im.model.UIMessage
import com.ly123.tes.mgs.metacloud.message.PostCardMessage
import com.ly123.tes.mgs.metacloud.model.Message
import com.socialplay.gpark.R
import com.socialplay.gpark.util.ThreadHelper
import com.socialplay.gpark.util.extension.dp

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/04/11
 *     desc   :
 *
 */
@ProviderTag(
    messageContent = PostCardMessage::class,
    showReadState = false,
    showWarning = true,
    showSenderPortrait = true
)
class SharePostMessageItemProvider :
    IContainerItemProvider.MessageProvider<PostCardMessage>() {
    override fun newView(context: Context?, viewGroup: ViewGroup?): View {
        val view =
            LayoutInflater.from(context).inflate(R.layout.item_im_share_post, null as ViewGroup?)
        view.tag = ViewHolder(view)
        return view
    }

    override fun bindView(
        view: View,
        position: Int,
        content: PostCardMessage?,
        message: UIMessage?,
        messageClickListener: MessageListAdapter.OnMessageClickListener?
    ) {
        val holder: ViewHolder = view.tag as ViewHolder

        val data =  content?.getPostInfo()
        holder.tvTitle.text =data?.content
        ThreadHelper.runOnUiThreadCatching {
            if (!data?.picUrl.isNullOrEmpty()) {
                Glide.with(view.context).load(data?.picUrl)
                    .transform(CenterCrop(), RoundedCorners(10.dp)).into(holder.icon)
            } else {
                Glide.with(view.context).load(R.mipmap.ic_launcher)
                    .transform(CenterCrop(), RoundedCorners(10.dp)).into(holder.icon)
            }
        }
        if (message?.messageDirection == Message.MessageDirection.SEND) {
            holder.root.setBackgroundResource(R.drawable.bg_chat_message_send)
            holder.root.setPadding(view.dp(10), view.dp(10), view.dp(28), view.dp(10))
            holder.tvTitle.setTextColor(ContextCompat.getColor(view.context, R.color.textColorPrimary))
        } else {
            holder.root.setBackgroundResource(R.drawable.bg_chat_message_received)
            holder.root.setPadding(view.dp(28), view.dp(10), view.dp(10), view.dp(10))
            holder.tvTitle.setTextColor(
                ContextCompat.getColor(
                    view.context,
                    R.color.textColorPrimary
                )
            )
        }
        view.setOnClickListener {
            data?.postId?.let { it1 -> messageClickListener?.postCardClick(it1) }
        }
    }


    override fun getContentSummary(context: Context?, data: PostCardMessage?): Spannable {
        return SpannableString(context?.getString(R.string.share_post_content))
    }

    override fun onItemClick(
        var1: View?,
        var2: Int,
        var3: PostCardMessage?,
        var4: UIMessage?,
        messageClickListener: MessageListAdapter.OnMessageClickListener?
    ) {
    }

    private class ViewHolder(view: View) {
        var tvTitle: TextView = view.findViewById(R.id.tv_title)
        var root : View = view.findViewById(R.id.cl_root)
        val icon = view.findViewById<ImageView>(R.id.img_icon)
    }

}