package com.socialplay.gpark.ui.kol.list.provider

import android.view.LayoutInflater
import android.view.View
import androidx.core.view.isVisible
import com.bumptech.glide.RequestManager
import com.chad.library.adapter.base.provider.BaseItemProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.CreatorMultiInfo
import com.socialplay.gpark.data.model.creator.CreatorMultiType
import com.socialplay.gpark.data.model.creator.LabelCreatorUgcGame
import com.socialplay.gpark.data.model.creator.label.UgcPublishLabel
import com.socialplay.gpark.databinding.ProviderKolLabelUgcGameBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.ui.view.FlowLayout
import com.socialplay.gpark.ui.view.MetaTextView
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.context
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setWidth

/**
 * Kol创作者标签ugc
 */
class KolLabelUgcGameProvider(
    private val glide: RequestManager,
) : BaseItemProvider<CreatorMultiInfo>() {

    override val itemViewType: Int = CreatorMultiType.TYPE_LABEL_UGC_GAME_ITEM
    override val layoutId: Int
        get() = R.layout.provider_kol_label_ugc_game

    override fun convert(helper: BaseViewHolder, item: CreatorMultiInfo) {
        ProviderKolLabelUgcGameBinding.bind(helper.itemView).apply {
            val info = item.toUgcGameInfo()?.game ?: return
            initSize(this, info)
            glide.load(info.banner)
                .placeholder(R.drawable.placeholder_corner_12)
                .into(ivIcon)
            glide.load(info.userIcon)
                .placeholder(R.drawable.icon_default_avatar)
                .into(ivAuthorAvatar)
            tvName.text = info.ugcGameName
            tvAuthorName.text = info.userName
            tvHeat.text = UnitUtil.formatPlayerCount(info.pvCount)
            initLabelList(this, info.gameTagList)
        }
    }

    override fun onViewAttachedToWindow(holder: BaseViewHolder) {
        getAdapter()?.let { adapter ->
            val position = holder.layoutPosition - adapter.headerLayoutCount
            if (position in adapter.data.indices) {
                adapter.getItemOrNull(position)?.toUgcGameInfo()?.game?.let { info ->
                    Analytics.track(
                        EventConstants.UGC_FEED_ITEM_SHOW,
                        EventParamConstants.KEY_GAMEID to info.id,
                        EventParamConstants.KEY_SHOW_CATEGORYID to CategoryId.CATEGORY_ID_KOL_LABEL_UGC,
                        EventParamConstants.KEY_PACKAGENAME to info.packageName,
                    )
                }
            }
        }
        super.onViewAttachedToWindow(holder)
    }

    override fun onViewDetachedFromWindow(holder: BaseViewHolder) {
        holder.getViewOrNull<FlowLayout>(R.id.flLabel)?.apply {
            this.removeAllViews()
        }
        super.onViewDetachedFromWindow(holder)
    }

    private fun initLabelList(
        binding: ProviderKolLabelUgcGameBinding,
        labels: List<UgcPublishLabel>?
    ) {
        binding.flLabel.removeAllViews()
        binding.flLabel.isVisible = !labels.isNullOrEmpty()
        labels?.mapNotNull { it.name }?.take(2)?.forEach {
            binding.flLabel.addView(getLabelView(binding, it))
        }
    }

    private fun getLabelView(binding: ProviderKolLabelUgcGameBinding, name: String): View {
        val tv = LayoutInflater.from(binding.context).inflate(R.layout.view_kol_ugc_label, null)
        (tv as? MetaTextView)?.apply {
            text = name
        }
        return tv
    }

    private fun initSize(binding: ProviderKolLabelUgcGameBinding, info: LabelCreatorUgcGame) {
        val dp16 = binding.dp(16)
        val dp6 = binding.dp(6)
        binding.spaceStart.setWidth(if (info.localColumnType == LabelCreatorUgcGame.COLUMN_TYPE_START) dp16 else dp6)
        binding.spaceEnd.setWidth(if (info.localColumnType == LabelCreatorUgcGame.COLUMN_TYPE_START) dp6 else dp16)
    }
}