package com.socialplay.gpark.ui.pay

import android.app.Activity
import android.content.ComponentCallbacks
import androidx.lifecycle.Observer
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.bin.cpbus.CpEventBus
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.UserMemberInteractor
import com.socialplay.gpark.data.model.event.PayResult
import com.socialplay.gpark.data.model.member.MemberInfo
import com.socialplay.gpark.data.model.pay.CommonPayParams
import com.socialplay.gpark.data.model.pay.IAPConstants
import com.socialplay.gpark.data.model.pay.IAPConstants.IAP_SCENE_VIP_PLUS_RENEW
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.pay.PayProvider
import com.socialplay.gpark.function.pay.RechargeProductCompat
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.toLongOrZero
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.koin.android.ext.android.get
import java.util.concurrent.atomic.AtomicBoolean

data class BuyCoinsState(
    val isMember: Boolean = false,
    val rewardCoinNum: Long = 0,
    val rewardCoinRatio: String = "0",
    val balance: Long = -1,
    val memberRewardPercent: Float = 0f,
    val products: Async<List<RechargeProductCompat>> = Uninitialized,
    val payResult: Async<PayResult> = Uninitialized
) : MavericksState

class BuyCoinsViewModel(
    initialState: BuyCoinsState,
    val metaRepository: com.socialplay.gpark.data.IMetaRepository,
    val payInteractor: IPayInteractor,
    val memberInteractor: UserMemberInteractor,
) : BaseViewModel<BuyCoinsState>(initialState) {
    private val memberChangeListener = Observer<MemberInfo?> { memberInfo ->
        setState {
            copy(isMember = memberInfo?.isActive() ?: false)
        }
    }

    init {
        CpEventBus.register(this)
        memberInteractor.vipPlusLiveData.observeForever(memberChangeListener)
    }

    @Subscribe
    fun onPayResultEvent(payResult: PayResult){
        if (payResult.isSuccess && (IAPConstants.IAP_SCENE_VIP_PLUS == payResult.iapScene || payResult.iapScene == IAP_SCENE_VIP_PLUS_RENEW)) {
            // 充值会员成功之后, 购币页面的显示内容需要刷新
            loadData()
        }
    }

    override fun onCleared() {
        memberInteractor.vipPlusLiveData.removeObserver(memberChangeListener)
        super.onCleared()
    }

    fun loadData() {
        getBalance()
        loadMemberInfo()
        loadProducts()
    }

    fun getBalance() {
        viewModelScope.launch {
            payInteractor.getBalance { coinsCount, points ->
                setState {
                    copy(
                        balance = coinsCount
                    )
                }
            }
        }
    }

    fun loadMemberInfo() {
        if (!PayProvider.ENABLE_PREMIUM) {
            return
        }
        viewModelScope.launch {
            metaRepository.getSubsProduct(IAPConstants.IAP_PRODUCT_TYPE_MEMBER_AWARD).collect {
                if (it.succeeded && it.data != null) {
                    val subscriberInfo = it.data!!.firstOrNull() ?: return@collect
                    // 前端用的是 extendInfo, 但根接口定义用 memberInfo 更合适, 为了兼容, 这里两个都用
                    val rewardCoinNum1 = subscriberInfo.extendInfo?.rewardCoinNum
                    val rewardCoinRatio1 = subscriberInfo.extendInfo?.rewardLecoinRatio

                    val rewardCoinNum2 = subscriberInfo.memberInfo?.rewardCoinNum
                    val rewardCoinRatio2 = subscriberInfo.memberInfo?.rewardLecoinRatio
                    val rewardCoinNum = (rewardCoinNum1 ?: rewardCoinNum2).toLongOrZero
                    val rewardCoinRatio = rewardCoinRatio1 ?: rewardCoinRatio2 ?: "0"
                    setState {
                        copy(
                            rewardCoinNum = rewardCoinNum,
                            rewardCoinRatio = rewardCoinRatio,
                        )
                    }
                }
            }
        }
    }

    fun loadProducts() {
        viewModelScope.launch {
            setState {
                copy(products = Loading())
            }
            payInteractor.loadRechargeProducts {
                if (it.succeeded) {
                    setState {
                        copy(products = Success(it.data!!))
                    }
                } else {
                    setState {
                        copy(products = Fail(it.exception ?: Exception("unknown exception")))
                    }
                }
            }
        }
    }

    private val isPaying = AtomicBoolean(false)
    fun startPay(aty: Activity, product: RechargeProductCompat, pageSource: String? = null) {
        if (isPaying.get()) {
            return
        }
        isPaying.set(true)
        setState {
            copy(payResult = Loading())
        }
        payInteractor.startPay(
            aty,
            CommonPayParams(
                ourProductId = product.ourProductId,
                parentProductId = product.parentProductId,
                source = null,
                gameId = null,
                scene = IAPConstants.IAP_SCENE_PG_COIN,
                productId = product.productId,
                price = product.price,
                sceneCode = IAPConstants.IAP_SCENE_CODE_PG_COIN,
                currencyCode = product.currencyCode
            ),
            product.extra,
            pageSource,
        ) { payResult ->
            isPaying.set(false)
            setState {
                copy(payResult = Success(payResult))
            }
        }
    }

    companion object : KoinViewModelFactory<BuyCoinsViewModel, BuyCoinsState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: BuyCoinsState
        ): BuyCoinsViewModel {
            return BuyCoinsViewModel(state, get(), get(), get())
        }
    }
}