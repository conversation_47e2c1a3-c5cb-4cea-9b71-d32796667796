package com.socialplay.gpark.ui.im.conversation

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AbsListView
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.ly123.tes.mgs.im.base.MessageListAdapter
import com.ly123.tes.mgs.im.model.UIMessage
import com.ly123.tes.mgs.metacloud.MetaCloud
import com.ly123.tes.mgs.metacloud.message.AvatarGiftCardMessage
import com.ly123.tes.mgs.metacloud.message.ImMessageEvent
import com.ly123.tes.mgs.metacloud.message.ImageMessage
import com.ly123.tes.mgs.metacloud.message.InviteMessage
import com.ly123.tes.mgs.metacloud.message.OverseaPgcGameCardMessage
import com.ly123.tes.mgs.metacloud.message.OverseaUgcGameCardMessage
import com.ly123.tes.mgs.metacloud.message.ProfileCardMessage
import com.ly123.tes.mgs.metacloud.message.UgcDesignCardMessage
import com.ly123.tes.mgs.metacloud.message.UgcGameCardMessage
import com.ly123.tes.mgs.metacloud.message.VideoFeedCardMessage
import com.ly123.tes.mgs.metacloud.model.Conversation
import com.ly123.tes.mgs.metacloud.model.Message
import com.meta.box.biz.friend.internal.model.FriendStatus
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BanBlockInteractor
import com.socialplay.gpark.data.interactor.ImInteractor
import com.socialplay.gpark.data.model.LoadStatus
import com.socialplay.gpark.data.model.LoadType
import com.socialplay.gpark.data.model.event.OnMessageSendErrorEvent
import com.socialplay.gpark.data.model.im.LocalMessageInfo
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.databinding.FragmentConversationV2Binding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.im.conversation.CustomMessageListAdapter.Role
import com.socialplay.gpark.ui.imgpre.ImgPreDialogFragment
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.ui.view.AutoRefreshListView
import com.socialplay.gpark.util.InputUtil.hideKeyboard
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.SoftHideKeyBoardUtil
import com.socialplay.gpark.util.SystemUtils
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.collectWithLifecycle
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

abstract class BaseChatFragment :
    BaseFragment<FragmentConversationV2Binding>(R.layout.fragment_conversation_v2),
    AbsListView.OnScrollListener {
    val chatViewModel by viewModel<ChatViewModel>()
    private val accountInteractor: AccountInteractor by inject()
    private val banBlockInteractor: BanBlockInteractor by inject()
    val imInteractor: ImInteractor by inject()

    companion object {
        //图片数量
        const val IMG_COUNT = 9

        //视频选择数量
        const val VIDEO_COUNT = 1
        const val REQ_COUNT = 10

        //滚动模式
        //根据当前消息的位置和手势滚动到自由区间
        const val MODE_SCROLL_FREE = 1

        //置顶
        const val MODE_SCROLL_TOP = 2

        //最底部
        const val MODE_SCROLL_BOTTOM = 3
        const val SHARE_CALLBACK = "requestKey"

        /**
         * 当未读消息数大于等于当前值时, 右上角显示当前未读消息的数量
         */
        const val MIN_UNREAD_MESSAGE_COUNT = 2
    }

    /**
     * 群聊, 则为群聊id, 也就是服务器返回的 imId
     * 私聊, 则为用户id
     */
    var targetId: String? = ""
    abstract val conversationType: Conversation.ConversationType
    private var mNewMessageCount = 0
    var messageAdapter: CustomMessageListAdapter? = null

    private var mHasMoreLocalMessagesUp = true
    private var mHasMoreLocalMessagesDown = true

    //未读消息状态
    private var mSyncReadStatus = false
    private var mSyncReadStatusMsgTime: Long = 0
    private var mLastMentionMsgId = "0"
    private var mIsInsertNewMsg = true

    private var lastItemBottomOffset = 0
    private var lastItemPosition = -1
    private var lastItemHeight = 0
    private var lastListHeight = 0
    private var isShowTipMessageCountInBackground = true
    private var mConversation: Conversation? = null
    private var isNeedRefresh = false
    private var isClickUnread = false
    private var friendStatus: FriendStatus? = null
    private var softHideKeyBoardUtil: SoftHideKeyBoardUtil? = null
    private var textWatcher: TextWatcher? = null
    private var isGoSet = false

    abstract fun initTitleView()
    abstract fun initPageOnceData()
    open fun initPageObserver() {}
    open fun initData() {}
    open val childTextWatcher: TextWatcher? = null

    /**
     * 判断消息是否为当前会话的消息
     */
    abstract fun isCurrentConversionMessage(msg: Message): Boolean

    abstract fun getRole(userId: String): Role
    abstract fun getNickname(userId: String): String?
    open fun onAvatarLongClick(senderId: String?):Boolean {
        return false
    }
    open fun onReceivedMessage(msg: Message) {}
    open fun onKeyboardHide() {}

//    private var windowInsetsHelper: WindowInsetsHelper = WindowInsetsHelper()

//    private val keyboardListener = object : KeyboardListener() {
//        override fun onProgress(keyboardHeight: Int) {
//            if (isBindingAvailable()) {
//                val layoutParams = binding.keyboardSpace.layoutParams
//                layoutParams.height = keyboardHeight
//                binding.keyboardSpace.layoutParams = layoutParams
//            }
//        }
//
//        override fun onKeyboardHideEnd() {
//            if (isBindingAvailable()) {
//                val layoutParams = binding.keyboardSpace.layoutParams
//                layoutParams.height = 0
//                binding.keyboardSpace.layoutParams = layoutParams
//            }
//        }
//    }

    private var loadedDataOnce = false

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentConversationV2Binding? {
        return FragmentConversationV2Binding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val window = requireActivity().window
//        if (window != null) {
//            windowInsetsHelper.apply(
//                window,
//                binding.root,
//                keyboardListener
//            )
//        }

        initTitleView()
        if (!loadedDataOnce) {
            loadedDataOnce = true
            initPageOnceData()
        }
        EventBus.getDefault().register(this)
        iniView()
        initEvent()
        registerObserver()
        initPageObserver()
        setNewMessageStatus()
        initData()
        initHistoryMessage()
    }

    private fun iniView() {
        messageAdapter = context?.let {
            object : CustomMessageListAdapter(it, object : MessageListAdapter.OnMessageClickListener {
                override fun userIconClick(uuid: String) {
                    //用户资料卡片
                    Timber.d("click portrait targetId %s uuid %s ", targetId, uuid)
                    Analytics.track(EventConstants.EVENT_CLICK_COMMUNITY_MAIN_UPORTRAIT)
                    MetaRouter.IM.goAddFriendDialog(this@BaseChatFragment, uuid, false)
                }
//                override fun itemClick(type: String, content: String?) {

//
//                }

                override fun copy(message: Message?) {
                    Analytics.track(EventConstants.EVENT_CONVERSATION_COPY_CLICK)
                }

                override fun gameCardClick(gameId: String) {

                }

                override fun hutNeighborCardClick() {
                    // playza 的需求了
                    ToastUtil.showShort(requireContext(), R.string.not_currently_support)
                }

                override fun imagePreviewClick(imgUrls: Array<String>) {
                    ImgPreDialogFragment.show(
                        requireActivity(),
                        imgUrls,
                        "im_conversation",
                        showSaveBtn = true
                    )
                }

                override fun onFamilyPhotoInviteClick(targetId: String, nickName: String) {
                    // todo 暂时处理邀请点击跳转逻辑
                    PermissionRequest.goNotification(
                        requireContext()
                    )
                    isGoSet = true
                }

                override fun onFamilyPhotoShareClick(groupId: String) {
                    // TODO 家庭合影
                }

                override fun onFamilyPhotoSuccessClick(groupId: String) {
                    // TODO 家庭合影
                }

                override fun onGroupPairSuccessClick(groupId: String) {
                    // TODO 家庭合影
                }

                override fun onInviteClick(inviteInfo: InviteMessage.InviteInfo) {
                    //
                }

//                override fun onLinkCardClick(targetUrl: String) {
//
//                }

                override fun onLinkClick(link: String) {
                    //
                }

                override fun onWarningViewClick(position: Int, message: Message?, view: View?) {
                    Timber.d("onWarningViewClick")
                    if (MetaCloud.isInitialized() && MetaCloud.isConnected()) {
                        message?.let { it -> chatViewModel.deleteMessages(it) }
                    } else {
                        toast(R.string.im_conversation_connection_error)
                    }
                }

                override fun postCardClick(postId: String) {
                    MetaRouter.Post.goPostDetail(this@BaseChatFragment, postId, "conversation")
                }

                override fun postTopicCardClick(tagId: String, tagName: String) {
                    MetaRouter.Post.topicDetail(
                        this@BaseChatFragment,
                        PostTag(tagId.toLong(), tagName),
                        EventParamConstants.SOURCE_TOPIC_IM,
                        null
                    )
                }

                override fun recall(message: Message?) {
                    Analytics.track(EventConstants.EVENT_CONVERSATION_RECALL_CLICK)
                }

                override fun ugcGameCardClick(ugcGameInfo: UgcGameCardMessage.UgcGameInfo) {

                }

                override fun onLinkCardClick(targetUrl: String) {

                }

                override fun itemClick(type: String, content: String?) {
                    // TODO shengsen 合另一边的
                }

                override fun clickVideoFeedCard(videoFeedInfo: VideoFeedCardMessage.VideoFeedCardInfo) {
                    MetaRouter.Video.goRecommendVideoFeed(
                        this@BaseChatFragment,
                        ResIdBean().setCategoryID(CategoryId.IM_VIDEO_FEED_CARD),
                        videoFeedInfo.videoId
                    )
                }

                override fun clickProfileCard(userInfo: ProfileCardMessage.ProfileCardInfo) {
                    MetaRouter.Profile.other(
                        this@BaseChatFragment,
                        userInfo.uid.orEmpty(),
                        "im_conversation"
                    )
                }

                override fun clickOverseaUgcCard(gameInfo: OverseaUgcGameCardMessage.UgcCardInfo) {
                    MetaRouter.MobileEditor.ugcDetail(
                        this@BaseChatFragment,
                        gameInfo.id.orEmpty(),
                        gameInfo.gameCode,
                        ResIdBean().setCategoryID(CategoryId.IM_UGC_CARD)
                    )
                }

                override fun avatarGiftCardClick(
                    avatarUgcGameInfo: AvatarGiftCardMessage.AvatarGiftInfo,
                    message: UIMessage?,
                    type: Int
                ) {

                }

                override fun clickOverseaPgcCard(gameInfo: OverseaPgcGameCardMessage.PgcCardInfo) {
                    MetaRouter.GameDetail.navigate(
                        this@BaseChatFragment,
                        gameInfo.id.orEmpty(),
                        ResIdBean().setCategoryID(CategoryId.IM_PGC_CARD),
                        gameInfo.pkg.orEmpty()
                    )
                }

                override fun clickUgcDesignCard(
                    message: Message?,
                    ugcDesignCard: UgcDesignCardMessage.UgcDesignInfo
                ) {
                    MetaRouter.UgcDesign.detail(
                        this@BaseChatFragment,
                        ugcDesignCard.itemId,
                        CategoryId.UGC_DESIGN_IM_CARD
                    )
                }
            }){
                override fun getRole(userId: String): Role {
                    return <EMAIL>(userId)
                }

                override fun getNickname(userId: String): String? {
                    return <EMAIL>(userId)
                }

                override fun onAvatarLongClick(senderId: String?): Boolean {
                    return <EMAIL>(senderId)
                }
            }
        }
        binding.rcList.requestDisallowInterceptTouchEvent(true)
        binding.rcList.setMode(AutoRefreshListView.Mode.START)
        binding.rcList.adapter = messageAdapter
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initEvent() {
        binding.rcList.setOnRefreshListener(object : AutoRefreshListView.OnRefreshListener {
            override fun onRefreshFromStart() {
                if (mHasMoreLocalMessagesUp) {
                    val eldestMessage =
                        messageAdapter?.let { if (it.count > 0) it.getItem(0)?.message else null }
                    val newestMessage =
                        messageAdapter?.let { if (it.count > 0) it.getItem(it.count - 1)?.message else null }

                    getHistoryMessage(
                        conversationType,
                        targetId ?: "",
                        REQ_COUNT,
                        AutoRefreshListView.Mode.START,
                        MODE_SCROLL_FREE,
                        eldestMessage,
                        newestMessage
                    )
                } else {
                    getRemoteHistoryMessages(conversationType, targetId, REQ_COUNT)
                }
            }

            override fun onRefreshFromEnd() {
            }
        })
        binding.rcList.setOnTouchListener { v, event ->
            if (event.action == 2 && binding.rcList.count - binding.rcList.headerViewsCount - binding.rcList.footerViewsCount == 0) {
                if (mHasMoreLocalMessagesUp) {
//TODO                    getHistoryMessage(mConversationType, targetId ?: "", REQ_COUNT, AutoRefreshListView.Mode.START, MODE_SCROLL_FREE, FIRST_MESSAGE_ID)
                } else if (binding.rcList.refreshState != AutoRefreshListView.State.REFRESHING) {
                    getRemoteHistoryMessages(conversationType, targetId, REQ_COUNT)
                }
                true
            } else {
                if (event.action == 0) {
                    hideInput()
                }
                false
            }
        }
        binding.rcList.addOnScrollListener(this)
        if (MetaCloud.isInitialized()) {
            binding.rcNewMessageCount.setOnClickListener {
                mNewMessageCount = 0
                binding.rcList.setSelection(binding.rcList.count)
                setNewMessageStatus()
                mIsInsertNewMsg = true
                messageAdapter?.clear()
                messageAdapter?.notifyDataSetChanged()
                getHistoryMessage(
                    conversationType,
                    targetId ?: "",
                    REQ_COUNT,
                    AutoRefreshListView.Mode.START,
                    MODE_SCROLL_BOTTOM,
                    null,
                    null
                )
            }
        }

        binding.rcSendToggle.setOnAntiViolenceClickListener {
            sendMessage()
        }
        textWatcher = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
                childTextWatcher?.beforeTextChanged(s, start, count, after)
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                childTextWatcher?.onTextChanged(s, start, before, count)
                if (s.isNotEmpty()) {
                    binding.rcSendToggle.alpha = 1f
                } else {
                    binding.rcSendToggle.alpha = 0.5f
                }
                val offset: Int = if (count == 0) {
                    -before
                } else {
                    count
                }
                if (conversationType == Conversation.ConversationType.PRIVATE && offset != 0) {
                    MetaCloud.sendTypingStatus(
                        conversationType,
                        targetId!!,
                        Message.MessageType.TXT
                    )
                }
            }

            override fun afterTextChanged(s: Editable) {
                childTextWatcher?.afterTextChanged(s)
            }
        }
        binding.rcEditText.addTextChangedListener(textWatcher)
        binding.rcEditText.onFocusChangeListener = View.OnFocusChangeListener { p0, p1 ->
            if (p1)
                GlobalScope.launch {
                    delay(200)
                    withContext(Dispatchers.Main) {
                        onExtensionExpanded(0)
                    }
                }
        }
    }

    /**
     * 是否加载过历史消息
     */
    var isHistoryMessageLoad = false
    fun initHistoryMessage() {
        if (targetId.isNullOrEmpty()) {
            return
        }
        isHistoryMessageLoad = true
        //获取最新的消息
        val mode = AutoRefreshListView.Mode.START
        val scrollMode = MODE_SCROLL_BOTTOM
        getHistoryMessage(
            this.conversationType,
            targetId ?: "",
            REQ_COUNT,
            mode,
            scrollMode,
            null,
            null
        )
    }

    private fun registerObserver() {
        chatViewModel.cleanAllMessage()
        chatViewModel.remoteLiveData.observe(viewLifecycleOwner) {
            updateRemoteMessageList(it)
        }
        chatViewModel.unReadCountFlow.collectWithLifecycle(viewLifecycleOwner.lifecycle){ unreadCount->
            if (unreadCount != null && unreadCount > MIN_UNREAD_MESSAGE_COUNT) {
                binding.rcUnreadMessageLayout.visible()
                binding.rcUnreadMessageCount.setTextWithArgs(
                    R.string.chat_page_unread_message_count,
                    unreadCount.toString()
                )
                // 将所有的未读消息拉取下来, 方便快速滚动到指定消息位置
                // 进入页面的时候, 就会拉取 REQ_COUNT 条历史消息
                // 所以当未读消息数大于 REQ_COUNT 时, 才重新拉取
                if (REQ_COUNT < unreadCount) {
                    val mode = AutoRefreshListView.Mode.START
                    val scrollMode = MODE_SCROLL_BOTTOM
                    getHistoryMessage(
                        this.conversationType,
                        targetId ?: "",
                        unreadCount,
                        mode,
                        scrollMode,
                        null,
                        null
                    )
                }
                binding.rcUnreadMessageLayout.setOnClickListener {
                    binding.rcUnreadMessageLayout.gone()
                    val messageCount = messageAdapter?.count ?: 0
                    if (messageCount >= unreadCount) {
                        binding.rcList.smoothScrollToPosition(messageCount-unreadCount)
                    }
                }
            } else {
                binding.rcUnreadMessageLayout.unsetOnClick()
                binding.rcUnreadMessageLayout.gone()
            }
        }
        chatViewModel.showIMNotificationRecord.collectWithLifecycle(viewLifecycleOwner.lifecycle) {

        }
        chatViewModel.openNotification.collectWithLifecycle(viewLifecycleOwner.lifecycle) {
            if (messageAdapter != null && it != null) {
                messageAdapter?.notifyDataSetChanged()
            }
        }
        chatViewModel.localMessageLiveData.observe(viewLifecycleOwner) {
            updateLocalMessageList(it)
        }
        chatViewModel.lastMentionLiveData.observe(viewLifecycleOwner) {
            updateLastMentionMessage(it)
        }

        chatViewModel.onDeleteMessageLiveData.observe(viewLifecycleOwner) {
            it?.let { it1 -> deleteMessages(it1) }
        }

        chatViewModel.sendMessageResultLiveData.observe(viewLifecycleOwner) { success ->
            if (!success) {
                ToastUtil.showShort(requireContext(), R.string.risk_review_not_pass)
            }
        }
    }

    /**
     * 删除发送失败的消息
     */
    private fun deleteMessages(data: Message) {
        val message: UIMessage? =
            messageAdapter?.getList()?.find { it.message.messageId == data.messageId }
        val mPosition = message?.let { messageAdapter?.findPosition(it) } ?: -1
        if (mPosition >= 0) {
            messageAdapter?.remove(mPosition)
            messageAdapter?.notifyDataSetChanged()
        }
    }

    private fun updateRemoteMessageList(it: Pair<LoadStatus?, List<Message?>?>?) {
        val first = it?.first
        val second = it?.second
        if (first?.status == LoadType.Fail) {
            updateRemoteMessageList(false, null, 0)
        } else {
            updateRemoteMessageList(true, second, first?.updateSize ?: 0)
        }
    }

    private fun updateLocalMessageList(it: Pair<LocalMessageInfo?, List<Message?>?>?) {
        val localMessageInfo = it?.first
        val messages = it?.second
        if (localMessageInfo?.isSuccess == true) {
            if (mConversation == null && !messages.isNullOrEmpty()) {
                targetId?.let { chatViewModel.sendReadReceiptMessage(conversationType!!, it) }
            }
            val msgCount = messages?.size ?: 0
            Timber.i("BaseChatFragment  getHistoryMessage $msgCount")
            updateRefreshStatus(
                localMessageInfo.direction,
                msgCount,
                localMessageInfo.getHistoryCount
            )
            if (messages.isNullOrEmpty() || localMessageInfo.isClean) {
                return
            }
            updateMessageList(
                localMessageInfo.direction,
                localMessageInfo.scrollMode,
                localMessageInfo.finalCount,
                messages
            )
        } else {
            binding.rcList.onRefreshComplete(
                localMessageInfo?.getHistoryCount ?: 0,
                localMessageInfo?.getHistoryCount ?: 0,
                false
            )
        }
    }

    private fun updateLastMentionMessage(messages: List<Message?>?) {
        if (!messages.isNullOrEmpty()) {
            mLastMentionMsgId = messages[0]!!.messageId
            val message: UIMessage? =
                messageAdapter?.getList()?.find { it.message.messageId == mLastMentionMsgId }
            val index = message?.let { messageAdapter?.findPosition(it) } ?: -1
            if (index >= 0) {
                binding.rcList.setSelection(index)
                mLastMentionMsgId = "0"
            }
        }
    }

    private fun hideInput() {
        hideKeyboard(binding.rcEditText)
        binding.rcEditText.clearFocus()
        onKeyboardHide()
    }


    /**
     * 是否是自己发的消息
     */
    private fun isSelfSendMessage(msg: Message): Boolean {
        return chatViewModel.isSelfSendMessage(msg.senderUserId)
    }

    private fun getListViewChildAt(adapterIndex: Int): View {
        val header = binding.rcList.headerViewsCount
        val first = binding.rcList.firstVisiblePosition
        return binding.rcList.getChildAt(adapterIndex + header - first)
    }

    /**
     * 列表刷新状态更改
     */
    private fun updateRefreshStatus(
        direction: LoadMessageDirection,
        msgCount: Int,
        getHistoryCount: Int
    ) {
        if (direction == LoadMessageDirection.DOWN) {
            //最新
            binding.rcList.onRefreshComplete(if (msgCount > 1) msgCount else 0, msgCount, false)
            mHasMoreLocalMessagesDown = msgCount > 1
            mIsInsertNewMsg = msgCount < 11
        } else {
            //更多
            binding.rcList.onRefreshComplete(msgCount, getHistoryCount, false)
            mHasMoreLocalMessagesUp = msgCount == getHistoryCount
        }
    }


    /**
     * 更新消息列表
     */
    private fun updateMessageList(
        direction: LoadMessageDirection,
        scrollMode: Int,
        finalCount: Int,
        messages: List<Message?>
    ) {
        val index = if (direction == LoadMessageDirection.DOWN) {
            messageAdapter?.count ?: 0
        } else {
            0
        }
        val resultList = filterMessage(messages)
        if (resultList.isNotEmpty()) {
            val uiMessages = resultList.map { message -> UIMessage.obtain(message) }
            // fragment入场动画过程中, 如果主线程的任务太重, fragment的入场动画会掉帧
            // messageAdapter.addView 和 messageAdapter.bindView 比较耗时
            // 所以先在子线程中预加载, 预加载完了之后再交给主线程
            if (!isEnterAnimateEnd()) {
                lifecycleScope.launch(Dispatchers.IO) {
                    messageAdapter?.preLoad(uiMessages)
                    withContext(Dispatchers.Main) {
                        if (isBindingAvailable()) {
                            uiMessages.forEach { uiMessage ->
                                messageAdapter?.add(uiMessage, index)
                            }
                            refreshUI(messages, scrollMode, finalCount, direction)
                        }
                    }
                }
            } else {
                uiMessages.forEach { uiMessage ->
                    messageAdapter?.add(uiMessage, index)
                }
                refreshUI(messages, scrollMode, finalCount, direction)
            }
        }
    }


    private fun refreshUI(
        messages: List<Message?>,
        scrollMode: Int,
        last: Int,
        direction: LoadMessageDirection
    ) {
        messageAdapter?.notifyDataSetChanged()
        val selected: Int
        if (isSuccess(mLastMentionMsgId)) {
            val message: UIMessage? =
                messageAdapter?.getList()?.find { it.message.messageId == mLastMentionMsgId }
            selected = message?.let { messageAdapter?.findPosition(it) } ?: -1
            binding.rcList.setSelection(selected)
            mLastMentionMsgId = "0"
        } else if (scrollMode == MODE_SCROLL_TOP) {
            binding.rcList.setSelection(0)
        } else if (scrollMode == MODE_SCROLL_BOTTOM) {
            binding.rcList.setSelection(binding.rcList.count)
        } else if (direction == LoadMessageDirection.DOWN) {
            selected = binding.rcList.selectedItemPosition
            if (selected > 0) {
                binding.rcList.setSelection(messageAdapter!!.count - messages.size)
                return
            }
        } else {
            binding.rcList.setSelection(messages.size + 1)
        }

    }

    /**
     * 获取本地的历史记录
     */
    private fun getHistoryMessage(
        conversationType: Conversation.ConversationType,
        targetId: String,
        reqCount: Int,
        mode: AutoRefreshListView.Mode,
        scrollMode: Int,
        eldestMessage: Message?,
        newestMessage: Message?,
    ) {

        binding.rcList.onRefreshStart(mode)
        val getHistoryCount: Int = reqCount

        val direction =
            if (mode == AutoRefreshListView.Mode.START) LoadMessageDirection.UP else LoadMessageDirection.DOWN

        val localMessageInfo = LocalMessageInfo(
            eldestMessage = eldestMessage,
            newestMessage = newestMessage,
            reqCount = reqCount,
            scrollMode = scrollMode,
            direction = direction,
            adapterCount = messageAdapter?.count ?: 0,
            isClickUnread = isClickUnread,
            mHasMoreLocalMessagesDown = mHasMoreLocalMessagesDown,
            getHistoryCount = getHistoryCount,
            isSuccess = false,
            finalCount = REQ_COUNT
        )

        chatViewModel.getHistoryMessage(conversationType, targetId, localMessageInfo)
    }


    private fun isSuccess(messageId: String): Boolean {
        return (!(messageId == "0" || messageId == "-1"))
    }

    /**
     * 获取远程的历史记录
     */
    private fun getRemoteHistoryMessages(
        conversationType: Conversation.ConversationType?,
        targetId: String?,
        reqCount: Int
    ) {
        binding.rcList.onRefreshStart(AutoRefreshListView.Mode.START)
        Timber.d("getRemoteHistoryMessages")
        val dateTime = if (messageAdapter!!.count == 0) null else messageAdapter!!.getItem(0)
        chatViewModel.getRemoteHistoryMessages(
            conversationType!!,
            targetId!!,
            dateTime?.message,
            reqCount
        )
    }

    /**
     * 远程数据请求回来后处理
     */
    private fun updateRemoteMessageList(
        isSuccess: Boolean,
        messages: List<Message?>?,
        reqCount: Int
    ) {
        if (!isSuccess) {
            binding.rcList.onRefreshComplete(0, reqCount, false)
            return
        }
        var lastMessage: Message? = null
        if (messages.isNullOrEmpty()) {
            binding.rcList.onRefreshComplete(0, reqCount, false)
            return
        }
        if (messageAdapter?.count == 0) {
            lastMessage = messages[0]
        }
        val remoteList = filterMessage(messages)
        if (remoteList.isNullOrEmpty()) {
            binding.rcList.onRefreshComplete(0, reqCount, false)
            return
        }
        remoteList.forEach {
            val uiMessage = UIMessage.obtain(it)
            uiMessage.sentStatus = Message.SentStatus.READ
            messageAdapter?.add(uiMessage, 0)
        }
        messageAdapter?.notifyDataSetChanged()
        binding.rcList.setSelection(messages.size + 1)
        binding.rcList.onRefreshComplete(messages.size, reqCount, false)
    }

    /**
     * 过滤出不存在本地消息列表的消息
     */
    private fun filterMessage(srcList: List<Message?>): ArrayList<Message?> {
        val destList = ArrayList<Message?>()
        if (messageAdapter?.count ?: 0 > 0) {
            srcList.forEach { uiMessage ->
                val list =
                    messageAdapter?.getList()?.filter { it.messageId == uiMessage?.messageId }
                if (list.isNullOrEmpty()) {
                    destList.add(uiMessage)
                }
            }
        } else {
            destList.addAll(srcList)
        }
        return destList
    }

    override fun onScrollStateChanged(view: AbsListView, scrollState: Int) {
        if (scrollState == 1) {
            hideInput()
        } else if (scrollState == 0) {
            val last = binding.rcList.lastVisiblePosition
            if (last == binding.rcList.count - 1) {
                mNewMessageCount = 0
                setNewMessageStatus()
            }
        }
    }

    override fun onScroll(
        view: AbsListView,
        firstVisibleItem: Int,
        visibleItemCount: Int,
        totalItemCount: Int
    ) {
        if (binding.rcList.height == lastListHeight) {
            val childCount = binding.rcList.childCount
            if (childCount != 0) {
                val lastView = binding.rcList.getChildAt(childCount - 1)
                lastItemBottomOffset = lastView.bottom - lastListHeight
                lastItemHeight = lastView.height
                lastItemPosition = firstVisibleItem + visibleItemCount - 1
            }
        }
        if (binding.rcUnreadMessageLayout.isVisible) {
            val unReadCount = chatViewModel.unReadCountFlow.value
            val messageCount = messageAdapter?.count
            if (unReadCount != null && unReadCount > MIN_UNREAD_MESSAGE_COUNT && messageCount != null && messageCount >= unReadCount) {
                val targetPosition = messageCount - unReadCount
                val isVisible =
                    targetPosition >= firstVisibleItem && targetPosition < firstVisibleItem + visibleItemCount
                if (isVisible) {
                    // 所有未读的消息都已读了, 无需再显示右上角的未读消息数
                    binding.rcUnreadMessageLayout.gone()
                }
            }
        }
    }


    override fun onResume() {
        if (isGoSet) {
            chatViewModel.checkNotification()
            isGoSet = false
        }
        if (softHideKeyBoardUtil == null) {
            softHideKeyBoardUtil = SoftHideKeyBoardUtil(requireActivity(), null)
        } else {
            softHideKeyBoardUtil?.addGlobalLayoutListener()
        }
        if (isNeedRefresh && messageAdapter != null) {
            isNeedRefresh = false
            messageAdapter?.notifyDataSetChanged()
        }
        super.onResume()
    }

    override fun onPause() {
        isShowTipMessageCountInBackground = binding.rcList.isLastItemVisible(1)
        if (conversationType != null && targetId != null) {
            Timber.tag("leown-conversation").d("onPause--- conversationType:$conversationType")
            Timber.tag("leown-conversation").d("onPause--- targetId:$targetId")
            chatViewModel.clearMessageUnReadStatus(conversationType!!, targetId!!)
            val time =
                if (mSyncReadStatusMsgTime == 0L) System.currentTimeMillis() else mSyncReadStatusMsgTime
            chatViewModel.syncConversationReadStatus(conversationType!!, targetId!!, time)
        }
        hideInput()
        softHideKeyBoardUtil?.removeGlobalLayoutListener()
        super.onPause()
    }


    fun onBackPressed(): Boolean {
        hideInput()
        return false
    }

    private fun receivedMessage(msg: Message) {
        if (isCurrentConversionMessage(msg)) {
            onReceivedMessage(msg)
            if (msg.sentStatus == Message.SentStatus.FAILED) {
                if (msg.errorCode == Message.ErrorCode.NOT_GROUP_MEMBER) {
                    toast(R.string.toast_send_group_message_failed_not_group_members)
                }
                if (msg.errorCode == Message.ErrorCode.GROUP_DISBANDED) {
                    toast(R.string.toast_send_group_message_failed_group_disbanded)
                }
            }
            val message: UIMessage? =
                messageAdapter?.getList()?.find { it.message.messageId == msg.messageId }
            val position = message?.let { messageAdapter?.findPosition(it) } ?: -1
            if (position >= 0) {
                //已经有这条消息的时候,更新消息
                if (msg.sentStatus == Message.SentStatus.FAILED) {
                    val serverTime = msg.sentTime - MetaCloud.getDeltaTime()
                    msg.sentTime = serverTime
                }
                messageAdapter?.getItem(position)!!.message = msg
                messageAdapter?.getView(position, getListViewChildAt(position), binding.rcList)
            } else {
                //没有这条消息的时候,插入消息
                val uiMessage = UIMessage.obtain(msg)
                var sentTime = uiMessage.sentTime
                if (uiMessage.messageDirection == Message.MessageDirection.SEND && uiMessage.sentStatus == Message.SentStatus.SENDING) {
                    sentTime = uiMessage.sentTime - MetaCloud.getDeltaTime()
                    uiMessage.sentTime = sentTime
                }
                val insertPosition = messageAdapter!!.getPositionBySendTime(sentTime)
                if (mIsInsertNewMsg) {
                    messageAdapter!!.add(uiMessage, insertPosition)
                    messageAdapter!!.notifyDataSetChanged()
                }
            }
            scrollToBottom(msg)
        }
    }

    /**
     * 接受到新消息后自动滚动到底部
     */
    private fun scrollToBottom(msg: Message) {
        val isNotification =
            msg.messageType == Message.MessageType.TXT || msg.messageType == Message.MessageType.IMAGE
        if (mNewMessageCount <= 0 && (isNotification || binding.rcList.lastVisiblePosition == binding.rcList.count - binding.rcList.headerViewsCount - 1 || isSelfSendMessage(
                msg
            ))
        ) {
            binding.rcList.transcriptMode = 2
            binding.rcList.setSelection(binding.rcList.count)
            binding.rcList.transcriptMode = 0
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(msg: Message) {
        Timber.d("metaCloud %s", msg.messageId)
        receivedMessage(msg)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: com.socialplay.gpark.data.model.event.OnReceiveMessageProgressEvent) {
        Timber.d("update_pic %s", event.message.messageId)
        if (this.targetId == event.message.targetId && conversationType == event.message.conversationType && isSuccess(
                event.message.messageId
            )
        ) {
            updatePositionProgressMessage(event.message, event.progress)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(messageEvent: ImMessageEvent) {
        //接受到远端发送的消息
        Timber.i("Conversation:Received message event CurrentUserId:${accountInteractor.accountLiveData.value?.uuid} ConversationTargetId:$targetId msg.targetId:${messageEvent.message.targetId} msg.senderId:${messageEvent.message.senderUserId}")
        onReceiveMessage(messageEvent)
        receivedMessage(messageEvent.message)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: OnMessageSendErrorEvent) {
        //消息发送失败处理
        Timber.d("OnMessageSendErrorEvent")
        event.message?.let { receivedMessage(it) }
    }

    private fun onReceiveMessage(event: ImMessageEvent) {
        val message: Message = event.message
        val conversationType = message.conversationType
        val targetId = message.targetId
        if (this.targetId == targetId) {
            if (this.mSyncReadStatus) {
                this.mSyncReadStatusMsgTime = message.sentTime
            }
            if (isSuccess(event.message.messageId)) {
                if (!SystemUtils.isInBackground(this.activity)) {
                    message.receivedStatus.setRead()
                    MetaCloud.setMessageReceivedStatus(message.messageId, message.receivedStatus) {
                        Timber.d("metaCloud%s", it)
                    }
                }
            }
            updateNewMessageCountIfNeed(message, true)
        }
    }

    private fun updateNewMessageCountIfNeed(message: Message, increase: Boolean) {
        if (this.context != null) {
            if (SystemUtils.isInBackground(this.context)) {
                if (!isShowTipMessageCountInBackground) {
                    return
                }
            } else if (this.binding.rcList.isLastItemVisible(1) && mIsInsertNewMsg) {
                Timber.d(
                    "unread message %s %s",
                    this.binding.rcList.isLastItemVisible(1),
                    mIsInsertNewMsg
                )
                return
            }
            if (message.messageDirection != Message.MessageDirection.SEND
                && message.conversationType != Conversation.ConversationType.CUSTOMER_SERVICE
                && message.conversationType != Conversation.ConversationType.APP_PUBLIC_SERVICE
                && message.conversationType != Conversation.ConversationType.PUBLIC_SERVICE
            ) {
                if (increase) {
                    ++mNewMessageCount
                } else {
                    --mNewMessageCount
                }
                setNewMessageStatus()
            }
        }
    }

    /**
     * 上传进度更新
     */
    private fun updatePositionProgressMessage(msg: Message, progress: Int) {
        val message: UIMessage? =
            messageAdapter?.getList()?.find { it.message.messageId == msg.messageId }
        val position = message?.let { messageAdapter?.findPosition(it) } ?: -1
        Timber.d("update_pic updatePositionProgressMessage %s", position)
        if (position >= 0) {
            val uiMessage = messageAdapter?.getItem(position) as UIMessage
            if (uiMessage.messageId == msg.messageId && (progress != uiMessage.progress || progress == 100)) {
                uiMessage.message = msg
                uiMessage.progress = progress
                if (msg.content is ImageMessage) {
                    Timber.d(
                        "update_pic pic path %s",
                        (msg.content as ImageMessage?)?.localUri?.path
                    )
                    Timber.d(
                        "update_pic remote path %s",
                        (msg.content as ImageMessage?)?.remoteUri?.path
                    )
                }
                (messageAdapter?.getItem(position) as UIMessage).message = msg
                val first: Int = binding.rcList.firstVisiblePosition
                val last: Int = binding.rcList.lastVisiblePosition
                if (position in first..last) {
                    messageAdapter?.getView(position, getListViewChildAt(position), binding.rcList)
                }
            }
        }
    }

    private fun sendMessage(): Boolean {
        if (binding.rcEditText.text.toString().isEmpty()) {
            return false
        }
        if (!NetUtil.isNetworkAvailable()) {
            toast(R.string.net_unavailable)
            return false
        }
        if (accountInteractor.isAccountBanned()) {
            // 封禁
            banBlockInteractor.showBanDialog(BanBlockInteractor.REASON_IM_MESSAGE, this)
            return false
        }
        if (chatViewModel.blocked) {
            // 屏蔽
            banBlockInteractor.showBlockDialog(BanBlockInteractor.REASON_IM_MESSAGE, this)
            return false
        }
        val text: String = binding.rcEditText.text.toString()
        onSendBtnClicked(text)
        binding.rcEditText.setText("")
        return true
    }

    abstract fun onSendBtnClicked(text: String?)

    private fun onExtensionExpanded(h: Int) {
        if (isVisible && !isStateSaved && !isDetached) {
            binding.rcList.setSelection(binding.rcList.count)
            if (mNewMessageCount > 0) {
                mNewMessageCount = 0
                setNewMessageStatus()
            }
        }
    }

    override fun onDestroyView() {
//        windowInsetsHelper.unApply()
        binding.rcList.removeCurrentOnScrollListener()
        binding.rcList.removeOnScrollListener(this)
        binding.rcList.removeRefreshListener()
        binding.rcEditText.onFocusChangeListener = null
        binding.rcEditText.removeTextChangedListener(textWatcher)
        messageAdapter?.messageClickListener = null
        textWatcher = null

        Timber.d("onViewDestroyed Detail")
        super.onDestroyView()
        EventBus.getDefault().unregister(this)
    }

    /**
     * 新消息状态
     */
    private fun setNewMessageStatus() {
        if (MetaCloud.isInitialized() && mNewMessageCount >= 0) {
            this.binding.rcNewMessageCount.visible(mNewMessageCount != 0)
            this.binding.rcNewMessageNumber.visible(mNewMessageCount != 0)
            this.binding.rcNewMessageNumber.text =
                if (mNewMessageCount > 99) "99+" else mNewMessageCount.toString()
        }
    }

    override fun invalidate() {
    }
}
