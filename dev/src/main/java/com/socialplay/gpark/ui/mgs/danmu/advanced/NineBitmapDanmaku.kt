package com.socialplay.gpark.ui.mgs.danmu.advanced

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.NinePatch
import android.graphics.Paint
import android.graphics.RectF
import com.bytedance.danmaku.render.engine.control.DanmakuConfig
import com.bytedance.danmaku.render.engine.data.DanmakuData
import com.bytedance.danmaku.render.engine.render.draw.DrawItem

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/02/21
 *     desc   :
 *
 */
open class NineBitmapData: DanmakuData() {

    override var drawType: Int = DRAW_TYPE_NINE_BITMAP

    /**
     * The bitmap will not be automatically recycled,
     * and occupy the memory before you recycle it manually.
     * Please check the memory problems carefully.
     */
    var bitmap: Bitmap? = null
    var height :Float = 0f


}


open class NineBitmapDrawItem: DrawItem<NineBitmapData>() {

    private val mBitmapPaint = Paint(Paint.ANTI_ALIAS_FLAG or Paint.DITHER_FLAG)
    private val mRectF = RectF()

    override fun getDrawType(): Int {
        return DRAW_TYPE_NINE_BITMAP
    }

    override fun onDraw(canvas: Canvas, config: DanmakuConfig) {

    }

    override fun onBindData(data: NineBitmapData) {
        mBitmapPaint.flags = Paint.ANTI_ALIAS_FLAG or Paint.DITHER_FLAG
    }

    override var width: Float = 0F
    override var height: Float = 0F
    override var x: Float = 0F
    override var y: Float = 0F

    override fun onMeasure(config: DanmakuConfig) {
        if (width > 0 && height > 0) {
            this.width = width
            this.height = height
        } else {
            this.width = data?.bitmap?.width?.toFloat() ?: 0F
            this.height = data?.bitmap?.height?.toFloat() ?: 0F
        }
    }



    override fun recycle() {
        super.recycle()
        mBitmapPaint.reset()
        mRectF.set(0F, 0F, 0F, 0F)
    }
}