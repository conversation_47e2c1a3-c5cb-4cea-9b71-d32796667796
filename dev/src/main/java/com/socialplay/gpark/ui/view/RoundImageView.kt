package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Path
import android.graphics.RectF
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import com.socialplay.gpark.R

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/01/20
 *     desc   :
 * </pre>
 */
class RoundImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : AppCompatImageView(context, attrs, defStyle) {

    private var cRadii = 0f

    private var tlRadius = 0f
    private var trRadius = 0f
    private var blRadius = 0f
    private var brRadius = 0f

    private val mRoundRect = RectF()
    private val mRoundPath = Path()
    private val mRoundArr = FloatArray(8)

    private val isRegular: <PERSON>olean
        get() = cRadii != 0f

    init {
        val typedArr =
            context.obtainStyledAttributes(attrs, R.styleable.RoundView, defStyle, 0)
        cRadii = typedArr.getDimension(R.styleable.RoundView_cornerRadii, 0f)
        tlRadius = typedArr.getDimension(R.styleable.RoundView_topLeftRadius, 0f)
        trRadius = typedArr.getDimension(R.styleable.RoundView_topRightRadius, 0f)
        brRadius = typedArr.getDimension(R.styleable.RoundView_bottomRightRadius, 0f)
        blRadius = typedArr.getDimension(R.styleable.RoundView_bottomLeftRadius, 0f)
        typedArr.recycle()
        updateRoundArr()
    }

    override fun draw(canvas: Canvas) {
        mRoundRect.set(0.toFloat(), 0.toFloat(), width.toFloat(), height.toFloat())
        mRoundPath.reset()
        mRoundPath.addRoundRect(mRoundRect, mRoundArr, Path.Direction.CW)
        canvas.clipPath(mRoundPath)
        super.draw(canvas)
    }

    private fun updateRoundArr() {
        if (isRegular) {
            for (i in mRoundArr.indices) {
                mRoundArr[i] = cRadii
            }
        } else {
            mRoundArr[0] = tlRadius
            mRoundArr[1] = tlRadius
            mRoundArr[2] = trRadius
            mRoundArr[3] = trRadius
            mRoundArr[4] = brRadius
            mRoundArr[5] = brRadius
            mRoundArr[6] = blRadius
            mRoundArr[7] = blRadius
        }
    }

    fun setCornerRadius(cornerRadius: Int): RoundImageView {
        cRadii = cornerRadius.toFloat()
        updateRoundArr()
        invalidate()
        return this
    }

    fun setCornerRadii(
        topLeft: Int,
        topRight: Int,
        bottomRight: Int,
        bottomLeft: Int
    ): RoundImageView {
        cRadii = 0f
        tlRadius = topLeft.toFloat()
        trRadius = topRight.toFloat()
        brRadius = bottomRight.toFloat()
        blRadius = bottomLeft.toFloat()
        updateRoundArr()
        invalidate()
        return this
    }
}