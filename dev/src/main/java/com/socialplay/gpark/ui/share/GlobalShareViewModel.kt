package com.socialplay.gpark.ui.share

import android.content.ComponentCallbacks
import android.content.Context
import android.net.Uri
import android.os.SystemClock
import android.view.View
import androidx.core.view.doOnNextLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.friend.CommonShareRequest
import com.socialplay.gpark.data.model.friend.CommonShareResult
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.post.PostUgcDesignCard
import com.socialplay.gpark.data.model.share.ShareConfig
import com.socialplay.gpark.data.model.share.ShareContent
import com.socialplay.gpark.data.model.share.ShareFeature
import com.socialplay.gpark.data.model.share.SharePendingData
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.data.model.share.ShareResult
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.download.SimpleDownloader
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.share.GlobalShareCreateConfigHelper
import com.socialplay.gpark.function.share.GlobalShareHelper
import com.socialplay.gpark.function.share.GlobalSharePlatformHelper
import com.socialplay.gpark.function.share.GlobalShareTrackHelper
import com.socialplay.gpark.function.share.ShareWrapper
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.function.share.platform.SystemShare
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.Md5Util.md5
import com.socialplay.gpark.util.MediaStoreUtils
import com.socialplay.gpark.util.ToastData
import com.socialplay.gpark.util.bitmap.BitmapUtil
import com.socialplay.gpark.util.extension.LifecycleCallback
import com.socialplay.gpark.util.extension.registerHermes
import com.socialplay.gpark.util.extension.unregisterHermes
import com.socialplay.gpark.util.ifNullOrEmpty
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.single
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.Subscribe
import org.koin.android.ext.android.get
import java.io.File

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/01
 *     desc   :
 * </pre>
 */
data class GlobalShareState(
    val features: List<ShareFeature>?,
    val platforms: List<SharePlatform>,
    val longImagePlatforms: List<SharePlatform>,
    val raw: ShareRawData,
    val type: Int,
    val config: ShareConfig? = null,
    val data: Async<SharePendingData> = Uninitialized,
    val result: Pair<Boolean, SharePendingData>? = null,
    val pendingData: SharePendingData? = null,
    val progress: Async<Int> = Uninitialized,
    val paths: List<String>? = null,
    val longImageStatus: Int = STATUS_INIT,
    val longImagePath: String? = null,
    val toast: ToastData = ToastData.EMPTY,
    val defaultCircleId: Async<String> = Uninitialized,
    val qrCode: String? = null
) : MavericksState {

    companion object {
        const val TYPE_UNKNOWN = 0
        const val TYPE_NORMAL = 1
        const val TYPE_LONG_IMAGE = 2
        const val TYPE_LONG_IMAGE_PRE = 3
        const val TYPE_LONG_IMAGE_POST = 4

        const val STATUS_INIT = 0
        const val STATUS_LOADING = 1
        const val STATUS_DONE = 2
        const val STATUS_FAIL = 3
        const val STATUS_RESET = 4
    }

    val isLongImageMode get() = type in TYPE_LONG_IMAGE..TYPE_LONG_IMAGE_POST
}

class GlobalShareViewModel(
    initialState: GlobalShareState,
    private val repo: IMetaRepository,
    val accountInteractor: AccountInteractor,
    private val tTaiInteractor: TTaiInteractor,
) : BaseViewModel<GlobalShareState>(initialState) {

    val requestId = "share_${initialState.raw.scene}_${SystemClock.elapsedRealtime()}"
    private var tempVideoFile: File? = null
    private var doneCount: Int = 0
    private var glide: RequestManager? = null

    private var longImageSet = HashSet<String>()
    private var longImageKey = 0L
    private var prepareKey = 0L

    val incrementCallback = LifecycleCallback<() -> Unit>()

    init {
        registerHermes()
    }

    //region create share info
    fun createShare(fragment: Fragment, platform: String) = withState { s ->
        val activity = fragment.requireActivity()
        if (!GlobalSharePlatformHelper.checkInstallation(activity, platform)) {
            setState { copy(toast = toast.toResMsg(R.string.not_installed)) }
            return@withState
        }
        when (s.type) {
            GlobalShareState.TYPE_NORMAL -> {
                when (s.raw.scene) {
                    ShareHelper.SCENE_PROFILE -> {
                        createShareProfile(fragment, activity, s, platform)
                    }

                    ShareHelper.SCENE_PGC_DETAIL -> {
                        createSharePgc(fragment, activity, s, platform)
                    }

                    ShareHelper.SCENE_UGC_DETAIL -> {
                        createShareUgc(fragment, activity, s, platform)
                    }

                    ShareHelper.SCENE_POST_DETAIL -> {
                        createSharePost(fragment, activity, s, platform)
                    }

                    ShareHelper.SCENE_VIDEO_FEED -> {
                        createShareVideo(fragment, activity, s, platform)
                    }

                    ShareHelper.SCENE_OC_MOMENT -> {
                        createShareOcMoment(fragment, activity, s, platform)
                    }

                    ShareHelper.SCENE_UGC_DESIGN_DETAIL -> {
                        createShareUgcDesign(fragment, activity, s, platform)
                    }
                }
            }

            GlobalShareState.TYPE_LONG_IMAGE_PRE -> {
                when (s.raw.scene) {
                    ShareHelper.SCENE_PROFILE -> {
                        createShareProfile(fragment, activity, s, platform)
                    }
                }
            }

            GlobalShareState.TYPE_LONG_IMAGE_POST -> {
                when (s.raw.scene) {
                    ShareHelper.SCENE_SCREENSHOT -> {
                        createShareScreenshot(fragment, activity, s, platform)
                    }
                }
            }

            GlobalShareState.TYPE_LONG_IMAGE -> {
                when (s.raw.scene) {
                    ShareHelper.SCENE_PGC_DETAIL,
                    ShareHelper.SCENE_UGC_DETAIL -> {
                        createConfig4LongImage(activity, s, platform)
                    }
                }
            }
        }
    }

    private fun createShareProfile(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        platform: String
    ) {
        when (platform) {
            SharePlatform.PLATFORM_FRIEND,
            SharePlatform.PLATFORM_SAVE -> {
                createConfig4Nothing(activity, s, platform)
            }
        }

        create3rdConfigDispatcher(
            fragment,
            activity,
            s,
            platform,
            GlobalShareCreateConfigHelper.createConfig4Profile(s.type, platform)
        )
    }

    private fun createSharePgc(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        platform: String
    ) {
        when (s.type) {
            GlobalShareState.TYPE_NORMAL -> {
                when (platform) {
                    SharePlatform.PLATFORM_POST,
                    SharePlatform.PLATFORM_FRIEND,
                    SharePlatform.PLATFORM_SAVE,
                    SharePlatform.PLATFORM_SYSTEM -> {
                        createConfig4Nothing(activity, s, platform)
                    }

                    SharePlatform.PLATFORM_LONG_IMAGE -> {
                        createConfig4QrCode(activity, s, platform)
                    }

                    SharePlatform.PLATFORM_LINK -> {
                        createConfig4Link(activity, s, platform)
                    }
                }
            }

            GlobalShareState.TYPE_LONG_IMAGE -> {
                when (platform) {
                    SharePlatform.PLATFORM_SAVE -> {
                        createConfig4Nothing(activity, s, platform)
                    }
                }
            }
        }

        create3rdConfigDispatcher(
            fragment,
            activity,
            s,
            platform,
            GlobalShareCreateConfigHelper.createConfig4Pgc(s.type, platform)
        )
    }

    private fun createShareUgc(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        platform: String
    ) {
        when (s.type) {
            GlobalShareState.TYPE_NORMAL -> {
                when (platform) {
                    SharePlatform.PLATFORM_POST,
                    SharePlatform.PLATFORM_FRIEND,
                    SharePlatform.PLATFORM_SAVE,
                    SharePlatform.PLATFORM_SYSTEM -> {
                        createConfig4Nothing(activity, s, platform)
                    }

                    SharePlatform.PLATFORM_LONG_IMAGE -> {
                        createConfig4QrCode(activity, s, platform)
                    }

                    SharePlatform.PLATFORM_LINK -> {
                        createConfig4Link(activity, s, platform)
                    }
                }
            }

            GlobalShareState.TYPE_LONG_IMAGE -> {
                when (platform) {
                    SharePlatform.PLATFORM_SAVE -> {
                        createConfig4Nothing(activity, s, platform)
                    }
                }
            }
        }

        create3rdConfigDispatcher(
            fragment,
            activity,
            s,
            platform,
            GlobalShareCreateConfigHelper.createConfig4Ugc(s.type, platform)
        )
    }

    private fun createSharePost(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        platform: String
    ) {
        when (platform) {
            SharePlatform.PLATFORM_FRIEND -> {
                createConfig4Nothing(activity, s, platform)
            }

            SharePlatform.PLATFORM_LINK -> {
                createConfig4Link(activity, s, platform)
            }

            SharePlatform.PLATFORM_SYSTEM -> {
                if (s.raw.hasMedia) {
                    createConfig4Nothing(activity, s, platform)
                } else {
                    createConfig4Link(activity, s, platform)
                }
            }
        }

        create3rdConfigDispatcher(
            fragment,
            activity,
            s,
            platform,
            GlobalShareCreateConfigHelper.createConfig4Post(s.type, platform)
        )
    }

    private fun createShareVideo(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        platform: String
    ) {
        when (platform) {
            SharePlatform.PLATFORM_FRIEND,
            SharePlatform.PLATFORM_SAVE,
            SharePlatform.PLATFORM_SYSTEM -> {
                createConfig4Nothing(activity, s, platform)
            }

            SharePlatform.PLATFORM_LINK -> {
                createConfig4Link(activity, s, platform)
            }
        }

        create3rdConfigDispatcher(
            fragment,
            activity,
            s,
            platform,
            GlobalShareCreateConfigHelper.createConfig4Video(s.type, platform)
        )
    }

    private fun createShareOcMoment(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        platform: String
    ) {
        when (platform) {
            SharePlatform.PLATFORM_SYSTEM -> {
                createConfig4Ttai(activity, s, platform)
            }

            SharePlatform.PLATFORM_SAVE -> {
                createConfig4Nothing(activity, s, platform)
            }
        }

        create3rdConfigDispatcher(
            fragment,
            activity,
            s,
            platform,
            GlobalShareCreateConfigHelper.createConfig4OcMoment(s.type, platform)
        )
    }

    private fun createShareUgcDesign(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        platform: String
    ) {
        when (platform) {
            SharePlatform.PLATFORM_FRIEND,
            SharePlatform.PLATFORM_POST -> {
                createConfig4Nothing(activity, s, platform)
            }
        }
    }

    private fun createShareScreenshot(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        platform: String
    ) {
        when (platform) {
            SharePlatform.PLATFORM_FRIEND,
            SharePlatform.PLATFORM_SAVE -> {
                createConfig4Nothing(activity, s, platform)
            }

            SharePlatform.PLATFORM_SYSTEM -> {
                createConfig4Ttai(activity, s, platform)
            }
        }

        create3rdConfigDispatcher(
            fragment,
            activity,
            s,
            platform,
            GlobalShareCreateConfigHelper.createConfig4Screenshot(s.type, platform)
        )
    }

    /**
     * Simple
     */
    private fun createConfig4Nothing(context: Context, s: GlobalShareState, platform: String) {
        val data = Success(SharePendingData(platform))
        setState {
            copy(
                data = data,
                longImageStatus = if (longImageStatus == GlobalShareState.STATUS_FAIL) {
                    GlobalShareState.STATUS_RESET
                } else {
                    longImageStatus
                },
                result = null
            )
        }
    }

    private fun createShareRequest(
        context: Context,
        s: GlobalShareState,
        platform: String,
        needQrCode: Boolean
    ): CommonShareRequest? {
        return when (s.raw.scene) {
            ShareHelper.SCENE_PGC_DETAIL -> {
                CommonShareRequest.pgc(
                    s.raw.pgcGame?.id ?: return null,
                    SharePlatform.platformTrackParam(platform),
                    needQrCode,
                    s.raw.reqId
                )
            }

            ShareHelper.SCENE_UGC_DETAIL -> {
                CommonShareRequest.ugc(
                    s.raw.ugcGame?.id ?: return null,
                    SharePlatform.platformTrackParam(platform),
                    needQrCode,
                    s.raw.reqId
                )
            }

            ShareHelper.SCENE_POST_DETAIL -> {
                CommonShareRequest.post(
                    s.raw.postDetail?.postId ?: return null,
                    SharePlatform.platformTrackParam(platform),
                    needQrCode,
                    s.raw.reqId
                )
            }

            ShareHelper.SCENE_VIDEO_FEED -> {
                CommonShareRequest.video(
                    s.raw.videoFeed?.videoId ?: return null,
                    SharePlatform.platformTrackParam(platform),
                    needQrCode,
                    s.raw.reqId
                )
            }

            else -> {
                null
            }
        }
    }

    /**
     * 获取分享链接
     */
    private fun createConfig4Link(context: Context, s: GlobalShareState, platform: String) {
        val request = createShareRequest(context, s, platform, false) ?: return
        repo.createShare(request).map {
            SharePendingData(
                platform,
                info = it
            )
        }.execute {
            copy(
                data = it,
                longImageStatus = if (longImageStatus == GlobalShareState.STATUS_FAIL) {
                    GlobalShareState.STATUS_RESET
                } else {
                    longImageStatus
                },
                result = null
            )
        }
    }

    /**
     * 获取QR
     */
    private fun createConfig4QrCode(context: Context, s: GlobalShareState, platform: String) {
        if (s.qrCode.isNullOrBlank()) {
            val request = createShareRequest(context, s, platform, true) ?: return
            repo.createShare(request).map {
                SharePendingData(
                    platform,
                    info = it.copy(
                        jumpUrl = it.shareQrCode ?: it.jumpUrl
                    )
                )
            }.execute {
                copy(
                    data = it,
                    longImageStatus = if (longImageStatus == GlobalShareState.STATUS_FAIL) {
                        GlobalShareState.STATUS_RESET
                    } else {
                        longImageStatus
                    },
                    result = null,
                    qrCode = it()?.info?.jumpUrl
                )
            }
        } else {
            val data = Success(
                SharePendingData(
                    platform, info = CommonShareResult(
                        null,
                        null,
                        s.qrCode,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null
                    )
                )
            )
            setState {
                copy(
                    data = data,
                    longImageStatus = if (longImageStatus == GlobalShareState.STATUS_FAIL) {
                        GlobalShareState.STATUS_RESET
                    } else {
                        longImageStatus
                    },
                    result = null
                )
            }
        }
    }

    /**
     * 获取T台分享配置
     */
    private fun createConfig4Ttai(
        context: Context,
        s: GlobalShareState,
        platform: String
    ) {
        val data = Success(SharePendingData(platform))
        if (s.config == null) {
            tTaiInteractor.getSceneShareConfig(ShareWrapper.mapScene2ConfigScene(s.raw.scene))
                .execute {
                    when (it) {
                        is Success -> {
                            copy(
                                config = it.invoke(),
                                data = data,
                                longImageStatus = if (longImageStatus == GlobalShareState.STATUS_FAIL) {
                                    GlobalShareState.STATUS_RESET
                                } else {
                                    longImageStatus
                                },
                                result = null
                            )
                        }

                        is Fail -> {
                            copy(data = Fail(it.error))
                        }

                        else -> {
                            copy(data = Loading())
                        }
                    }
                }
        } else {
            setState {
                copy(
                    data = data,
                    longImageStatus = if (longImageStatus == GlobalShareState.STATUS_FAIL) {
                        GlobalShareState.STATUS_RESET
                    } else {
                        longImageStatus
                    },
                    result = null
                )
            }
        }
    }

    /**
     * 获取T台分享配置-transformer
     */
    private fun createConfig4TtaiWithTransformer(
        context: Context,
        s: GlobalShareState,
        platform: String
    ) {
        val data = Success(SharePendingData(platform))
        if (s.config == null) {
            tTaiInteractor.getSceneShareConfig(ShareWrapper.mapScene2ConfigScene(s.raw.scene)).map {
                GlobalShareCreateConfigHelper.ttaiConfigTransformer(context, platform, it)
            }.execute(dispatcher = Dispatchers.IO) {
                when (it) {
                    is Success -> {
                        copy(
                            config = it.invoke(),
                            data = data,
                            longImageStatus = if (longImageStatus == GlobalShareState.STATUS_FAIL) {
                                GlobalShareState.STATUS_RESET
                            } else {
                                longImageStatus
                            },
                            result = null
                        )
                    }

                    is Fail -> {
                        copy(data = Fail(it.error))
                    }

                    else -> {
                        copy(data = Loading())
                    }
                }
            }
        } else {
            setState {
                copy(
                    data = data,
                    longImageStatus = if (longImageStatus == GlobalShareState.STATUS_FAIL) {
                        GlobalShareState.STATUS_RESET
                    } else {
                        longImageStatus
                    },
                    result = null
                )
            }
        }
    }

    /**
     * Config for Ttai & Link
     */
    private fun createConfig4TtaiLink(
        context: Context,
        s: GlobalShareState,
        platform: String
    ) {
        val request = createShareRequest(context, s, platform, false) ?: return
        if (s.config == null) {
            tTaiInteractor.getSceneShareConfig(ShareWrapper.mapScene2ConfigScene(s.raw.scene))
                .combine(repo.createShare(request).asFlow()) { config, link ->
                    config to SharePendingData(platform, info = link)
                }
                .execute {
                    when (it) {
                        is Success -> {
                            copy(
                                config = it().first,
                                data = it.map { it.second },
                                longImageStatus = if (longImageStatus == GlobalShareState.STATUS_FAIL) {
                                    GlobalShareState.STATUS_RESET
                                } else {
                                    longImageStatus
                                },
                                result = null
                            )
                        }

                        is Fail -> {
                            copy(data = Fail(it.error))
                        }

                        else -> {
                            copy(data = Loading())
                        }
                    }
                }
        } else {
            repo.createShare(request).map {
                SharePendingData(
                    platform,
                    info = it
                )
            }.execute {
                copy(
                    data = it,
                    longImageStatus = if (longImageStatus == GlobalShareState.STATUS_FAIL) {
                        GlobalShareState.STATUS_RESET
                    } else {
                        longImageStatus
                    },
                    result = null
                )
            }
        }
    }

    /**
     * Config for LongImage
     */
    private fun createConfig4LongImage(
        context: Context,
        s: GlobalShareState,
        platform: String
    ) {
        val oldData = s.data.invoke() ?: return
        val newData = Success(
            oldData.copy(subPlatform = platform, ts = SystemClock.elapsedRealtime())
        )
        setState {
            copy(
                data = newData,
                longImageStatus = if (longImageStatus == GlobalShareState.STATUS_FAIL) {
                    GlobalShareState.STATUS_RESET
                } else {
                    longImageStatus
                },
                result = null
            )
        }
    }

    private fun createConfig4Post(
        context: Context,
        s: GlobalShareState,
        platform: String
    ) {
        val data = Success(SharePendingData(platform))
        if (s.defaultCircleId.shouldLoad) {
            tTaiInteractor.getTTaiV3(TTaiKV.ID_COMMUNITY_CIRCLE_ID).execute {
                when (it) {
                    is Success -> {
                        copy(
                            defaultCircleId = it,
                            data = data
                        )
                    }

                    is Fail -> {
                        copy(data = Fail(it.error))
                    }

                    else -> {
                        copy(data = Loading())
                    }
                }
            }
        } else {
            setState {
                copy(
                    data = data,
                    result = null
                )
            }
        }
    }

    private fun create3rdConfigDispatcher(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        platform: String,
        configType: Int
    ) {
        when (configType) {
            CONFIG_NOTHING -> {
                createConfig4Nothing(activity, s, platform)
            }

            CONFIG_LINK -> {
                createConfig4Link(activity, s, platform)
            }

            CONFIG_TTAI -> {
                createConfig4Ttai(activity, s, platform)
            }

            CONFIG_TTAI_TRANSFORM -> {
                createConfig4TtaiWithTransformer(activity, s, platform)
            }

            CONFIG_LONG_IMAGE -> {
                createConfig4LongImage(activity, s, platform)
            }

            CONFIG_POST -> {
                createConfig4Post(activity, s, platform)
            }

            CONFIG_QR_CODE -> {
                createConfig4QrCode(activity, s, platform)
            }

            CONFIG_TTAI_LINK -> {
                createConfig4TtaiLink(activity, s, platform)
            }
        }
    }
    //endregion

    //region share
    fun share(fragment: Fragment, data: SharePendingData) {
        val activity = fragment.requireActivity()
        val s = oldState
        when (s.raw.scene) {
            ShareHelper.SCENE_PROFILE -> {
                shareProfile(fragment, activity, s, data)
            }

            ShareHelper.SCENE_PGC_DETAIL -> {
                sharePgc(fragment, activity, s, data)
            }

            ShareHelper.SCENE_UGC_DETAIL -> {
                shareUgc(fragment, activity, s, data)
            }

            ShareHelper.SCENE_POST_DETAIL -> {
                sharePost(fragment, activity, s, data)
            }

            ShareHelper.SCENE_VIDEO_FEED -> {
                shareVideo(fragment, activity, s, data)
            }

            ShareHelper.SCENE_OC_MOMENT -> {
                shareOcMoment(fragment, activity, s, data)
            }

            ShareHelper.SCENE_SCREENSHOT -> {
                shareScreenshot(fragment, activity, s, data)
            }

            ShareHelper.SCENE_UGC_DESIGN_DETAIL -> {
                shareUgcDesign(fragment, activity, s, data)
            }
        }
    }

    private fun shareProfile(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData
    ) {
        val user = s.raw.user ?: return
        when (data.platform) {
            SharePlatform.PLATFORM_FRIEND -> {
                MetaRouter.Main.anyToContacts(
                    activity,
                    fragment,
                    shareRequestKey = requestId,
                    shareContent = ShareContent.userProfileString(user)
                )
            }

            SharePlatform.PLATFORM_SAVE -> {
                s.longImagePath?.let {
                    MediaStoreUtils.savePathToAlbumWithPermissionCheck(
                        fragment,
                        it,
                        ::notifySaveAlbum
                    )
                }
            }

            SharePlatform.PLATFORM_SYSTEM -> {
                val (title, desc) = GlobalShareCreateConfigHelper.getProfileTitleContent(
                    activity,
                    s.config,
                    user.nickname.orEmpty()
                )
                s.longImagePath?.let {
                    val result = SystemShare.shareImagePathBySystem(
                        activity,
                        it,
                        title = title,
                        desc = desc,
                        requestId = requestId
                    )
                    if (!result) {
                        notifyResult(false, data)
                    }
                }
            }
        }

        share3rdDispatcher(
            fragment,
            activity,
            s,
            data,
            GlobalShareHelper.shareProfile(fragment, activity, s, data, requestId)
        )
    }

    private fun sharePgc(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData
    ) {
        val pgcGame = s.raw.pgcGame ?: return
        when (data.platform) {
            SharePlatform.PLATFORM_POST -> {
                MetaRouter.Post.goPublishPost(
                    fragment,
                    ResIdBean().setCategoryID(CategoryId.GAME_SHARE_CARD_PGC),
                    games = listOf(
                        PostCardInfo(
                            PostCardInfo.TYPE_PGC,
                            pgcGame.id.orEmpty(),
                            pgcGame.pkg.orEmpty(),
                            pgcGame.likeCount ?: 0,
                            pgcGame.playerCount ?: 0,
                            pgcGame.authorName,
                            pgcGame.icon,
                            null,
                            pgcGame.name,
                            pgcGame.avgScore ?: 0.0f,
                            null
                        )
                    ),
                    shareReqId = requestId
                )
            }

            SharePlatform.PLATFORM_FRIEND -> {
                MetaRouter.Main.anyToContacts(
                    activity,
                    fragment,
                    shareRequestKey = requestId,
                    shareContent = ShareContent.pgcDetailString(pgcGame)
                )
            }

            SharePlatform.PLATFORM_SAVE -> {
                if (s.paths.isNullOrEmpty()) {
                    prepareFiles(fragment, activity, data)
                } else {
                    MediaStoreUtils.savePathsToAlbumWithPermissionCheck(
                        fragment,
                        s.paths,
                        ::notifySaveAlbum
                    )
                }
            }

            SharePlatform.PLATFORM_SYSTEM -> {
                if (s.paths.isNullOrEmpty()) {
                    prepareFiles(fragment, activity, data)
                } else {
                    val result = SystemShare.shareImagePathsBySystem(
                        activity,
                        s.paths,
                        title = activity.getString(
                            R.string.global_share_content_2,
                            pgcGame.name.orEmpty()
                        ),
                        desc = activity.getString(R.string.global_share_content_3),
                        requestId = requestId
                    )
                    if (!result) {
                        notifyResult(false, data)
                    }
                }
            }

            SharePlatform.PLATFORM_LONG_IMAGE -> {
                when (data.subPlatform) {
                    SharePlatform.PLATFORM_SAVE -> {
                        s.longImagePath?.let {
                            MediaStoreUtils.savePathToAlbumWithPermissionCheck(
                                fragment,
                                it,
                                ::notifySaveAlbum
                            )
                        }
                    }

                    else -> {
                        if (!GlobalShareHelper.sharePgcSupportLongImage(data.subPlatform)) {
                            setState { copy(type = GlobalShareState.TYPE_LONG_IMAGE) }
                        }
                    }
                }
            }

            SharePlatform.PLATFORM_LINK -> {
                val url = data.info?.jumpUrl ?: return
                viewModelScope.launch {
                    ClipBoardUtil.setClipBoardContent(url, activity)
                }
                notifyResult(true, data)
            }
        }

        share3rdDispatcher(
            fragment,
            activity,
            s,
            data,
            GlobalShareHelper.sharePgc(
                fragment,
                activity,
                s,
                data,
                requestId,
                pgcGame,
                viewModelScope
            )
        )
    }

    private fun shareUgc(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData
    ) {
        val ugcGame = s.raw.ugcGame ?: return
        when (data.platform) {
            SharePlatform.PLATFORM_POST -> {
                MetaRouter.Post.goPublishPost(
                    fragment,
                    ResIdBean().setCategoryID(CategoryId.GAME_SHARE_CARD_UGC),
                    games = listOf(
                        PostCardInfo(
                            PostCardInfo.TYPE_UGC,
                            ugcGame.id,
                            ugcGame.packageName.orEmpty(),
                            ugcGame.loveQuantity,
                            ugcGame.pvCount,
                            ugcGame.author?.name,
                            ugcGame.banner,
                            null,
                            ugcGame.ugcGameName,
                            0.0f,
                            null
                        )
                    ),
                    shareReqId = requestId
                )
            }

            SharePlatform.PLATFORM_FRIEND -> {
                MetaRouter.Main.anyToContacts(
                    activity,
                    fragment,
                    shareRequestKey = requestId,
                    shareContent = ShareContent.ugcDetailString(ugcGame)
                )
            }

            SharePlatform.PLATFORM_SAVE -> {
                if (s.paths.isNullOrEmpty()) {
                    prepareFiles(fragment, activity, data)
                } else {
                    MediaStoreUtils.savePathsToAlbumWithPermissionCheck(
                        fragment,
                        s.paths,
                        ::notifySaveAlbum
                    )
                }
            }

            SharePlatform.PLATFORM_SYSTEM -> {
                if (s.paths.isNullOrEmpty()) {
                    prepareFiles(fragment, activity, data)
                } else {
                    val result = SystemShare.shareImagePathsBySystem(
                        activity,
                        s.paths,
                        title = activity.getString(
                            R.string.global_share_content_2,
                            ugcGame.ugcGameName.orEmpty()
                        ),
                        desc = activity.getString(R.string.global_share_content_3),
                        requestId = requestId
                    )
                    if (!result) {
                        notifyResult(false, data)
                    }
                }
            }

            SharePlatform.PLATFORM_LONG_IMAGE -> {
                when (data.subPlatform) {
                    SharePlatform.PLATFORM_SAVE -> {
                        s.longImagePath?.let {
                            MediaStoreUtils.savePathToAlbumWithPermissionCheck(
                                fragment,
                                it,
                                ::notifySaveAlbum
                            )
                        }
                    }

                    else -> {
                        if (!GlobalShareHelper.shareUgcSupportLongImage(data.subPlatform)) {
                            setState { copy(type = GlobalShareState.TYPE_LONG_IMAGE) }
                        }
                    }
                }
            }

            SharePlatform.PLATFORM_LINK -> {
                val url = data.info?.jumpUrl ?: return
                viewModelScope.launch {
                    ClipBoardUtil.setClipBoardContent(url, activity)
                }
                notifyResult(true, data)
            }
        }

        share3rdDispatcher(
            fragment,
            activity,
            s,
            data,
            GlobalShareHelper.shareUgc(
                fragment,
                activity,
                s,
                data,
                requestId,
                ugcGame,
                viewModelScope
            )
        )
    }

    private fun sharePost(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData
    ) {
        val postDetail = s.raw.postDetail ?: return
        when (data.platform) {
            SharePlatform.PLATFORM_FRIEND -> {
                MetaRouter.Main.anyToContacts(
                    activity,
                    fragment,
                    shareRequestKey = requestId,
                    shareContent = ShareContent.postDetailString(postDetail)
                )
            }

            SharePlatform.PLATFORM_LINK -> {
                val url = data.info?.jumpUrl ?: return
                viewModelScope.launch {
                    ClipBoardUtil.setClipBoardContent(url, activity)
                }
                notifyResult(true, data)
            }

            SharePlatform.PLATFORM_SYSTEM -> {
                if (s.raw.hasMedia) {
                    if (s.paths.isNullOrEmpty()) {
                        prepareFiles(fragment, activity, data)
                    } else if (s.raw.hasVideo) {
                        val videoPath = s.paths.firstOrNull() ?: return
                        val result = SystemShare.shareVideoPathBySystem(
                            activity,
                            videoPath,
                            title = activity.getString(
                                R.string.global_share_content_2,
                                postDetail.content.ifNullOrEmpty { activity.getString(R.string.community_post_title) }
                            ),
                            desc = activity.getString(R.string.global_share_content_3),
                            requestId = requestId
                        )
                        if (!result) {
                            notifyResult(false, data)
                        }
                    } else {
                        val result = SystemShare.shareImagePathsBySystem(
                            activity,
                            s.paths,
                            title = activity.getString(
                                R.string.global_share_content_2,
                                postDetail.content.ifNullOrEmpty { activity.getString(R.string.community_post_title) }
                            ),
                            desc = activity.getString(R.string.global_share_content_3),
                            requestId = requestId
                        )
                        if (!result) {
                            notifyResult(false, data)
                        }
                    }
                } else {
                    val url = data.info?.jumpUrl ?: return
                    val result = SystemShare.shareTextViaSystem(
                        activity,
                        title = activity.getString(R.string.global_share_content_3),
                        desc = url,
                        requestId = requestId
                    )
                    if (!result) {
                        notifyResult(false, data)
                    }
                }
            }
        }

        share3rdDispatcher(
            fragment,
            activity,
            s,
            data,
            GlobalShareHelper.sharePost(
                fragment,
                activity,
                s,
                data,
                requestId,
                postDetail,
                viewModelScope
            )
        )
    }

    private fun shareVideo(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData
    ) {
        val videoFeed = s.raw.videoFeed ?: return
        when (data.platform) {
            SharePlatform.PLATFORM_FRIEND -> {
                MetaRouter.Main.anyToContacts(
                    activity,
                    fragment,
                    shareRequestKey = requestId,
                    shareContent = ShareContent.videoFeedString(videoFeed)
                )
            }

            SharePlatform.PLATFORM_SAVE -> {
                if (s.paths.isNullOrEmpty()) {
                    prepareFiles(fragment, activity, data)
                } else {
                    val videoPath = s.paths.firstOrNull() ?: return
                    MediaStoreUtils.savePathToAlbumWithPermissionCheck(
                        fragment,
                        videoPath,
                        ::notifySaveAlbum
                    )
                }
            }

            SharePlatform.PLATFORM_SYSTEM -> {
                if (s.paths.isNullOrEmpty()) {
                    prepareFiles(fragment, activity, data)
                } else {
                    val videoPath = s.paths.firstOrNull() ?: return
                    val result = SystemShare.shareVideoPathBySystem(
                        activity,
                        videoPath,
                        title = activity.getString(
                            R.string.global_share_content_2,
                            videoFeed.videoContent
                        ),
                        desc = activity.getString(R.string.global_share_content_3),
                        requestId = requestId
                    )
                    if (!result) {
                        notifyResult(false, data)
                    }
                }
            }

            SharePlatform.PLATFORM_LINK -> {
                val url = data.info?.jumpUrl ?: return
                viewModelScope.launch {
                    ClipBoardUtil.setClipBoardContent(url, activity)
                }
                notifyResult(true, data)
            }
        }

        share3rdDispatcher(
            fragment,
            activity,
            s,
            data,
            GlobalShareHelper.shareVideo(
                fragment,
                activity,
                s,
                data,
                requestId,
                videoFeed,
                viewModelScope
            )
        )
    }

    private fun shareOcMoment(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData
    ) {
        when (data.platform) {
            SharePlatform.PLATFORM_SAVE -> {
                if (s.raw.hasVideo) {
                    MediaStoreUtils.savePathToAlbumWithPermissionCheck(
                        fragment,
                        s.raw.video ?: return,
                        ::notifySaveAlbum
                    )
                } else if (s.raw.hasImage) {
                    MediaStoreUtils.savePathsToAlbumWithPermissionCheck(
                        fragment,
                        s.raw.images ?: return,
                        ::notifySaveAlbum
                    )
                }
            }

            SharePlatform.PLATFORM_SYSTEM -> {
                if (s.raw.hasVideo) {
                    SystemShare.shareVideoPathBySystem(
                        activity,
                        s.raw.video ?: return,
                        config = s.config,
                        requestId = requestId
                    )
                } else if (s.raw.hasImage) {
                    SystemShare.shareImagePathsBySystem(
                        activity,
                        s.raw.images ?: return,
                        config = s.config,
                        requestId = requestId
                    )
                }
            }
        }

        share3rdDispatcher(
            fragment,
            activity,
            s,
            data,
            GlobalShareHelper.shareOcMoment(fragment, activity, s, data, requestId)
        )
    }

    private fun shareScreenshot(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData
    ) {
        when (data.platform) {
            SharePlatform.PLATFORM_FRIEND -> {
                s.raw.image?.let {
                    MetaRouter.Main.anyToContacts(
                        activity,
                        fragment,
                        shareRequestKey = requestId,
                        shareContent = ShareContent.imageString(it)
                    )
                }
            }

            SharePlatform.PLATFORM_SAVE -> {
                s.longImagePath?.let {
                    MediaStoreUtils.savePathToAlbumWithPermissionCheck(
                        fragment,
                        it,
                        ::notifySaveAlbum
                    )
                }
            }

            SharePlatform.PLATFORM_SYSTEM -> {
                s.longImagePath?.let {
                    val result = SystemShare.shareImagePathBySystem(
                        activity,
                        it,
                        config = s.config,
                        requestId = requestId
                    )
                    if (!result) {
                        notifyResult(false, data)
                    }
                }
            }
        }

        share3rdDispatcher(
            fragment,
            activity,
            s,
            data,
            GlobalShareHelper.shareScreenshot(fragment, activity, s, data, requestId)
        )
    }

    private fun shareUgcDesign(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData
    ) {
        val ugcDesignDetail = s.raw.ugcDesignDetail ?: return
        when (data.platform) {
            SharePlatform.PLATFORM_FRIEND -> {
                MetaRouter.Main.anyToContacts(
                    activity,
                    fragment,
                    shareRequestKey = requestId,
                    shareContent = ShareContent.ugcDesignDetailString(ugcDesignDetail)
                )
            }

            SharePlatform.PLATFORM_POST -> {
                MetaRouter.Post.goPublishPost(
                    fragment,
                    ResIdBean().setCategoryID(CategoryId.UGC_DESIGN_DETAIL),
                    ugcDesign = PostUgcDesignCard.createUgcDesignCard(
                        ugcDesignDetail.feedId,
                        ugcDesignDetail.cover,
                        ugcDesignDetail.title
                    ),
                    shareReqId = requestId
                )
            }
        }
    }

    private fun share3rdDispatcher(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData,
        opt: Int
    ) {
        when (opt) {
            OPT_PREPARE_FILES -> {
                prepareFiles(fragment, activity, data)
            }
        }
    }
    //endregion

    private fun prepareFiles(fragment: Fragment, context: Context, data: SharePendingData) {
        val s = oldState
        setState { copy(pendingData = data) }
        if (s.progress is Loading) return
        runCatching {
            if (s.raw.hasVideo) {
                val url = s.raw.video ?: return
                val ext = url.substringAfterLast(".").ifEmpty { "mp4" }
                val filename = "${url.md5()}.$ext"
                val cacheDir = ShareHelper.getCacheDir(context)
                val file = File(
                    cacheDir,
                    filename
                )
                if (file.exists()) {
                    val paths = listOf(file.absolutePath)
                    setState { copy(paths = paths) }
                } else {
                    val tempVideoFile = File(
                        cacheDir,
                        filename
                    )
                    if (SimpleDownloader.isDownloading(tempVideoFile)) {
                        return
                    } else {
                        this.tempVideoFile = tempVideoFile
                        SimpleDownloader.download(
                            tempVideoFile,
                            url,
                            progress = { totalSize, completeSize ->
                                val percent = ((completeSize.toDouble() / totalSize) * 100).toInt()
                                setState { copy(progress = Loading(percent)) }
                            },
                            callback = { succeeded ->
                                if (succeeded) {
                                    tempVideoFile.renameTo(file)
                                    val paths = listOf(file.absolutePath)
                                    setState {
                                        copy(
                                            progress = Success(100),
                                            paths = paths
                                        )
                                    }
                                } else {
                                    setState {
                                        copy(
                                            data = Uninitialized,
                                            pendingData = null,
                                            progress = Uninitialized,
                                            toast = toast.toResMsg(R.string.create_share_info_failed)
                                        )
                                    }
                                }
                                this.tempVideoFile = null
                            }
                        )
                    }
                }
            } else if (s.raw.hasImage) {
                val urls = s.raw.images
                if (urls.isNullOrEmpty()) return
                val cacheDir = ShareHelper.getCacheDir(context)
                val paths = ArrayList<String>()
                val totalCount = urls.size
                if (glide == null) {
                    glide = Glide.with(fragment)
                }
                doneCount = 0
                val tempKey = SystemClock.elapsedRealtime()
                prepareKey = tempKey
                var needGlide = false
                for (url in urls) {
                    val ext = url.substringAfterLast(".").ifEmpty { "png" }
                    val filename = "${url.md5()}.$ext"
                    val file = File(
                        cacheDir,
                        filename
                    )
                    paths.add(file.absolutePath)
                    if (file.exists()) {
                        doneCount++
                    } else {
                        needGlide = true
                        glide?.asFile()
                            ?.load(url)
                            ?.listener(object : RequestListener<File> {
                                override fun onLoadFailed(
                                    e: GlideException?,
                                    model: Any?,
                                    target: Target<File>,
                                    isFirstResource: Boolean
                                ): Boolean {
                                    if (tempKey != prepareKey) return true
                                    prepareKey = 0L
                                    if (doneCount >= 0) {
                                        doneCount = -1
                                        setState {
                                            copy(
                                                data = Uninitialized,
                                                pendingData = null,
                                                progress = Uninitialized,
                                                toast = toast.toResMsg(R.string.share_fail)
                                            )
                                        }
                                    }
                                    return true
                                }

                                override fun onResourceReady(
                                    resource: File,
                                    model: Any,
                                    target: Target<File>?,
                                    dataSource: DataSource,
                                    isFirstResource: Boolean
                                ): Boolean {
                                    if (tempKey != prepareKey) return true
                                    if (doneCount >= 0 && viewModelScope.isActive) {
                                        resource.copyTo(file, overwrite = true)
                                        doneCount++
                                        if (doneCount == totalCount) {
                                            prepareKey = 0L
                                            setState {
                                                copy(
                                                    progress = Success(100),
                                                    paths = paths
                                                )
                                            }
                                        } else {
                                            val progress =
                                                ((doneCount.toFloat() / totalCount) * 100).toInt()
                                            setState {
                                                copy(progress = Loading(progress))
                                            }
                                        }
                                    } else {
                                        prepareKey = 0L
                                    }
                                    return true
                                }
                            })
                            ?.skipMemoryCache(true)
                            ?.submit()
                    }
                }
                if (!needGlide && doneCount == totalCount) {
                    prepareKey = 0L
                    setState {
                        copy(
                            progress = Success(100),
                            paths = paths
                        )
                    }
                }
            }
        }.onFailure {
            prepareKey = 0L
            setState {
                copy(
                    data = Uninitialized,
                    pendingData = null,
                    progress = Uninitialized,
                    toast = toast.toResMsg(R.string.share_fail)
                )
            }
        }
    }

    fun longImageLoading(urls: List<String>): Long {
        val tempKey = SystemClock.elapsedRealtime()
        longImageKey = tempKey
        longImageSet.clear()
        longImageSet.addAll(urls)
        setState {
            copy(
                longImageStatus = GlobalShareState.STATUS_LOADING
            )
        }
        return tempKey
    }

    fun longImageProgress(view: View, statusBarHeight: Int, url: String, key: Long) {
        if (longImageKey == key && viewModelScope.isActive) {
            withState { s ->
                val isLongImage = s.isLongImageMode
                val hasImage = longImageSet.remove(url)
                val noImageLeft = longImageSet.isEmpty()
                if (isLongImage && hasImage && noImageLeft) {
                    viewModelScope.launch {
                        view.requestLayout()
                        view.doOnNextLayout {
                            if (longImageKey == key && viewModelScope.isActive) {
                                viewModelScope.launch(Dispatchers.IO) {
                                    val bitmap = if (statusBarHeight > 0) {
                                        BitmapUtil.getBitmapNoBackground(view, statusBarHeight)
                                    } else {
                                        BitmapUtil.getBitmapNoBackground(view)
                                    }
                                    val file =
                                        ShareHelper.saveBitmap(
                                            view.context,
                                            bitmap,
                                            "one_time_$requestId.jpg"
                                        )
                                    withContext(Dispatchers.Main) {
                                        if (file == null) {
                                            longImageFail(key = key)
                                        } else {
                                            longImageDone(file.absolutePath, key)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    fun longImageDone(path: String, key: Long) = withState { s ->
        if (key == longImageKey && s.isLongImageMode) {
            longImageKey = 0L
            setState {
                copy(
                    longImageStatus = GlobalShareState.STATUS_DONE,
                    longImagePath = path
                )
            }
        }
    }

    fun longImageFail(url: String? = null, key: Long? = null) {
        if ((key != null && key != longImageKey)
            || (!url.isNullOrEmpty() && !longImageSet.contains(url))
        ) return
        if (viewModelScope.isActive) {
            withState { s ->
                if (s.isLongImageMode) {
                    longImageKey = 0L
                    longImageSet.clear()
                    setState {
                        copy(
                            longImageStatus = GlobalShareState.STATUS_FAIL
                        )
                    }
                }
            }
        }
    }

    fun longImageQuit() = withState { s ->
        if (s.isLongImageMode) {
            longImageKey = 0L
            longImageSet.clear()
            setState {
                copy(
                    type = if (type == GlobalShareState.TYPE_LONG_IMAGE) {
                        GlobalShareState.TYPE_NORMAL
                    } else {
                        type
                    },
                    data = Uninitialized,
                    longImageStatus = GlobalShareState.STATUS_RESET,
                    longImagePath = null
                )
            }
        }
    }

    private fun notifySaveAlbum(success: Boolean, uris: List<Uri>) {
        if (viewModelScope.isActive) {
            withState { s ->
                s.data.invoke()?.let {
                    when (it.platform) {
                        SharePlatform.PLATFORM_SAVE -> {
                            notifyResult(success, it)
                        }

                        SharePlatform.PLATFORM_LONG_IMAGE -> {
                            when (it.subPlatform) {
                                SharePlatform.PLATFORM_SAVE -> {
                                    notifyResult(success, it)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    fun notifyShareToFriend(success: Boolean, uuid: String?) = withState { s ->
        s.data.invoke()?.let {
            if (it.platform == SharePlatform.PLATFORM_FRIEND) {
                notifyResult(success, it, extra = uuid)
            }
        }
    }

    fun notifyResult(success: Boolean, data: SharePendingData, extra: String? = null) =
        withState { s ->
            val platform = if (data.platform == SharePlatform.PLATFORM_LONG_IMAGE) {
                val newData = Success(
                    data.copy(subPlatform = null, ts = SystemClock.elapsedRealtime())
                )
                setState { copy(data = newData, result = success to data) }
                recordShare(success, data.subPlatform)
                data.subPlatform
            } else {
                setState {
                    copy(data = Uninitialized, result = success to data)
                }
                recordShare(success, data.platform)
                data.platform
            }
            val map: HashMap<String, Any> = hashMapOf(
                "result" to (if (success) 1 else 0)
            )
            s.raw.reqId?.let {
                map["reqid"] = it
            }
            map["type"] = SharePlatform.platformTrackParam(platform)
            map["source"] = s.raw.trackSource
            when (platform) {
                SharePlatform.PLATFORM_FRIEND -> {
                    if (extra != null) {
                        map["extra"] = extra
                    }
                }

                SharePlatform.PLATFORM_LINK -> {

                }

                SharePlatform.PLATFORM_SYSTEM -> {
                    if (extra != null) {
                        map["extra"] = extra
                    }

                }

                SharePlatform.PLATFORM_SAVE -> {

                }

                SharePlatform.PLATFORM_POST -> {

                }

                else -> {
                    GlobalShareTrackHelper.trackResultParam(platform, map, extra)
                }
            }
            when (s.raw.scene) {
                ShareHelper.SCENE_PGC_DETAIL -> {
                    s.raw.pgcGame?.id?.let {
                        map["gameid"] = it
                    }
                }

                ShareHelper.SCENE_UGC_DETAIL -> {
                    s.raw.ugcGame?.id?.let {
                        map["ugcid"] = it
                    }
                }

                ShareHelper.SCENE_VIDEO_FEED -> {
                    s.raw.videoFeed?.videoId?.let {
                        map["postid"] = it
                    }
                }

                ShareHelper.SCENE_OC_MOMENT -> {
                    s.raw.gameId?.let {
                        map["gameid"] = it
                    }
                }

                ShareHelper.SCENE_SCREENSHOT -> {
                    s.raw.gameId?.let {
                        map["gameid"] = it
                    }
                }

                ShareHelper.SCENE_UGC_DESIGN_DETAIL -> {
                    s.raw.ugcDesignDetail?.let {
                        map["metrialidid"] = it.trackId
                    }
                }
            }
            Analytics.track(EventConstants.EVENT_SHARE_TYPE, map)
        }

    private fun recordShare(success: Boolean, platform: String?) = withState { s ->
        if (platform == null || !success) return@withState
        val (resId, resType) = s.raw.getResIdAndType() ?: return@withState
        val scene = s.raw.scene
        val shareRecordId = "${scene}_${resId}"
        incrementCallback.dispatchOnMainThread { invoke() }
        GlobalScope.launch(Dispatchers.IO) {
            repo.insertShareRecord(shareRecordId, platform).single()
            repo.commonAddPvCount(
                resType,
                resId
            ).collect {}
        }
    }

    override fun onCleared() {
        unregisterHermes()
        glide = null

        tempVideoFile?.let {
            SimpleDownloader.stop(null, it)
            tempVideoFile = null
        }
        longImageSet.clear()
        super.onCleared()
    }

    @Subscribe
    fun onEvent(shareResult: ShareResult) = withState { s ->
        s.data.invoke()?.let {
            if (!shareResult.match(requestId, it.platform)) return@let
            notifyResult(shareResult.isSuccess, it, shareResult.extra)
        }
    }

    companion object : KoinViewModelFactory<GlobalShareViewModel, GlobalShareState>() {

        const val CONFIG_UNSUPPORTED = 0
        const val CONFIG_NOTHING = 1
        const val CONFIG_LINK = 2
        const val CONFIG_TTAI = 3
        const val CONFIG_TTAI_TRANSFORM = 4
        const val CONFIG_LONG_IMAGE = 5
        const val CONFIG_POST = 6
        const val CONFIG_QR_CODE = 7
        const val CONFIG_TTAI_LINK = 8

        const val OPT_UNSUPPORTED = 0
        const val OPT_PREPARE_FILES = 1

        override fun ComponentCallbacks.initialState(viewModelContext: ViewModelContext): GlobalShareState {
            val args = viewModelContext.args as GlobalShareArgs
            val raw =
                GsonUtil.gsonSafeParseCollection<ShareRawData>(args.raw) ?: ShareRawData(
                    ShareHelper.SCENE_UNKNOWN
                )
            val platforms = ArrayList<SharePlatform>()
            val longImagePlatforms = ArrayList<SharePlatform>()

            val type = when (raw.scene) {
                ShareHelper.SCENE_PROFILE -> {
                    val accountInteractor: AccountInteractor = get()
                    val isMe = accountInteractor.isMe(raw.user?.uid)
                    GlobalSharePlatformHelper.profile(isMe, platforms)
                    if (isMe) {
                        GlobalShareState.TYPE_LONG_IMAGE_PRE
                    } else {
                        GlobalShareState.TYPE_NORMAL
                    }
                }

                ShareHelper.SCENE_PGC_DETAIL -> {
                    GlobalSharePlatformHelper.pgc(raw, platforms)
                    GlobalSharePlatformHelper.pgcLongImage(longImagePlatforms)
                    GlobalShareState.TYPE_NORMAL
                }

                ShareHelper.SCENE_UGC_DETAIL -> {
                    GlobalSharePlatformHelper.ugc(raw, platforms)
                    GlobalSharePlatformHelper.ugcLongImage(longImagePlatforms)
                    GlobalShareState.TYPE_NORMAL
                }

                ShareHelper.SCENE_POST_DETAIL -> {
                    GlobalSharePlatformHelper.postDetail(raw, platforms)
                    GlobalShareState.TYPE_NORMAL
                }

                ShareHelper.SCENE_VIDEO_FEED -> {
                    GlobalSharePlatformHelper.videoFeed(platforms)
                    GlobalShareState.TYPE_NORMAL
                }

                ShareHelper.SCENE_OC_MOMENT -> {
                    GlobalSharePlatformHelper.ocMoment(raw, platforms)
                    GlobalShareState.TYPE_NORMAL
                }

                ShareHelper.SCENE_SCREENSHOT -> {
                    GlobalSharePlatformHelper.screenshot(raw, platforms)
                    GlobalShareState.TYPE_LONG_IMAGE_POST
                }

                ShareHelper.SCENE_UGC_DESIGN_DETAIL -> {
                    GlobalSharePlatformHelper.assetDetail(platforms)
                    GlobalShareState.TYPE_NORMAL
                }

                else -> {
                    GlobalShareState.TYPE_UNKNOWN
                }
            }

            return GlobalShareState(
                args.features,
                platforms,
                longImagePlatforms,
                raw,
                type
            )
        }

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: GlobalShareState,
        ): GlobalShareViewModel {
            return GlobalShareViewModel(state, get(), get(), get())
        }
    }
}