package com.socialplay.gpark.ui.aibot.ugc.create

import android.content.Context
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.UploadFileInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.aibot.AIBotCreateImageInfo
import com.socialplay.gpark.data.model.aibot.AIBotCreateRequest
import com.socialplay.gpark.data.model.aibot.AIBotCreateResult
import com.socialplay.gpark.data.model.aibot.AIBotStyle
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.GsonUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.single
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/26
 *     desc   :
 *
 */
class AIBotUGCCreateResultViewModel(
    val metaRepository: IMetaRepository,
    private val uploadFileInteractor: UploadFileInteractor,
    private val metaKV: MetaKV,
) :
    ViewModel() {
    // 图生图，文生图结果
    private val _generateCreateInfo =
        MutableLiveData<DataResult<List<AIBotCreateImageInfo>?>?>(null)
    val generateCreateInfo = _generateCreateInfo
    private val _generateProgress = MutableLiveData<Int>(0)
    val generateProgress = _generateProgress

    //背景列表
    private val _bigImageList = MutableLiveData<List<String>>()
    val bigImageList = _bigImageList

    //背景列表
    private val _smallImageList = MutableLiveData<List<AIBotCreateImageInfo>?>()
    val smallImageList = _smallImageList

    //当前选中的
    private val _resultSelect = MutableLiveData<AIBotCreateImageInfo?>()
    val resultSelect = _resultSelect
    private val _aiBotCreateRequest = MutableLiveData<AIBotCreateRequest?>()
    val aiBotCreateRequest = _aiBotCreateRequest

    //查询进度的最大时长
    private val maxTime = 2 * 60 * 1000L

    private val timeSpace = 2000L
    private var retryTimes = 0
    private val maxRetryTimes = (maxTime / timeSpace).toInt()

    /**
     * 自动生成
     */
    fun generate(data: String, context: Context) = viewModelScope.launch {
        val body = GsonUtil.gsonSafeParse<AIBotCreateRequest>(data) ?: return@launch
        aiBotCreateRequest.postValue(body)
        startGenerate(body, context)
    }

    /**
     * 更新本地生成次数
     */
    private fun updateCreateCount(){
        val count = metaKV.account.getAiCreateTodayCount()
        metaKV.account.saveAiCreateTime()
        metaKV.account.setAICreateTodayCount(count + 1)
    }

    /**
     * 开始生成
     */
    private suspend fun startGenerate(body: AIBotCreateRequest, context: Context) {
        metaRepository.generateAIBotImage(body).collect() {
            if (it.succeeded) {
                it.data?.requestId?.let { it1 -> startCheckResult(it1, context) }
                updateCreateCount()
            } else {
                _generateCreateInfo.value = DataResult.Error(
                    it.code ?: 400,
                    it.message?:context.getString(R.string.ai_bot_upload_fail_error)
                )
            }
        }
    }

    /**
     * 开始轮训生成结果
     */
    private fun startCheckResult(resId: String, context: Context) = viewModelScope.launch(Dispatchers.IO) {
        while (retryTimes < maxRetryTimes) {
            val result = metaRepository.generateAIBotImageResult(resId)
            if (result.code != 200) {
                //接口
                retryTimes = maxRetryTimes
                withContext(Dispatchers.Main) {
                    //失败
                    updateGenerateResult(DataResult.Error(result.code ?: 400, result.message ?: context.getString(R.string.ai_bot_upload_fail_error)))
                }
                break
            }
            if (result.data?.generateStatus == true && !result.data?.imageList.isNullOrEmpty()) {
                retryTimes = maxRetryTimes
                withContext(Dispatchers.Main) {
                    updateGenerateResult(DataResult.Success(result.data?.imageList))
                }
                break
            } else {
                //更新进度
                _generateProgress.postValue(result.data?.buildProgress?:0)
                delay(timeSpace)
                retryTimes++
                if (retryTimes == maxRetryTimes) {
                    withContext(Dispatchers.Main) {
                        //超时
                        updateGenerateResult(DataResult.Error(400, context.getString(R.string.api_error_net_time_out)))
                    }
                }
            }
        }
    }

    /**
     * 更新结果
     */
    private fun updateGenerateResult(data: DataResult<List<AIBotCreateImageInfo>?>) {
        if (!data.data.isNullOrEmpty()) {
            val item = data.data?.first()
            item?.isSelect = true
            item?.selectType = aiBotCreateRequest.value?.type
            _resultSelect.value = item
            val list = data.data?.map {
                it.isSelect = it == item
                it
            }
            _bigImageList.value = data.data?.map { it.backgroundImage }
            _smallImageList.value = list
        }
        _generateCreateInfo.value = data
    }

    /**
     * 更新用户生成结果
     */
    fun changeResultSelect(item: AIBotCreateImageInfo, pos: Int) {
        item.selectType = aiBotCreateRequest.value?.type
        _resultSelect.value = item
        val list = _smallImageList.value?.mapIndexed { index, aiBotCreateImageInfo ->
            aiBotCreateImageInfo.isSelect = pos == index
            aiBotCreateImageInfo
        }
        _smallImageList.value = list
    }
}