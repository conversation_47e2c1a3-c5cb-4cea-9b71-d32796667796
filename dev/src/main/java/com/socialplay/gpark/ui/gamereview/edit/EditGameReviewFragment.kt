package com.socialplay.gpark.ui.gamereview.edit

import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.core.os.bundleOf
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.BanBlockInteractor
import com.socialplay.gpark.data.model.gamereview.RequestGameReviewsParam.Companion.MODULE_TYPE_AI_BOT
import com.socialplay.gpark.data.model.gamereview.RequestGameReviewsParam.Companion.MODULE_TYPE_GAME_ONLY
import com.socialplay.gpark.databinding.FragmentEditGameReviewBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.gamereview.GameReviewConstants
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.view.RatingView
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.TextWatcherAdapter
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.setFragmentResultByActivity
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.property.viewBinding
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2022/7/11
 * Desc: 编辑游戏评论页
 */
class EditGameReviewFragment : BaseFragment<FragmentEditGameReviewBinding>() {

    private val viewModel by viewModel<EditGameReviewViewModel>()
    private val banBlockInteractor: BanBlockInteractor by inject()
    private val args by navArgs<EditGameReviewFragmentArgs>()
    private val params by lazy {
        hashMapOf(
            "gameid" to (if(!args.gameId.isNullOrEmpty()) args.gameId else args.botId),
            "from" to REVIEW_PAGE
        )
    }

    private var ratingNum: Int? = null
    private var reviewContent: String? = null
    private var wordsLimit = 0L
    private var errMsg = "Write a Review"
    private var commentId = ""
    private var btnChangeListener: TextWatcherAdapter? = null
    private var isReview = false

    private val ratingChangeListener by lazy {
        object : RatingView.OnRatingChangedListener {
            override fun onRatingChange(oldRating: Float, newRating: Float) {
                ratingNum = newRating.toInt()
                updatePostBtn()
            }

            override fun onClickRating(newRating: Float) {

            }
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentEditGameReviewBinding? {
        return FragmentEditGameReviewBinding.inflate(inflater, container, false)
    }

    private fun getBackPressed() = object : OnBackPressedCallback(true) {
        override fun handleOnBackPressed() {
            sendBackAnalytic()
            findNavController().navigateUp()
        }
    }

    private fun sendBackAnalytic() {
        Analytics.track(EventConstants.WRITE_REVIEW_BACK_CLICK) {
            putAll(params)
            put("type", getReviewType())
        }
    }

    companion object {
        const val REQUEST_SUCCESS_UPDATE_MY_REVIEW = "request_success_update_my_review"
        const val RESULT_KEY_UPDATE_REVIEW = "update_review"
        const val REVIEW_PAGE = 1
    }

    override fun init() {
        initView()
        initData()
    }

    private fun initView() {
        binding.titleEditReview.setOnBackClickedListener {
            sendBackAnalytic()
            findNavController().navigateUp()
        }
        binding.ratingbar.rating = args.attitude.toFloat()
        ratingNum = args.attitude
        binding.ratingbar.onRatingChangedListener = ratingChangeListener
        wordsLimit = args.lengthLimit
        if (!TextUtils.isEmpty(args.errorMessage)) {
            errMsg = args.errorMessage ?: getString(R.string.write_review)
        }
        binding.etGameReview.hint = errMsg
        Timber.d("initData wordsLimit=$wordsLimit errMsg=$errMsg")
        updatePostBtn()
        initEvent()
        Analytics.track(EventConstants.GAME_REVIEW_SHOW) {
            putAll(params)
        }
    }

    private fun initEvent() {
        binding.tvPost.setOnAntiViolenceClickListener {
            ratingNum?.let { score ->
                binding.loading.showLoading()
                Analytics.track(EventConstants.REVIEW_POST) {
                    putAll(params)
                    put("type", getReviewType())
                    put("rate", score)
                }
                Timber.d("isReview $isReview")
                if(args.gameId!=null){
                    if (commentId.isEmpty()) {
                        viewModel.publishGameReview(
                            args.gameId!!,
                            binding.etGameReview.text.toString(),
                            score,
                            moduleTypeCode = MODULE_TYPE_GAME_ONLY
                        )
                    } else {
                        viewModel.updateGameReview(
                            commentId,
                            args.gameId!!,
                            binding.etGameReview.text.toString(),
                            score
                        )
                    }

                } else if (args.botId != null) {
                    viewModel.publishAiBotReview(
                        args.botId!!,
                        binding.etGameReview.text.toString(),
                        score,
                        moduleTypeCode = MODULE_TYPE_AI_BOT
                    )
                }

            }
        }
        btnChangeListener = object : TextWatcherAdapter() {
            override fun afterTextChanged(s: Editable?) {
                super.afterTextChanged(s)
                reviewContent = s.toString()
                updatePostBtn()
            }
        }
        binding.etGameReview.addTextChangedListener(btnChangeListener)
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, getBackPressed())
    }

    private fun updatePostBtn() {
        val canPost = !TextUtils.isEmpty(binding.etGameReview.text)
                && ratingNum != null
                && (reviewContent?.length ?: 0) > wordsLimit
        binding.tvPost.isEnabled = canPost
        binding.tvPost.alpha = if (canPost) 1F else 0.5F
    }

    private fun initData() {

        viewModel.myReviewLiveData.observe(viewLifecycleOwner) {
            binding.loading.hide()
            it?.let { data ->
                isReview = true
                commentId = data.commentId
                try {
                    binding.ratingbar.rating = data.score.toFloat()
                } catch (_: Exception) {

                }
                if (!TextUtils.isEmpty(data.content)) {
                    binding.etGameReview.setText(data.content)
                }
            }
        }

        viewModel.submitLiveData.observe(viewLifecycleOwner) {
            binding.loading.hide()
            val isSuccess = it.first
            val message = it.second
            if (isSuccess) {
                Analytics.track(EventConstants.GAME_REVIEW_PUBLISH_SUCCESS) {
                    putAll(params)
                    put("reviewid", message ?: "")
                    put("start_count", ratingNum ?: 0)
                    put("content", reviewContent ?: "")
                    put("time", DateUtil.getTodayHM())
                }
                setFragmentResultByActivity(
                    REQUEST_SUCCESS_UPDATE_MY_REVIEW,
                    bundleOf(RESULT_KEY_UPDATE_REVIEW to isSuccess)
                )
                findNavController().navigateUp()
            } else {
                Timber.d("sub`mitLiveData message $message")
            }
        }
        viewModel.accountBanLiveData.observe(viewLifecycleOwner) {
            binding.loading.hide()
            banBlockInteractor.showBanDialog(BanBlockInteractor.REASON_POST_REVIEW, this)
        }
        viewModel.toastLiveData.observe(viewLifecycleOwner) {
            toast(it)
        }
    }

    private fun getReviewType(): String {
        return if (viewModel.myReviewLiveData.value == null) GameReviewConstants.TYPE_WRITE else GameReviewConstants.TYPE_EDIT
    }

    override fun loadFirstData() {
        if (args.needFetchReview) {
            binding.loading.showLoading()
            args.gameId?.let { viewModel.fetchMyReviewData(it) }
        }
    }

    override fun onDestroyView() {
        binding.etGameReview.removeTextChangedListener(btnChangeListener)
        super.onDestroyView()
    }

    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_EDIT_GAME_REVIEW
}