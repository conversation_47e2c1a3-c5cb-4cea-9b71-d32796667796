package com.socialplay.gpark.ui.videofeed.comment

import android.annotation.SuppressLint
import android.app.Dialog
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.text.TextWatcher
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import androidx.core.os.bundleOf
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.navArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogBasicInputBinding
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.DeviceUtil
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.KeyboardHeightUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.ifNullOrEmpty
import com.socialplay.gpark.util.property.viewBinding
import timber.log.Timber
import java.util.Locale

class BasicInputDialog : BaseDialogFragment() {

    override var navColorRes = R.color.white

    override val binding by viewBinding(DialogBasicInputBinding::inflate)

    private val isHarmonyOs = DeviceUtil.isHarmonyOs()

    private val args: BasicInputDialogArgs by navArgs<BasicInputDialogArgs>()

    private var textWatcher: TextWatcher? = null
    private var isKeyBoardActive = false

    private val handler by lazy { Handler() }

    private var result: String? = null


    companion object {

        private const val KEY_RESULT = "key.result"

        fun show(
            fragment: Fragment,
            hint: String?,
            sendText: String,
            dimAmount: Float = 0f,
            callback: (String?) -> Unit = {}
        ) {

            val request = KEY_RESULT

            fragment.childFragmentManager.setFragmentResultListener(
                request,
                fragment.viewLifecycleOwner
            ) { _, bundle ->
                fragment.childFragmentManager.clearFragmentResultListener(request)
                callback(bundle.getString(request))
            }

            val dialog = BasicInputDialog()
            val dialogArgs = BasicInputDialogArgs(
                hint = hint,
                sendText = sendText,
                requestKey = request,
                dimAmount = dimAmount
            )
            dialog.arguments = dialogArgs.toBundle()
            dialog.show(fragment.childFragmentManager, request)
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return super.onCreateDialog(savedInstanceState).apply {
            // 默认情况下不显示软键盘，防止显示Emoji的时候也显示了软键盘会导致页面被顶高闪烁
            window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN)

            // 设置为全屏，解决部分手机闪烁的问题
            window?.setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN
            )
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun init() {
        enableSend(false)

        textWatcher = binding.etInputMessage.doAfterTextChanged {
            enableSend(!it.isNullOrBlank())
        }
        binding.tvSend.setOnAntiViolenceClickListener {
            sendMessage()
        }

        binding.etInputMessage.setOnTouchListener { view, motionEvent ->
            onEditTextTouch(view, motionEvent)
        }
        initView()
        showInput()
    }

    private fun initView() {
        binding.tvSend.text = args.sendText.ifNullOrEmpty { getString(R.string.send) }
        binding.etInputMessage.hint = args.hint.ifNullOrEmpty { getString(R.string.article_comment_hint) }
    }

    private fun sendMessage() {
        if (binding.etInputMessage.text.isNullOrEmpty()) {
            return
        }
        enableSend(false)
        val text: String = binding.etInputMessage.text.toString()
        binding.etInputMessage.setText("")
        result = text
        dismissAllowingStateLoss()
    }

    private fun enableSend(enable: Boolean) {
        if (enable) {
            binding.tvSend.isEnabled = true
            binding.tvSend.alpha = 1F
        } else {
            binding.tvSend.isEnabled = false
            binding.tvSend.alpha = 0.3F
        }
    }

    private fun isKeyBoardActive(): Boolean {
        return isKeyBoardActive
    }

    /**
     * 隐藏软件盘
     */
    private fun hideInputKeyBoard() {
        InputUtil.hideKeyboard(binding.etInputMessage)
        binding.etInputMessage.clearFocus()
        isKeyBoardActive = false
    }

    //显示软键盘
    private fun showInputKeyBoard() {
        this.binding.etInputMessage.requestFocusFromTouch()
        InputUtil.showSoftBoard(this.binding.etInputMessage)
        isKeyBoardActive = true
    }

    override fun loadFirstData() {

    }

    private fun onEditTextTouch(v: View?, event: MotionEvent): Boolean {
        if (0 == event.action) {
            requestInput()
        }
        return false
    }

    private fun requestInput() {
        if (Build.BRAND.lowercase(Locale.getDefault()).contains("meizu")) {
            binding.etInputMessage.requestFocusFromTouch()
            isKeyBoardActive = true
        } else {
            showInputKeyBoard()
        }
    }

    override fun onResume() {
        super.onResume()

        dialog?.window?.apply {
            // 修改状态栏颜色的方法里面会设置此Flag
            // 会导致部分机型(Oppo R11 Pluskt 8.1)输入框无法被顶上来
            clearFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        }

        KeyboardHeightUtil.registerKeyboardHeightListener(requireActivity()) {
            Timber.d("KeyboardHeightUtil $it")
            if (isHarmonyOs) {
                binding.root.setPaddingEx(
                    bottom = if (it > 200) {
                        if (ScreenUtil.getNavigationBarHeightIfRoom(requireActivity()) > 0) it - 36.dp else it
                    } else {
                        0
                    }
                )
            }
        }
    }

    private fun showInput() {
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
        showInputKeyBoard()
    }

    override fun onPause() {
        if (isHarmonyOs) {
            KeyboardHeightUtil.unregisterKeyboardHeightListener(requireActivity())
        }
        if (isHarmonyOs) {
            binding.root.setPaddingEx(bottom = 0)
        } else {
            binding.root.translationY = 0F
        }
        InputUtil.hideKeyboard(binding.etInputMessage)
        super.onPause()
    }

    override fun getStyle(): Int {
        return if (isHarmonyOs) R.style.CustomDialog_Input_HarmonyOs_Always else R.style.CustomDialog_Input_Always
    }

    override fun dimAmount(): Float {
        return args.dimAmount
    }


    override fun onDestroyView() {
        if (textWatcher != null) {
            binding.etInputMessage.removeTextChangedListener(textWatcher)
            textWatcher = null
        }
        super.onDestroyView()
    }

    override fun onDestroy() {
        setFragmentResult(args.requestKey, bundleOf(args.requestKey to result))
        handler.removeCallbacksAndMessages(null)
        super.onDestroy()
    }


}