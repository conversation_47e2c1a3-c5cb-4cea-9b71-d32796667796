package com.socialplay.gpark.ui.gamedetail.unify

import android.app.Application
import android.content.ComponentCallbacks
import android.os.SystemClock
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.bin.cpbus.CpEventBus
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.ApiDataException
import com.socialplay.gpark.data.base.PagingApiResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.account.ClearRedDotEvent
import com.socialplay.gpark.data.model.account.UserFollowEvent
import com.socialplay.gpark.data.model.community.PostCommentContent
import com.socialplay.gpark.data.model.editor.UgcDetailInfo
import com.socialplay.gpark.data.model.game.OperationInfo
import com.socialplay.gpark.data.model.game.UpdateGameLikeEvent
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostCommentListRequestBody
import com.socialplay.gpark.data.model.post.PostCommentRequestBody
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.data.model.post.PostReplyListRequestBody
import com.socialplay.gpark.data.model.post.PostReplyRequestBody
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.editor.detail.commentlist.BaseCommentListViewModel
import com.socialplay.gpark.util.ToastData
import com.socialplay.gpark.util.extension.collectIn
import com.socialplay.gpark.util.extension.registerHermes
import com.socialplay.gpark.util.extension.unregisterHermes
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.koin.android.ext.android.get

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/22
 *     desc   :
 * </pre>
 */
data class UgcGameDetailState(
    val ugcId: String,
    val parentId: String?,
    val targetCommentId: String?,
    val targetReplyId: String?,
    val showCommentPinRedDot: Boolean,
    val detail: Async<UgcDetailInfo> = Uninitialized,
    val likeStatus: Pair<Boolean, Long> = true to -1L,
    val shareCount: Long = 0L,
    val commentList: Async<PagingApiResult<PostComment>> = Uninitialized,
    val commentListLoadMore: Async<LoadMoreState> = Uninitialized,
    val page: Int = 1,
    val addCommentResult: Async<PostComment> = Uninitialized,
    val addReplyResult: Async<PostReply> = Uninitialized,
    val toast: ToastData = ToastData.EMPTY,
    val follow: Boolean = false,
    val operationList: Async<List<OperationInfo>> = Uninitialized,
    val sortType: Int = PostCommentListRequestBody.QUERY_TYPE_DEFAULT,
    val filterType: Int? = null,
    val isCommentListRefresh: Async<Boolean> = Uninitialized,
    val uniqueTag: Int = 0
) : MavericksState

class UgcGameDetailViewModel(
    initialState: UgcGameDetailState,
    context: Application,
    repo: IMetaRepository,
    accountInteractor: AccountInteractor,
) : BaseCommentListViewModel<UgcGameDetailState>(initialState, context, repo, accountInteractor) {

    val requestKey = "UgcDetailHalfViewModel_${SystemClock.elapsedRealtime()}"

    val detail get() = oldState.detail()
    val parentId: String
        get() = oldState.let { it.detail()?.availableGameCode ?: it.parentId }.orEmpty()
    val isLike: Boolean
        get() = oldState.likeStatus.first
    val replyTargetName get() = detail?.userName

    override val oldListResult: PagingApiResult<PostComment>?
        get() = oldState.commentList()
    override val authorId: String?
        get() = detail?.userUuid
    override val moduleContentId: String
        get() = oldState.ugcId
    override val pageType: Long = 1L
    override val contentType: Int = CONTENT_TYPE_UGC_GAME_COMMENT

    override val moduleContentType: Int = MODULE_UGC

    override val UgcGameDetailState.oldListResult: PagingApiResult<PostComment>?
        get() = commentList()

    init {
        registerHermes()
        getUgcDetailInfo(true)
        getOperationList()
    }

    fun initCommentList() = withState { s ->
        if (s.commentListLoadMore is Uninitialized) {
            getCommentList(true, s.targetCommentId, s.targetReplyId)
        }
    }

    fun getUgcDetailInfo(isRefresh:Boolean) = withState { s ->
        if (!isRefresh && !s.detail.shouldLoad) return@withState
        setState {
            copy(detail = Loading())
        }
        repo.getUgcDetailPage(s.ugcId).execute { result ->
            if (result is Success) {
                val newDetail = result.invoke()
                copy(
                    detail = result,
                    parentId = newDetail.availableGameCode ?: parentId,
                    likeStatus = newDetail.likeIt to newDetail.loveQuantity,
                    follow = newDetail.followUser,
                    shareCount = newDetail.shareCount
                )
            } else {
                copy(detail = result)
            }
        }
    }

    fun like() = withState { s ->
        val (isLike, likeCount) = s.likeStatus
        val newIsLike = !isLike
        val newLikeCount: Long
        if (newIsLike) {
            newLikeCount = likeCount + 1
            repo.mgsSceneLike(s.ugcId)
        } else {
            newLikeCount = (likeCount - 1).coerceAtLeast(0)
            repo.mgsSceneUnLike(s.ugcId)
        }.collectIn(viewModelScope) {
            if (it.succeeded && it.data == true) {
                CpEventBus.post(UpdateGameLikeEvent(s.ugcId, newLikeCount))
            }
        }
        setState { copy(likeStatus = newIsLike to newLikeCount) }
    }

    fun follow() = withState { s ->
        val uid = s.detail.invoke()?.userUuid ?: return@withState
        val toFollow = !s.follow
        Analytics.track(
            EventConstants.EVENT_FOLLOW_CLICK,
            "userid" to authorId.orEmpty(),
            "location" to "4",
            "type" to (if (toFollow) "1" else "2"),
            "creation_type" to "2"
        )
        if (toFollow) {
            repo.followUser(uid)
        } else {
            repo.unfollowUser(uid)
        }.execute { result ->
            when (result) {
                is Success -> {
                    EventBus.getDefault()
                        .post(UserFollowEvent(uid, toFollow, UserFollowEvent.FROM_GAME_DETAIL))

                    if (toFollow != follow) {
                        copy(follow = toFollow)
                    } else {
                        this
                    }
                }

                is Fail -> {
                    copy(toast = toast.toError(result))
                }

                else -> {
                    this
                }
            }
        }
    }

    fun follow(isFollow: Boolean) = withState { s ->
        if (s.follow != isFollow) {
            setState { copy(follow = isFollow) }
        }
    }

    fun shareCountIncrement() {
        setState {
            copy(shareCount = shareCount + 1)
        }
    }

    fun getCommentList(
        refresh: Boolean,
        targetCommentId: String? = null,
        targetReplyId: String? = null
    ) = withState { s ->
        if (s.commentListLoadMore is Loading) return@withState
        getCommentList(
            moduleContentId = s.ugcId,
            sortType = s.sortType,
            page = s.page,
            pageSize = 10,
            replySize = 0,
            replySortType = PostReplyListRequestBody.QUERY_LATEST,
            refresh = refresh,
            targetCommentId = targetCommentId,
            targetReplyId = targetReplyId,
            commentCollapse = false,
            commentReplyStatus = PostComment.REPLY_STATUS_INIT,
            withAuthorReply = true,
            authorReplySize = 1,
            filterType = s.filterType
        ) { result, targetPage ->
            when (result) {
                is Success -> {
                    copy(
                        commentList = result,
                        commentListLoadMore = Success(LoadMoreState(result().end)),
                        page = targetPage,
                        isCommentListRefresh = if (commentList is Success) {
                            Success(refresh)
                        } else {
                            isCommentListRefresh
                        },
                        uniqueTag = if (refresh) uniqueTag.xor(1) else uniqueTag
                    )
                }

                is Fail -> {
                    copy(
                        commentList = if (commentList is Success) {
                            commentList
                        } else {
                            Fail(result.error)
                        },
                        commentListLoadMore = Fail(result.error),
                        isCommentListRefresh = if (commentList is Success) {
                            Fail(result.error, refresh)
                        } else {
                            isCommentListRefresh
                        }
                    )
                }

                else -> {
                    copy(
                        commentList = if (commentList is Success) {
                            commentList
                        } else {
                            Loading()
                        },
                        commentListLoadMore = Loading(),
                        isCommentListRefresh = if (commentList is Success) {
                            Loading(refresh)
                        } else {
                            isCommentListRefresh
                        }
                    )
                }
            }
        }
    }

    fun addCommentViaNet(commentContent: PostCommentContent) = withState { s ->
        if (!commentContent.valid || s.addCommentResult is Loading) return@withState
        clearReplyTarget()
        val ts = System.currentTimeMillis()
        val requestBody = PostCommentRequestBody(
            commentContent.text,
            MODULE_UGC,
            s.ugcId,
            mediaList = commentContent.mediaList
        )
        val tempComment = requestBody.toPostComment(
            "",
            accountInteractor.accountLiveData.value,
            ts
        )
        repo.addPostComment(requestBody).map {
            Analytics.track(
                EventConstants.UGC_GAME_REVIEW_PUBLISH_SUCCESS,
                "gameid" to s.ugcId,
                "reviewtype" to 0
            )
            Analytics.track(
                EventConstants.GAME_REVIEW_REPLIES_SUCCESS,
                "type" to 1,
                "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_UGC,
                "gameid" to moduleContentId
            )
            val result = tempComment.copy(commentId = it.data.orEmpty())
            if (result.commentId.isNotEmpty()) {
                addComment(result)
            }
            result to it.toastMsg
        }.execute { result ->
            when (result) {
                is Success -> {
                    val (data, toastMsg) = result()
                    copy(
                        addCommentResult = if (data.commentId.isNotEmpty()) {
                            Success(data)
                        } else {
                            Fail(ApiDataException(String::class))
                        },
                        toast = if (toastMsg.isNullOrEmpty()) {
                            toast
                        } else {
                            toast.toMsg(toastMsg)
                        }
                    )
                }

                is Fail -> {
                    copy(addCommentResult = Fail(result.error, tempComment))
                }

                else -> {
                    copy(addCommentResult = Loading())
                }
            }
        }
    }

    fun addReplyViaNet(replyContent: PostCommentContent) = withState { s ->
        if (!replyContent.valid || s.addCommentResult is Loading) return@withState
        val replyTarget = getReplyTarget() ?: return@withState
        clearReplyTarget()
        val ts = System.currentTimeMillis()
        val userInfo = accountInteractor.accountLiveData.value
        val requestBody = if (replyTarget.isTargetComment) {
            PostReplyRequestBody(
                replyContent.text,
                userInfo?.uuid.orEmpty(),
                replyTarget.asComment.commentId,
                mediaList = replyContent.mediaList
            )
        } else {
            val targetReply = replyTarget.asReply
            PostReplyRequestBody(
                content = replyContent.text,
                uid = userInfo?.uuid.orEmpty(),
                commentId = replyTarget.commentId.orEmpty(),
                replyUid = targetReply.uid,
                replyNickname = targetReply.nickname,
                replyContentId = targetReply.replyId,
                mediaList = replyContent.mediaList
            )
        }
        repo.addPostReply(requestBody).map {
            Analytics.track(
                EventConstants.UGC_GAME_REVIEW_PUBLISH_SUCCESS,
                "gameid" to s.ugcId,
                "reviewtype" to 1
            )
            Analytics.track(
                EventConstants.GAME_REVIEW_REPLIES_SUCCESS,
                "type" to 2,
                "creatortype" to BaseGameDetailCommonFragment.GAME_TYPE_UGC,
                "gameid" to moduleContentId
            )
            val result = requestBody.toPostReply(it, userInfo, ts)
            addReply(result to replyTarget)
            result
        }.execute {
            copy(addReplyResult = it)
        }
    }

    fun getOperationList() = withState { s ->
        if (s.operationList is Loading) return@withState
        repo.getGameDetailOperationInfo("game_detail", s.ugcId, 1, 3).map {
            it.dataList.take(3)
        }.execute {
            copy(operationList = it)
        }
    }

    fun updateSortType(sortType: Int) = withState { s ->
        if (s.commentListLoadMore is Loading) return@withState
        if (s.sortType != sortType || s.filterType != null) {
            setState { copy(sortType = sortType, filterType = null) }
        }
        getCommentList(true)
    }

    fun updateFilterType(filterType: Int) = withState { s ->
        if (s.commentListLoadMore is Loading) return@withState
        if (s.filterType != filterType) {
            setState {
                copy(
                    sortType = PostCommentListRequestBody.QUERY_TYPE_DEFAULT,
                    filterType = filterType
                )
            }
        }
        getCommentList(true)
    }

    override fun UgcGameDetailState.updateCommentList(
        result: PagingApiResult<PostComment>?,
        msg: Any?
    ): UgcGameDetailState {
        return if (result == null && msg == null) {
            this
        } else {
            copy(
                commentList = if (result != null) commentList.copyEx(result) else commentList,
                toast = if (msg != null) toast.tryToMsg(msg) else toast
            )
        }
    }

    override fun trackDelete(id: String, isMe: Boolean, type: Long) {
        Analytics.track(
            EventConstants.UGC_GAME_REVIEW_DELETE_SUCCESS,
            "gameid" to moduleContentId,
            "reviewtype" to type,
            "reviewid" to id,
            "deletetype" to if (isMe) 1L else 0L,
            "pagetype" to pageType
        )
    }

    override fun clearCommentPinRedDot() = withState { s ->
        super.clearCommentPinRedDot()
        if (!s.showCommentPinRedDot) return@withState
        setState { copy(showCommentPinRedDot = false) }
    }

    override fun invokeToast(resId: Int) = withState {
        setState { copy(toast = toast.toResMsg(resId)) }
    }

    @Subscribe
    fun onUserFollowEvent(event: UserFollowEvent) = withState { s ->
        if (isMe(event.uuid)
            || event.uuid != authorId
            || event.followStatus == s.follow
        ) return@withState
        setState { copy(follow = event.followStatus) }
    }

    @Subscribe
    fun onClearRedDotEvent(event: ClearRedDotEvent) = withState { s ->
        if (!event.isCommentPin || !s.showCommentPinRedDot) return@withState
        setState { copy(showCommentPinRedDot = false) }
    }

    override fun onCleared() {
        unregisterHermes()
        super.onCleared()
    }

    companion object : KoinViewModelFactory<UgcGameDetailViewModel, UgcGameDetailState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: UgcGameDetailState
        ): UgcGameDetailViewModel {
            return UgcGameDetailViewModel(state, get(), get(), get())
        }

        override fun ComponentCallbacks.initialState(viewModelContext: ViewModelContext): UgcGameDetailState {
            val args = viewModelContext.args as UgcGameDetailFragmentArgs
            val accountInteractor: AccountInteractor = get()

            return UgcGameDetailState(
                args.ugcId,
                args.parentId,
                args.targetCommentId,
                args.targetReplyId,
                accountInteractor.showCommentPinRedDot
            )
        }
    }
}