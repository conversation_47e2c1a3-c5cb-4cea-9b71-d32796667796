package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Color
import android.text.Editable
import android.text.Spannable
import android.text.style.BackgroundColorSpan
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.View
import androidx.core.text.getSpans
import androidx.lifecycle.LifecycleOwner
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.PostPublish
import com.socialplay.gpark.data.model.post.TopicBean
import com.socialplay.gpark.ui.post.v2.PublishPostFragment
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.extension.addTextChangedListener
import com.socialplay.gpark.util.extension.setOnKeyListener
import timber.log.Timber
import java.util.regex.Pattern


/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/04/08
 *     desc   : 话题输入框
 *
 */
class REditText :androidx.appcompat.widget.AppCompatEditText {
    // 默认,话题文本高亮颜色

    private val FOREGROUND_COLOR = Color.parseColor("#4AB4FF")
    // 默认,话题背景高亮颜色
    private val BACKGROUND_COLOR = Color.parseColor("#FFDEAD")

    private var pattern: Pattern? = null
    private var containPatten: Pattern? = null
    private var mListener: ReEditTextListener? = null

    /**
     * 开发者可设置内容
     */
    private var  mForegroundColor = FOREGROUND_COLOR;// 话题文本高亮颜色
    private var mBackgroundColor = BACKGROUND_COLOR;// 话题背景高亮颜色
    private var textColor = Color.BLACK
    //话题合集
    private var mTopicList = ArrayList<TopicBean>()

    private var preTextLength = 0

    //超出限制部分文字，起始位置
    private var txtChangeStart = 0
    //是否有超出限制部分文字
    private var hasExceedCharSpan = false
    // 忽略选中位置
    private var ignoreChange = false
    // 刻意选中位置
    private var replaceChange = false
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs){
        val  a = context.obtainStyledAttributes(attrs, R.styleable.REditText)
        mBackgroundColor = a
            .getColor(
                R.styleable.REditText_edit_background_color,
                BACKGROUND_COLOR
            )
        mForegroundColor = a
            .getColor(
                R.styleable.REditText_edit_foreground_color,
                FOREGROUND_COLOR
            )
        textColor = a
            .getColor(
                R.styleable.REditText_edit_text_color,
                Color.BLACK
            )
        a.recycle()
        // 初始化设置
        initView()

    }
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    override fun onSelectionChanged(selStart: Int, selEnd: Int) {
        super.onSelectionChanged(selStart, selEnd)
        if (ignoreChange) {
            ignoreChange = false
            return
        }
        if (selStart == selEnd && (selEnd >= 1 && this.text.toString().substring(selEnd - 1, selEnd) == PublishPostFragment.TAG_RULE)|| (this.text.toString() == PublishPostFragment.TAG_RULE)
        ) {
            //当前输入的是一个#
            Timber.d("onSelectionChanged: input# selStart:$selStart selEnd:$selEnd")
            mListener?.onTopicEnter(PublishPostFragment.TAG_RULE)
            return
        }
        if (selStart != selEnd) {
            return
        }
        val data = mTopicList?.find { it.index < selStart && selStart <= (it.localEnd) }
        if (data != null) {
            Timber.d("onSelectionChanged: content" + data.tagName + "selStart:$selStart selEnd:$selEnd\" + \"replaceChange$replaceChange\"")
            if (!replaceChange) {
                mListener?.onTopicEnter(data.tagName)
            }
        } else {
            if (replaceChange && selEnd == 0) {
                replaceChange = false
            }
            mListener?.onTopicRemove()
            Timber.d("onSelectionChanged: remove selStart:$selStart selEnd:$selEnd")
        }
    }
    fun initView() {
        pattern = Pattern.compile(context.getString(R.string.topic_pattern))
        containPatten = Pattern.compile(context.getString(R.string.contain_topic_pattern))
    }
    fun onKeyListener(viewLifecycleOwner: LifecycleOwner) {
        this.setOnKeyListener(viewLifecycleOwner, object :OnKeyListener{
            override fun onKey(v: View?, keyCode: Int, event: KeyEvent?): Boolean {
                if (keyCode == KeyEvent.KEYCODE_DEL && event?.action == KeyEvent.ACTION_DOWN) {
                    val selectionStart = <EMAIL>
                    if (selectionStart < 1) {
                        return false
                    }
                    /**
                     * 如果光标起始位置是话题规则，删除话题
                     */
                    val start = (selectionStart - 1)
                    val indexSting = text.toString().substring(start, selectionStart)
                    if (indexSting == PublishPostFragment.TAG_RULE) {
                        val data = mTopicList.find { it.index == start }?:return false
                        mTopicList.remove(data)
                        val subString = text.toString().substring(data.index, data.localEnd)
                        if (subString == data.tagName) {
                            val editable = <EMAIL>
                            val colorSpan = ForegroundColorSpan(textColor)
                            editable.setSpan(colorSpan, data.index, data.localEnd, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                        }
                        return false
                    }
                }
                return false
            }
        })

    }


    fun onTextChangedListener(viewLifecycleOwner: LifecycleOwner, callback: (String?) -> Unit) {
        this.addTextChangedListener(
            viewLifecycleOwner,
            onTextChanged = { _, start, _, _ ->
                txtChangeStart = start
            }, afterTextChanged = {
                it?.let { text ->
                    if (hasExceedCharSpan) {
                        val oldSpans = text.getSpans<ExceedCharSpan>(
                            txtChangeStart.coerceAtMost(text.length)
                        )
                        var toDelSpanSize = oldSpans.size
                        for (oldSpan in oldSpans) {
                            runCatching {
                                text.removeSpan(oldSpan)
                                toDelSpanSize--
                            }
                        }
                        hasExceedCharSpan = toDelSpanSize > 0
                    }
                    if (text.length > PostPublish.MAX_LETTER_COUNT) {
                        runCatching {
                            text.setSpan(
                                ExceedCharSpan(),
                                PostPublish.MAX_LETTER_COUNT,
                                text.length,
                                Spannable.SPAN_INCLUSIVE_EXCLUSIVE
                            )
                        }
                        hasExceedCharSpan = true
                    }
                    //赋值话题列表数据
                    removeTopic(text)
                    addTopicList(text)
                    refreshEditTextUI(text.toString())
                }

                callback.invoke(it?.toString())
            })

    }

    /**
     * 字符变化后，可能导致当前话题不符合规则，需要删除
     */
    private fun removeTopic(text: Editable){
        val selectionStart = this.selectionStart
        val data = mTopicList.find { it.index <= selectionStart && selectionStart <= it.localEnd }
        if (preTextLength < text.length && data != null) {
            //当前新增的字符
            val indexSubstring = text.substring(txtChangeStart, txtChangeStart + 1)
            //如果是特殊字符，删除话题
            val matcher = containPatten?.matcher(indexSubstring)
            if (matcher?.find() != true) {
                if (txtChangeStart > 0) {
                    val subString = text.substring(txtChangeStart - 1, txtChangeStart)
                    var start = txtChangeStart
                    val end = data.localEnd + 1
                    if (subString == PublishPostFragment.TAG_RULE) {
                        start = txtChangeStart - 1
                    }
                    //不在话题规则内，删除话题
                    val editable = <EMAIL>
                    val colorSpan = ForegroundColorSpan(textColor)
                    editable.setSpan(colorSpan, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                }
            }
        }
    }
    private fun refreshEditTextUI(content: String) {
        /*
         * 重新设置span
         */
        if (mTopicList == null || mTopicList.size == 0) {
            return
        }
        if (content.isNullOrEmpty()) {
            mTopicList.clear()
            return
        }
        val editable = this.editableText
        val textLength = editable.length
        mTopicList.forEach {
            val objectText = it.tagName
            if (it.localEnd <= textLength && it.index >= 0 && textLength > 0) {
                val subString = content.substring(it.index, it.localEnd)
                if (subString == objectText) {
                    val colorSpan = ForegroundColorSpan(mForegroundColor)
                    editable.setSpan(
                        colorSpan,
                        it.index,
                        it.localEnd,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
            }
        }

    }

    private fun addTopicList(text: Editable) {
        val length = text.toString().length
        val editable = this.editableText
        val matcher = pattern?.matcher(editable) ?: return
        Timber.d("onTextChangedListener: ${text.toString()}")
        //剩下的已经完成的话题
        mTopicList.clear()
        while (matcher.find()) {
            val start = matcher.start()
            val end = matcher.end()
            val tagText = editable.toString().substring(matcher.start(), matcher.end()).trim()
            val topicBean = TopicBean()
            topicBean.index = start
            topicBean.localEnd = end
            topicBean.tagName = tagText
            mTopicList.add(topicBean)
        }
        preTextLength = length
    }

    /**
     * 替换一个话题
     */
    fun setReplaceTopic(text: String) {
        var selectionStart = this.selectionStart
        if (selectionStart == -1) {
            selectionStart = 0
        }
        val originText = this.text.toString()
        val originTopic = mTopicList.find { it.index < selectionStart && selectionStart <= it.localEnd  }
        if (originTopic != null) {
            //原有话题替换
            this.editableText.replace(originTopic.index, originTopic.localEnd, text)
            replaceSelection(0)
        } else {
            //非话题替换
            val subString = originText.substring(0, selectionStart)
            val index = subString.lastIndexOf(PublishPostFragment.TAG_RULE)
            if (index == -1) {
                return
            }
            this.editableText.replace(index, selectionStart, text)
            replaceSelection(0)

        }

    }

    /**
     * 结束话题输入
     */
    fun setTopicComplete(): Boolean {
        var selectionStart = this.selectionStart
        if (selectionStart == -1) {
            selectionStart = 0
        }
        val originText = this.text.toString()
        val subString = originText.substring(0, selectionStart)
        val index = subString.lastIndexOf(PublishPostFragment.TAG_RULE)
        if(index == -1){
            return false
        }
        val text = this.editableText.substring(index, selectionStart)
        if (text == PublishPostFragment.TAG_RULE) {
            return false
        }
        replaceChange = true
        this.editableText.replace(index, selectionStart, "$text ")
        replaceSelection(0)
        return true
    }
    fun addTopic(topic: String) {
        replaceChange = true
        this.editableText.append(topic)
        replaceChange = true
        addTopicList(this.editableText)
        refreshEditTextUI(this.editableText.toString())
        replaceSelection(0)
    }

    fun setOnReEditTextListener(listener: ReEditTextListener?){
        mListener = listener
    }

    fun getTopicList(): List<TopicBean> {
        return mTopicList
    }

    fun addStartTag(text: String){
        ignoreChange = true
        val selectPosition = this.selectionStart
        val editable = this.text
        if (selectPosition < 0) {
            return
        }
        editable?.insert(selectPosition, text)
        ignoreChange = true
        this.text = editable
        this.setSelection(selectPosition +1)
        InputUtil.showSoftBoard(this)
    }
    private fun replaceSelection(pos: Int) {
        return
        replaceChange = true
        this.setSelection(pos)
    }

    inner class ExceedCharSpan : BackgroundColorSpan(mBackgroundColor)

}
interface  ReEditTextListener{
    fun onTopicRemove()
    fun onTopicEnter(topic:String)

}