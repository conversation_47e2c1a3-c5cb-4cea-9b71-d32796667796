package com.socialplay.gpark.ui.main.maps

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import androidx.core.view.isInvisible
import androidx.fragment.app.Fragment
import androidx.viewpager2.widget.ViewPager2
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.FragmentHomeMapsBinding
import com.socialplay.gpark.databinding.TabIndicatorHomeMapsBinding
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.main.maps.newgame.NewGamesFragment
import com.socialplay.gpark.ui.recommend.RecommendFragment
import com.socialplay.gpark.ui.view.viewpager.CommonTabStateAdapter
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.extension.collectWithLifecycleOwner
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTouchSlop
import com.socialplay.gpark.util.extension.visible
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import kotlin.getValue

class HomeMapsFragment : BaseFragment<FragmentHomeMapsBinding>(R.layout.fragment_home_maps) {

    private val viewModel: HomeMapsViewModel by fragmentViewModel()
    private val mainViewModel: MainViewModel by sharedViewModel()
    private var tabLayoutMediator: TabLayoutMediator? = null
    private lateinit var pagerAdapter: CommonTabStateAdapter
    private val tabCallback = object : TabLayout.OnTabSelectedListener {
        override fun onTabSelected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, true)
        }

        override fun onTabUnselected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, false)
        }

        override fun onTabReselected(tab: TabLayout.Tab) {}
    }

    private val vpCallback = object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            viewModel.changeSelectedTag(position)
        }
    }

    private fun setTabSelectUI(tab: TabLayout.Tab, select: Boolean) {
        val cv = tab.customView ?: return
        val tabBinding = TabIndicatorHomeMapsBinding.bind(cv)
        tabBinding.tvNormal.isInvisible = select
        tabBinding.tvSelected.isInvisible = !select

        updateTabStyle()
    }

    private fun updateTabStyle() {
        val selectedTabPosition = binding.tabLayout.selectedTabPosition

        val selectedTab = binding.tabLayout.getTabAt(selectedTabPosition)
        val selectedTagType = selectedTab?.tag as? HomeMapsTab

        for (i in 0 until binding.tabLayout.tabCount) {
            val tab = binding.tabLayout.getTabAt(i)
            val tagType = tab?.tag as? HomeMapsTab
            val customView = tab?.customView

            if (customView != null && tagType != null) {
                val binding = TabIndicatorHomeMapsBinding.bind(customView)

                if (i != selectedTabPosition && selectedTagType?.otherTagTextColorWhenSelected != null) {
                    binding.tvNormal.setTextColor(selectedTagType.otherTagTextColorWhenSelected)
                } else {
                    binding.tvNormal.setTextColor(tagType.normalTextColor)
                }

                val textShadow = selectedTagType?.textShadowWhenSelected
                if (textShadow != null) {
                    binding.tvNormal.setShadowLayer(
                        textShadow.radius,
                        textShadow.dx,
                        textShadow.dy,
                        textShadow.color
                    )
                    binding.tvSelected.setShadowLayer(
                        textShadow.radius,
                        textShadow.dx,
                        textShadow.dy,
                        textShadow.color
                    )
                } else {
                    binding.tvNormal.setShadowLayer(0F, 0F, 0F, 0)
                    binding.tvSelected.setShadowLayer(0F, 0F, 0F, 0)
                }
            }
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentHomeMapsBinding? {
        return FragmentHomeMapsBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initViewPager()
        initData()
    }

    private fun initViewPager() {
        withState(viewModel) {

            val vc = ViewConfiguration.get(requireContext())
            binding.viewpagerTabs.setTouchSlop(vc.scaledPagingTouchSlop * 3)

            binding.viewpagerTabs.offscreenPageLimit = 1
            binding.tabLayout.addOnTabSelectedListener(tabCallback)
            binding.viewpagerTabs.registerOnPageChangeCallback(vpCallback)
            pagerAdapter = CommonTabStateAdapter(
                it.tabs.map { type ->
                    { createTabFragment(type) }
                },
                childFragmentManager,
                viewLifecycleOwner.lifecycle
            )
            binding.viewpagerTabs.adapterAllowStateLoss = pagerAdapter
            tabLayoutMediator = TabLayoutMediator(
                binding.tabLayout,
                binding.viewpagerTabs
            ) { tab: TabLayout.Tab, position: Int ->
                withState(viewModel) {
                    val tabBinding = TabIndicatorHomeMapsBinding.inflate(layoutInflater)
                    val name = kotlin.runCatching { it.blockNameList[position] }.getOrNull()
                    tabBinding.tvNormal.text = name
                    tabBinding.tvSelected.text = name

                    val type = it.tabs[position]

                    tabBinding.tvSelected.setTextColor(type.selectedTextColor)
                    tabBinding.tvNormal.setTextColor(type.normalTextColor)

                    tab.customView = tabBinding.root
                    tab.tag = type
                }
            }
            tabLayoutMediator?.attach()
        }
    }


    private fun createTabFragment(type: HomeMapsTab): Fragment {
        return when (type) {
            is HomeMapsTab.MapsTab -> {
                // 地图
                RecommendFragment()
            }

            is HomeMapsTab.NewestTab -> {
                // 新游
                NewGamesFragment()
            }
        }
    }

    private fun initData() {

        viewModel.onEach(HomeMapsModelState::tabs, deliveryMode = uniqueOnly()) {
            val tabs = it.map { type ->
                { createTabFragment(type) }
            }
            pagerAdapter.fragmentCreators = tabs
            pagerAdapter.notifyDataSetChanged()

            if (tabs.size == 1) {
                // 藏起来indicator
                binding.tabLayout.setSelectedTabIndicator(null)
            }
        }
        viewModel.registerToast(HomeMapsModelState::toastData)
        viewModel.onEach(HomeMapsModelState::selectedTag) {
            viewModel.oldState.tabs.getOrNull(it)?.let { type ->
                binding.viewpagerTabs.isUserInputEnabled = type.isNestedScrollEnabled
            }

            if (binding.viewpagerTabs.currentItem != it && it in 0 until pagerAdapter.itemCount) {
                binding.viewpagerTabs.setCurrentItem(it, false)
            }
        }
        mainViewModel.msgUnReadCountFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
            binding.vMsgRedDot.visible(it > 0)
        }
    }

    private fun initView() {

        binding.ivSearch.setOnAntiViolenceClickListener {
            MetaRouter.Search.navigate(this)
        }
        binding.ivSearch.visible(PandoraToggle.isSearchOpen)

        binding.ivMsg.setOnAntiViolenceClickListener {
            MetaRouter.IM.goChatTabFragment(
                this,
                source = EventParamConstants.SRC_MESSAGE_LIST_ENTRANCE_MAPS
            )
        }
    }

    override fun invalidate() {

    }

    override fun onDestroyView() {
        tabLayoutMediator?.detach()
        tabLayoutMediator = null
        binding.tabLayout.clearOnTabSelectedListeners()
        binding.viewpagerTabs.adapterAllowStateLoss = null
        binding.viewpagerTabs.unregisterOnPageChangeCallback(vpCallback)
        super.onDestroyView()
    }

    override fun getPageName(): String {
        return PageNameConstants.FRAGMENT_MAPS
    }
}