package com.socialplay.gpark.ui.editor.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.meta.biz.ugc.model.GameTransform
import com.socialplay.gpark.data.model.editor.AvatarData
import com.socialplay.gpark.function.editor.EditorGameInteractHelper
import com.socialplay.gpark.function.editor.RoleGameTryOn
import com.socialplay.gpark.function.editor.onTsGameTransform
import com.socialplay.gpark.function.mw.lifecycle.AvatarGameTime
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.shareIn

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2023/12/19
 *     desc   :
 */
class HomeFullAvatarViewModel(

) : ViewModel() {


    private val _fullAvatarStatusFlow = MutableStateFlow(AvatarData(false,null, null))
    val fullAvatarStatusFlow: StateFlow<AvatarData> get() = _fullAvatarStatusFlow

    val fullAvatarStatusEvent: Flow<AvatarData> = _fullAvatarStatusFlow.shareIn(viewModelScope, SharingStarted.Eagerly)
    private var fromDress = false

    private val onTransform = onTsGameTransform { status, _ ->
        if (status == GameTransform.STATUS_ROLE_VIEW) {
            exitFullAvatar()
        }
    }

    init {
        EditorGameInteractHelper.registerOnGameTransform(onTransform)
    }

    override fun onCleared() {
        //移除TS游戏监听
        EditorGameInteractHelper.removeOnGameTransform(onTransform)
        super.onCleared()
    }

    /**
     * 进入全屏编辑器模式
     * @param opacityData 传递给游戏的透传数据
     */
    fun enterFullAvatar(
        opacityData: String? = null,
        fromDress: Boolean = false,
        tryOnData: RoleGameTryOn? = null
    ) {
        this.fromDress = fromDress
        if (fromDress) {
            AvatarGameTime.start(true)
        }
        _fullAvatarStatusFlow.value = AvatarData(true, opacityData, tryOnData)
    }

    fun exitFullAvatar() {
        _fullAvatarStatusFlow.value = AvatarData(false, null, null)
        AvatarGameTime.stop(true)
    }

    fun isFullAvatar(): Boolean {
        return _fullAvatarStatusFlow.value.isFullMode
    }
}