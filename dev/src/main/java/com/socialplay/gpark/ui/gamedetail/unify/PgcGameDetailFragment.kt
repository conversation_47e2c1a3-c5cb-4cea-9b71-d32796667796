package com.socialplay.gpark.ui.gamedetail.unify

import android.graphics.Rect
import android.os.Bundle
import android.os.Parcelable
import android.view.View
import androidx.core.view.doOnNextLayout
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.meta.box.biz.friend.model.LabelInfo
import com.meta.lib.mwbiz.MWBiz
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.SnsInfo
import com.socialplay.gpark.data.model.community.IPostCommentDraftProvider
import com.socialplay.gpark.data.model.community.PostCommentDraft
import com.socialplay.gpark.data.model.community.PostMedia
import com.socialplay.gpark.data.model.game.OperationInfo
import com.socialplay.gpark.data.model.post.AddPostCommentReplyTarget
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.data.model.post.ReviewOpinion
import com.socialplay.gpark.data.model.reportBlock.ReportType
import com.socialplay.gpark.data.model.share.ShareFeature
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.apm.apiStart
import com.socialplay.gpark.function.mw.MWGameStartScenes
import com.socialplay.gpark.function.mw.launch.OnDownloadListener
import com.socialplay.gpark.function.mw.launch.TSLaunch
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.mw.launch.ui.TSLaunchFailedWrapper
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.gamedetail.GameDetailAnalyticsObserver
import com.socialplay.gpark.ui.gamedetail.GameDetailViewModel
import com.socialplay.gpark.ui.gamedetail.cover.DetailVideoAnalytics
import com.socialplay.gpark.ui.gamereview.GameReviewViewModel
import com.socialplay.gpark.ui.gamereview.ReviewListFragment
import com.socialplay.gpark.ui.gamereview.dialog.GameAppraiseAdapter
import com.socialplay.gpark.ui.imgpre.ImgPreDialogFragment
import com.socialplay.gpark.ui.post.comment.ArticleCommentInputDialog
import com.socialplay.gpark.ui.post.comment.ArticleCommentInputDialogParams
import com.socialplay.gpark.ui.post.list.GameOpinionViewModel
import com.socialplay.gpark.ui.profile.BaseProfilePage
import com.socialplay.gpark.ui.reportBlock.ReportReasonDialog
import com.socialplay.gpark.ui.reportBlock.ReportSuccessDialogAnalyticsParams
import com.socialplay.gpark.ui.share.GlobalShareDialog
import com.socialplay.gpark.ui.view.DownloadProgressButton
import com.socialplay.gpark.ui.view.ExpandableTextView
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.util.BlurTransformation
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.DownloadProgressUtil
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.apiMonitor
import com.socialplay.gpark.util.extension.cancelAnimationIfAnimating
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setFragmentResultListenerByHostFragment
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import com.socialplay.gpark.util.simMsg
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/20
 *     desc   :
 * </pre>
 */
@Parcelize
data class PgcGameDetailFragmentArgs(
    val gId: String,
    val resIdBean: ResIdBean,
    val packageName: String,
    val type: String,
    val isComment: Boolean,
    val targetCommentId: String?,
    val expandPage: Boolean,
    val targetReplyId: String?,
    val fromGameId: String?,
    val fromPkgName: String?,
    val autoDownloadGame: Boolean
) : Parcelable

class PgcGameDetailFragment : BaseGameDetailCommonFragment(), IPostCommentDraftProvider {

    companion object {
        const val TRACK_TAG = "game_review"
    }

    override val gameId: String
        get() = args.gId

    override val gameType = GAME_TYPE_PGC

    override val enableShare: Boolean
        get() = PandoraToggle.enableSharePgc

    private val viewModel by viewModel<GameDetailViewModel>()
    private val gameReviewViewModel by viewModel<GameReviewViewModel>()
    private val gameOpinionViewModel by viewModel<GameOpinionViewModel>()

    private val vmV2: PgcGameDetailViewModel by fragmentViewModel()

    // 正在安装更新
    private val analyticsObserve: GameDetailAnalyticsObserver by lazy { GameDetailAnalyticsObserver() }
    val args by args<PgcGameDetailFragmentArgs>()

    private val gameStartScenes by lazy { MWGameStartScenes(this) }
    private val tsLaunch: TSLaunch by lazy { TSLaunch() }

    private var loadedDataOnce = false

    private var contentHeight = 0
    private var descExpandState = ExpandableTextView.STATE_SHRINK

    override val itemListener = ItemListener()

    private val operationController by lazy { buildOperationController() }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initView()
        initData()
        initTSGame()
    }

    private fun initTSGame() {
        //监听MW引擎下载
        tsLaunch.onDownloadListener(this, object : OnDownloadListener {
            override fun invoke(info: GameDetailInfo, percent: Float) {
                updateDownloadProgress(percent * 100, info.id)
            }
        })
        //监听TS游戏拉起状态
        tsLaunch.onLaunchListener(viewLifecycleOwner) {
            onLaunchPrepare {
                showLaunchingGameUI()
            }
            onLaunchGame {
                gameStartScenes.show()
            }
            onPauseDownload {
                showPauseDownloadUI()
            }
            onLaunchGameEnd { params, e ->
                showStartGameUI()
                if (e != null) gameStartScenes.hide()
                TSLaunchFailedWrapper.show(this@PgcGameDetailFragment, params, e)
                Analytics.track(EventConstants.EVENT_MW_CLICK_STATUS) {
                    put("status", if (e == null) "success" else "failed")
                    if (e != null) put("reason", e.simMsg() ?: "")
                    put("game_version_name", params.getGameVersionName())
                }
            }
        }

        DialogShowManager.triggerGameDetailScene(this, gameId)
//        UpdateDialog.showFromGameDetail(this, viewModel)
    }

    private fun initView() {
        if (contentHeight != 0) {
            binding.clTop.minHeight = contentHeight
        }

        if (!loadedDataOnce) {
            loadFirstData()
        }

        getBtnDownload().state = DownloadProgressButton.STATE_NORMAL
        getBtnDownload().setDownloadingText(getString(R.string.play))
        initEvent()

        binding.lv.showLoading()
        binding.ivMyAvatar.setOnAntiViolenceClickListener {
            showReplyDialog(AddPostCommentReplyTarget(args.gId, viewModel.replyTargetName))
        }
        binding.tvReplyHint.setOnAntiViolenceClickListener {
            showReplyDialog(AddPostCommentReplyTarget(args.gId, viewModel.replyTargetName))
        }
        binding.tvReplyHint2.setOnAntiViolenceClickListener {
            showReplyDialog(AddPostCommentReplyTarget(args.gId, viewModel.replyTargetName), source = 3)
        }
        binding.ivEmojiBtn.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.gId, viewModel.replyTargetName),
                showEmoji = true
            )
        }
        binding.ivEmojiBtn2.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.gId, viewModel.replyTargetName),
                showEmoji = true
            )
        }
        binding.ivImageBtn.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.gId, viewModel.replyTargetName),
                showImage = true
            )
        }
        binding.ivImageBtn2.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.gId, viewModel.replyTargetName),
                showImage = true
            )
        }
        binding.rvNotice.setController(operationController)
        binding.layerComment.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.gId, viewModel.replyTargetName),
                source = 2
            )
        }
    }

    private fun initData() {
        setFragmentResultListenerByHostFragment(
            BaseProfilePage.RESULT_FOLLOW,
            viewLifecycleOwner
        ) { _, bundle ->
            val uuid = bundle.getString(BaseProfilePage.KEY_UUID)
            if (viewModel.authorUuid == uuid) {
                viewModel.follow(bundle.getBoolean(BaseProfilePage.KEY_IS_FOLLOW))
            }
        }
        viewModel.gameDetailFlow.observe(viewLifecycleOwner) {
            it?.first ?: return@observe
            Timber.d("game detail info %s", it.first.data)
            vmV2.updateGameDetailState(it.first)
            it.first.data.let { data ->
                if (data != null && (NetUtil.isNetworkAvailable() || loadedDataOnce)) {
                    args.resIdBean.setGameVersionName(data.gameVersion())
                    updateView(data)
                    //预获取游戏启动数据
                    tsLaunch.preLoadLaunchParams(data)
                    loadedDataOnce = true
                    binding.lv.hide()
                    vmV2.init(data.author?.id)
                    vmV2.initCommentList()
                    sparkVm.init(
                        data.id,
                        data.author?.id.orEmpty(),
                        data.myTippedCoins,
                        data.totalTippedCoins,
                        data.myTippedTimes,
                        data.isCache
                    )
                } else {
                    showCdnErrorUI()
                    binding.lv.showError()
                    binding.ivMoreBtn.gone()
                }
            }
        }

        gameOpinionViewModel.likeInfoLiveData.observe(viewLifecycleOwner) {
            // 游戏点赞数
            updateLikeView(it)
        }

        gameOpinionViewModel.opinionFlow.observe(viewLifecycleOwner) {
            updateLikeIcon(it)
        }

        gameOpinionViewModel.errorCallback.observe(viewLifecycleOwner) {
            toast(it ?: getString(R.string.hut_wrong))
        }

        viewModel.toastLifeCallback.observe(viewLifecycleOwner) {
            toast(it)
        }

        viewModel.followLiveData.observe(viewLifecycleOwner) {
            updateFollow(it)
        }

        viewModel.accountInteractor.accountLiveData.observe(viewLifecycleOwner) {
            updateMyAvatar(it?.portrait)
        }

        vmV2.onEach(PgcGameDetailState::operationList) {
            if (it().isNullOrEmpty()) {
                binding.rvNotice.gone()
            } else {
                binding.rvNotice.visible()
            }
        }
        vmV2.onEach(
            PgcGameDetailState::commentList,
            PgcGameDetailState::commentListLoadMore
        ) { commentList, loadMore ->
            updateCommentStatus(commentList, loadMore)
        }
        vmV2.onEach(
            PgcGameDetailState::sortType,
            PgcGameDetailState::filterType
        ) { sortType, filterType ->
            updateSortTypeText(sortType, filterType)
        }
        vmV2.onEach(PgcGameDetailState::isCommentListRefresh, deliveryMode = uniqueOnly()) {
            if (it.invoke() == true) {
                when (it) {
                    is Loading -> {
                        animateCommentRefresh(dp(52))
                    }

                    is Success -> {
                        animateCommentRefresh(0)
                    }

                    else -> {
                        animateCommentRefresh(0)
                    }
                }
            }
        }
        vmV2.setupRefreshLoading(
            PgcGameDetailState::isGameDetailRefresh,
            binding.lv,
            binding.mrl
        ) {
            vmV2.updateGameDetailState(null)
            loadFirstData(true)
            getCommentList(true)
        }
        vmV2.registerToast(PgcGameDetailState::toast)
        vmV2.registerAsyncErrorToast(PgcGameDetailState::commentListLoadMore)
        vmV2.registerAsyncErrorToast(PgcGameDetailState::addCommentResult)
        vmV2.registerAsyncErrorToast(PgcGameDetailState::addReplyResult)
    }

    private fun initEvent() {
        getBtnDownload().setOnAntiViolenceClickListener {
            clickStartGame()
        }
        binding.tvDescription.setExpandListener(object : ExpandableTextView.OnExpandListener {
            override fun onExpand(view: ExpandableTextView) {
                Analytics.track(
                    EventConstants.DETAIL_DESCRIPTION_MORE_CLICK,
                    "gameid" to gameId,
                    "creatortype" to gameType
                )
                descExpandState = ExpandableTextView.STATE_EXPAND
            }

            override fun onShrink(view: ExpandableTextView) {
                descExpandState = ExpandableTextView.STATE_SHRINK
            }
        })
        binding.layerLike.setOnAntiViolenceClickListener {
            val opinion = if (gameOpinionViewModel.opinionFlow.value == ReviewOpinion.IS_LIKE) {
                Analytics.track(
                    EventConstants.GAME_DETAIL_PAGE_LIKE_CANCEL,
                    "gameid" to gameId,
                    "creatortype" to gameType
                )
                ReviewOpinion.IS_NONE
            } else {
                Analytics.track(
                    EventConstants.GAME_DETAIL_PAGE_LIKE,
                    "gameid" to gameId,
                    "creatortype" to gameType
                )
                DialogShowManager.triggerLike(this)
                ReviewOpinion.IS_LIKE
            }
            updateLikeIcon(opinion)
            gameOpinionViewModel.onClickLike(args.gId, opinion)
        }
        binding.ulv.setListener(viewLifecycleOwner) {
            UserLabelView.showDescDialog(this, it)
        }
    }

    private fun updateLikeIcon(opinion: Int) {
        if (opinion == ReviewOpinion.IS_LIKE) {
            binding.tvLike.setTextColor(getColorByRes(R.color.color_4AB4FF))
            binding.ivLike.invisible()
            binding.lavLikeAnim.visible()
            if (!likeAnim) {
                binding.lavLikeAnim.progress = 1.0f
            } else if (!binding.lavLikeAnim.isAnimating && binding.lavLikeAnim.progress == 0.0f) {
                binding.lavLikeAnim.playAnimation()
            }
        } else {
            binding.tvLike.setTextColor(getColorByRes(R.color.color_1A1A1A))
            binding.ivLike.visible()
            binding.lavLikeAnim.cancelAnimationIfAnimating()
            binding.lavLikeAnim.progress = 0.0f
            binding.lavLikeAnim.gone()
        }
        if (opinion != -1) {
            likeAnim = true
        }
    }

    private fun clickStartGame() {
        if (getBtnDownload().state == DownloadProgressButton.STATE_UNAVAILABLE) {
            return
        }
        val info = viewModel.getCurrentGameInfo()
        if (info != null && info.isTSGame()) {
            val resIdBean = args.resIdBean.setClickGameTime(System.currentTimeMillis())
            if (tsLaunch.isLaunching(info.id)) {
                //启动中
                Analytics.track(EventConstants.EVENT_TS_DETAIL_LAUNCHING) {
                    putAll(ResIdUtils.getAnalyticsMap(resIdBean))
                    put("game_type", "ts")
                    put("gameid", info.id)
                    put("packagename", info.packageName)
                }
                toast(R.string.launching)
            } else {
                val clickType = if (MWBiz.isAvailable()) {
                    GameDetailAnalyticsObserver.CLICK_TYPE_START_GAME
                } else {
                    GameDetailAnalyticsObserver.CLICK_TYPE_START_DOWNLOAD
                }
                analyticsObserve.sendClickDownloadAnalytic(info, clickType, resIdBean)
                gameReviewViewModel.metaKV.analytic.saveClickLaunchTime(
                    info.packageName,
                    System.currentTimeMillis()
                )
                val params = TSLaunchParams(info, resIdBean)
                tsLaunch.launch(requireContext(), params)
            }
        } else {
            toast(R.string.loading_failed_click_to_retry)
        }
    }

    private fun updateView(gameDetailInfo: GameDetailInfo) {
        binding.lv.hide()
        binding.ivMoreBtn.visible()
        binding.tvGameName.text = gameDetailInfo.name
        binding.tvTitleBarGameName.text = gameDetailInfo.name
        if (gameDetailInfo.description.isNullOrEmpty()) {
            visibleList(
                binding.tvDescriptionLabel,
                binding.tvDescription,
                visible = false
            )
        } else {
            visibleList(
                binding.tvDescriptionLabel,
                binding.tvDescription,
                visible = true
            )
            binding.tvDescription.updateForRecyclerView(
                gameDetailInfo.description,
                screenWidth - dp(32),
                descExpandState
            )
        }
        updatePv(gameDetailInfo.extend?.pv ?: 0)
        updateTime(gameDetailInfo.releaseTime.coerceAtLeast(gameDetailInfo.createTime))
        updateDownloadBtn(gameDetailInfo)

        val banner = gameDetailInfo.images?.firstOrNull {
            !it.url.isNullOrEmpty()
        }
        if (banner == null) {
            binding.ivGameBanner.gone()
            binding.ivGameBannerBg.gone()
        } else {
            binding.ivGameBanner.visible()
            if (banner.isHor()) {
                binding.ivGameBannerBg.gone()
                glide?.run {
                    load(banner.url).placeholder(R.drawable.placeholder_corner_12)
                        .centerCrop()
                        .into(binding.ivGameBanner)
                }
            } else {
                glide?.run {
                    load(banner.url).placeholder(R.drawable.placeholder_corner_12)
                        .transform(BlurTransformation(25), CenterCrop())
                        .into(binding.ivGameBannerBg)

                    load(banner.url).fitCenter()
                        .into(binding.ivGameBanner)
                }
            }
        }

        if (gameDetailInfo.author != null && gameDetailInfo.author.id.isNotEmpty() && !gameDetailInfo.author.name.isNullOrEmpty()) {
            visibleList(
                binding.ivAvatar,
                binding.vAuthorClick,
                binding.tvUsername,
                binding.tvPortfolio,
                binding.tvUserId,
                binding.ulv,
                visible = true
            )
            binding.ulv.show(
                gameDetailInfo.author.tagIds,
                gameDetailInfo.labelInfo,
                isCreator = true,
                glide = glide
            )
            val isMe = viewModel.isMe(gameDetailInfo.author.id)
            visibleList(
                binding.tvFollowBtn,
                popupBinding.mtvAuthorOnly,
                popupBinding.vAuthorOnlyClick,
                popupBinding.vDivider4,
                visible = !isMe
            )
            glide?.run {
                load(gameDetailInfo.author.avatar).placeholder(R.drawable.placeholder_circle)
                    .error(R.drawable.placeholder_circle)
                    .circleCrop()
                    .into(binding.ivAvatar)
            }
            binding.tvUsername.text = gameDetailInfo.author.name
            binding.tvPortfolio.text = SpannableHelper.Builder()
                .text(getString(R.string.game_detail_page_maps_count))
                .textAppearance(context, R.style.MetaTextView_S12_PoppinsMedium500)
                .colorRes(R.color.color_999999)
                .text("${gameDetailInfo.extend?.onlineGameCount ?: 1L}")
                .textAppearance(context, R.style.MetaTextView_S12_PoppinsRegular400)
                .colorRes(R.color.color_999999)
                .build()
            binding.tvUserId.text = SpannableHelper.Builder()
                .text(getString(R.string.game_detail_page_user_id))
                .textAppearance(context, R.style.MetaTextView_S12_PoppinsMedium500)
                .colorRes(R.color.color_999999)
                .text(gameDetailInfo.author.number ?: "unknown")
                .textAppearance(context, R.style.MetaTextView_S12_PoppinsRegular400)
                .colorRes(R.color.color_999999)
                .build()

            binding.vAuthorClick.setOnAntiViolenceClickListener {
                MetaRouter.Profile.other(
                    this@PgcGameDetailFragment,
                    gameDetailInfo.author.id,
                    "8",
                    checkFollow = true
                )
            }
            binding.tvFollowBtn.setOnAntiViolenceClickListener {
                viewModel.follow()
            }
            if (isMe) {
                binding.ivMoreBtn.setImageResource(R.drawable.ic_detail_more)
            } else {
                binding.ivMoreBtn.setImageResource(R.drawable.ic_detail_share)
                binding.ivMoreBtn.setPaddingEx(top = dp(8), bottom = dp(8))
            }
        } else {
            visibleList(
                binding.ivAvatar,
                binding.vAuthorClick,
                binding.tvUsername,
                binding.tvPortfolio,
                binding.ulv,
                binding.tvFollowBtn,
                popupBinding.mtvAuthorOnly,
                popupBinding.vAuthorOnlyClick,
                popupBinding.vDivider4,
                visible = false
            )
            binding.ivMoreBtn.setImageResource(R.drawable.ic_detail_share)
            binding.ivMoreBtn.setPaddingEx(top = dp(8), bottom = dp(8))
        }
        updateMwCompatibilityTips(gameDetailInfo.mwTip)
        binding.clTop.doOnNextLayout {
            binding.clTop.minHeight = 0
        }
    }

    private fun updateLikeView(sns: SnsInfo?) {
        val likeCount = UnitUtil.formatKMCount(sns?.likeCount ?: 0L)
        binding.tvLike.text = likeCount
    }

    /**
     * 获取到数据后更新下载按钮
     */
    private fun updateDownloadBtn(gameDetailInfo: GameDetailInfo) {
        // 重新拉取信息后，将错误状态重置回正常状态
        getBtnDownload().state = when {
            getBtnDownload().state == DownloadProgressButton.STATE_CDN_ERROR -> {
                DownloadProgressButton.STATE_NORMAL
            }

            else -> {
                DownloadProgressButton.STATE_NORMAL
            }
        }
        when (getBtnDownload().state) {
            DownloadProgressButton.STATE_NORMAL -> {
                showStartGameUI()
            }

            DownloadProgressButton.STATE_DOWNLOAD_ERROR -> {
                showDownloadErrorUI()
            }

            DownloadProgressButton.STATE_CDN_ERROR -> {
                showCdnErrorUI()
            }

            DownloadProgressButton.STATE_DOWNLOADING -> {
                showStartDownloadUI()
            }

            DownloadProgressButton.STATE_PAUSE -> {
                showPauseDownloadUI()
            }

            DownloadProgressButton.STATE_UNAVAILABLE -> {
                showGameUnAvailableUI()
            }
        }
    }

    /**
     * 显示下载游戏的UI
     */
    private fun showStartDownloadUI() {
        setDownloadState(DownloadProgressButton.STATE_DOWNLOADING)
    }

    /**
     * 显示暂停下载的UI
     */
    private fun showPauseDownloadUI() {
        setDownloadState(DownloadProgressButton.STATE_PAUSE)
        getBtnDownload().setDownloadingText(getString(R.string.resume_download_game))
    }

    /**
     * 显示获取信息错误的UI
     */
    private fun showCdnErrorUI() {
        setDownloadState(DownloadProgressButton.STATE_CDN_ERROR)
        getBtnDownload().setCurrentText(getString(R.string.retry_download_game))
    }

    /**
     * 显示下载信息错误的UI
     */
    private fun showDownloadErrorUI() {
        setDownloadState(DownloadProgressButton.STATE_DOWNLOAD_ERROR)
        getBtnDownload().setCurrentText(getString(R.string.retry_download_game))
    }

    /**
     * 显示可以开始游戏的UI
     */
    private fun showStartGameUI() {
        setDownloadState(DownloadProgressButton.STATE_NORMAL)
        getBtnDownload().setCurrentText(getString(R.string.play))
    }

    /**
     * 显示启动游戏中的UI
     */
    private fun showLaunchingGameUI() {
        setDownloadState(DownloadProgressButton.STATE_NORMAL)
        getBtnDownload().setCurrentText(getString(R.string.launching))
    }

    /**
     * 显示游戏不可玩UI
     */
    private fun showGameUnAvailableUI() {
        setDownloadState(DownloadProgressButton.STATE_UNAVAILABLE)
        getBtnDownload().setCurrentText(getString(R.string.unavailable))
    }

    /**
     * 更新下载进度UI
     */
    private fun updateDownloadProgress(progress: Float, gameId: String) {
        var nowProgress = progress
        if (nowProgress > 99) {
            getBtnDownload().initProgress(nowProgress)
            showLaunchingGameUI()
        } else {
            setDownloadState(DownloadProgressButton.STATE_DOWNLOADING)
            nowProgress = DownloadProgressUtil.getShowProgress(nowProgress)
            getBtnDownload().setProgress(progress = nowProgress)
        }
    }

    /**
     * 设置下载状态
     */
    private fun setDownloadState(newState: Int) {
        getBtnDownload().state = newState
    }

    private fun getCommonAnalyticParams(): Map<String, String> {
        val info = viewModel.getCurrentGameInfo()
        return hashMapOf("gameid" to (info?.id ?: args.gId))
    }

    private fun getBtnDownload() = binding.dpbEnter2

    private fun loadFirstData(isRefresh: Boolean = false) {
        apiStart()
        viewModel.getGameDetailById(args.gId, isRefresh)
        gameOpinionViewModel.fetchLikeInfo(gameId = args.gId)
        gameOpinionViewModel.getLikeInfo(args.gId)
    }

    override fun onDestroyView() {
        contentHeight = binding.clTop.height
        DetailVideoAnalytics.gameId = null
        super.onDestroyView()
    }

    override fun onShareCountIncrease(data: ShareRawData) {
        if (data.scene == ShareHelper.SCENE_PGC_DETAIL && data.pgcGame?.id == args.gId) {
            viewModel.shareCountIncrement()
        }
    }

    override fun epoxyController() = simpleController(
        vmV2,
        PgcGameDetailState::commentList,
        PgcGameDetailState::commentListLoadMore,
        PgcGameDetailState::uniqueTag,
        PgcGameDetailState::showCommentPinRedDot,
    ) { comments, loadMore, uniqueTag, showCommentPinRedDot ->
        buildCommentController(comments, loadMore, uniqueTag, showCommentPinRedDot)
    }

    private fun buildOperationController() = simpleController(
        vmV2,
        PgcGameDetailState::operationList
    ) {
        it()?.forEachIndexed { index, item ->
            gameOperationItem(item, index, itemListener)
        }
    }

    override fun getPageName() = PageNameConstants.FRAGMENT_NAME_GAME_DETAIL

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.gameDetailFlow.apiMonitor(this) {
            it?.first == null || !it.first.succeeded
        }
        analyticsObserve.sendEnterGameDetailAnalytic(
            args.gId,
            args.packageName,
            viewModel.getGameDetailEnteredTimes(args.gId),
            args.type,
            args.resIdBean,
            creatorType = gameType
        )

        DetailVideoAnalytics.gameId = args.gId
        operationController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        operationController.onSaveInstanceState(outState)
    }

    /**
     * @param source 来源: 1: 默认; 2: 点击底部评论计数图标; 3: 点击底部评论输入栏
     */
    private fun showReplyDialog(
        target: AddPostCommentReplyTarget,
        showEmoji: Boolean = false,
        showImage: Boolean = false,
        source: Int = 1
    ) {
        val replyType: Long
        val reviewId: String
        val type: Int
        if (target.isTargetComment) {
            Analytics.track(
                EventConstants.GAME_MORE_REVIEW_WRITE_CLICK,
                "gameid" to gameId,
                "creatortype" to gameType
            )
            replyType = 0L
            reviewId = target.asComment.commentId
            type = ArticleCommentInputDialog.TYPE_COMMENT_COMMENT
        } else if (target.isTargetReply) {
            Analytics.track(
                EventConstants.GAME_MORE_REVIEW_WRITE_CLICK,
                "gameid" to gameId,
                "creatortype" to gameType
            )
            replyType = 1L
            reviewId = target.asReply.replyId
            type = ArticleCommentInputDialog.TYPE_COMMENT_REPLAY
        } else {
            Analytics.track(
                EventConstants.GAME_FIRST_REVIEW_WRITE_CLICK,
                "source" to source,
                "gameid" to gameId,
                "creatortype" to gameType
            )
            replyType = -1L
            reviewId = args.gId
            type = ArticleCommentInputDialog.TYPE_COMMENT_ARTICLE
        }
        vmV2.setReplyTarget(target)
        ArticleCommentInputDialog.show(
            this,
            target.toNickname,
            gameId,
            null,
            type,
            0.7f,
            showEmoji,
            showImage,
            getPageName(),
            ArticleCommentInputDialogParams.CONTENT_TYPE_PGC_DETAIL,
            1,
            true,
            pageName = getPageName()
        ) {
            if (it == null || !it.valid) return@show
            if (target.isTargetPost) {
                vmV2.addCommentViaNet(it)
            } else {
                vmV2.addReplyViaNet(it)
            }
        }
    }

    private fun handleOperateComment(
        view: View,
        comment: PostComment,
        commentPosition: Int,
        showRedDot: Boolean
    ) {
        handleOperationHelper(
            view,
            comment = comment,
            showRedDot = showRedDot,
            commentPosition = commentPosition
        )
    }

    private fun handleOperateReply(
        view: View,
        reply: PostReply,
        replyPosition: Int,
        commentPosition: Int,
        isAuthorReply: Boolean
    ) {
        handleOperationHelper(
            view,
            reply = reply,
            commentPosition = commentPosition,
            replyPosition = replyPosition,
            isAuthorReply = isAuthorReply
        )
    }

    private fun handleOperationHelper(
        view: View,
        comment: PostComment? = null,
        showRedDot: Boolean = false,
        reply: PostReply? = null,
        commentPosition: Int = 0,
        replyPosition: Int = 0,
        isAuthorReply: Boolean = false
    ) {
        val isMe = vmV2.isMe(comment?.uid ?: reply?.uid)
        val isCreator = vmV2.isCreator(vmV2.myUuid)

        if (comment != null && isCreator) {
            if (comment.top == true) {
                visibleList(
                    popupBindingComment.mtvPin,
                    popupBindingComment.vPinClick,
                    popupBindingComment.vPinRedDot,
                    popupBindingComment.vDivider1,
                    visible = false
                )
                visibleList(
                    popupBindingComment.mtvUnpin,
                    popupBindingComment.vUnpinClick,
                    popupBindingComment.vDivider2,
                    visible = true
                )
            } else {
                visibleList(
                    popupBindingComment.mtvPin,
                    popupBindingComment.vPinClick,
                    popupBindingComment.vDivider1,
                    visible = true
                )
                popupBindingComment.vPinRedDot.visible(showRedDot)
                visibleList(
                    popupBindingComment.mtvUnpin,
                    popupBindingComment.vUnpinClick,
                    popupBindingComment.vDivider2,
                    visible = false
                )
            }
        } else {
            visibleList(
                popupBindingComment.mtvPin,
                popupBindingComment.vPinClick,
                popupBindingComment.vPinRedDot,
                popupBindingComment.vDivider1,
                popupBindingComment.mtvUnpin,
                popupBindingComment.vUnpinClick,
                popupBindingComment.vDivider2,
                visible = false
            )
        }
        visibleList(
            popupBindingComment.mtvReport,
            popupBindingComment.vReportClick,
            popupBindingComment.vDivider3,
            visible = !isMe
        )
        visibleList(
            popupBindingComment.mtvDelete,
            popupBindingComment.vDeleteClick,
            popupBindingComment.vDivider4,
            visible = isMe || isCreator
        )
        val showCopy = if (comment != null && comment.content.isNullOrEmpty()) {
            false
        } else if (reply != null && reply.content.isNullOrEmpty()) {
            false
        } else {
            true
        }
        visibleList(
            popupBindingComment.mtvCopy,
            popupBindingComment.vCopyClick,
            visible = showCopy
        )
        if (!showCopy) {
            visibleList(
                popupBindingComment.vDivider1,
                popupBindingComment.vDivider2,
                visible = false
            )
        }

        popupBindingComment.vCopyClick.setOnAntiViolenceClickListener {
            val reviewId: String = comment?.commentId ?: reply?.replyId.orEmpty()
            val reviewType: Long = if (comment != null) 1L else 2L
            viewLifecycleOwner.lifecycleScope.launch {
                ClipBoardUtil.setClipBoardContent(
                    comment?.content ?: reply?.content,
                    requireContext(),
                    if (comment != null) "Comment" else "Reply"
                )
            }

            toast(R.string.copied_to_clipboard)
            popupWindowComment.dismiss()
        }
        popupBindingComment.vPinClick.setOnAntiViolenceClickListener {
            if (comment != null) {
                Analytics.track(
                    EventConstants.GAME_REVIEW_PIN_CLICK,
                    "gameid" to gameId,
                    "creatortype" to gameType
                )
                vmV2.pinComment(comment, commentPosition, showRedDot)
            }
            popupWindowComment.dismiss()
        }
        popupBindingComment.vUnpinClick.setOnAntiViolenceClickListener {
            if (comment != null) {
                Analytics.track(
                    EventConstants.GAME_CANCEL_REVIEW_PIN_CLICK,
                    "gameid" to gameId,
                    "creatortype" to gameType
                )
                vmV2.pinComment(comment, commentPosition, showRedDot)
            }
            popupWindowComment.dismiss()
        }
        popupBindingComment.vReportClick.setOnAntiViolenceClickListener {
            if (comment != null) {
                goReport(comment.commentId, ReportType.PgcReview)
            } else if (reply != null) {
                goReport(reply.replyId, ReportType.PgcReply)
            }
            popupWindowComment.dismiss()
        }
        popupBindingComment.vDeleteClick.setOnAntiViolenceClickListener {
            ConfirmDialog.Builder(this)
                .image(R.drawable.dialog_icon_cry)
                .content(
                    getString(
                        R.string.delete_confirm,
                        getString(
                            if (comment != null) {
                                R.string.comment
                            } else {
                                R.string.reply
                            }
                        )
                    )
                )
                .cancelBtnTxt(getString(R.string.dialog_cancel))
                .confirmBtnTxt(getString(R.string.delete_cap))
                .isRed(true)
                .cancelCallback {
                    if (comment != null) {
                        Analytics.track(EventConstants.YOUR_REVIEW_DELETE_CANCEL) {
                            put("gameid", gameId)
                            put("reviewid", comment.commentId)
                            put(
                                "type",
                                GameAppraiseAdapter.AppraiseItemListener.TYPE_COMMENT
                            )
                        }
                    } else if (reply != null) {
                        Analytics.track(EventConstants.YOUR_REVIEW_DELETE_CANCEL) {
                            put("gameid", gameId)
                            put("reviewid", reply.replyId)
                            put(
                                "type",
                                GameAppraiseAdapter.AppraiseItemListener.TYPE_REPLAY
                            )
                        }
                    }
                }
                .confirmCallback {
                    if (comment != null) {
                        vmV2.deleteComment(comment, commentPosition)
                        Analytics.track(EventConstants.MY_GAME_REVIEW_DELETE_SUCCESS) {
                            put("gameid", gameId)
                            put("reviewid", comment.commentId)
                            put(
                                "type",
                                GameAppraiseAdapter.AppraiseItemListener.TYPE_COMMENT
                            )
                        }
                        Analytics.track(EventConstants.YOUR_REVIEW_DELETE) {
                            put("gameid", gameId)
                            put("reviewid", comment.commentId)
                            put(
                                "type",
                                GameAppraiseAdapter.AppraiseItemListener.TYPE_COMMENT
                            )
                        }
                    } else if (reply != null) {
                        vmV2.deleteReply(
                            reply,
                            replyPosition,
                            commentPosition,
                            isAuthorReply
                        )
                        Analytics.track(EventConstants.MY_GAME_REVIEW_DELETE_SUCCESS) {
                            put("gameid", gameId)
                            put("reviewid", reply.replyId)
                            put(
                                "type",
                                GameAppraiseAdapter.AppraiseItemListener.TYPE_REPLAY
                            )
                        }
                        Analytics.track(EventConstants.YOUR_REVIEW_DELETE) {
                            put("gameid", gameId)
                            put("reviewid", reply.replyId)
                            put(
                                "type",
                                GameAppraiseAdapter.AppraiseItemListener.TYPE_REPLAY
                            )
                        }
                    }
                }
                .show()
            popupWindowComment.dismiss()
        }

        popupBindingComment.cv.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        val rect = Rect()
        view.getGlobalVisibleRect(rect)
        val dp5 = dp(5)
        val dp16 = dp(16)
        val x = -popupBindingComment.cv.measuredWidth + dp(8)
        val rawBottom = rect.top - dp5 + popupBindingComment.cv.measuredHeight
        binding.root.getGlobalVisibleRect(rect)
        val y = if (rawBottom > rect.bottom) {
            -dp16 - popupBindingComment.cv.measuredHeight + dp5
        } else {
            -dp16 - view.height.coerceAtLeast(view.measuredHeight) - dp5
        }
        popupWindowComment.showAsDropDownByLocation(view, x, y, autoHeight = false)
    }

    private fun goReport(reportId: String, reportType: ReportType) {
        Analytics.track(
            EventConstants.EVENT_REVIEW_REPORT_CLICK,
            "gameid" to gameId,
            "type" to (if (reportType == ReportType.PgcReview) {
                GameAppraiseAdapter.AppraiseItemListener.TYPE_COMMENT
            } else {
                GameAppraiseAdapter.AppraiseItemListener.TYPE_REPLAY
            })
        )
        MetaRouter.Report.postReport(this, reportId, reportType, gameId = gameId) {
            if (it) {
                val analyticsParams = when (reportType) {
                    ReportType.PgcReview -> {
                        ReportSuccessDialogAnalyticsParams.GameComment(
                            gameId = gameId,
                            commentId = reportId,
                        )
                    }

                    ReportType.PgcReply -> {
                        ReportSuccessDialogAnalyticsParams.GameCommentReply(
                            gameId = gameId,
                            replyId = reportId,
                        )
                    }

                    else -> {
                        null
                    }
                }
                ReportReasonDialog.showReportSuccessDialog(this, analyticsParams)
            }
        }
    }

    override fun getCommentList(isRefresh: Boolean) {
        vmV2.getCommentList(isRefresh)
    }

    override fun initCommentList() {
        vmV2.initCommentList()
    }

    override fun onMoreClick() {
        viewModel.gameDetail?.let {
            GlobalShareDialog.show(
                childFragmentManager,
                ShareRawData.pgc(
                    it,
                    gameReviewViewModel.gameScoreResultLiveData.value,
                    gameOpinionViewModel.likeInfoLiveData.value ?: it.sns,
                    args.resIdBean.getReqId()
                ),
                requestKey = viewModel.requestKey,
                features = listOf(
                    ShareFeature(
                        FEAT_REPORT,
                        R.drawable.ic_share_feat_feedback,
                        titleRes = R.string.feedback
                    )
                )
            )
        }
    }

    override fun saveDraft(data: PostCommentDraft) {
        vmV2.saveDraft(data)
    }

    override fun retrieveDraft(): PostCommentDraft? {
        return vmV2.retrieveDraft()
    }

    override fun removeDraft() {
        vmV2.removeDraft()
    }

    override fun updateSortType(sortType: Int) {
        super.updateSortType(sortType)
        vmV2.updateSortType(sortType)
    }

    override fun updateFilterType(filterType: Int) {
        super.updateFilterType(filterType)
        vmV2.updateFilterType(filterType)
    }

    inner class ItemListener : IGameDetailCommonListener {
        override fun isMe(uid: String?): Boolean {
            return vmV2.isMe(uid)
        }

        override fun isCreator(uid: String?): Boolean {
            return vmV2.isCreator(uid)
        }

        override fun iAmCreator(): Boolean {
            return vmV2.iAmCreator
        }

        override fun goUserPage(uid: String?) {
            if (!uid.isNullOrBlank()) {
                MetaRouter.Profile.other(this@PgcGameDetailFragment, uid, TRACK_TAG)
            }
        }

        override fun operateComment(
            view: View,
            comment: PostComment,
            commentPosition: Int,
            showRedDot: Boolean
        ) {
            handleOperateComment(view, comment, commentPosition, showRedDot)
        }

        override fun likeComment(comment: PostComment, commentPosition: Int) {
            if (!comment.isLike) {
                Analytics.track(EventConstants.GAME_REVIEW_LIKE_CLICK) {
                    put("gameid", args.gId)
                    put("reviewid", comment.commentId)
                    put("type", ReviewListFragment.REVIEW_AUTHOR)
                }
            }
            if (!comment.isLike) {
                Analytics.track(
                    EventConstants.C_GAME_REVIEW_LIKE_CLICK,
                    "type" to 1,
                    "creatortype" to gameType,
                    "gameid" to gameId
                )
                DialogShowManager.triggerLike(this@PgcGameDetailFragment)
            } else {
                Analytics.track(
                    EventConstants.C_GAME_REVIEW_LIKE_CANCEL_CLICK,
                    "type" to 1,
                    "creatortype" to gameType,
                    "gameid" to gameId
                )
            }
            vmV2.likeComment(comment, commentPosition)
        }

        override fun reply2Comment(comment: PostComment, commentPosition: Int) {
            showReplyDialog(AddPostCommentReplyTarget(comment, commentPosition))
        }

        override fun operateReply(
            view: View,
            reply: PostReply,
            replyPosition: Int,
            commentPosition: Int,
            isAuthorReply: Boolean
        ) {
            handleOperateReply(view, reply, replyPosition, commentPosition, isAuthorReply)
        }

        override fun likeReply(
            reply: PostReply,
            replyPosition: Int,
            commentPosition: Int,
            isAuthorReply: Boolean
        ) {
            if (!reply.isLike) {
                Analytics.track(
                    EventConstants.C_GAME_REVIEW_LIKE_CLICK,
                    "type" to 2,
                    "creatortype" to gameType,
                    "gameid" to gameId
                )
                DialogShowManager.triggerLike(this@PgcGameDetailFragment)
            } else {
                Analytics.track(
                    EventConstants.C_GAME_REVIEW_LIKE_CANCEL_CLICK,
                    "type" to 2,
                    "creatortype" to gameType,
                    "gameid" to gameId
                )
            }
            vmV2.likeReply(reply, replyPosition, commentPosition, isAuthorReply)
        }

        override fun reply2Reply(reply: PostReply, commentPosition: Int) {
            Analytics.track(EventConstants.EVENT_GAME_REVIEW_REPLIES_CLICK)
            showReplyDialog(AddPostCommentReplyTarget(reply, reply.commentId, commentPosition))
        }

        override fun loadMoreReply(comment: PostComment, commentPosition: Int) {
            Analytics.track(
                EventConstants.EVENT_GAME_REVIEW_EXPAND,
                "creatortype" to gameType
            )
            vmV2.loadMoreReplies(comment, commentPosition)
        }

        override fun collapseReply(comment: PostComment, commentPosition: Int) {
            Analytics.track(
                EventConstants.GAME_REVIEW_COLLAPSE_CLICK,
                "creatortype" to gameType
            )
            vmV2.collapseReply(comment, commentPosition, true)
        }

        override fun goCommentListPage() {}

        override fun previewImage(mediaList: List<PostMedia>?, imagePosition: Int) {
            val images = mediaList?.filter { it.isImage }?.map { it.resourceValue }
            if (images.isNullOrEmpty()) return
            ImgPreDialogFragment.show(
                requireActivity(),
                images.toTypedArray(),
                TRACK_TAG,
                imagePosition
            )
        }

        override fun clickOperation(item: OperationInfo, position: Int) {
            item.content ?: return
            if (item.isWebType()) {
                MetaRouter.Web.navigate(this@PgcGameDetailFragment, item.title, item.content)
            } else if (item.isArticleType()) {
                MetaRouter.Post.goPostDetail(
                    this@PgcGameDetailFragment,
                    item.content,
                    "pgc_detail_operation"
                )
            }
        }

        override fun showComment(comment: PostComment, commentPosition: Int) {
            Analytics.track(EventConstants.GAME_REVIEW_ITEM_SHOW) {
                put("gameid", gameId)
                put("reviewid", comment.commentId)
                put("from", "2") //2 游戏详情页
            }
            if (trackGameReviewShow) {
                trackGameReviewShow = false
                Analytics.track(
                    EventConstants.GAME_REVIEW_SHOW,
                    "creatortype" to gameType
                )
            }
        }

        override fun clickLabel(data: Pair<Int, LabelInfo?>) {
            UserLabelView.showDescDialog(this@PgcGameDetailFragment, data)
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }
}