package com.socialplay.gpark.ui.view

import android.content.Context
import android.os.Parcelable
import android.util.AttributeSet
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager

/**
 * Created by bo.li
 * Date: 2024/5/22
 * Desc:
 */
class MStaggeredLayoutManager : StaggeredGridLayoutManager {

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int) : super(context, attrs, defStyleAttr, defStyleRes)

    constructor(spanCount: Int, orientation: Int) : super(spanCount, orientation)

    private var pausedState: Parcelable? = null
    private var onDetachedFromWindowCallback: ((parcelable: Parcelable?) -> Unit)? = null
    private var onRestoreInstanceStateCallback: (() -> Parcelable?)? = null

    fun setOutState(parcelable: Parcelable?) {
        pausedState = parcelable
    }

    fun setOnDetachedCallback(callback: (parcelable: Parcelable?) -> Unit) {
        onDetachedFromWindowCallback = callback
    }

    fun setOnRestoreCallback(callback: () -> Parcelable?) {
        onRestoreInstanceStateCallback = callback
    }

    override fun onDetachedFromWindow(view: RecyclerView?, recycler: RecyclerView.Recycler?) {
        /**
         * 因为在viewPager2包裹下时，layoutManager的onDetachedFromWindow调用时机比onSaveInstanceState早，导致onSaveInstanceState时，需要的数据都被清除了，
         * 所以手动保存一下，用手动保存的数据恢复
         */
        onDetachedFromWindowCallback?.invoke(onSaveInstanceState())
        super.onDetachedFromWindow(view, recycler)
    }

    override fun onAdapterChanged(oldAdapter: RecyclerView.Adapter<*>?, newAdapter: RecyclerView.Adapter<*>?) {
        if (newAdapter != null) {
            /**
             * 断点自测只有newAdapter = null时此方法才被调用：比如跳到二级页面。
             * 此时super方法会清空layoutManager的排序记录表，所以从二级页面回来后，手动滑到第一页有可能出现空白间距，因为重新排序可能和之前排序的逻辑不一样了
             * 先不走清除逻辑
             * 另外也可以用此代码解决：深拷贝saveState，保证清除排序记录表时，深拷贝的排序记录表不会被清除，restoreState时再恢复
             *         val copiedState = Parcel.obtain()
             *         staggeredGridLayoutManager?.onSaveInstanceState()?.let {
             *             it.writeToParcel(copiedState, 0)
             *             copiedState.setDataPosition(0)
             *             layoutManagerState = StaggeredGridLayoutManager.SavedState.CREATOR.createFromParcel(copiedState)
             *         }
             */
            super.onAdapterChanged(oldAdapter, newAdapter)
        }
    }

    override fun onRestoreInstanceState(state: Parcelable?) {
        super.onRestoreInstanceState(onRestoreInstanceStateCallback?.invoke() ?: state)
    }
}