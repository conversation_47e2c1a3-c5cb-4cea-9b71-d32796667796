package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.os.Build
import android.os.Parcel
import android.os.Parcelable
import android.text.*
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.util.AttributeSet
import android.view.View
import androidx.annotation.RequiresApi
import androidx.core.content.res.ResourcesCompat
import com.socialplay.gpark.R

/**
 * Created by bo.li
 * Date: 2021/5/14
 * Desc:尾部带 展开 的textView
 */
class FolderTextView : MetaTextView {
    // 展开时显示的文字
    private var mFoldText: String = ""

    // 收起时显示的文字
    private var mUnFoldText: String = ""

    // 固定行数
    private var mFoldLine: Int = DEFAULT_FOLD_LINE

    // 收起时显示的文字字体
    private var unFoldFont: Int = 0

    // 尾部文字颜色
    private var mTailColor: Int = DEFAULT_TAIL_TEXT_COLOR

    // 是否可以再次收起
    private var mCanFoldAgain = DEFAULT_CAN_FOLD_AGAIN

    // 当前是否收起
    private var mIsFold = true

    // 绘制，防止重复进行绘制
    private var mHasDrawn = false

    // 内部绘制
    private var mIsInner = false

    // 全文本
    private var fullText: String? = null

    //按钮文字画笔
    private lateinit var mTextPaint: Paint

    // 防止重复计算截断文字
    private var needReCount = true

    // 防止重复计算截断文字
    private var foldedText: SpannableString? = null

    // 折叠行数
    private var foldLine: Int
        get() = mFoldLine
        set(foldLine) {
            mFoldLine = foldLine
            invalidate()
        }

    private var mListener: OnFoldTextStateChangeListener? = null

    var isFold: Boolean
        get() = mIsFold
        set(value) {
            if (mIsFold == value) return
            mIsFold = value
            changeFoldState(value, needCallListener = false)
        }

    companion object {
        // 默认打点文字
        private const val DEFAULT_ELLIPSIZE = "  "

        // 默认固定行数
        private const val DEFAULT_FOLD_LINE = 6

        // 默认收起和展开文字颜色
        private val DEFAULT_TAIL_TEXT_COLOR = Color.parseColor("#00CA81")

        // 默认是否可以再次收起
        private const val DEFAULT_CAN_FOLD_AGAIN = true
    }

    constructor(context: Context) : super(context) {
        inflateLayout(context, null)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        inflateLayout(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        inflateLayout(context, attrs)
    }

    override fun inflateLayout(context: Context, attrs: AttributeSet?) {
        super.inflateLayout(context, attrs)
        val a = context.obtainStyledAttributes(attrs, R.styleable.FolderTextView)
        mFoldText = a.getString(R.styleable.FolderTextView_foldText) ?: ""
        mUnFoldText = a.getString(R.styleable.FolderTextView_unFoldText) ?: ""
        mFoldLine = a.getInteger(R.styleable.FolderTextView_foldLine, DEFAULT_FOLD_LINE)
        unFoldFont = a.getResourceId(R.styleable.FolderTextView_unFold_typeface, 0)
        mTailColor = a.getColor(R.styleable.FolderTextView_tailTextColor, DEFAULT_TAIL_TEXT_COLOR)
        mCanFoldAgain = a.getBoolean(R.styleable.FolderTextView_canFoldAgain, DEFAULT_CAN_FOLD_AGAIN)
        a.recycle()
        initView()
    }

    private fun initView() {
        changeFoldState(false)
        initMoreText()
    }

    override fun setOnClickListener(l: OnClickListener?) {
        super.setOnClickListener(l)
        changeFoldState(!mIsFold)
    }

    private var moreWidth = 0F
    private var moreHeight = 0F
    private var moreBottom = 0F

    private fun initMoreText() {
        mTextPaint = Paint()
        mTextPaint.isAntiAlias = true
        mTextPaint.typeface = typeface
        if (unFoldFont != 0) {
            mTextPaint.typeface = ResourcesCompat.getFont(context, unFoldFont)
        }
        mTextPaint.textSize = textSize
        mTextPaint.color = mTailColor
        moreWidth = mTextPaint.measureText(mUnFoldText)
        moreHeight = textSize
        moreBottom = mTextPaint.descent()
    }

    private fun changeFoldState(state: Boolean, needCallListener: Boolean = true) {
        if (state == mIsFold) {
            return
        }
        mIsFold = state
        mHasDrawn = false
        if (needCallListener) {
            mListener?.onFoldChange(!state)
        }
        invalidate()
    }

    /**
     * 点击处理
     */
    private val textClickSpan: ClickableSpan = object : ClickableSpan() {
        override fun onClick(widget: View) {
            changeFoldState(!mIsFold)
        }

        override fun updateDrawState(ds: TextPaint) {
            ds.color = currentTextColor
        }
    }

    override fun setText(text: CharSequence?, type: BufferType) {
        if (TextUtils.isEmpty(fullText) || !mIsInner) {
            mHasDrawn = false
            updateFoldedText(true, null)
            fullText = text.toString()
        }
        super.setText(text, type)
    }

    private fun updateFoldedText(recount: Boolean, newText: SpannableString?) {
        needReCount = recount
        foldedText = newText
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        if (mIsFold) {
            return
        }
        var layout = layout
        val line = foldLine
        if (line >= layout.lineCount) {
            return
        }
        val index = layout.getLineEnd(line - 1)
        if (index > 0) {
            // 得到一个字符串，该字符串恰好占据mFoldLine行数的高度
            val strWhichHasExactlyFoldLine = text.subSequence(0, index).toString()
            layout = makeTextLayout(strWhichHasExactlyFoldLine)
            // 把这个高度设置成最终的高度，这样下方View就不会抖动了
            setMeasuredDimension(measuredWidth, layout.height + paddingTop + paddingBottom)
        }
    }

    override fun onDraw(canvas: Canvas) {
        if (!mHasDrawn) {
            resetText()
        }
        if (!mIsFold && fullText?.length != length()) {
            canvas.drawText(mUnFoldText, measuredWidth - paddingRight - moreWidth, measuredHeight - paddingBottom - moreBottom, mTextPaint)
        }
        super.onDraw(canvas)
        mHasDrawn = true
        mIsInner = false
    }

    /**
     * 获取TextView的Layout，注意这里使用getWidth()得到宽度
     * @param text 源文本
     * @return Layout
     */
    private fun makeTextLayout(text: String): Layout {
        val testWidth = (width - paddingLeft - paddingRight).coerceAtLeast(0)
        return StaticLayout(
            text,
            paint,
            testWidth,
            Layout.Alignment.ALIGN_NORMAL,
            lineSpacingMultiplier,
            lineSpacingExtra,
            true
        )
    }

    /**
     * 重置文字
     */
    private fun resetText() {
        // 文字本身就小于固定行数的话，不添加尾部的收起/展开文字
        val layout = makeTextLayout(fullText ?:"")
        if (layout.lineCount <= foldLine) {
            text = fullText
            return
        }
        var spanStr = SpannableString(fullText)
        if (mIsFold) { // 变为展开状态
            if (mCanFoldAgain) {
                spanStr = createUnFoldSpan(fullText ?:"")
            }
        } else { // 变为收起状态
            spanStr = if (!needReCount && foldedText != null) foldedText!! else createFoldSpan(fullText ?:"")
            updateFoldedText(false, spanStr)
        }
        updateText(spanStr)
    }

    /**
     * 不更新全文本下，进行展开和收缩操作
     * @param text 源文本
     */
    private fun updateText(text: CharSequence) {
        mIsInner = true
        setText(text)
    }

    /**
     * 创建展开状态下的Span
     * @param text 源文本
     * @return 展开状态下的Span
     */
    private fun createUnFoldSpan(text: String): SpannableString {
        val spanStr = SpannableString(text)
        spanStr.setSpan(textClickSpan, 0, text.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        return spanStr
    }

    /**
     * 创建收缩状态下的Span
     * @param text
     * @return 收缩状态下的Span
     */
    private fun createFoldSpan(text: String): SpannableString {
        val destStr = tailorText(text)
        val spanStr = SpannableString(destStr)
        spanStr.setSpan(textClickSpan, 0, destStr.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        return spanStr
    }

    /**
     * 裁剪文本至固定行数（备用方法）
     * @param text 源文本
     * @return 裁剪后的文本
     */
    private fun tailorTextBackUp(text: String): String {
        val destStr = text + DEFAULT_ELLIPSIZE + mUnFoldText
        val layout = makeTextLayout(destStr)

        return if (layout.lineCount > foldLine) {
            // 如果行数大于固定行数
            var index = layout.getLineEnd(foldLine - 1)
            if (text.length < index) {
                index = text.length
            } // 从最后一位逐渐试错至固定行数（可以考虑用二分法改进）
            if (index <= 1) {
                return ""
            }
            val subText = text.substring(0, index - 1)
            tailorText(subText)
        } else {
            text
        }
    }

    /**
     * 裁剪文本至固定行数（二分法）。经试验，在文字长度不是很长时，效率比备用方法高不少；当文字长度过长时，备用方法则优势明显。
     * @param text 源文本
     * @return 裁剪后的文本
     */
    private fun tailorText(text: String): String { // return tailorTextBackUp(text);
        var start = 0
        var end = text.length - 1
        var mid = (start + end) / 2
        var find = finPos(text, mid)
        while (find != 0 && end > start) {
            if (find > 0) {
                end = mid - 1
            } else if (find < 0) {
                start = mid + 1
            }
            mid = (start + end) / 2
            find = finPos(text, mid)
        }
        return if (find == 0) {
            text.substring(0, mid)
        } else {
            tailorTextBackUp(text)
        }
    }

    /**
     * 查找一个位置P，到P时为mFoldLine这么多行，加上一个字符‘A’后则刚好为mFoldLine+1这么多行
     * @param text 源文本
     * @param pos  位置
     * @return 查找结果
     */
    private fun finPos(text: String, pos: Int): Int {
        val destStr = text.substring(0, pos) + DEFAULT_ELLIPSIZE + mUnFoldText
        val layout = makeTextLayout(destStr)
        val layoutMore = makeTextLayout(destStr + "A")
        val lineCount = layout.lineCount
        val lineCountMore = layoutMore.lineCount
        return if (lineCount == foldLine && lineCountMore == foldLine + 1) {
            // 行数刚好到折叠行数
            0
        } else if (lineCount > foldLine) {
            // 行数比折叠行数多
            1
        } else {
            // 行数比折叠行数少
            -1
        }
    }

    fun setOnFoldTextStateChangeListener(listener: OnFoldTextStateChangeListener?) {
        mListener = listener
    }

    interface OnFoldTextStateChangeListener{
        fun onFoldChange(isFold: Boolean)
    }

    override fun onRestoreInstanceState(state: Parcelable?) {
        // 6.0以下调用super会崩溃, 但是不调用也会崩溃, 所以需要针对性catch
        kotlin.runCatching { super.onRestoreInstanceState(state) }
        foldedText = SpannableString((state as SavedState).foldedText)
        changeFoldState(state.isFold, false)
    }

    override fun onSaveInstanceState(): Parcelable {
        val superState = super.onSaveInstanceState()
        val ss = SavedState(superState)
        ss.isFold = mIsFold
        ss.foldedText = foldedText.toString()
        return ss
    }


    internal class SavedState : BaseSavedState {

        var isFold: Boolean = false
        var foldedText: String? = null

        @RequiresApi(Build.VERSION_CODES.N)
        constructor(source: Parcel?, loader: ClassLoader?) : super(source, loader) {
            isFold = source?.readInt() != 0
            foldedText = source?.readString()
        }

        constructor(source: Parcel?) : super(source) {
            isFold = source?.readInt() != 0
            foldedText = source?.readString()
        }

        constructor(superState: Parcelable?) : super(superState)

        override fun writeToParcel(out: Parcel, flags: Int) {
            super.writeToParcel(out, flags)
            out.writeInt(if (isFold) 1 else 0)
            out.writeString(foldedText)
        }

        companion object CREATOR : Parcelable.ClassLoaderCreator<SavedState> {

            override fun createFromParcel(source: Parcel?, loader: ClassLoader?): SavedState {
                return if (Build.VERSION.SDK_INT >= 24) SavedState(source, loader) else SavedState(
                    source
                )
            }

            override fun createFromParcel(source: Parcel?): SavedState {
                return SavedState(source)
            }

            override fun newArray(size: Int): Array<SavedState?> {
                return arrayOfNulls(size)
            }
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        movementMethod = InterceptClickEventLinkMovementMethod(this)
    }

    override fun onDetachedFromWindow() {
        movementMethod = null
        super.onDetachedFromWindow()
    }
}