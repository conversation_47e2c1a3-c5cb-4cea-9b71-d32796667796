package com.socialplay.gpark.ui.post.topic.detail

import android.os.Bundle
import android.os.Parcelable
import android.view.View
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.parentFragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.PostPublishStatus
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.request.PostTagFeedRequest
import com.socialplay.gpark.data.model.post.topic.TopicTabType
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.post.feed.CommunityFeedFragment
import com.socialplay.gpark.ui.post.feed.base.BaseCommunityFeedViewModel
import com.socialplay.gpark.ui.post.feed.base.ICommunityFeedModelState
import com.socialplay.gpark.ui.view.LoadingView
import com.socialplay.gpark.ui.view.video.FeedVideoHelper
import com.socialplay.gpark.util.extension.toast
import kotlinx.parcelize.Parcelize
import org.koin.core.context.GlobalContext

/**
 * Created by bo.li
 * Date: 2024/4/7
 * Desc:
 */
@Parcelize
data class TopicSortFeedFragmentArgs(val tagInfo: PostTag, val sortType: Int) :
    Parcelable

class TopicSortFeedFragment : CommunityFeedFragment() {

    private val viewModel: TopicSortFeedViewModel by fragmentViewModel()
    private val parentViewModel: TopicDetailTabViewModel by parentFragmentViewModel()
    private val args: TopicSortFeedFragmentArgs by args()
    override val feedViewModel: BaseCommunityFeedViewModel<ICommunityFeedModelState>
        get() = viewModel as BaseCommunityFeedViewModel<ICommunityFeedModelState>

    companion object {
        fun newInstance(args: TopicSortFeedFragmentArgs): TopicSortFeedFragment {
            return TopicSortFeedFragment().apply {
                arguments = args.asMavericksArgs()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (args.sortType == PostTagFeedRequest.ORDER_TYPE_NEWEST) {
            observePublish()
        }
    }

    private fun observePublish() {
        viewModel.onAsync(
            TopicSortFeedModelState::scrollToTop,
            onSuccess = {
                if (it) {
                    recyclerView.scrollToPosition(0)
                    viewModel.removeScrollToTop()
                }
            }
        )
        parentViewModel.onEach(
            TopicDetailTabModelState::publishingState,
            uniqueOnly()
        ) { publishingState ->
            if (publishingState == null) {
                viewModel.removePublishingItem(null)
                return@onEach
            }
            when {
                publishingState.status == PostPublishStatus.STATUS_SUCCEEDED -> {
                    publishingState.post?.toPublishFeed(
                        GlobalContext.get().get<AccountInteractor>().accountLiveData.value,
                        publishingState.ts,
                    )?.let { it1 ->
                        viewModel.addPublishingItem(it1, false)
                    }
                }

                publishingState.status == PostPublishStatus.STATUS_FAILED -> {
                    viewModel.removePublishingItem(publishingState.ts)
                }

                !publishingState.publishOver() -> {
                    publishingState.post?.toPublishFeed(
                        GlobalContext.get().get<AccountInteractor>().accountLiveData.value,
                        publishingState.ts,
                    )?.let { it1 ->
                        viewModel.addPublishingItem(it1, true)
                    }
                }
            }
        }
    }

    override val showUserStatus: Boolean = true

    override val likeLocation: String = EventParamConstants.LOCATION_LIKE_TOPIC_DETAIL

    override val showPin: Boolean = false

    override val showTagList: Boolean = false

    override fun goPost(item: CommunityFeedInfo) {
        Analytics.track(EventConstants.EVENT_COMMUNITY_POST_CLICK) {
            put(EventParamConstants.KEY_POSTID, item.postId)
            item.tagList?.map { it.tagId }?.let { put(EventParamConstants.KEY_TAG_LIST, it) }
        }
        val videoProgress = FeedVideoHelper.getVideoProgressByResId(item.localUniqueId)
        MetaRouter.Post.goPostDetail(this, item.postId, "topic_detail", fromTagDetailInfo = args.tagInfo, videoProgress = videoProgress)
    }

    override fun goProfile(uuid: String) {
        MetaRouter.Profile.other(this, uuid, "topic_detail")
    }

    override fun goTopic(tag: PostTag, postId: String) {
        if (tag.tagId <= 0) {
            toast(R.string.tag_reviewing_toast)
            return
        }
        topDetail(tag, postId)
    }

    private fun topDetail(tag: PostTag, postId: String?) {
        if (tag.tagId != args.tagInfo.tagId) {
            MetaRouter.Post.topicDetail(this, tag, EventParamConstants.SOURCE_TOPIC_TOPIC, postId)
        }
    }

    override fun refreshPage() {
        viewModel.refresh()
    }

    override fun loadMoreFeed() {
        viewModel.loadMoreFeed()
    }

    override fun getFeedTag(): String? = null

    override fun initLoadingView(loading: LoadingView) {

    }

    override fun refreshParent() {

    }

    override fun invalidate() {

    }

    override val useVideoFunc: Boolean = false

    override fun getPageName(): String = PageNameConstants.FRAGMENT_TOPIC_DETAIL_CHILD
}