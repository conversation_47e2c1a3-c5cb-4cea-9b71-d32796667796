package com.socialplay.gpark.ui.kol.creator.label

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.model.account.UserFollowEvent
import com.socialplay.gpark.data.model.creator.FollowResult
import com.socialplay.gpark.data.model.creator.KolCreatorInfo
import com.socialplay.gpark.data.model.creator.label.KolCreatorLabel
import com.socialplay.gpark.data.model.user.RelationType
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import org.greenrobot.eventbus.EventBus
import org.koin.android.ext.android.get

/**
 * Created by bo.li
 * Date: 2024/8/9
 * Desc: kol 标签创作者
 */
data class LabelKolCreatorModelState(
    val labelList: List<KolCreatorLabel>,
    val asyncCreatorList: Async<List<KolCreatorInfo>> = Uninitialized,
    val creatorLoadMore: Async<LoadMoreState> = Uninitialized,
    val followResult: Async<FollowResult> = Uninitialized,
    val sinceId: String? = null
) : MavericksState {

    constructor(args: LabelKolCreatorFragmentArgs) : this(args.labelList.mapIndexed { index, kolCreatorLabel ->
        kolCreatorLabel.copy(localSelected = index == 0)
    })

    val creatorList: List<KolCreatorInfo> get() = asyncCreatorList() ?: emptyList()

    // 当前选中的标签
    val selectedLabelId: Int? get() = labelList.firstOrNull { it.localSelected }?.tagId
}

class LabelKolCreatorViewModel(
    private val repository: IMetaRepository,
    initialState: LabelKolCreatorModelState
) : BaseViewModel<LabelKolCreatorModelState>(initialState) {

    init {
        onEach(LabelKolCreatorModelState::selectedLabelId) {
            refreshCreatorList()
        }
        onAsync(LabelKolCreatorModelState::followResult, onSuccess = { result ->
            setState {
                copy(
                    asyncCreatorList = asyncCreatorList.map { list ->
                        list.map {
                            if (it.uuid == result.uuid) {
                                it.copy(followUser = result.toFollow)
                            } else {
                                it
                            }
                        }
                    },
                )
            }
        })
    }

    companion object :
        KoinViewModelFactory<LabelKolCreatorViewModel, LabelKolCreatorModelState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: LabelKolCreatorModelState
        ): LabelKolCreatorViewModel {
            return LabelKolCreatorViewModel(get(), state)
        }
    }

    fun selectLabel(tagId: Int) {
        setState {
            copy(labelList = labelList.map { it.copy(localSelected = tagId == it.tagId) })
        }
    }

    fun refreshCreatorList() {
        withState { oldState ->
            val selectedLabelId = oldState.selectedLabelId
            if (selectedLabelId == null || oldState.asyncCreatorList is Loading || oldState.labelList.isEmpty()) return@withState
            repository.getLabelCreatorList(selectedLabelId, null).execute { result ->
                val nextSinceId = result()?.userList?.lastOrNull()?.sinceId
                val end =
                    result()?.end == true || result()?.userList.isNullOrEmpty() || nextSinceId.isNullOrEmpty()
                copy(
                    asyncCreatorList = result.map {
                        it.userList?.distinctBy { it.uuid } ?: emptyList()
                    },
                    sinceId = if (result is Success) nextSinceId else null,
                    creatorLoadMore = result.map { LoadMoreState(end) },
                )
            }
        }
    }

    fun loadMoreCreatorList() {
        withState { oldState ->
            val selectedLabelId = oldState.selectedLabelId
            if (selectedLabelId == null || oldState.asyncCreatorList is Loading || oldState.labelList.isEmpty()) return@withState
            repository.getLabelCreatorList(selectedLabelId, oldState.sinceId).execute { result ->
                val nextSinceId = result()?.userList?.lastOrNull()?.sinceId
                val end =
                    result()?.end == true || result()?.userList.isNullOrEmpty() || nextSinceId.isNullOrEmpty()
                copy(
                    asyncCreatorList = if (result is Success) {
                        val oldList = asyncCreatorList.invoke()
                        result.map { wrapper ->
                            if (oldList.isNullOrEmpty()) {
                                wrapper.userList ?: emptyList()
                            } else {
                                oldList + (wrapper.userList ?: emptyList())
                            }.distinctBy { it.uuid }
                        }
                    } else {
                        asyncCreatorList
                    },
                    sinceId = if (result is Success) nextSinceId else sinceId,
                    creatorLoadMore = result.map { LoadMoreState(end) },
                )
            }
        }
    }

    fun changeFollow(uuid: String, toFollow: Boolean) {
        if (oldState.asyncCreatorList is Loading || oldState.followResult is Loading) return
        Analytics.track(EventConstants.EVENT_FOLLOW_CLICK) {
            put(EventParamConstants.KEY_USERID, uuid)
            put(EventParamConstants.KEY_LOCATION, EventParamConstants.LOCATION_KOL_MORE_STAR_CREATOR)
            put(
                EventParamConstants.KEY_TYPE,
                if (toFollow) EventParamConstants.TYPE_FOLLOW else EventParamConstants.TYPE_UNFOLLOW
            )
        }
        if (toFollow) {
            repository.relationAddV2(uuid, RelationType.Follow.value)
        } else {
            repository.relationDelV2(uuid, RelationType.Follow.value)
        }.map {
            EventBus.getDefault().post(UserFollowEvent(uuid, toFollow, UserFollowEvent.FROM_KOL_MORE))
            FollowResult(uuid, toFollow)
        }.execute { result ->
            copy(followResult = result)
        }
    }
}