package com.socialplay.gpark.ui.core

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import androidx.annotation.CallSuper
import androidx.annotation.LayoutRes
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.viewbinding.ViewBinding
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.DeliveryMode
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.RedeliverOnStart
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.UniqueOnly
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.ApiError
import com.socialplay.gpark.function.analytics.handle.AppLaunchAnalytics
import com.socialplay.gpark.function.apm.PageMonitor
import com.socialplay.gpark.function.apm.onPageCreate
import com.socialplay.gpark.function.apm.onPageDestroyView
import com.socialplay.gpark.function.apm.onPageResume
import com.socialplay.gpark.function.apm.onPageStart
import com.socialplay.gpark.function.apm.onPageViewCreated
import com.socialplay.gpark.function.apm.page.IPageMonitor
import com.socialplay.gpark.function.apm.page.view.ISpeedLayout
import com.socialplay.gpark.ui.core.statusbar.StatusBarViewModel
import com.socialplay.gpark.ui.core.statusbar.isDarkTextStatusBar
import com.socialplay.gpark.ui.view.LoadingView
import com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
import com.socialplay.gpark.util.ShortToast
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.ToastData
import com.socialplay.gpark.util.ToastMode
import com.socialplay.gpark.util.extension.collectWithLifecycle
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.navColor
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.ifNullOrEmpty
import com.socialplay.gpark.util.property.FragmentViewBindingDelegate
import com.socialplay.gpark.util.property.getBindMethodFrom
import kotlinx.coroutines.isActive
import org.koin.core.context.GlobalContext
import timber.log.Timber
import kotlin.reflect.KProperty1

interface MavericksViewEx : MavericksView {

    private val context: Context
        get() = kotlin.runCatching {
            when (this) {
                is Fragment -> {
                    requireContext()
                }

                is Activity -> {
                    this
                }

                else -> {
                    error("not found context")
                }
            }
        }.getOrDefault(GlobalContext.get().get())

    fun <S : MavericksState, T> MavericksViewModel<S>.registerAsyncErrorToast(
        asyncProp: KProperty1<S, Async<T>>,
        toastMode: ToastMode = ShortToast
    ) =
        onAsync(asyncProp, deliveryMode = uniqueOnly("$toastMode-async-error"), onFail = { error ->
            toastMode.showToast(
                context,
                (error as? ApiError)?.getToast(context) ?: error.message.orEmpty()
                    .ifEmpty { error.toString() })
        })

    fun <S : MavericksState, T> MavericksViewModel<S>.registerAsyncErrorToast(
        asyncProp: KProperty1<S, Async<T>>,
        ifToast: (Throwable) -> Boolean,
        toastMode: ToastMode = ShortToast
    ) =
        onAsync(asyncProp, deliveryMode = uniqueOnly("$toastMode-async-error"), onFail = { error ->
            if (ifToast(error)) {
                toastMode.showToast(
                    context,
                    (error as? ApiError)?.getToast(context) ?: error.message.orEmpty()
                        .ifEmpty { error.toString() })
            }
        })

    fun <S : MavericksState> MavericksViewModel<S>.registerToast(
        prop1: KProperty1<S, Any>,
        toastMode: ToastMode = ShortToast
    ) = onEach(prop1, deliveryMode = uniqueOnly(toastMode.name)) {
        when (it) {
            is String -> toastMode.showToast(context, it)
//            is Int -> toastMode.showToast(context, it)
            is ToastData -> toastMode.showToast(context, it)
            else -> toastMode.showToast(context, it.toString())
        }
    }

    /**
     * copy from onAsync add loading callback
     */
    fun <S : MavericksState, T> MavericksViewModel<S>.onAsync(
        asyncProp: KProperty1<S, Async<T>>,
        deliveryMode: DeliveryMode = RedeliverOnStart,
        onFail: (suspend (Throwable, T?) -> Unit)? = null,
        onLoading: (suspend (T?) -> Unit)? = null,
        onSuccess: (suspend (T) -> Unit)? = null,
    ) = onEach(asyncProp, deliveryMode.appendPropertiesToId(asyncProp)) { asyncValue ->
        if (onSuccess != null && asyncValue is Success) {
            onSuccess(asyncValue())
        } else if (onFail != null && asyncValue is Fail) {
            onFail(asyncValue.error, asyncValue.invoke())
        } else if (onLoading != null && asyncValue is Loading) {
            onLoading(asyncValue())
        }
    }

    private fun DeliveryMode.appendPropertiesToId(vararg properties: KProperty1<*, *>): DeliveryMode {
        return when (this) {
            is RedeliverOnStart -> RedeliverOnStart
            is UniqueOnly -> UniqueOnly(properties.joinToString(
                ",", prefix = subscriptionId + "_"
            ) { it.name })
        }
    }

    fun <S : MavericksState> MavericksViewModel<S>.setupRefreshLoading(
        asyncProp: KProperty1<S, Async<List<*>>>,
        loadingView: LoadingView,
        refreshLayout: MetaRefreshLayout?,
        emptyMsg: String = "",
        emptyImg: Int = R.drawable.icon_no_recent_activity,
        emptyMsgBlock: (() -> String?)? = null,
        onRefresh: () -> Unit
    ) {
        refreshLayout?.setOnRefreshListener {
            onRefresh()
        }
        loadingView.setRetry {
            onRefresh()
        }
        onAsync(
            asyncProp,
            onLoading = {
                if (it.isNullOrEmpty()) {
                    loadingView.showLoading()
                } else {
                    refreshLayout?.isRefreshing = true
                }
            },
            onFail = { _, list ->
                refreshLayout?.isRefreshing = false
                if (list.isNullOrEmpty()) {
                    loadingView.showError()
                }
            },
            onSuccess = {
                refreshLayout?.isRefreshing = false
                if (it.isEmpty()) {
                    loadingView.showEmpty(
                        emptyMsgBlock?.invoke().ifNullOrEmpty { emptyMsg.ifNullOrEmpty { loadingView.context.getString(R.string.no_data) } },
                        emptyImg
                    )
                } else {
                    loadingView.hide()
                }
            }
        )
    }

    fun <S : MavericksState> MavericksViewModel<S>.setupRefreshLoading(
        asyncProp: KProperty1<S, Async<List<*>>>,
        loadingView: LoadingView,
        refreshLayout: MetaRefreshLayout?,
        emptyView: View?,
        onRefresh: () -> Unit
    ) {
        refreshLayout?.setOnRefreshListener {
            onRefresh()
        }
        loadingView.setRetry {
            onRefresh()
        }
        onAsync(
            asyncProp,
            onLoading = {
                if (it.isNullOrEmpty()) {
                    loadingView.showLoading()
                } else {
                    refreshLayout?.isRefreshing = true
                }
                emptyView?.isVisible = false
            },
            onFail = { _, list ->
                refreshLayout?.isRefreshing = false
                if (list.isNullOrEmpty()) {
                    loadingView.showError()
                }
                emptyView?.isVisible = false
            },
            onSuccess = {
                refreshLayout?.isRefreshing = false
                loadingView.hide()
                emptyView?.isVisible = it.isEmpty()
            }
        )
    }

    fun <S : MavericksState> MavericksViewModel<S>.setupRefreshLoading(
        asyncProp: KProperty1<S, Async<*>>,
        loadingView: LoadingView,
        refreshLayout: MetaRefreshLayout?,
        onRefresh: () -> Unit
    ) {
        refreshLayout?.setOnRefreshListener {
            onRefresh()
        }
        loadingView.setRetry {
            onRefresh()
        }
        onAsync(
            asyncProp,
            onLoading = { data ->
                if (data == null) {
                    loadingView.showLoading()
                } else {
                    refreshLayout?.isRefreshing = true
                }
            },
            onFail = { _, data ->
                refreshLayout?.isRefreshing = false
                if (data == null) {
                    loadingView.showError()
                }
            },
            onSuccess = { data ->
                refreshLayout?.isRefreshing = false
                loadingView.hide()
            }
        )
    }

    fun <S : MavericksState> MavericksViewModel<S>.setupRefreshLoadingCustomFail(
        asyncProp: KProperty1<S, Async<*>>,
        loadingView: LoadingView,
        refreshLayout: MetaRefreshLayout?,
        onFail: (e: Throwable, data: Any?) -> Unit,
        onRefresh: () -> Unit
    ) {
        refreshLayout?.setOnRefreshListener {
            onRefresh()
        }
        loadingView.setRetry {
            onRefresh()
        }
        onAsync(
            asyncProp,
            onLoading = { data ->
                if (data == null) {
                    loadingView.showLoading()
                } else {
                    refreshLayout?.isRefreshing = true
                }
            },
            onFail = { e, data ->
                refreshLayout?.isRefreshing = false
                onFail(e, data)
            },
            onSuccess = { data ->
                refreshLayout?.isRefreshing = false
                loadingView.hide()
            }
        )
    }

    fun <S : MavericksState> MavericksViewModel<S>.setupRefreshLoading(
        asyncProp: KProperty1<S, Async<List<*>>>,
        loadingView: LoadingView,
        refreshLayout: MetaRefreshLayout?,
        loadingViewEmptyAction: () -> Unit,
        onRefresh: () -> Unit
    ) {
        refreshLayout?.setOnRefreshListener {
            onRefresh()
        }
        loadingView.setRetry {
            onRefresh()
        }
        onAsync(
            asyncProp,
            onLoading = {
                if (it.isNullOrEmpty()) {
                    loadingView.showLoading()
                } else {
                    refreshLayout?.isRefreshing = true
                }
            },
            onFail = { _, list ->
                refreshLayout?.isRefreshing = false
                if (list.isNullOrEmpty()) {
                    loadingView.showError()
                } else {
                    loadingView.hide()
                }
            },
            onSuccess = {
                refreshLayout?.isRefreshing = false
                if (it.isEmpty()) {
                    loadingViewEmptyAction()
                } else {
                    loadingView.hide()
                }
            }
        )
    }
}

abstract class BaseFragment<VB : ViewBinding>(@LayoutRes contentLayoutId: Int) :
    Fragment(contentLayoutId), MavericksViewEx, PageExposureView, IPageMonitor {

    private var _binding: VB? = null
    protected open val binding: VB get() = _binding!!
    protected val statusBarViewModel: StatusBarViewModel by fragmentViewModel()
    protected open val destId: Int = 0
    private var hasNavUp = false

    protected open var navColorRes: Int = R.color.white
    private var prevNavColor: Int = 0
    private var nextNavColor: Int = 0

    protected var glide: RequestManager? = null
        get() {
            if (field == null && enableGlide) {
                field = Glide.with(this)
            }
            return field
        }
    private var enableGlide: Boolean = false

    @CallSuper
    override fun onCreate(savedInstanceState: Bundle?) {
        onPageCreate()
        super.onCreate(savedInstanceState)
        registerPageExposureTracker()
    }

    abstract fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): VB?

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = onCreateViewBinding(inflater, container, savedInstanceState)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        onPageViewCreated()
        super.onViewCreated(view, savedInstanceState)
        enableGlide = true
        binding.root.apply {
            if (this is ISpeedLayout) {
                setPageConfig(
                    this@BaseFragment::class.java.simpleName,
                    <EMAIL>(),
                    <EMAIL>()
                )
            } else {
                if (PageMonitor.getConfig(this@BaseFragment::class.java.simpleName) != null) {
                    Timber.tag("PageMonitor")
                        .e("${this@BaseFragment::class.java.simpleName} not set page monitor layout!")
                }
            }
        }

        statusBarViewModel.stateFlow.collectWithLifecycle(
            viewLifecycleOwner.lifecycle,
            Lifecycle.State.RESUMED
        ) {
            StatusBarUtil.setDarkText(this, it.isDarkText.value)
        }
        if (nextNavColor == 0) {
            nextNavColor = getColorByRes(navColorRes)
        }
    }

    private var enterAnimateEnd = false
    private var enterAnimateEndCallbacks = mutableListOf<() -> Unit>()
    override fun onCreateAnimation(transit: Int, enter: Boolean, nextAnim: Int): Animation? {
        if (enter && nextAnim != 0) {
            enterAnimateEnd = false
            val anim = AnimationUtils.loadAnimation(requireContext(), nextAnim)
            anim?.setAnimationListener(object : Animation.AnimationListener {
                override fun onAnimationStart(animation: Animation?) {}
                override fun onAnimationRepeat(animation: Animation?) {}
                override fun onAnimationEnd(animation: Animation?) {
                    // Fragment入场动画执行完毕
                    if (lifecycleScope.isActive) {
                        enterAnimateEnd = true
                        if (enterAnimateEndCallbacks.isNotEmpty()) {
                            // 使用toList() 创建副本遍历, 防止遍历过程中, MutableList被其它地方修改了, 导致 ConcurrentModificationException 异常
                            enterAnimateEndCallbacks.toList().forEach { callback ->
                                callback()
                            }
                            enterAnimateEndCallbacks.clear()
                        }
                    }
                }
            })
            return anim
        }
        return super.onCreateAnimation(transit, enter, nextAnim)
    }

    fun isEnterAnimateEnd(): Boolean {
        return enterAnimateEnd
    }

    /**
     * 入场动画结束时, 才执行任务
     */
    fun runAfterEnterAnimationEnd(callback: () -> Unit) {
        if (enterAnimateEnd) {
            callback()
        } else {
            enterAnimateEndCallbacks.add(callback)
        }
    }

    open fun isBindingAvailable(): Boolean {
        return _binding != null
    }

    protected fun navUp() {
        if (hasNavUp) return
        hasNavUp = true
        if (destId != 0) {
            navigateUp(destId)
        } else {
            navigateUp()
        }
    }

    override fun onResume() {
        onPageResume()
        super.onResume()
        if (isFirstPage()) {
            AppLaunchAnalytics.handleAppBootTime(requireActivity())
        }
        statusBarViewModel.update(isDarkTextStatusBar())
        prevNavColor = requireActivity().window.navigationBarColor
        if (nextNavColor != prevNavColor) {
            requireActivity().window.navColor = nextNavColor
        }
    }

    override fun onPause() {
        super.onPause()
        if (requireActivity().window.navigationBarColor != prevNavColor) {
            requireActivity().window.navColor = prevNavColor
        }
    }

    override fun onStart() {
        onPageStart()
        super.onStart()
    }

    override fun onDestroyView() {
        onPageDestroyView()
        enableGlide = false
        glide = null
        super.onDestroyView()
        _binding = null
    }
}


private fun <T : ViewBinding> BaseFragment<T>.viewBindingBySuperclassParameterizedType() =
    FragmentViewBindingDelegate<T>(getBindMethodFrom(javaClass), this)

abstract class BaseRecyclerViewFragment<VB : ViewBinding>(@LayoutRes contentLayoutId: Int) :
    BaseFragment<VB>(contentLayoutId) {

    protected abstract val recyclerView: EpoxyRecyclerView

    protected val epoxyController by lazy { epoxyController() }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        AppLaunchAnalytics.handleFragmentCreated(this)
        epoxyController.onRestoreInstanceState(savedInstanceState)
    }

    @CallSuper
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView.setController(epoxyController)
    }

    abstract fun epoxyController(): EpoxyController

    override fun invalidate() {
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        epoxyController.onSaveInstanceState(outState)
    }

}

interface IBaseEpoxyItemListener {
    fun getGlideOrNull(): RequestManager?
}

typealias GlideGetter = () -> RequestManager?