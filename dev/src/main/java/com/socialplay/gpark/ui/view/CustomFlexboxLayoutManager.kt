package com.socialplay.gpark.ui.view

import android.content.Context
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.recyclerview.widget.RecyclerView
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/01/12
 *     desc   :
 * </pre>
 */
open class CustomFlexboxLayoutManager @JvmOverloads constructor(
    context: Context,
    @FlexDirection flexDirection: Int = FlexDirection.ROW,
    @FlexWrap flexWrap: Int = FlexWrap.WRAP
) : FlexboxLayoutManager(context, flexDirection, flexWrap) {

    override fun generateLayoutParams(lp: ViewGroup.LayoutParams): RecyclerView.LayoutParams {
        return when (lp) {
            is RecyclerView.LayoutParams -> {
                LayoutParams(lp)
            }

            is MarginLayoutParams -> {
                LayoutParams(lp)
            }

            else -> {
                LayoutParams(lp)
            }
        }
    }
}