package com.socialplay.gpark.ui.pay

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.pay.CommonPayParams
import com.socialplay.gpark.data.model.pay.IAPConstants
import com.socialplay.gpark.data.model.pay.SubsData
import com.socialplay.gpark.databinding.FragmentPayBinding
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2022/06/14
 *     desc   :
 *
 */
class PayFragment : BaseFragment<FragmentPayBinding>() {
    private val payInteractor: IPayInteractor = GlobalContext.get().get()// 不能用inject 需要即时初始化

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentPayBinding? {
        return FragmentPayBinding.inflate(inflater, container, false)
    }

    override fun init() {
        binding.tvPay.setOnAntiViolenceClickListener {
            //点击支付
            if (binding.etProduct.text.toString().isEmpty()) {
                ToastUtil.showLong(requireContext(), R.string.enter_item_id)
                return@setOnAntiViolenceClickListener
            }
            val type = binding.etType.text.toString()
            //判断google play商店版本是否支持这种类型,不支持走旧版本的查询方式
            viewLifecycleOwner.lifecycleScope.launch {
                val scence =  binding.senceText.text.toString()
                payInteractor.startPay(
                    requireActivity(),
                    CommonPayParams(
                        "",
                        binding.etProduct.text.toString(),
                        null,
                        null,
                        binding.senceText.text.toString(),
                        if (scence.equals(IAPConstants.IAP_SCENE_PG_COIN)) null else binding.etPlan.text.toString(),
                        0,
                        IAPConstants.IAP_SCENE_CODE_PG_COIN,
                        ""
                    ),
                    null,
                    "PayFragment",
                )
            }
        }
        binding.tvGetProduct.setOnAntiViolenceClickListener {
            viewLifecycleOwner.lifecycleScope.launch {
                payInteractor.loadProducts(IAPConstants.IAP_SCENE_PG_COIN,null){}
            }
        }
        binding.tvGetSubProduct.setOnAntiViolenceClickListener {
            viewLifecycleOwner.lifecycleScope.launch {
                val list = arrayListOf<SubsData>( SubsData(binding.etProduct.text.toString(), binding.etPlan.text.toString()))
                payInteractor.loadProducts(IAPConstants.IAP_SCENE_VIP_PLUS, list){}
            }
        }
        payInteractor.products.observe(viewLifecycleOwner){
            Timber.d("productList%s", GsonUtil.safeToJson(it))
        }

        payInteractor.subsProducts.observe(viewLifecycleOwner){
            Timber.d("subsProductsList%s", GsonUtil.safeToJson(it))
        }
    }

    override fun loadFirstData() {

    }

    override fun getFragmentName(): String = "test pay"
}