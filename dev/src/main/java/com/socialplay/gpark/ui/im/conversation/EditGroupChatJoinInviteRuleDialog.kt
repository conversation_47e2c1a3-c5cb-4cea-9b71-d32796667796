package com.socialplay.gpark.ui.im.conversation

import android.os.Bundle
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.navArgs
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.groupchat.GroupChatDetailInfo
import com.socialplay.gpark.databinding.DialogEditGroupChatJoinInviteRuleBinding
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.property.viewBinding

class EditGroupChatJoinInviteRuleDialog : BaseDialogFragment() {
    companion object {
        const val REQUEST_KEY_GROUP_EDIT_JOIN_INVITE_RULE =
            "request_key_group_edit_join_invite_rule"
        const val KEY_GROUP_EDIT_JOIN_INVITE_RULE = "key_group_edit_join_invite_rule"
    }
    private val vm: EditGroupChatInfoViewModel by fragmentViewModel()
    private val args by navArgs<EditGroupChatJoinInviteRuleDialogArgs>()

    override val binding: DialogEditGroupChatJoinInviteRuleBinding by viewBinding(
        DialogEditGroupChatJoinInviteRuleBinding::inflate
    )

    override fun maxWidth(): Int {
        val ctx = context
        ctx ?: return 0
        return (ScreenUtil.getScreenWidth(ctx) * 2 / 3).coerceAtLeast(dp(343))
    }

    private var joinType: Int = 0
        set(value) {
            field = value
            binding.ivJoinTypeEveryone.isSelected = value == GroupChatDetailInfo.JOIN_TYPE_EVERYONE
            binding.ivJoinTypeApply.isSelected = value == GroupChatDetailInfo.JOIN_TYPE_APPLY
            updateSaveBtn()
        }
    private var inviteType: Int = 0
        set(value) {
            field = value
            binding.ivInviteTypeEveryone.isSelected =
                value == GroupChatDetailInfo.INVITE_TYPE_EVERYONE
            binding.ivInviteTypeManager.isSelected =
                value == GroupChatDetailInfo.INVITE_TYPE_MANAGER
            updateSaveBtn()
        }

    override fun init() {
        joinType = args.currentJoinType
        inviteType = args.currentInviteType

        binding.root.setOnClickListener {
            dismissAllowingStateLoss()
        }
        binding.dialogLayout.isClickable = true
        binding.layoutJoinTypeEveryone.setOnClickListener {
            joinType = GroupChatDetailInfo.JOIN_TYPE_EVERYONE
        }
        binding.layoutJoinTypeApply.setOnClickListener {
            joinType = GroupChatDetailInfo.JOIN_TYPE_APPLY
        }
        binding.layoutInviteTypeEveryone.setOnClickListener {
            inviteType = GroupChatDetailInfo.INVITE_TYPE_EVERYONE
        }
        binding.layoutInviteTypeManager.setOnClickListener {
            inviteType = GroupChatDetailInfo.INVITE_TYPE_MANAGER
        }
        binding.tvSave.setOnAntiViolenceClickListener {
            vm.editGroupChatJoinInviteRule(args.groupId, joinType, inviteType)
        }
        binding.tvCancel.setOnAntiViolenceClickListener {
            dismissAllowingStateLoss()
        }
        vm.onAsync(
            EditGroupChatInfoModelState::editResult,
            deliveryMode = uniqueOnly(),
            onFail = { _, _ ->
                ToastUtil.showShort(R.string.toast_edit_group_join_invite_rules_failed)
            },
            onLoading = {

            },
            onSuccess = { groupDetailResult ->
                if (groupDetailResult.succeeded) {
                    dismissWithResult(groupDetailResult.data)
                } else {
                    ToastUtil.showShort(
                        groupDetailResult.message
                            ?: getString(R.string.toast_edit_group_join_invite_rules_failed)
                    )
                }
            })
    }

    private fun dismissWithResult(groupDetail: GroupChatDetailInfo?) {
        if (groupDetail != null) {
            setFragmentResult(REQUEST_KEY_GROUP_EDIT_JOIN_INVITE_RULE, Bundle().apply {
                putParcelable(
                    KEY_GROUP_EDIT_JOIN_INVITE_RULE, groupDetail.simplifiedMembers()
                )
            })
        }
        dismissAllowingStateLoss()
    }

    private fun updateSaveBtn() {
        val canSave = GroupChatDetailInfo.isJoinTypeOk(joinType)
                && GroupChatDetailInfo.isInviteTypeOk(inviteType)
        if (canSave) {
            binding.tvSave.isEnabled = true
            binding.tvSave.setBackgroundResource(R.drawable.bg_ffef30_round_40)
            binding.tvSave.setTextColorByRes(R.color.color_1A1A1A)
        } else {
            binding.tvSave.isEnabled = false
            binding.tvSave.setBackgroundResource(R.drawable.bg_f0f0f0_corner_40)
            binding.tvSave.setTextColorByRes(R.color.color_999999)
        }
    }
}