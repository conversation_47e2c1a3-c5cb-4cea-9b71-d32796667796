package com.socialplay.gpark.ui.gamedetail.unify

import android.graphics.Rect
import android.os.Bundle
import android.os.Parcelable
import android.view.View
import androidx.core.view.doOnNextLayout
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.meta.box.biz.friend.model.LabelInfo
import com.meta.lib.mwbiz.MWLifeCallback
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.community.IPostCommentDraftProvider
import com.socialplay.gpark.data.model.community.PostCommentDraft
import com.socialplay.gpark.data.model.community.PostMedia
import com.socialplay.gpark.data.model.editor.UgcDetailInfo
import com.socialplay.gpark.data.model.game.OperationInfo
import com.socialplay.gpark.data.model.post.AddPostCommentReplyTarget
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.data.model.reportBlock.ReportType
import com.socialplay.gpark.data.model.share.ShareFeature
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.editor.EditorGameLaunchHelper
import com.socialplay.gpark.function.editor.IEditorLaunchCallback
import com.socialplay.gpark.function.editor.LaunchOverResult
import com.socialplay.gpark.function.mw.MWGameStartScenes
import com.socialplay.gpark.function.mw.launch.exception.TSEngineVersionNotMatchException
import com.socialplay.gpark.function.mw.launch.exception.TSUserCancelledException
import com.socialplay.gpark.function.mw.launch.ui.TSEngineNotMatchDialog
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.gamedetail.GameDetailAnalyticsObserver
import com.socialplay.gpark.ui.gamereview.dialog.GameAppraiseAdapter
import com.socialplay.gpark.ui.imgpre.ImgPreDialogFragment
import com.socialplay.gpark.ui.post.comment.ArticleCommentInputDialog
import com.socialplay.gpark.ui.post.comment.ArticleCommentInputDialogParams
import com.socialplay.gpark.ui.profile.BaseProfilePage
import com.socialplay.gpark.ui.reportBlock.ReportReasonDialog
import com.socialplay.gpark.ui.reportBlock.ReportSuccessDialogAnalyticsParams
import com.socialplay.gpark.ui.share.GlobalShareDialog
import com.socialplay.gpark.ui.view.ExpandableTextView
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.cancelAnimationIfAnimating
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setFragmentResultListenerByHostFragment
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import com.socialplay.gpark.util.ifNullOrEmpty
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/22
 *     desc   :
 * </pre>
 */
@Parcelize
data class UgcGameDetailFragmentArgs(
    val ugcId: String,
    val parentId: String,
    val resIdBean: ResIdBean,
    val targetCommentId: String?,
    val targetReplyId: String?,
    val source: String
) : Parcelable {
    companion object {
        const val DEFAULT_SOURCE = "other"
    }
}

class UgcGameDetailFragment : BaseGameDetailCommonFragment(), IPostCommentDraftProvider {

    companion object {
        const val TRACK_TAG = "game_review"
    }

    override val gameId: String
        get() = args.ugcId

    override val gameType = GAME_TYPE_UGC

    override val enableShare: Boolean
        get() = PandoraToggle.enableShareUgc

    private val vm: UgcGameDetailViewModel by fragmentViewModel()
    private val args by args<UgcGameDetailFragmentArgs>()

    private val analyticsObserve: GameDetailAnalyticsObserver by lazy { GameDetailAnalyticsObserver() }

    private val pageType = 1L

    override val itemListener = ItemListener()

    private val operationController by lazy { buildOperationController() }

    private var editorGameLaunchHelper: EditorGameLaunchHelper? = null

    private var contentHeight = 0
    private var descExpandState = ExpandableTextView.STATE_SHRINK

    private val gameStartScenes by lazy { MWGameStartScenes(this) }

    private val editorDownloadCallback: IEditorLaunchCallback = object : IEditorLaunchCallback {
        override fun onLaunchOver(result: LaunchOverResult) {
            Timber.d("checkcheck onLaunchOver, ${result.launchSuccess}, ${if (!result.launchSuccess) result.msg else null}")
            if (isBindingAvailable()) {
                if (result.e is TSUserCancelledException) {
                    hideLoadingUI(result.launchSuccess, result.msg, result.needGoMineLocal)
                } else if (result.e is TSEngineVersionNotMatchException) {
                    TSEngineNotMatchDialog.show(this@UgcGameDetailFragment, result.gameInfo?.icon)
                    hideLoadingUI(result.launchSuccess, "", result.needGoMineLocal)
                } else {
                    hideLoadingUI(result.launchSuccess, result.msg, result.needGoMineLocal)
                }
            }
        }

        override fun onChecking(
            gameInfo: GameDetailInfo?,
            id: String?,
            path: String?,
            type: String?
        ) {
            Timber.d("checkcheck onLaunchingGame type:$type")
            if (isBindingAvailable() && type == EditorGameLaunchHelper.TYPE_TEMPLATE) {
                showLoadingUI()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initView()
        initData()
        initTs()
    }

    private fun initView() {
        if (contentHeight != 0) {
            binding.clTop.minHeight = contentHeight
        }

        binding.lv.showLoading()
        binding.dpbEnter2.setCurrentText(getString(R.string.play))
        binding.dpbEnter2.setOnAntiViolenceClickListener {
            clickStartGame()
        }
        binding.vAuthorClick.setOnAntiViolenceClickListener {
            handleUserClick()
        }
        binding.tvFollowBtn.setOnAntiViolenceClickListener {
            vm.follow()
        }
        binding.layerLike.setOnAntiViolenceClickListener {
            val type = if (vm.isLike) {
                Analytics.track(
                    EventConstants.GAME_DETAIL_PAGE_LIKE_CANCEL,
                    "gameid" to gameId,
                    "creatortype" to gameType
                )
                "1"
            } else {
                Analytics.track(
                    EventConstants.GAME_DETAIL_PAGE_LIKE,
                    "gameid" to gameId,
                    "creatortype" to gameType
                )
                DialogShowManager.triggerLike(this)
                "2"
            }
            Analytics.track(
                EventConstants.UGC_DETAIL_PAGE_LIKE,
                "ugcid" to args.ugcId,
                "parentid" to vm.parentId,
                "type" to type
            )
            vm.like()
        }
        binding.ivMyAvatar.setOnAntiViolenceClickListener {
            showReplyDialog(AddPostCommentReplyTarget(args.ugcId, vm.replyTargetName))
        }
        binding.tvReplyHint.setOnAntiViolenceClickListener {
            showReplyDialog(AddPostCommentReplyTarget(args.ugcId, vm.replyTargetName))
        }
        binding.tvReplyHint2.setOnAntiViolenceClickListener {
            showReplyDialog(AddPostCommentReplyTarget(args.ugcId, vm.replyTargetName), source = 3)
        }
        binding.ivEmojiBtn.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.ugcId, vm.replyTargetName),
                showEmoji = true
            )
        }
        binding.ivEmojiBtn2.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.ugcId, vm.replyTargetName),
                showEmoji = true
            )
        }
        binding.ivImageBtn.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.ugcId, vm.replyTargetName),
                showImage = true
            )
        }
        binding.ivImageBtn2.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.ugcId, vm.replyTargetName),
                showImage = true
            )
        }
        binding.rvNotice.setController(operationController)
        binding.tvDescription.setExpandListener(object : ExpandableTextView.OnExpandListener {
            override fun onExpand(view: ExpandableTextView) {
                Analytics.track(
                    EventConstants.DETAIL_DESCRIPTION_MORE_CLICK,
                    "gameid" to gameId,
                    "creatortype" to gameType
                )
                descExpandState = ExpandableTextView.STATE_EXPAND
            }

            override fun onShrink(view: ExpandableTextView) {
                descExpandState = ExpandableTextView.STATE_SHRINK
            }
        })
        binding.layerComment.setOnAntiViolenceClickListener {
            showReplyDialog(AddPostCommentReplyTarget(args.ugcId, vm.replyTargetName), source = 2)
        }
        binding.ulv.setListener(viewLifecycleOwner) {
            UserLabelView.showDescDialog(this, it)
        }
    }

    private fun initData() {
        setFragmentResultListenerByHostFragment(
            BaseProfilePage.RESULT_FOLLOW,
            viewLifecycleOwner
        ) { _, bundle ->
            val uuid = bundle.getString(BaseProfilePage.KEY_UUID)
            if (vm.authorId == uuid) {
                vm.follow(bundle.getBoolean(BaseProfilePage.KEY_IS_FOLLOW))
            }
        }
        vm.accountInteractor.accountLiveData.observe(viewLifecycleOwner) {
            updateMyAvatar(it?.portrait)
        }
        vm.onAsync(UgcGameDetailState::detail, onFail = { _ ->
            binding.lv.showError()
            binding.ivMoreBtn.gone()
        }) {
            vm.initCommentList()
            initDetail(it)
            sparkVm.init(
                it.id,
                it.userUuid.orEmpty(),
                it.myTippedCoins,
                it.totalTippedCoins,
                it.myTippedTimes,
            )
        }
        vm.onEach(UgcGameDetailState::likeStatus) { (isLike, likeCount) ->
            if (isLike) {
                binding.tvLike.setTextColor(getColorByRes(R.color.color_4AB4FF))
                binding.ivLike.invisible()
                binding.lavLikeAnim.visible()
                if (!likeAnim) {
                    binding.lavLikeAnim.progress = 1.0f
                } else {
                    binding.lavLikeAnim.progress = 0.0f
                    binding.lavLikeAnim.playAnimation()
                }
            } else {
                binding.tvLike.setTextColor(getColorByRes(R.color.color_1A1A1A))
                binding.ivLike.visible()
                binding.lavLikeAnim.cancelAnimationIfAnimating()
                binding.lavLikeAnim.gone()
            }
            binding.tvLike.text = UnitUtil.formatKMCount(likeCount)
            if (likeCount > -1) {
                likeAnim = true
            }
        }
        vm.onEach(
            UgcGameDetailState::commentList,
            UgcGameDetailState::commentListLoadMore
        ) { commentList, loadMore ->
            updateCommentStatus(commentList, loadMore)
            if (commentList is Success && loadMore is Success) {
                Analytics.track(
                    EventConstants.UGC_DETAIL_PAGE_REVIEW_SHOW,
                    "gameid" to args.ugcId
                )
            }
        }
        vm.onEach(UgcGameDetailState::follow) {
            updateFollow(it)
        }
        vm.onEach(UgcGameDetailState::operationList) {
            if (it().isNullOrEmpty()) {
                binding.rvNotice.gone()
            } else {
                binding.rvNotice.visible()
            }
        }
        vm.onEach(
            UgcGameDetailState::sortType,
            UgcGameDetailState::filterType
        ) { sortType, filterType ->
            updateSortTypeText(sortType, filterType)
        }
        vm.onEach(UgcGameDetailState::isCommentListRefresh, deliveryMode = uniqueOnly()) {
            if (it.invoke() == true) {
                when (it) {
                    is Loading -> {
                        animateCommentRefresh(dp(52))
                    }

                    is Success -> {
                        animateCommentRefresh(0)
                    }

                    else -> {
                        animateCommentRefresh(0)
                    }
                }
            }
        }
        vm.setupRefreshLoading(
            UgcGameDetailState::detail,
            binding.lv,
            binding.mrl
        ) {
            vm.getUgcDetailInfo(true)
            vm.getOperationList()
            getCommentList(true)
        }
        vm.registerToast(UgcGameDetailState::toast)
        vm.registerAsyncErrorToast(UgcGameDetailState::detail)
        vm.registerAsyncErrorToast(UgcGameDetailState::commentListLoadMore)
        vm.registerAsyncErrorToast(UgcGameDetailState::addCommentResult)
        vm.registerAsyncErrorToast(UgcGameDetailState::addReplyResult)
    }

    private fun initTs() {
        editorGameLaunchHelper = EditorGameLaunchHelper(editorDownloadCallback)
        editorGameLaunchHelper?.init(this)
        val invoke = { data: Pair<String, String> ->
            Timber.d("checkcheck onReceivedStartGame errorMsg:${data.first}")
            if (isBindingAvailable()) {
                viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
                    if (data.first.isNullOrEmpty()) {
                        gameStartScenes.show()
                    } else {
                        hideLoadingUI(false, null, false)
                    }
                }
            }
        }
        MWLifeCallback.startGame.observe(viewLifecycleOwner, false, observer = invoke)
        MWLifeCallback.startLocalGame.observe(viewLifecycleOwner, false, observer = invoke)
    }

    private fun initDetail(detail: UgcDetailInfo) {
        binding.lv.hide()
        binding.ivMoreBtn.visible()
        binding.tvGameName.text = detail.ugcGameName
        binding.tvTitleBarGameName.text = detail.ugcGameName
        binding.tvDescription.updateForRecyclerView(
            detail.ugcGameDesc.ifNullOrEmpty { getString(R.string.ugc_detail_default_desc) },
            screenWidth - dp(32),
            descExpandState
        )
        updatePv(detail.pageView)
        updateTime(detail.updateTime.coerceAtLeast(detail.createTime))
        binding.ivGameBannerBg.gone()
        glide?.run {
            load(detail.banner).placeholder(R.drawable.placeholder_corner_12)
                .centerCrop()
                .into(binding.ivGameBanner)
        }
        // 作者信息
        binding.ulv.show(
            detail.tagIds,
            detail.labelInfo,
            isCreator = true,
            glide = glide
        )
        val isMe = vm.isMe(detail.userUuid)
        visibleList(
            binding.tvFollowBtn,
            popupBinding.mtvAuthorOnly,
            popupBinding.vAuthorOnlyClick,
            popupBinding.vDivider4,
            visible = !isMe
        )
        glide?.run {
            load(detail.userIcon).placeholder(R.drawable.placeholder_circle)
                .error(R.drawable.placeholder_circle)
                .circleCrop()
                .into(binding.ivAvatar)
        }
        binding.tvUsername.text = detail.userName
        binding.tvPortfolio.text = SpannableHelper.Builder()
            .text(getString(R.string.game_detail_page_maps_count))
            .textAppearance(context, R.style.MetaTextView_S12_PoppinsMedium500)
            .colorRes(R.color.color_999999)
            .text("${detail.userReleaseCount}")
            .textAppearance(context, R.style.MetaTextView_S12_PoppinsRegular400)
            .colorRes(R.color.color_999999)
            .build()
        binding.tvUserId.text = SpannableHelper.Builder()
            .text(getString(R.string.game_detail_page_user_id))
            .textAppearance(context, R.style.MetaTextView_S12_PoppinsMedium500)
            .colorRes(R.color.color_999999)
            .text(detail.metaNum ?: "unknown")
            .textAppearance(context, R.style.MetaTextView_S12_PoppinsRegular400)
            .colorRes(R.color.color_999999)
            .build()
        if (isMe) {
            binding.ivMoreBtn.setImageResource(R.drawable.ic_detail_more)
        } else {
            binding.ivMoreBtn.setImageResource(R.drawable.ic_detail_share)
            binding.ivMoreBtn.setPaddingEx(top = dp(8), bottom = dp(8))
        }
        //MW引擎不兼容提示

        updateMwCompatibilityTips(detail.mwTip)
        binding.clTop.doOnNextLayout {
            binding.clTop.minHeight = 0
        }
    }

    private fun handleUserClick() {
        Analytics.track(
            EventConstants.UGC_DETAIL_PAGE_PROFILE,
            "ugcid" to args.ugcId,
            "parentid" to vm.parentId,
        )
        val uuid = vm.detail?.userUuid ?: return
        MetaRouter.Profile.other(requireParentFragment(), uuid, "9", checkFollow = true)
    }

    private fun clickStartGame() {
        Analytics.track(
            EventConstants.UGC_DETAIL_PAGE_CLICK,
            "ugcid" to args.ugcId,
            "parentid" to vm.parentId,
        )
        vm.detail?.let { detail ->
            editorGameLaunchHelper?.startUgcGame(
                this,
                detail,
                args.resIdBean
            )
        }
    }

    override fun epoxyController() = simpleController(
        vm,
        UgcGameDetailState::commentList,
        UgcGameDetailState::commentListLoadMore,
        UgcGameDetailState::uniqueTag,
        UgcGameDetailState::showCommentPinRedDot,
    ) { comments, loadMore, uniqueTag, showCommentPinRedDot ->
        buildCommentController(comments, loadMore, uniqueTag, showCommentPinRedDot)
    }

    private fun buildOperationController() = simpleController(
        vm,
        UgcGameDetailState::operationList
    ) {
        it()?.forEachIndexed { index, item ->
            gameOperationItem(item, index, itemListener)
        }
    }

    override fun getPageName() = PageNameConstants.FRAGMENT_UGC_DETAIL

    override fun onShareCountIncrease(data: ShareRawData) {
        if (data.scene == ShareHelper.SCENE_UGC_DETAIL && data.ugcGame?.id == args.ugcId) {
            vm.shareCountIncrement()
        }
    }

    private fun handleOperateComment(
        view: View,
        comment: PostComment,
        commentPosition: Int,
        showRedDot: Boolean
    ) {
        handleOperationHelper(
            view,
            comment = comment,
            showRedDot = showRedDot,
            commentPosition = commentPosition
        )
    }

    private fun handleOperateReply(
        view: View,
        reply: PostReply,
        replyPosition: Int,
        commentPosition: Int,
        isAuthorReply: Boolean
    ) {
        handleOperationHelper(
            view,
            reply = reply,
            commentPosition = commentPosition,
            replyPosition = replyPosition,
            isAuthorReply = isAuthorReply
        )
    }

    private fun handleOperationHelper(
        view: View,
        comment: PostComment? = null,
        showRedDot: Boolean = false,
        reply: PostReply? = null,
        commentPosition: Int = 0,
        replyPosition: Int = 0,
        isAuthorReply: Boolean = false
    ) {
        Analytics.track(
            EventConstants.UGC_GAME_REVIEW_SET_SHOW,
            "gameid" to args.ugcId,
            "reviewid" to (comment?.commentId ?: reply?.replyId.orEmpty()),
            "reviewtype" to if (comment != null) 1L else 2L,
            "pagetype" to pageType
        )

        val isMe = vm.isMe(comment?.uid ?: reply?.uid)
        val isCreator = vm.isCreator(vm.myUuid)

        if (comment != null && isCreator) {
            if (comment.top == true) {
                visibleList(
                    popupBindingComment.mtvPin,
                    popupBindingComment.vPinClick,
                    popupBindingComment.vPinRedDot,
                    popupBindingComment.vDivider1,
                    visible = false
                )
                visibleList(
                    popupBindingComment.mtvUnpin,
                    popupBindingComment.vUnpinClick,
                    popupBindingComment.vDivider2,
                    visible = true
                )
            } else {
                visibleList(
                    popupBindingComment.mtvPin,
                    popupBindingComment.vPinClick,
                    popupBindingComment.vDivider1,
                    visible = true
                )
                popupBindingComment.vPinRedDot.visible(showRedDot)
                visibleList(
                    popupBindingComment.mtvUnpin,
                    popupBindingComment.vUnpinClick,
                    popupBindingComment.vDivider2,
                    visible = false
                )
            }
        } else {
            visibleList(
                popupBindingComment.mtvPin,
                popupBindingComment.vPinClick,
                popupBindingComment.vPinRedDot,
                popupBindingComment.vDivider1,
                popupBindingComment.mtvUnpin,
                popupBindingComment.vUnpinClick,
                popupBindingComment.vDivider2,
                visible = false
            )
        }
        visibleList(
            popupBindingComment.mtvReport,
            popupBindingComment.vReportClick,
            popupBindingComment.vDivider3,
            visible = !isMe
        )
        visibleList(
            popupBindingComment.mtvDelete,
            popupBindingComment.vDeleteClick,
            popupBindingComment.vDivider4,
            visible = isMe || isCreator
        )
        val showCopy = if (comment != null && comment.content.isNullOrEmpty()) {
            false
        } else if (reply != null && reply.content.isNullOrEmpty()) {
            false
        } else {
            true
        }
        visibleList(
            popupBindingComment.mtvCopy,
            popupBindingComment.vCopyClick,
            visible = showCopy
        )
        if (!showCopy) {
            visibleList(
                popupBindingComment.vDivider1,
                popupBindingComment.vDivider2,
                visible = false
            )
        }

        popupBindingComment.vCopyClick.setOnAntiViolenceClickListener {
            val reviewId: String = comment?.commentId ?: reply?.replyId.orEmpty()
            val reviewType: Long = if (comment != null) 1L else 2L
            Analytics.track(
                EventConstants.UGC_GAME_REVIEW_COPY_CLICK,
                "gameid" to args.ugcId,
                "reviewid" to reviewId,
                "reviewtype" to reviewType,
                "pagetype" to pageType
            )
            viewLifecycleOwner.lifecycleScope.launch {
                ClipBoardUtil.setClipBoardContent(
                    comment?.content ?: reply?.content,
                    requireContext(),
                    if (comment != null) "Comment" else "Reply"
                )
            }

            toast(R.string.copied_to_clipboard)
            popupWindowComment.dismiss()
        }
        popupBindingComment.vPinClick.setOnAntiViolenceClickListener {
            if (comment != null) {
                Analytics.track(
                    EventConstants.UGC_GAME_REVIEW_TOP_CLICK,
                    "gameid" to args.ugcId,
                    "reviewid" to comment.commentId,
                    "toptype" to 0L,
                    "pagetype" to pageType
                )
                Analytics.track(
                    EventConstants.GAME_REVIEW_PIN_CLICK,
                    "gameid" to gameId,
                    "creatortype" to gameType
                )
                vm.pinComment(comment, commentPosition, showRedDot)
            }
            popupWindowComment.dismiss()
        }
        popupBindingComment.vUnpinClick.setOnAntiViolenceClickListener {
            if (comment != null) {
                Analytics.track(
                    EventConstants.UGC_GAME_REVIEW_TOP_CLICK,
                    "gameid" to args.ugcId,
                    "reviewid" to comment.commentId,
                    "toptype" to 1L,
                    "pagetype" to pageType
                )
                Analytics.track(
                    EventConstants.GAME_CANCEL_REVIEW_PIN_CLICK,
                    "gameid" to gameId,
                    "creatortype" to gameType
                )
                vm.pinComment(comment, commentPosition, showRedDot)
            }
            popupWindowComment.dismiss()
        }
        popupBindingComment.vReportClick.setOnAntiViolenceClickListener {
            if (comment != null) {
                goReport(comment.commentId, ReportType.UgcReview)
            } else if (reply != null) {
                goReport(reply.replyId, ReportType.UgcReply)
            }
            popupWindowComment.dismiss()
        }
        popupBindingComment.vDeleteClick.setOnAntiViolenceClickListener {
            ConfirmDialog.Builder(this)
                .image(R.drawable.dialog_icon_cry)
                .content(
                    getString(
                        R.string.delete_confirm,
                        if (comment != null) {
                            getString(R.string.comment)
                        } else {
                            getString(R.string.reply)
                        }
                    )
                )
                .cancelBtnTxt(getString(R.string.dialog_cancel))
                .confirmBtnTxt(getString(R.string.delete_cap))
                .isRed(true)
                .confirmCallback {
                    if (comment != null) {
                        vm.deleteComment(comment, commentPosition)
                    } else if (reply != null) {
                        vm.deleteReply(
                            reply,
                            replyPosition,
                            commentPosition,
                            isAuthorReply
                        )
                    }
                }
                .show()
            popupWindowComment.dismiss()
        }

        popupBindingComment.cv.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        val rect = Rect()
        view.getGlobalVisibleRect(rect)
        val dp5 = dp(5)
        val dp16 = dp(16)
        val x = -popupBindingComment.cv.measuredWidth + dp(8)
        val rawBottom = rect.top - dp5 + popupBindingComment.cv.measuredHeight
        binding.root.getGlobalVisibleRect(rect)
        val y = if (rawBottom > rect.bottom) {
            -dp16 - popupBindingComment.cv.measuredHeight + dp5
        } else {
            -dp16 - view.height.coerceAtLeast(view.measuredHeight) - dp5
        }
        popupWindowComment.showAsDropDownByLocation(view, x, y, autoHeight = false)
    }

    private fun goReport(reportId: String, reportType: ReportType) {
        Analytics.track(
            EventConstants.EVENT_REVIEW_REPORT_CLICK,
            "gameid" to gameId,
            "type" to (if (reportType == ReportType.UgcReview) {
                GameAppraiseAdapter.AppraiseItemListener.TYPE_COMMENT
            } else {
                GameAppraiseAdapter.AppraiseItemListener.TYPE_REPLAY
            })
        )
        MetaRouter.Report.postReport(this, reportId, reportType, gameId = args.ugcId) {
            if (it) {
                val analyticsParams = when (reportType) {
                    ReportType.UgcReview -> {
                        ReportSuccessDialogAnalyticsParams.GameComment(
                            gameId = gameId,
                            commentId = reportId,
                        )
                    }

                    ReportType.UgcReply -> {
                        ReportSuccessDialogAnalyticsParams.GameCommentReply(
                            gameId = gameId,
                            replyId = reportId,
                        )
                    }

                    else -> {
                        null
                    }
                }
                ReportReasonDialog.showReportSuccessDialog(this, analyticsParams)
                Analytics.track(
                    EventConstants.UGC_GAME_REVIEW_REPORT_SUCCESS,
                    "gameid" to args.ugcId,
                    "reviewid" to reportId,
                    "reviewtype" to if (reportType == ReportType.UgcReview) 1L else 2L
                )
            }
        }
    }

    /**
     * @param source 来源: 1: 默认; 2: 点击底部评论计数图标; 3: 点击底部评论输入栏
     */
    private fun showReplyDialog(
        target: AddPostCommentReplyTarget,
        showEmoji: Boolean = false,
        showImage: Boolean = false,
        source: Int = 1
    ) {
        val replyType: Long
        val reviewId: String
        val type: Int
        if (target.isTargetComment) {
            Analytics.track(
                EventConstants.GAME_MORE_REVIEW_WRITE_CLICK,
                "gameid" to gameId,
                "creatortype" to gameType
            )
            replyType = 0L
            reviewId = target.asComment.commentId
            type = ArticleCommentInputDialog.TYPE_COMMENT_COMMENT
        } else if (target.isTargetReply) {
            Analytics.track(
                EventConstants.GAME_MORE_REVIEW_WRITE_CLICK,
                "gameid" to gameId,
                "creatortype" to gameType
            )
            replyType = 1L
            reviewId = target.asReply.replyId
            type = ArticleCommentInputDialog.TYPE_COMMENT_REPLAY
        } else {
            Analytics.track(
                EventConstants.GAME_FIRST_REVIEW_WRITE_CLICK,
                "source" to source,
                "gameid" to gameId,
                "creatortype" to gameType
            )
            replyType = -1L
            reviewId = args.ugcId
            type = ArticleCommentInputDialog.TYPE_COMMENT_ARTICLE
        }
        if (replyType != -1L) {
            Analytics.track(
                EventConstants.UGC_GAME_REVIEW_REPLAY_CLICK,
                "reviewid" to reviewId,
                "gameid" to args.ugcId,
                "replaytype" to replyType,
                "pagetype" to pageType
            )
        }
        vm.setReplyTarget(target)
        ArticleCommentInputDialog.show(
            this,
            target.toNickname,
            gameId,
            null,
            type,
            0.7f,
            showEmoji,
            showImage,
            getPageName(),
            ArticleCommentInputDialogParams.CONTENT_TYPE_UGC_DETAIL,
            1,
            true,
            pageName = getPageName()
        ) {
            if (it == null || !it.valid) return@show
            if (target.isTargetPost) {
                vm.addCommentViaNet(it)
            } else {
                vm.addReplyViaNet(it)
            }
        }
    }

    private fun trackLike(id: String, type: Long) {
        Analytics.track(
            EventConstants.UGC_GAME_REVIEW_LIKE_CLICK,
            "reviewid" to id,
            "gameid" to args.ugcId,
            "reviewtype" to type,
            "pagetype" to pageType
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        vm.apiMonitor(
            this,
            UgcGameDetailState::detail
        )
        analyticsObserve.sendEnterGameDetailAnalytic(
            args.ugcId,
            "",
            -1,
            "UGC",
            args.resIdBean,
            creatorType = gameType
        )
        operationController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        operationController.onSaveInstanceState(outState)
    }

    override fun getCommentList(isRefresh: Boolean) {
        vm.getCommentList(isRefresh)
    }

    override fun initCommentList() {
        vm.initCommentList()
    }

    override fun onMoreClick() {
        val state = vm.oldState
        val detail = state.detail() ?: return
        GlobalShareDialog.show(
            childFragmentManager,
            ShareRawData.ugc(
                detail.copy(loveQuantity = state.likeStatus.second),
                args.resIdBean.getReqId()
            ),
            requestKey = vm.requestKey,
            features = listOf(
                ShareFeature(
                    FEAT_REPORT,
                    R.drawable.ic_share_feat_feedback,
                    titleRes = R.string.feedback
                )
            )
        )
    }

    /**
     * 隐藏加载界面
     */
    private fun hideLoadingUI(launchSuccess: Boolean, msg: String?, needGoMine: Boolean) {
        viewLifecycleOwner.lifecycleScope.launchWhenResumed {
            if (!launchSuccess) {
                toast(msg ?: getString(R.string.verse_download_failed))
                gameStartScenes.hide()
            }
            if (needGoMine && PandoraToggle.isUgcBackup) {
                MetaRouter.MobileEditor.creation(this@UgcGameDetailFragment, initTab = 1)
            }
        }
    }

    /**
     * 展示加载界面
     */
    private fun showLoadingUI() {
        gameStartScenes.show()
    }

    override fun onDestroyView() {
        contentHeight = binding.clTop.height
        editorGameLaunchHelper?.onDestroyHelper()
        editorGameLaunchHelper = null
        super.onDestroyView()
    }

    override fun saveDraft(data: PostCommentDraft) {
        vm.saveDraft(data)
    }

    override fun retrieveDraft(): PostCommentDraft? {
        return vm.retrieveDraft()
    }

    override fun removeDraft() {
        vm.removeDraft()
    }

    override fun updateSortType(sortType: Int) {
        super.updateSortType(sortType)
        vm.updateSortType(sortType)
    }

    override fun updateFilterType(filterType: Int) {
        super.updateFilterType(filterType)
        vm.updateFilterType(filterType)
    }

    inner class ItemListener : IGameDetailCommonListener {
        override fun isMe(uid: String?): Boolean {
            return vm.isMe(uid)
        }

        override fun isCreator(uid: String?): Boolean {
            return vm.isCreator(uid)
        }

        override fun iAmCreator(): Boolean {
            return vm.iAmCreator
        }

        override fun goUserPage(uid: String?) {
            if (!uid.isNullOrBlank()) {
                MetaRouter.Profile.other(this@UgcGameDetailFragment, uid, TRACK_TAG)
            }
        }

        override fun operateComment(
            view: View,
            comment: PostComment,
            commentPosition: Int,
            showRedDot: Boolean
        ) {
            handleOperateComment(view, comment, commentPosition, showRedDot)
        }

        override fun likeComment(comment: PostComment, commentPosition: Int) {
            if (!comment.isLike) {
                Analytics.track(
                    EventConstants.C_GAME_REVIEW_LIKE_CLICK,
                    "type" to 1,
                    "creatortype" to gameType,
                    "gameid" to gameId
                )
                DialogShowManager.triggerLike(this@UgcGameDetailFragment)
            } else {
                Analytics.track(
                    EventConstants.C_GAME_REVIEW_LIKE_CANCEL_CLICK,
                    "type" to 1,
                    "creatortype" to gameType,
                    "gameid" to gameId
                )
            }
            trackLike(comment.commentId, 1L)
            vm.likeComment(comment, commentPosition)
        }

        override fun reply2Comment(comment: PostComment, commentPosition: Int) {
            showReplyDialog(AddPostCommentReplyTarget(comment, commentPosition))
        }

        override fun operateReply(
            view: View,
            reply: PostReply,
            replyPosition: Int,
            commentPosition: Int,
            isAuthorReply: Boolean
        ) {
            handleOperateReply(view, reply, replyPosition, commentPosition, isAuthorReply)
        }

        override fun likeReply(
            reply: PostReply,
            replyPosition: Int,
            commentPosition: Int,
            isAuthorReply: Boolean
        ) {
            if (!reply.isLike) {
                Analytics.track(
                    EventConstants.C_GAME_REVIEW_LIKE_CLICK,
                    "type" to 2,
                    "creatortype" to gameType,
                    "gameid" to gameId
                )
                DialogShowManager.triggerLike(this@UgcGameDetailFragment)
            } else {
                Analytics.track(
                    EventConstants.C_GAME_REVIEW_LIKE_CANCEL_CLICK,
                    "type" to 2,
                    "creatortype" to gameType,
                    "gameid" to gameId
                )
            }
            trackLike(reply.replyId, 2L)
            vm.likeReply(reply, replyPosition, commentPosition, isAuthorReply)
        }

        override fun reply2Reply(reply: PostReply, commentPosition: Int) {
            showReplyDialog(AddPostCommentReplyTarget(reply, reply.commentId, commentPosition))
        }

        override fun loadMoreReply(comment: PostComment, commentPosition: Int) {
            Analytics.track(
                EventConstants.UGC_DETAIL_PAGE_REVIEW_COLLAPSE_CLICK,
                "pagetype" to pageType,
                "reviewid" to comment.commentId,
                "gameid" to args.ugcId
            )
            Analytics.track(
                EventConstants.EVENT_GAME_REVIEW_EXPAND,
                "creatortype" to gameType
            )
            vm.loadMoreReplies(comment, commentPosition)
        }

        override fun collapseReply(comment: PostComment, commentPosition: Int) {
            Analytics.track(
                EventConstants.UGC_DETAIL_PAGE_REVIEW_REPLISE_CLICK,
                "pagetype" to pageType,
                "reviewid" to comment.commentId,
                "gameid" to args.ugcId
            )
            Analytics.track(
                EventConstants.GAME_REVIEW_COLLAPSE_CLICK,
                "creatortype" to gameType
            )
            vm.collapseReply(comment, commentPosition, true)
        }

        override fun goCommentListPage() {}

        override fun previewImage(mediaList: List<PostMedia>?, imagePosition: Int) {
            val images = mediaList?.filter { it.isImage }?.map { it.resourceValue }
            if (images.isNullOrEmpty()) return
            ImgPreDialogFragment.show(
                requireActivity(),
                images.toTypedArray(),
                TRACK_TAG,
                imagePosition
            )
        }

        override fun clickOperation(item: OperationInfo, position: Int) {
            item.content ?: return
            if (item.isWebType()) {
                MetaRouter.Web.navigate(this@UgcGameDetailFragment, item.title, item.content)
            } else if (item.isArticleType()) {
                MetaRouter.Post.goPostDetail(
                    this@UgcGameDetailFragment,
                    item.content,
                    "ugc_detail_operation"
                )
            }
        }

        override fun showComment(comment: PostComment, commentPosition: Int) {
            if (trackGameReviewShow) {
                trackGameReviewShow = false
                Analytics.track(
                    EventConstants.GAME_REVIEW_SHOW,
                    "creatortype" to gameType
                )
            }
        }

        override fun clickLabel(data: Pair<Int, LabelInfo?>) {
            UserLabelView.showDescDialog(this@UgcGameDetailFragment, data)
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }
}