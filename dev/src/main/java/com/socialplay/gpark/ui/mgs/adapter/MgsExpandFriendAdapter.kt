package com.socialplay.gpark.ui.mgs.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.socialplay.gpark.R
import com.meta.box.biz.friend.internal.model.FriendStatus
import com.meta.box.biz.friend.internal.model.toLocalStatus
import com.socialplay.gpark.data.model.im.MgsFriendInfo
import com.socialplay.gpark.databinding.ItemMgsExpandFriendBinding
import com.socialplay.gpark.ui.base.adapter.BasePagingDataAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/08/09
 *     desc   : 房间成员
 *
 */
class MgsExpandFriendAdapter : BasePagingDataAdapter<MgsFriendInfo, ItemMgsExpandFriendBinding>(DIFF_CALLBACK) {

    override fun createViewBinding(layoutInflater: LayoutInflater, parent: ViewGroup, viewType: Int): ItemMgsExpandFriendBinding {
        return ItemMgsExpandFriendBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    }
    companion object {
        const val PAYLOAD_UPDATE_CLICKED_STATE = "payload_update_clicked_state"
        private val DIFF_CALLBACK = object : DiffUtil.ItemCallback<MgsFriendInfo>() {

            override fun areItemsTheSame(oldItem: MgsFriendInfo, newItem: MgsFriendInfo) = oldItem.friendInfo.uuid == newItem.friendInfo.uuid

            override fun areContentsTheSame(oldItem: MgsFriendInfo, newItem: MgsFriendInfo): Boolean {
                val oldFriendInfo = oldItem.friendInfo
                val newFriendInfo = newItem.friendInfo
                return oldItem.clickedInvited == newItem.clickedInvited && oldItem.lastInviteTime == newItem.lastInviteTime && oldFriendInfo.uuid == newFriendInfo.uuid && oldFriendInfo.name == newFriendInfo.name && oldFriendInfo.remark == newFriendInfo.remark && oldFriendInfo.avatar == newFriendInfo.avatar && oldFriendInfo.status == newFriendInfo.status
            }
        }
    }

    override fun convert(holder: BindingViewHolder<ItemMgsExpandFriendBinding>, item: MgsFriendInfo, position: Int, payloads: MutableList<Any>) {
        super.convert(holder, item, position, payloads)
        if (payloads.contains(PAYLOAD_UPDATE_CLICKED_STATE)) {
            val currentTimeMills = System.currentTimeMillis()
            setClickedState(item, holder.binding)
        }
    }

    override fun convert(holder: BindingViewHolder<ItemMgsExpandFriendBinding>, item: MgsFriendInfo, position: Int) {
        val binding = holder.binding
        val friendInfo = item.friendInfo
        Glide.with(context).load(item.friendInfo.avatar).error(R.drawable.icon_default_avatar).transform(CircleCrop()).into(holder.binding.ivMgsRoomMyFriendAvatar)
        holder.binding.tvMgsRoomMyFriendName.text = if (friendInfo.remark.isNullOrBlank()) friendInfo.name else friendInfo.remark
        updateFriendActiveStatus(binding, item)
    }

    private fun updateFriendActiveStatus(binding: ItemMgsExpandFriendBinding, item: MgsFriendInfo) {
        val friendInfo = item.friendInfo
        val isOnline = friendInfo.status.toLocalStatus() == FriendStatus.ONLINE || friendInfo.status.toLocalStatus() == FriendStatus.PLAYING_GAME
        binding.tvMgsRoomInvite.isVisible = true
        binding.tvMgsRoomMyFriendStatus.isVisible = isOnline
        when (friendInfo.status.toLocalStatus()) {
            FriendStatus.ONLINE       -> { //逛乐园
                binding.tvMgsRoomMyFriendStatus.text = context.getString(R.string.online_status)
            }
            FriendStatus.PLAYING_GAME -> { //玩游戏
                binding.tvMgsRoomMyFriendStatus.text = context.getString(R.string.playing_formatted).format(friendInfo.status?.gameStatus?.gameName)
            }
        }
        setClickedState(item, binding)
        binding.ivCertification.isVisible = item.friendInfo.isOfficial()
    }

    private fun setClickedState(item: MgsFriendInfo, binding: ItemMgsExpandFriendBinding) {
        binding.tvMgsRoomInvite.text = context.getString(if (item.clickedInvited) R.string.has_invited_friend else R.string.invite_friend)
        binding.tvMgsRoomInvite.isEnabled = !item.clickedInvited
    }
}