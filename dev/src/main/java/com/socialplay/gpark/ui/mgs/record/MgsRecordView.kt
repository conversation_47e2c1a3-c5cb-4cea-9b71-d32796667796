package com.socialplay.gpark.ui.mgs.record

import android.app.Application
import android.content.Context
import android.os.SystemClock
import android.util.Log
import android.view.LayoutInflater
import android.widget.RelativeLayout
import com.socialplay.gpark.R
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.databinding.MetaMgsRecordViewBinding
import com.socialplay.gpark.ui.mgs.listener.OnMgsRecordListener
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import org.koin.core.context.GlobalContext

/**
 * @des: 录屏中的计时布局
 * @author: lijunjia
 * @date: 2022/9/22 10:23
 */
class MgsRecordView(val app: Application, val metaApp: Context) : RelativeLayout(metaApp) {
    lateinit var binding: MetaMgsRecordViewBinding
    private val metaKv = GlobalContext.get().get<MetaKV>()
    var freeRecordStartTime: Long = 0

    init {
        initView()
    }

    private fun initView() {
        binding = MetaMgsRecordViewBinding.inflate(LayoutInflater.from(metaApp), this, true)
    }


    fun onStartRecord() {
        updateVoiceRecordState()
        startChronometer()
    }

    fun setRecordOnTouchListener(onTouchListener: OnTouchListener) {
        binding.ivVoice.setOnTouchListener(onTouchListener)
        binding.ivRecording.setOnTouchListener(onTouchListener)
        binding.chronometerFreeRecord.setOnTouchListener(onTouchListener)
    }

    fun onEndRecord() {
        stopChronometer()
    }

    fun resetChronometer() {
        binding.chronometerFreeRecord.text = "00:00"
    }

    private fun stopChronometer() {
        binding.chronometerFreeRecord.stop()
    }

    private fun startChronometer() {
        freeRecordStartTime = SystemClock.elapsedRealtime()
        binding.chronometerFreeRecord.base = freeRecordStartTime
        binding.chronometerFreeRecord.start()
    }

    fun updateVoiceRecordState() {
        binding.ivVoice.setImageResource(if (metaKv.screenRecordKV.isRecordAudio) R.drawable.icon_mgs_record_voice_a else R.drawable.icon_mgs_record_voice_b)
    }

}