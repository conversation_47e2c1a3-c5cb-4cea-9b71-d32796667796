package com.socialplay.gpark.ui.editor.tab.loadingscreen

import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.editor.AvatarLoadingStatus
import com.socialplay.gpark.databinding.LayoutScreenAvatarLoadingScreenBinding
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.editor.home.HomeFullAvatarViewModel
import com.socialplay.gpark.ui.view.TextViewMarqueeFactory
import com.socialplay.gpark.util.extension.collectIn
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.sharedViewModelFromParentFragment
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import timber.log.Timber
import kotlin.math.roundToInt

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2023/08/31
 *     desc   :
 */
class AvatarLoadingScreenFragment : BaseFragment<LayoutScreenAvatarLoadingScreenBinding>(
    R.layout.layout_screen_avatar_loading_screen
) {

    override fun getPageName() = ""

    override fun isEnableTrackPageExposure() = false

    private val vm: AvatarLoadingScreenViewModel by fragmentViewModel()

    private val fullAvatarViewModel: HomeFullAvatarViewModel by sharedViewModelFromParentFragment()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): LayoutScreenAvatarLoadingScreenBinding? {
        return LayoutScreenAvatarLoadingScreenBinding.inflate(inflater, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        vm.refreshData()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        vm.onEach(AvatarLoadingScreenState::fullBodyImg) {
            Timber.d("AvatarLoadingScreenFragment fullBodyImg url:$it")
            if (it == null) {
                binding.ivImg.setImageDrawable(ColorDrawable(0xFFEEEEEE.toInt()))
            } else {
                Glide.with(this)
                    .load(it.url)
                    .timeout(30 * 1000)
                    .placeholder(ColorDrawable(0xFFEEEEEE.toInt()))
                    .into(binding.ivImg)
            }
        }

        vm.onEach(AvatarLoadingScreenState::loadingStatusMessage) {
            binding.tvLoadingMessageAndProgress.text = it
        }

        vm.onEach(AvatarLoadingScreenState::artImg) {
            Timber.d("AvatarLoadingScreenFragment artImg url:$it")
            if(it == null){
                binding.ivArtImg.gone()
            }else{
                binding.ivArtImg.visible()
                Glide.with(this@AvatarLoadingScreenFragment)
                    .load(it)
                    .into(binding.ivArtImg)
            }
        }

        vm.onEach(
            AvatarLoadingScreenState::loadingStatus,
            AvatarLoadingScreenState::loadingStatusMessage) { loadStatus, loadStatusMessage->

            when (loadStatus) {
                is AvatarLoadingStatus.Loading -> {
                    binding.pbLoadingProgress.max = 100
                    val progress = (loadStatus.progress * 100).roundToInt()
                    binding.pbLoadingProgress.progress = progress
                    binding.tvLoadingMessageAndProgress.text =
                        "%s%s%%".format(loadStatusMessage ?: "", progress)
                }

                is AvatarLoadingStatus.Success -> {
                    binding.pbLoadingProgress.max = 100
                    binding.pbLoadingProgress.progress = 100
                }

                is AvatarLoadingStatus.Error -> {
                }

                else -> {
                }
            }
        }

        vm.onEach(AvatarLoadingScreenState::tipMessageList) {
            binding.smvList.visible(it.isNotEmpty())

            val marqueeFactory = TextViewMarqueeFactory<String>(requireContext()).apply { data = it }

            binding.smvList.setMarqueeFactory(marqueeFactory)
            binding.smvList.flipInterval = 3500

            binding.smvList.stopFlipping()
            binding.smvList.startFlipping()
        }

        binding.ivClose.setOnClickListener { fullAvatarViewModel.exitFullAvatar() }

        fullAvatarViewModel.fullAvatarStatusFlow
            .map { it.isFullMode }
            .distinctUntilChanged()
            .combine(vm.stateFlow) { a, b ->
                a && (b.loadingStatus is AvatarLoadingStatus.Loading || b.loadingStatus is AvatarLoadingStatus.Error)
            }.distinctUntilChanged().collectIn(viewLifecycleOwner.lifecycleScope) {
                Timber.d("AvatarLoadingScreenFragment setLoadingScreenVisibility $it")
                binding.clLoadingScreenLayout.visible(it)
            }
    }

    override fun invalidate() {}
}