package com.socialplay.gpark.ui.web

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.MutableContextWrapper
import android.os.Bundle
import android.view.ViewGroup
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.fragment.app.FragmentActivity
import com.socialplay.gpark.ui.view.FixedScrollWebView
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeApi
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeHelper
import com.socialplay.gpark.ui.web.jsinterfaces.contract.GameActivityJsBridgeContract
import com.socialplay.gpark.ui.web.webclients.DefaultWebSettings
import timber.log.Timber
import java.util.concurrent.atomic.AtomicReference

object WebViewCache {
    private val cacheStack = mutableMapOf<String, WebView>()
    private var app: Application? = null

    private val activityGetter = ActivityGetter()

    fun init(application: Application) {
        this.app = application
        application.registerActivityLifecycleCallbacks(activityGetter)
    }

    fun has(): Boolean = cacheStack.isNotEmpty()

    fun hasCache(unique: String?): Boolean {
        return !unique.isNullOrEmpty() && cacheStack.contains(unique)
    }

    fun preLoad(unique: String, url: String? = null) = synchronized(cacheStack) {
        val cache = loadUrlUseCache(unique, url)
        if (cache == null) {
            Timber.d("preLoad create :$unique $url $app")
            app?.also { context ->
                val webView = createWebView(context, unique)
                if (!url.isNullOrEmpty()) webView.loadUrl(url)
                replaceParent(webView)
            }
        } else {
            Timber.d("preLoad has cache:$unique $url")
        }
    }

    fun acquireWebView(unique: String, context: Context): WebView = synchronized(cacheStack) {
        Timber.d("acquireWebView：$unique")
        val webView = runCatching { cacheStack[unique] }.getOrNull()
        if (webView != null) {
            webView.parent?.let { it as? ViewGroup }?.also { it.removeView(webView) }
            val wrapper = webView.context as MutableContextWrapper
            wrapper.baseContext = context
            return webView
        } else {
            return createWebView(context, unique)
        }
    }

    fun releaseWebView(webView: WebView, needRelease: Boolean = false) = synchronized(cacheStack) {
        Timber.d("releaseWebView $needRelease")
        val wrapper = webView.context as MutableContextWrapper
        wrapper.baseContext = app

        (webView as? FixedScrollWebView)?.let {
            it.getJsBridgeApi()?.helper?.contract as? GameActivityJsBridgeContract
        }?.setProxyContract(null)

        replaceParent(webView)

        if (needRelease) {
            webView.loadDataWithBaseURL(null, "", "text/html", "utf-8", null);
            //移除内部消息
            webView.handler?.removeCallbacksAndMessages(null)
            //移除所有子View
            webView.removeAllViews()
            webView.webChromeClient = null
            webView.webViewClient = WebViewClient()
        }
    }

    private fun replaceParent(webView: WebView) {
        //移除webview
        webView.parent?.let { it as? ViewGroup }?.also {
            Timber.d("replaceParent remove from old parent")
            it.removeView(webView)
        }
        activityGetter.get()?.window?.decorView?.let { it as? ViewGroup }?.also {
            Timber.d("replaceParent add to new parent")
            it.addView(
                webView, 0, ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
                )
            )
        }
    }

    private fun loadUrlUseCache(unique: String, url: String? = null): WebView? {
        return cacheStack[unique]?.apply {
            url?.also { loadUrl(it) }
        }
    }

    private fun createWebView(context: Context, unique: String): WebView {
        Timber.d("createWebView:$unique")
        return FixedScrollWebView(MutableContextWrapper(context)).apply {
            activityGetter.get()?.let { it as? FragmentActivity }?.also {
                Timber.d("添加 JsBridgeApi ${JsBridgeHelper.JS_BRIDGE_ALIAS}")
                val jsBridgeHelper = JsBridgeHelper(GameActivityJsBridgeContract(it), this)
                addJsBridgeApi(JsBridgeApi(jsBridgeHelper), JsBridgeHelper.JS_BRIDGE_ALIAS)
            }
            DefaultWebSettings.setWebSettings(this, cache = WebSettings.LOAD_NO_CACHE)
            cacheStack[unique] = this
        }
    }

    class ActivityGetter : Application.ActivityLifecycleCallbacks {

        fun get(): Activity? {
            return activityRef.get()?.takeIf { !it.isDestroyed && !it.isFinishing }
        }

        private val activityRef = AtomicReference<Activity>(null)

        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {

        }

        override fun onActivityStarted(activity: Activity) {

        }

        override fun onActivityResumed(activity: Activity) {
            activityRef.set(activity)
        }

        override fun onActivityPaused(activity: Activity) {

        }

        override fun onActivityStopped(activity: Activity) {

        }

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {

        }

        override fun onActivityDestroyed(activity: Activity) {

        }
    }


}
