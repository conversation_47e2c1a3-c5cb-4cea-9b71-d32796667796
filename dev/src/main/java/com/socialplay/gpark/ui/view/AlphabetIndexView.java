package com.socialplay.gpark.ui.view;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.os.Handler;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.HapticFeedbackConstants;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.TextView;

import com.socialplay.gpark.R;
import com.socialplay.gpark.databinding.LayoutAlphabetBarBinding;
import com.socialplay.gpark.databinding.LayoutIndicatorBinding;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import timber.log.Timber;

/**
 * author : wei.zhu
 * e-mail : <EMAIL>
 * time   : 2023/03/03
 * desc   :
 */
public class AlphabetIndexView extends ViewGroup {
    /**
     * 如果当前项不参与字母索引, 则返回此字符
     */
    public static final char IGNORE_ALPHABET_CHAR = ' ';

    private static final String TAG = "AlphabetIndexView";
    public static final Comparator<AlphabetItem> ALPHABET_ITEM_COMPARATOR = (o1, o2) -> {
        int o1Weight = (o1.word >= 'A' && o1.word <= 'Z') ? o1.word : Integer.MAX_VALUE;
        int o2Weight = (o2.word >= 'A' && o2.word <= 'Z') ? o2.word : Integer.MAX_VALUE;

        return o1Weight - o2Weight;
    };

    private final AlphabetIndexAdapter     mAlphabetIndexAdapter;
    private final LayoutIndicatorBinding   mOverlayIndicatorBinding;
    private final LayoutAlphabetBarBinding mIndexBarBinding;
    private       AdapterItemTextRetriever mBoundAdapterItemTextRetriever;
    private       RecyclerView.Adapter<?>  mBoundAdapter;

    private int mLastPosition = -1;

    private final Rect mRectCache = new Rect();

    private final Runnable ACTION_REMOVE_OVERLAY = () -> hideOverlay(0, true);
    private final Runnable ACTION_SHOW_OVERLAY   = () -> showOverlay(0, true);

    private final int mSpacingBetweenAlphabetAndIndicatorView;

    private ValueAnimator mIndicatorFadeOutAnimator;
    private ValueAnimator mIndicatorFadeInAnimator;
    private RecyclerView  mBoundRecyclerView;


    public AlphabetIndexView(@NonNull Context context) {
        this(context, null);
    }

    public AlphabetIndexView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AlphabetIndexView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public AlphabetIndexView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);

        mIndexBarBinding = LayoutAlphabetBarBinding.inflate(LayoutInflater.from(context), this, true);

        mSpacingBetweenAlphabetAndIndicatorView = (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 8, context.getResources().getDisplayMetrics());

        final int scaledTouchSlop = ViewConfiguration.get(context).getScaledTouchSlop();

        mAlphabetIndexAdapter = new AlphabetIndexAdapter();
        mIndexBarBinding.rvList.setAdapter(mAlphabetIndexAdapter);

        mIndexBarBinding.rvList.addOnItemTouchListener(new RecyclerView.SimpleOnItemTouchListener() {
            private float mLastY;
            private float mLastX;

            private boolean mIsBeginDragging;
            private boolean mIsDownTouchedInAlphabets;

            @Override
            public boolean onInterceptTouchEvent(@NonNull RecyclerView rv, @NonNull MotionEvent e) {
                if (e.getActionMasked() == MotionEvent.ACTION_DOWN) {
                    mIsDownTouchedInAlphabets = isPointerInAnyView(rv, mRectCache, e.getX(), e.getY());
                }
                return mIsDownTouchedInAlphabets;
            }

            @Override
            public void onTouchEvent(@NonNull RecyclerView rv, @NonNull MotionEvent e) {
                switch (e.getActionMasked()) {
                    case MotionEvent.ACTION_DOWN:
                        mIsDownTouchedInAlphabets = isPointerInAnyView(rv, mRectCache, e.getX(), e.getY());
                        break;
                    case MotionEvent.ACTION_UP:
                    case MotionEvent.ACTION_CANCEL:
                        mIsBeginDragging = mIsDownTouchedInAlphabets = false;
                        hideOverlay(300, true);
                        break;
                    case MotionEvent.ACTION_MOVE:
                        if (mIsDownTouchedInAlphabets || mIsBeginDragging) {
                            float xDelta = e.getX() - mLastX;
                            float yDelta = e.getY() - mLastY;

                            if (Math.abs(xDelta) >= scaledTouchSlop || Math.abs(yDelta) >= scaledTouchSlop) {
                                if (!mIsBeginDragging) {
                                    showOverlay(0, true);
                                    mIsBeginDragging = true;
                                }

                                this.mLastX = e.getX();
                                this.mLastY = e.getY();
                            }

                        }
                        break;
                    default:
                        break;
                }

                if (mIsBeginDragging || mIsDownTouchedInAlphabets) {

                    final int N = rv.getChildCount();

                    View selectedView = null;


                    for (int i = 0; i < N; i++) {
                        final View view = rv.getChildAt(i);
                        mRectCache.set(view.getLeft(), view.getTop(), view.getRight(), view.getBottom());
                        if (mRectCache.contains(mIsBeginDragging ? mRectCache.centerX() : (int) e.getX(), (int) e.getY())) {
                            selectedView = view;
                            break;
                        }
                    }

                    if (selectedView != null) {
                        for (int i = 0; i < N; i++) {
                            final View view = rv.getChildAt(i);
                            if (view != selectedView) {
                                view.setSelected(false);
                            }
                        }
                    }


                    if (selectedView != null && !selectedView.isSelected()) {
                        select(selectedView, true);
                    }


                    postInvalidate();
                }
            }
        });

        this.mOverlayIndicatorBinding = LayoutIndicatorBinding.inflate(LayoutInflater.from(getContext()));

        getOverlay().add(mOverlayIndicatorBinding.getRoot());
    }

    private void onSelectionChanged(int position, boolean scrolledByIndexBar) {
        if (position < 0 || position >= mAlphabetIndexAdapter.getItemCount()) {
            return;
        }

        final AlphabetItem item = mAlphabetIndexAdapter.getItem(position);
        mOverlayIndicatorBinding.tvOverlay.setText(String.valueOf(item.word));

        final RecyclerView boundRecyclerView = mBoundRecyclerView;
        if (boundRecyclerView != null && scrolledByIndexBar) {

            final RecyclerView.LayoutManager layoutManager = boundRecyclerView.getLayoutManager();
            if (layoutManager instanceof LinearLayoutManager) {
                ((LinearLayoutManager) layoutManager).scrollToPositionWithOffset(item.position, 0);
            } else {
                boundRecyclerView.scrollToPosition(position);
            }
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {

        final ConstraintLayout overlayLayout = mOverlayIndicatorBinding.getRoot();
        final ConstraintLayout indexBarLayout = mIndexBarBinding.getRoot();

        overlayLayout.measure(MeasureSpec.makeMeasureSpec(MeasureSpec.getSize(widthMeasureSpec), MeasureSpec.AT_MOST), MeasureSpec.makeMeasureSpec(MeasureSpec.getSize(heightMeasureSpec), MeasureSpec.AT_MOST));

        indexBarLayout.measure(MeasureSpec.makeMeasureSpec(MeasureSpec.getSize(widthMeasureSpec), MeasureSpec.AT_MOST), MeasureSpec.makeMeasureSpec(MeasureSpec.getSize(heightMeasureSpec), MeasureSpec.AT_MOST));

        int width = overlayLayout.getMeasuredWidth() + indexBarLayout.getMeasuredWidth() + mSpacingBetweenAlphabetAndIndicatorView;
        int height = indexBarLayout.getMeasuredHeight() + overlayLayout.getMeasuredHeight();

        setMeasuredDimension(width, height);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        int indexLayoutLeft = (right - left) - mIndexBarBinding.getRoot().getMeasuredWidth();
        int indexLayoutRight = indexLayoutLeft + mIndexBarBinding.getRoot().getMeasuredWidth();

        int indexLayoutTop = ((bottom - top) - mIndexBarBinding.getRoot().getMeasuredHeight()) / 2;
        int indexLayoutBottom = indexLayoutTop + mIndexBarBinding.getRoot().getMeasuredHeight();


        mIndexBarBinding.getRoot().layout(indexLayoutLeft, indexLayoutTop, indexLayoutRight, indexLayoutBottom);
    }

    private void layoutOverlay(Rect rect) {
        rect.left -= mSpacingBetweenAlphabetAndIndicatorView;

        final ConstraintLayout overlayIndicator = mOverlayIndicatorBinding.getRoot();

        final int width = overlayIndicator.getMeasuredWidth();
        final int height = overlayIndicator.getMeasuredHeight();

        int l = rect.left - width;
        int t = rect.centerY() - (height / 2);
        int r = rect.left;
        int b = rect.centerY() + (height / 2);
        overlayIndicator.layout(l, t, r, b);
    }

    private void cancelOverlayAnimation() {
        final ValueAnimator animator = mIndicatorFadeOutAnimator;
        if (animator != null && animator.isRunning()) {
            animator.cancel();
            mIndicatorFadeOutAnimator = null;
        }
    }

    private void playTikEffect() {
        try {
            performHapticFeedback(HapticFeedbackConstants.CLOCK_TICK);
        } catch (Exception ignored) {
        }
    }

    private boolean isPointerInAnyView(RecyclerView list, Rect rect, float x, float y) {
        final int N = list.getChildCount();

        for (int i = 0; i < N; i++) {
            final View view = list.getChildAt(i);
            rect.set(view.getLeft(), view.getTop(), view.getRight(), view.getBottom());
            if (rect.contains((int) x, (int) y)) {
                return true;
            }
        }
        return false;
    }


    public void bind(RecyclerView rv, RecyclerView.Adapter<?> adapter, AdapterItemTextRetriever adapterItemTextRetriever) {
        this.mBoundRecyclerView = rv;
        this.mBoundAdapter = adapter;
        this.mBoundAdapterItemTextRetriever = adapterItemTextRetriever;

        adapter.registerAdapterDataObserver(new RecyclerView.AdapterDataObserver() {
            @Override
            public void onChanged() {
                super.onChanged();
                rebuildIndexList();
            }

            @Override
            public void onItemRangeChanged(int positionStart, int itemCount) {
                super.onItemRangeChanged(positionStart, itemCount);
                rebuildIndexList();
            }

            @Override
            public void onItemRangeChanged(int positionStart, int itemCount, @Nullable Object payload) {
                super.onItemRangeChanged(positionStart, itemCount, payload);
                rebuildIndexList();
            }

            @Override
            public void onItemRangeInserted(int positionStart, int itemCount) {
                super.onItemRangeInserted(positionStart, itemCount);
                rebuildIndexList();
            }

            @Override
            public void onItemRangeRemoved(int positionStart, int itemCount) {
                super.onItemRangeRemoved(positionStart, itemCount);
                rebuildIndexList();
            }

            @Override
            public void onItemRangeMoved(int fromPosition, int toPosition, int itemCount) {
                super.onItemRangeMoved(fromPosition, toPosition, itemCount);
                rebuildIndexList();
            }

        });

        rv.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                final RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
                if (layoutManager instanceof LinearLayoutManager) {
                    final int lastVisibleItemPosition = ((LinearLayoutManager) layoutManager).findFirstCompletelyVisibleItemPosition();
                    final RecyclerView.ViewHolder vh = recyclerView.findViewHolderForAdapterPosition(lastVisibleItemPosition);
                    if (vh != null && vh.getBindingAdapter() == mBoundAdapter) {
                        final char alphabet = mBoundAdapterItemTextRetriever.getPositionAlphabet(vh.getBindingAdapterPosition());

                        if (alphabet == IGNORE_ALPHABET_CHAR) {
                            return;
                        }

                        int alphabetPosition = -1;

                        for (int i = 0; i < mAlphabetIndexAdapter.getItemCount(); i++) {
                            final AlphabetItem item = mAlphabetIndexAdapter.getItem(i);
                            if (item.word == alphabet) {
                                alphabetPosition = i;
                                break;
                            }
                        }

                        final RecyclerView.ViewHolder viewHolder = mIndexBarBinding.rvList.findViewHolderForAdapterPosition(alphabetPosition);
                        if (viewHolder != null) {
                            select(viewHolder.itemView, false);
                        }
                    }
                }
            }
        });


        rebuildIndexList();
    }

    private void select(View selectedView, boolean scrolledByIndexBar) {
        final RecyclerView.ViewHolder viewHolder = mIndexBarBinding.rvList.getChildViewHolder(selectedView);
        final int alphabetPosition = viewHolder.getBindingAdapterPosition();

        if (mLastPosition != alphabetPosition && alphabetPosition != RecyclerView.NO_POSITION) {
            mLastPosition = alphabetPosition;

            mRectCache.set(selectedView.getLeft(), selectedView.getTop(), selectedView.getRight(), selectedView.getBottom());
            offsetDescendantRectToMyCoords(mIndexBarBinding.rvList, mRectCache);


            final int childCount = mIndexBarBinding.rvList.getChildCount();
            for (int i = 0; i < childCount; i++) {
                final View view = mIndexBarBinding.rvList.getChildAt(i);
                view.setSelected(view == viewHolder.itemView);
            }

            onSelectionChanged(alphabetPosition, scrolledByIndexBar);

            cancelOverlayAnimation();

            playTikEffect();

            cancelDelayedOverlayHideTask();

            //非用户通过IndexBar触发，这里设置延迟关闭，防止通过indexBar触发后再通过列表滚动触发无法关闭overlay的情况
            if (!scrolledByIndexBar) {
                hideOverlay(300, true);
            }else{
                layoutOverlay(mRectCache);
            }
        }
    }

    private void rebuildIndexList() {

        AlphabetItem[] items = new AlphabetItem[('Z' - 'A') + 2];

        for (int i = 0; i < mBoundAdapter.getItemCount(); i++) {
            final char ch = this.mBoundAdapterItemTextRetriever.getPositionAlphabet(i);
            final int pos;
            if (ch >= 'A' && ch <= 'Z') {
                pos = ch - 'A';
            } else {
                pos = items.length - 1;
            }
            if (ch != IGNORE_ALPHABET_CHAR) {
                if (items[pos] == null) {
                    items[pos] = new AlphabetItem(i, ch, false);
                }
            }
        }

        ArrayList<AlphabetItem> alphabetItems = new ArrayList<>();
        for (AlphabetItem item : items) {
            if (item != null) {
                alphabetItems.add(item);
            }
        }

        Collections.sort(alphabetItems, ALPHABET_ITEM_COMPARATOR);
        
        mAlphabetIndexAdapter.setDataList(alphabetItems);
        mAlphabetIndexAdapter.notifyDataSetChanged();
    }


    private void cancelDelayedOverlayHideTask() {
        final Handler handler = getHandler();
        if(handler == null){
            return;
        }
        handler.removeCallbacks(ACTION_REMOVE_OVERLAY);
    }

    private void hideOverlay(long delay, boolean transition) {
        Timber.tag(TAG).i("hideOverlay delay:%s", delay);

        final Handler handler = getHandler();
        if (handler == null) {
            return;
        }

        handler.removeCallbacks(ACTION_REMOVE_OVERLAY);


        if (delay <= 0) {

            if (mIndicatorFadeOutAnimator != null && mIndicatorFadeOutAnimator.isRunning()) {
                mIndicatorFadeOutAnimator.cancel();
                mIndicatorFadeOutAnimator = null;
            }

            final ConstraintLayout overlayIndicator = mOverlayIndicatorBinding.getRoot();

            if (transition) {
                mIndicatorFadeOutAnimator = ObjectAnimator.ofFloat(overlayIndicator, View.ALPHA, overlayIndicator.getAlpha(), 0F);
                mIndicatorFadeOutAnimator.setDuration(300);
                mIndicatorFadeOutAnimator.start();
            } else {
                overlayIndicator.layout(0, 0, 0, 0);
            }
        } else {
            handler.postDelayed(ACTION_REMOVE_OVERLAY, delay);
        }
    }


    private void showOverlay(long delay, boolean transition) {
        Timber.tag(TAG).i("showOverlay delay:" + delay);

        final Handler handler = getHandler();
        if(handler == null){
            return;
        }

        handler.removeCallbacks(ACTION_SHOW_OVERLAY);

        if (delay <= 0) {

            if (mIndicatorFadeInAnimator != null && mIndicatorFadeInAnimator.isRunning()) {
                mIndicatorFadeInAnimator.cancel();
                mIndicatorFadeInAnimator = null;
            }

            final ConstraintLayout overlayIndicator = mOverlayIndicatorBinding.getRoot();
            if (transition) {
                mIndicatorFadeInAnimator = ObjectAnimator.ofFloat(overlayIndicator, View.ALPHA, 0F, 1F);
                mIndicatorFadeInAnimator.setDuration(150);
                mIndicatorFadeInAnimator.start();
            } else {
                overlayIndicator.setAlpha(1F);
            }
        } else {
            handler.postDelayed(ACTION_SHOW_OVERLAY, delay);
        }
    }


    private static class AlphabetIndexAdapter extends RecyclerView.Adapter<AlphabetIndexAdapter.VH> {

        private List<AlphabetItem> mDataList = new ArrayList<>();

        @NonNull
        @Override
        public VH onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return new VH(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_alphabet, parent, false));
        }

        @Override
        public void onBindViewHolder(@NonNull VH holder, int position) {
            final AlphabetItem alphabetItem = mDataList.get(position);
            holder.textView.setText(String.valueOf(alphabetItem.word));
            holder.textView.setBackgroundColor(alphabetItem.isActive ? Color.RED : Color.TRANSPARENT);
        }

        public AlphabetItem getItem(int position) {
            return mDataList.get(position);
        }

        @Override
        public int getItemCount() {
            return mDataList.size();
        }

        public void setDataList(List<AlphabetItem> items) {
            this.mDataList = items;
        }

        public static class VH extends RecyclerView.ViewHolder {
            private final TextView textView;

            public VH(@NonNull View itemView) {
                super(itemView);
                textView = itemView.findViewById(R.id.tv_text);
            }
        }
    }

    public interface AdapterItemTextRetriever {
        /**
         * 如果当前项不参与字母索引, 则返回 AlphabetIndexView.IGNORE_ALPHABET
         */
        char getPositionAlphabet(int position);
    }

    public static final class AlphabetItem {

        public int     position;
        public char    word;
        public boolean isActive;

        public AlphabetItem(int pos, char word, boolean isActive) {
            this.position = pos;
            this.word = word;
            this.isActive = isActive;
        }
    }
}
