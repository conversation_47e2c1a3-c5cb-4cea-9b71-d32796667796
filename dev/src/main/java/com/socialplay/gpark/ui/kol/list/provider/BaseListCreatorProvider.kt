package com.socialplay.gpark.ui.kol.list.provider

import com.bumptech.glide.RequestManager
import com.chad.library.adapter.base.provider.BaseItemProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.CreatorMultiInfo
import com.socialplay.gpark.ui.kol.list.IKolCreatorAdapterListener
import com.socialplay.gpark.ui.view.MetaTextView

abstract class BaseListCreatorProvider(
    val glide: RequestManager,
    val listener: IKolCreatorAdapterListener?
) : BaseItemProvider<CreatorMultiInfo>() {
    override val layoutId: Int = R.layout.provider_creator_list_title_card

    override fun convert(helper: BaseViewHolder, item: CreatorMultiInfo) {
        helper.getView<MetaTextView>(R.id.tvTitle).text = item.title
        helper.getView<MetaTextView>(R.id.tvMore).setOnClickListener {
            onClickGoMore(helper, item)
        }
        buildRv(helper, item)
    }

    abstract fun buildRv(helper: BaseViewHolder, item: CreatorMultiInfo)
    abstract fun onClickGoMore(helper: BaseViewHolder, item: CreatorMultiInfo)

}