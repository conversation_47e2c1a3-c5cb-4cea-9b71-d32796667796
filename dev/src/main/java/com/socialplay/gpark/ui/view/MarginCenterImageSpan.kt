package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.text.style.ImageSpan
import java.lang.ref.WeakReference

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/01/29
 *     desc   :
 * </pre>
 */
class MarginCenterImageSpan @JvmOverloads constructor(
    context: Context,
    drawableRes: Int,
    private val marginLeft: Int = 0,
    private val marginTop: Int = 0,
    private val marginRight: Int = 0,
    private val marginBottom: Int = 0
) : ImageSpan(context, drawableRes) {

    private var mDrawableRef: WeakReference<Drawable>? = null
    private val cachedDrawable: Drawable
        get() {
            val wr = mDrawableRef
            var d: Drawable? = null
            if (wr != null) {
                d = wr.get()
            }
            if (d == null) {
                d = drawable!!
                mDrawableRef = WeakReference(d)
            }
            return d
        }

    override fun getSize(
        paint: Paint,
        text: CharSequence,
        start: Int,
        end: Int,
        fontMetricsInt: Paint.FontMetricsInt?
    ): Int {
        val drawable = cachedDrawable
        fontMetricsInt?.let {
            val fontHeight = paint.fontMetricsInt.descent - paint.fontMetricsInt.ascent
            val imageHeight = drawable.bounds.height()
            it.ascent = paint.fontMetricsInt.ascent - ((imageHeight - fontHeight) / 2.0f).toInt()
            it.top = it.ascent
            it.descent = it.ascent + imageHeight
            it.bottom = it.descent
        }
        return marginLeft + drawable.bounds.right + marginRight
    }

    override fun draw(
        canvas: Canvas,
        text: CharSequence,
        start: Int,
        end: Int,
        x: Float,
        top: Int,
        y: Int,
        bottom: Int,
        paint: Paint
    ) {
        val drawable = cachedDrawable
        val fontHeight = paint.fontMetricsInt.descent - paint.fontMetricsInt.ascent
        val imageAscent = paint.fontMetricsInt.ascent - ((drawable.bounds.height() - fontHeight) / 2.0f).toInt()
        val transX = x + marginLeft
        val transY = (y + imageAscent + marginTop - marginBottom).toFloat()
        canvas.save()
        canvas.translate(transX, transY)
        drawable.draw(canvas)
        canvas.restore()
    }
}