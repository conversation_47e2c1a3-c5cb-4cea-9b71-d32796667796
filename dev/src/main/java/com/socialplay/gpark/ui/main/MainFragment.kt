package com.socialplay.gpark.ui.main

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.os.Bundle
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.app.NotificationManagerCompat
import androidx.core.view.doOnPreDraw
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import com.google.android.material.tabs.TabLayout
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.EditorInteractor
import com.socialplay.gpark.data.interactor.QrCodeInteractor
import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.data.model.qrcode.ScanRequestData
import com.socialplay.gpark.data.model.qrcode.ScanResultData
import com.socialplay.gpark.data.model.startup.GuideInfo
import com.socialplay.gpark.databinding.FragmentMainBinding
import com.socialplay.gpark.databinding.ViewHomeBottomTabAddBinding
import com.socialplay.gpark.databinding.ViewHomeBottomTabBinding
import com.socialplay.gpark.function.MetaRouterWrapper
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.deeplink.ShareActiveHelper
import com.socialplay.gpark.function.mw.lifecycle.AvatarGameTime
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.account.startup.SelectModeState
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.main.startup.HomeStartupProject
import com.socialplay.gpark.ui.qrcode.QRCodeScanFragment
import com.socialplay.gpark.ui.realname.RealName
import com.socialplay.gpark.util.SimpleVibratorUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.addListener
import com.socialplay.gpark.util.extension.addOnTabSelectedListener
import com.socialplay.gpark.util.extension.collectIn
import com.socialplay.gpark.util.extension.collectWithLifecycleOwner
import com.socialplay.gpark.util.extension.compoundDrawables
import com.socialplay.gpark.util.extension.selectedTab
import com.socialplay.gpark.util.extension.setFragmentResultListenerByActivity
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import org.koin.core.context.GlobalContext
import timber.log.Timber
import kotlin.system.exitProcess

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/10
 * desc   :
 * </pre>
 */


class MainFragment : BaseFragment<FragmentMainBinding>() {

    companion object {
        const val KEY_LAST_SELECTED_ITEM_ID = "key_last_selected_item_id"
        const val TAB_FRAGMENT_PREFIX = "main_bottom_navigation_fragment_tag_"
    }

    private val viewModel by sharedViewModel<MainViewModel>()
    private var lastSelectedItemId: Int? = null
    private val editorInteractor: EditorInteractor by inject()
    private val accountInteractor: AccountInteractor by inject()
    private var isGoSys = false
    private var updatePostUnread = false
    private var lastMgsUnreadTs = -60000L

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentMainBinding? {
        return FragmentMainBinding.inflate(inflater, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (!accountInteractor.userInfoCacheValid()) {
            // 没有用户信息，去登录页面
            MetaRouter.Startup.guideLogin(this@MainFragment, loginSource = LoginSource.GuideLogin)
            return
        }
        savedInstanceState?.run {
            if (containsKey(KEY_LAST_SELECTED_ITEM_ID)) {
                lastSelectedItemId = getInt(KEY_LAST_SELECTED_ITEM_ID)
            }
        }
    }

    private var first = true
    override fun init() {
        childFragmentManager.beginTransaction().run {
            val selectItem = viewModel.selectedItemLiveData.value ?: return@run
            val fragmentTag = getFragmentTag(selectItem.itemId)

            childFragmentManager.fragments.forEach {
                val isTabFragment = it?.tag?.startsWith(TAB_FRAGMENT_PREFIX) == true
                if (isTabFragment) {
                    if (it.tag != fragmentTag) {
                        if (!it.isHidden) {
                            hide(it)
                        }
                        setMaxLifecycle(it, Lifecycle.State.CREATED)
                    }
                }
            }
            commitNowAllowingStateLoss()
        }
        if (first) {
            first = false
        } else {
            postponeEnterTransition()
            (view?.parent as? ViewGroup)?.doOnPreDraw {
                startPostponedEnterTransition()
            }
        }
        val userInfoFromCache = accountInteractor.getUserInfoFromCache()
        if (!accountInteractor.userInfoCacheValid(userInfoFromCache)) {
            // 没有用户信息，去登录页面
            MetaRouter.Startup.guideLogin(this@MainFragment, loginSource = LoginSource.GuideLogin)
            return
        }
        checkMode()
        binding.tabLayout.addOnTabSelectedListener(
            viewLifecycleOwner,
            object : TabLayout.OnTabSelectedListener {
                var isFist = true
                var tempTab: TabLayout.Tab? = null

                override fun onTabSelected(tab: TabLayout.Tab) {
                    if (tab.id == MainBottomNavigationItem.ADD.itemId) {
                        tempTab?.let {
                            binding.tabLayout.selectTab(it)
                        }
                        clickAddItem()
                        return
                    }

                    if (tempTab != null) {
                        tempTab = null
                        return
                    }

                    val selectItem = MainBottomNavigationItem.getItem(tab.id)
                    if (checkSelectAIBotItem(tab)) {
                        return
                    }
                    viewModel.saveLastBottomTab(tab.id)
                    if (selectItem != null) {
                        if (!isFist && selectItem != MainBottomNavigationItem.EDITOR_HOME) {
                            viewModel.setHomeVisitorCount(0)
                        }
                        lastSelectedItemId = tab.id

                        viewModel.setSelectedItem(selectItem.itemId)

                        switchFragment(selectItem)

                        if (lastSelectedItemId == null || lastSelectedItemId != tab.id) {
                            selectItem.clickSource?.let { clickSource ->
                                Analytics.track(EventConstants.EVENT_TAB_CLICK) {
                                    put("source", clickSource)
                                }
                            }
                        }

                        if (!isFist) {
                            SimpleVibratorUtil.vibrateClick()
                        }
                        isFist = false
                    }
                }

                override fun onTabUnselected(tab: TabLayout.Tab) {
                    if (tab.id == MainBottomNavigationItem.ADD.itemId) {
                        return
                    }
                    val selected = binding.tabLayout.selectedTab()
                    if (selected?.id == MainBottomNavigationItem.ADD.itemId) {
                        tempTab = tab
                        return
                    }
                }

                override fun onTabReselected(tab: TabLayout.Tab) {
                    val curItem = MainBottomNavigationItem.getItem(tab.id)
                    if (checkSelectAIBotItem(tab)) {
                        return
                    }
                    if (curItem != null) {
                        val fragmentTag = getFragmentTag(tab.id)
                        val cachedFragment: Fragment? =
                            childFragmentManager.findFragmentByTag(fragmentTag)
                        (cachedFragment as? OnBottomNavItemReselected)?.onBottomNavReselected(
                            curItem
                        )
                        viewModel.fireItemReSelectedEvent(curItem)
                    }
                }
            })

        val tabItems = viewModel.mainItems.value
        if (tabItems.isNullOrEmpty()) {
            viewModel.mainItems.observe(viewLifecycleOwner) { items ->
                initTabs(items)
            }
        } else {
            initTabs(tabItems)
        }

        // 进来设置默认值，不使用动画
        setTabBarVisibility(viewModel.isTabBarShowing(), false, 0, 0)
        viewModel.tabBarStatusChangedEvent.collectIn(viewLifecycleOwner.lifecycleScope) {
            setTabBarVisibility(it.isShow, it.isAnim, it.animDuration, it.animDelay)
        }

        viewModel.selectedItemLiveData.observe(viewLifecycleOwner) {
            for (i in 0 until binding.tabLayout.tabCount) {
                val tab = binding.tabLayout.getTabAt(i)
                if (tab != null && it != null && tab.id == it.itemId) {
                    if (!tab.isSelected) {
                        binding.tabLayout.selectTab(tab)
                    }
                    break
                }
            }

            HomeStartupProject.onTabSelect(it)
            HomeImageShowAnalytics.onSelectItem(it)
        }

        setQrCodeListener()

        updatePostUnread = true

        // 检查实名
        val metaUserInfo = accountInteractor.accountLiveData.value
        if (EnvConfig.isParty() && (metaUserInfo?.bindIdCard == false || userInfoFromCache?.bindIdCard == false)) {
            // 没有实名，去实名页面
            MetaRouterWrapper.RealName.openRealName(this)
        } else {
            viewModel.realNameLock.observe(viewLifecycleOwner) {
                // 后台配置说，这里需要拦截，所以要检查是否未成年
                if (it == true) {
                    // 检查实名的年龄是否未成年，如果未成年则退出App
                    RealName.fetchRealNameInfo { limit, isNotBlock ->
                        if (isNotBlock == false && limit != null) {
                            // 实名未成年，时间不够用了。弹框提示，然后退出App
                            RealName.showLimitDialog(limit, requireActivity(), "2") {
                                exitProcess(0)
                            }
                        }
                    }
                }
                Timber.d("checkRealNameLock: $it")
            }
            viewModel.checkRealNameLock()
        }

        AvatarGameTime.showPositiveCommentDialogFlow
            .collectIn(viewLifecycleOwner.lifecycleScope) {
                if (it > 0) {
                    AvatarGameTime.showPositiveCommentDialog(parentFragmentManager)
                }
            }
        AvatarGameTime.deleteCurDayGameTime()

        viewModel.guideCallback.clearCallback()
        viewModel.guideCallback.observe(viewLifecycleOwner) { dialog, guideInfo, onDismissCallback ->
            if (dialog.isAdded) {
                MainAddDialog.guideDialog = dialog
            }
            clickAddItem(guideInfo, onDismissCallback)
        }
    }

    private fun triggerDialogScene() {
        DialogShowManager.triggerMainScene(this)
    }

    private fun initTabs(items: ArrayList<MainBottomNavigationItem>) {
        binding.tabLayout.removeAllTabs()

        // 添加底栏icon title 构建fragment
        val selectedItem = viewModel.selectedItemLiveData.value
        items.forEach {
            val tab = createTab(it)
            binding.tabLayout.addTab(
                tab,
                selectedItem == null || selectedItem.itemId == it.itemId
            )
        }
        checkEditorTab(items)
        binding.tabLayout.isVisible = items.size > 1 && viewModel.isTabBarShowing()
        setUnReadCount()
    }

    private fun checkSelectAIBotItem(tab: TabLayout.Tab): Boolean {
        val selectItem = MainBottomNavigationItem.getItem(tab.id)
        if (selectItem?.itemId == MainBottomNavigationItem.AI_BOT_UGC.itemId) {
            Analytics.track(EventConstants.EVENT_AI_BOT_CREATE_CLICK, map = mapOf("click_type" to "tab"))
            if (viewModel.isEnoughAiBotCreate()) {
                MetaRouter.AiBot.showGenderDialog(this@MainFragment)
            } else {
                Analytics.track(EventConstants.EVENT_AI_BOT_GENDER_LIMIT)
                ToastUtil.showShort(R.string.ai_bot_limit_tip)
            }
            //当前选中位置
            val selectedItem = viewModel.selectedItemLiveData.value
            for (i in 0 until binding.tabLayout.tabCount) {
                val tab = binding.tabLayout.getTabAt(i)
                if (tab != null && selectedItem != null && tab.id == selectedItem.itemId) {
                    binding.tabLayout.selectTab(tab)
                    break
                }
            }
            return true
        } else {
            return false
        }
    }

    private fun clickAddItem(guide: GuideInfo? = null, onDismissCallback: ((Boolean) -> Unit)? = null) {
        MainAddDialog.show(this, guide, onDismissCallback)
    }

    private fun checkMode() {
        if (viewModel.mode == SelectModeState.MODE_WORLD_MAP) {
            MetaRouter.Startup.selectModeGame(this)
            ShareActiveHelper.skipCheck()
        } else if (viewModel.modeGameLiveData.value != null) {
            viewModel.resetModeGameInfo()
        }
    }

    private fun setUnReadCount() {
        viewModel.avatarV3CountFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
            for (i in 0 until binding.tabLayout.tabCount) {
                val tab = binding.tabLayout.getTabAt(i)
                if (tab != null && tab.id == MainBottomNavigationItem.EDITOR_HOME.itemId) {
                    tab.customView?.findViewById<View>(R.id.viewUnReadCount)?.visible(it)
                    break
                }
            }
        }
        viewModel.msgUnReadCountFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
            for (i in 0 until binding.tabLayout.tabCount) {
                val tab = binding.tabLayout.getTabAt(i)
                if (tab != null && tab.id == MainBottomNavigationItem.MESSAGE.itemId) {
                    tab.customView?.findViewById<View>(R.id.viewUnReadCount)?.visible(it > 0)
                    break
                }
            }
        }
        viewModel.ugcAssetRookieTabRedDotLiveData.observe(viewLifecycleOwner) {
            for (i in 0 until binding.tabLayout.tabCount) {
                val tab = binding.tabLayout.getTabAt(i)
                if (tab != null && tab.id == MainBottomNavigationItem.UGC_DESIGN.itemId) {
                    tab.customView?.findViewById<View>(R.id.viewUnReadCount)?.visible(it == true)
                    break
                }
            }
        }
    }

    private fun setTabBarVisibility(
        isShow: Boolean,
        isAnim: Boolean,
        animDuration: Long,
        animDelay: Long
    ) {

        Timber.d("setTabBarVisibility isShow:$isShow isAnim:$isAnim animDuration:$animDuration animDelay:$animDelay isBindingAvailable:${isBindingAvailable()}")

        if (!isBindingAvailable()) return

        val tabLayout = binding.tabLayout
        val vTabBarDivider = binding.vTabBarDivider

        if (!isAnim) {
            tabLayout.visible(isShow)
            vTabBarDivider.visible(isShow)
            return
        }

        val animatorSet = AnimatorSet()

        animatorSet.addListener(viewLifecycleOwner, onEnd = {
            tabLayout.visible(isShow)
            vTabBarDivider.visible(isShow)
        })

        val to = if (isShow) 1F else 0F

        val tabBarAlphaAnimator = ObjectAnimator.ofFloat(
            tabLayout, View.ALPHA,
            tabLayout.alpha, to
        )

        val tabBarDividerAlphaAnimator = ObjectAnimator.ofFloat(
            vTabBarDivider, View.ALPHA,
            vTabBarDivider.alpha, to
        )

        animatorSet.duration = animDuration
        animatorSet.playTogether(tabBarAlphaAnimator, tabBarDividerAlphaAnimator)
        animatorSet.startDelay = animDelay
        animatorSet.start()
    }

    private fun checkEditorTab(items: ArrayList<MainBottomNavigationItem>) {
        editorInteractor.hasEditorExploreFormalTab = false
    }

    private fun setQrCodeListener() {
        setFragmentResultListenerByActivity(QRCodeScanFragment.KEY_HOME_REQUEST_SCAN_QRCODE, viewLifecycleOwner) { _, bundle ->
            val request = ScanRequestData.from(bundle)
            val result = ScanResultData.from(bundle)

            if (result != null && request != null) {
                viewLifecycleOwner.lifecycleScope.launch {
                    GlobalContext.get().get<QrCodeInteractor>().dispatchQRCodeScanResult(this@MainFragment, request, result)
                }
            }
        }
    }

    private fun changeBottomBarColor(
        item: MainBottomNavigationItem,
        tab: TabLayout.Tab,
        select: Boolean
    ) {
    }

    private fun createTab(item: MainBottomNavigationItem): TabLayout.Tab {
        val tabViewBinding = if (item.itemId == MainBottomNavigationItem.ADD.itemId) {
            ViewHomeBottomTabAddBinding.inflate(layoutInflater).apply {
                root.setOnAntiViolenceClickListener(233) {
                    clickAddItem()
                }
            }
        } else {
            ViewHomeBottomTabBinding.inflate(layoutInflater).apply {
                tvTitle.compoundDrawables(top = item.iconRes)
                tvTitle.setText(item.titleRes)
            }
        }
        val tab = binding.tabLayout.newTab()
        tab.id = item.itemId
        tab.customView = tabViewBinding.root
        tab.tag = item
        return tab
    }

    private fun switchFragment(selectItem: MainBottomNavigationItem) {
        childFragmentManager.beginTransaction().run {
            val fragmentTag = getFragmentTag(selectItem.itemId)
            var cachedFragment: Fragment? = null

            childFragmentManager.fragments.forEach {
                val isTabFragment = it?.tag?.startsWith(TAB_FRAGMENT_PREFIX) == true
                if (isTabFragment) {
                    if (it.tag == fragmentTag) {
                        cachedFragment = it
                    } else if (!it.isHidden) {
                        hide(it)
                        setMaxLifecycle(it, Lifecycle.State.STARTED)
                    }
                }
            }

            if (cachedFragment != null) {
                show(cachedFragment!!)
                setMaxLifecycle(cachedFragment!!, Lifecycle.State.RESUMED)
            } else {
                val selectFragment = selectItem.factory.invoke()
                add(R.id.fragment_container, selectFragment, fragmentTag)
            }
            commitNowAllowingStateLoss()
        }
    }

    private fun getFragmentTag(itemId: Int) = "$TAB_FRAGMENT_PREFIX$itemId"

    override fun loadFirstData() {
        val enabled = NotificationManagerCompat.from(requireContext()).areNotificationsEnabled()
        Analytics.track(
            EventConstants.EVENT_DEVICE_NOTIFICATION_STATE,
            "state" to if (enabled) "on" else "off"
        )
    }

    override fun onDestroyView() {
        if (MainAddDialog.guideDialog != null) {
            MainAddDialog.guideDialog?.dismissAllowingStateLoss()
            MainAddDialog.guideDialog = null
        }
        super.onDestroyView()
    }

    override fun ignoreUsingTime() = true

    override fun getFragmentName(): String {
        return PageNameConstants.FRAGMENT_NAME_MAIN
    }

    override fun onResume() {
        super.onResume()
        if (updatePostUnread) {
            updatePostUnread = false
            viewModel.getPostUnread()
        }
        val curTs = SystemClock.elapsedRealtime()
        if (curTs - lastMgsUnreadTs >= 60000L) {
            lastMgsUnreadTs = curTs
            viewModel.getSysUnread()
        }
        if (isGoSys) {
            val enabled = NotificationManagerCompat.from(requireContext()).areNotificationsEnabled()
            Analytics.track(
                EventConstants.EVENT_NOTIFICATION_APPLICATION,
                "result" to enabled.toString()
            )
            isGoSys = false
        }
        HomeStartupProject.onEnterHome(viewModel.selectedItemLiveData.value)
        if (accountInteractor.userInfoCacheValid()) {
            // 登录的情况下，开始走弹框管理流程，看看该谁展示了
            triggerDialogScene()
        }
    }

    override fun onStop() {
        super.onStop()
        HomeStartupProject.onEnterOtherPage()
        HomeImageShowAnalytics.onMainFragmentStop()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        lastSelectedItemId?.let {
            outState.putInt(KEY_LAST_SELECTED_ITEM_ID, it)
        }
    }

    override fun isFirstPage() = true
}