package com.socialplay.gpark.ui.web.jsinterfaces.ext

import android.webkit.JavascriptInterface
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeApi
import com.socialplay.gpark.util.GsonUtil
import org.json.JSONArray
import org.json.JSONObject
import timber.log.Timber

/**
 * 账号相关js接口
 * <AUTHOR>
 * @date 2021/05/20
 */

/**
 * 返回用户信息
 */
@JavascriptInterface
fun JsBridgeApi.getUserInfo(): String {
    Timber.d("current Thread name = ${Thread.currentThread().name}")
    val metaUser = helper.getMetaUserInfo()
    val userInfo = metaUser ?: return createErrorResult(msg = "userInfo not exist")
    val json = GsonUtil.gson.toJson(userInfo)
    return createSuccessResult(data = json)
}

/**
 * 获取用户uuid
 */
@JavascriptInterface
fun JsBridgeApi.getUserUUID(): String {
    Timber.d("current Thread name = ${Thread.currentThread().name}")
    val metaUser = helper.getMetaUserInfo()
    return createSuccessResult(data = metaUser?.uuid)
}

/**
 * 弹出登录框
 */
@JavascriptInterface
suspend fun JsBridgeApi.gotoLogin(paramArray: JSONArray): String {
    val source = paramArray.optString(0)
    helper.navigateLogin(source)
    return createSuccessResult()
}

/**
 * 是否可以充值
 */
suspend fun JsBridgeApi.allowRecharge(): String {
    val guestRecharge = helper.isGuestRecharge()
    var type = ""
    var allowRecharge = true
    var reasonMsg = ""
    if (guestRecharge) {
        // 游客不能充值
        type = "visitor"
        allowRecharge = false
    } else if (helper.isFuncLimitByU13()) {
        // 小于13岁不能充值
        type = "u13"
        allowRecharge = false
    }
    reasonMsg = helper.getRechargeTip(type)
    return createSuccessResult(data = JSONObject(mapOf<String, Any>("allowRecharge" to allowRecharge, "type" to type, "reasonMsg" to reasonMsg)).toString())
}

