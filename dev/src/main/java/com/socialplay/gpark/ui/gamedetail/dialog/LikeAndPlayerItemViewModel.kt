package com.socialplay.gpark.ui.gamedetail.dialog

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.gamedetail.LikeAndPlayerListData
import com.socialplay.gpark.data.model.profile.RelationListResult
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import org.koin.android.ext.android.get

/**
 * 点赞和游玩列表ViewModel的State
 */
data class LikeAndPlayerItemModelState(
    val gameId: String,
    val type: String,
    val isOwner: Boolean = true,
    val refresh: Async<List<RelationListResult.RelationUserInfo>> = Uninitialized,
    val offset: Int? = null, // 分页偏移量
    val loadMore: Async<LoadMoreState> = Uninitialized
) : MavericksState {

    val list: List<RelationListResult.RelationUserInfo> = refresh.invoke() ?: emptyList()

    val isPlayerList: Boolean get() = type == LikeAndPlayerItemFragment.TYPE_PLAYER

    constructor(args: LikeAndPlayerItemFragmentArgs) : this(
        args.gameId,
        args.type,
        args.isOwner
    )
}

/**
 * 点赞和游玩列表ViewModel
 */
class LikeAndPlayerItemViewModel(
    val repository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    initialState: LikeAndPlayerItemModelState
) : BaseViewModel<LikeAndPlayerItemModelState>(initialState) {
    val maxSize = 50

    init {
        refreshList()
    }

    fun refreshList() {
        fetchUserList(true)
    }

    fun loadMoreList() {
        fetchUserList(false)
    }

    fun isPlayerList(): Boolean {
        return oldState.isPlayerList
    }

    private fun fetchUserList(isRefresh: Boolean) {
        withState { state ->
            val pageSize = PAGE_SIZE
            val offset = if (isRefresh) null else state.offset

//            if (state.type == LikeAndPlayerItemFragment.TYPE_PLAYER) {
//                // 使用模拟数据
//                val mockData = generateMockData(state.type, offset, pageSize)
//                setState {
//                    // 判断是否到达列表末尾
//                    // 游玩列表只展示前50个
//                    val isEnd = mockData.isEmpty() ||
//                        (state.type == LikeAndPlayerItemFragment.TYPE_PLAYER && (state.list.size + mockData.size) >= 50)
//
//                    if (isRefresh) {
//                        copy(
//                            refresh = Success(mockData),
//                            offset = null, // 重置偏移量
//                            loadMore = Success(LoadMoreState(isEnd)),
//                            total = if (state.type == LikeAndPlayerItemFragment.TYPE_LIKE) 120 else 80
//                        )
//                    } else {
//                        copy(
//                            refresh = Success(state.list + mockData),
//                            offset = state.list.size + mockData.size, // 模拟偏移量
//                            loadMore = Success(LoadMoreState(isEnd)),
//                            total = if (state.type == LikeAndPlayerItemFragment.TYPE_LIKE) 120 else 80
//                        )
//                    }
//                }
//            } else {
            // 根据类型选择不同的API
            if (state.type == LikeAndPlayerItemFragment.TYPE_LIKE) {
                // 点赞列表
                repository.getLikePlayerList(
                    moduleType = "game", // 固定为"game"
                    moduleContentId = state.gameId, // 使用gameId作为moduleContentId
                    offset = offset,
                    pageSize = pageSize,
                    expectSize = expectSize
                ).execute { result ->
                    handleApiResult(result, isRefresh, state, pageSize)
                }
            } else {
                // 游玩列表
                repository.getFlowerPlayerList(
//                    gameId = "1208850",   // 测试服的测试游戏ID
                    gameId = state.gameId,
                    offset = offset,
                    pageSize = pageSize,
                    expectSize = expectSize
                ).execute { result ->
                    handleApiResult(result, isRefresh, state, pageSize)
                }
            }
//            }
        }
    }

    /**
     * 生成模拟数据
     */
    private fun generateMockData(type: String, offset: Int?, pageSize: Int): List<RelationListResult.RelationUserInfo> {
        val result = mutableListOf<RelationListResult.RelationUserInfo>()

        // 模拟数据的总数量
        val totalCount = if (type == LikeAndPlayerItemFragment.TYPE_LIKE) 120 else 80

        // 计算当前页的数据范围
        val startIndex = offset ?: 0
        val endIndex = minOf(startIndex + pageSize, totalCount)

        // 如果超出范围，返回空列表
        if (startIndex >= totalCount) {
            return emptyList()
        }

        // 生成当前页的模拟数据
        for (i in startIndex until endIndex) {
            // 随机生成各种关系状态
            val followRelation = when ((i % 4)) {
                0 -> 0 // 无关系
                1 -> 1 // 我关注了对方
                2 -> 2 // 对方关注了我
                else -> 3 // 互相关注
            }

            val friendRelation = when ((i % 4)) {
                0 -> 0 // 无关系
                1 -> 1 // 双向好友
                2 -> 2 // 我的单向好友
                else -> 3 // 我是对方单向好友
            }

            // 在线状态，奇数在线，偶数离线
            val status = if (i % 2 == 0) 1 else 0

            result.add(
                RelationListResult.RelationUserInfo(
                    uuid = "user_$i",
                    avatar = "https://img2.baidu.com/it/u=1003272215,1878948666&fm=253&fmt=auto&app=120&f=JPEG?w=500&h=500", // 随机头像
                    nickname = if (type == LikeAndPlayerItemFragment.TYPE_LIKE) "点赞用户$i" else "游玩afaaaaaa啊a阿嘎啊啊发发用户$i 阿嘎Age阿嘎",
                    userNumber = "user$i",
                    type = null,
                    status = status,
                    friendRelation = friendRelation,
                    followRelation = followRelation
                )
            )
        }

        return result
    }

    private fun handleApiResult(
        result: Async<DataResult<LikeAndPlayerListData>>,
        isRefresh: Boolean,
        state: LikeAndPlayerItemModelState,
        pageSize: Int
    ): LikeAndPlayerItemModelState {
        val success = result.complete && result()?.succeeded == true
        val data = result()?.data

        return if (success && data != null) {
            // 将LikeAndPlayerItemData转换为RelationListResult.RelationUserInfo
            val newList = data.dataList.orEmpty().map { item ->
                RelationListResult.RelationUserInfo(
                    uuid = item.uid,
                    avatar = item.portrait,
                    nickname = item.nickname,
                    userNumber = null,
                    type = null,
                    status = item.onlineStatus,
                    friendRelation = item.friendRelation,
                    followRelation = item.followRelation
                )
            }

            // 获取最后一个元素的offset作为下一页的偏移量
            val lastOffset = if (data.dataList.isNullOrEmpty()) null else data.dataList.last().id
            // 判断是否到达列表末尾
            // 游玩列表只展示前50个
            val isEnd = newList.isEmpty() || newList.size < pageSize ||
                    (state.type == LikeAndPlayerItemFragment.TYPE_PLAYER && (state.list.size + newList.size) >= maxSize)


            if (isRefresh) {
                state.copy(
                    refresh = result.map { newList },
                    offset = lastOffset,
                    loadMore = result.map { LoadMoreState(isEnd) }
                )
            } else {
                var newSubList = state.list + newList
                // 数量超过了50，要截取前50个
                if (state.type == LikeAndPlayerItemFragment.TYPE_PLAYER && newSubList.size > maxSize) {
                    newSubList = newSubList.subList(0, maxSize)
                }

                state.copy(
                    refresh = result.map { newSubList },
                    offset = lastOffset,
                    loadMore = result.map { LoadMoreState(isEnd) }
                )
            }
        } else {
            if (isRefresh) {
                state.copy(
                    refresh = result.map { emptyList() },
                    offset = null,
                    loadMore = result.map { LoadMoreState(true) }
                )
            } else {
                state.copy(
                    loadMore = result.map { LoadMoreState(true) }
                )
            }
        }
    }

    companion object : KoinViewModelFactory<LikeAndPlayerItemViewModel, LikeAndPlayerItemModelState>() {
        private const val PAGE_SIZE = 20

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: LikeAndPlayerItemModelState
        ): LikeAndPlayerItemViewModel {
            return LikeAndPlayerItemViewModel(get(), get(), state)
        }
    }
}
