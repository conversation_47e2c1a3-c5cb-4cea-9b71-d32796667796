package com.socialplay.gpark.ui.editor.create

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asFlow
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.meta.biz.ugc.local.EditorLocalHelper
import com.meta.biz.ugc.model.EditorConfigJsonEntity
import com.meta.biz.ugc.model.EditorTemplate
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.EditorInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.LoadStatus
import com.socialplay.gpark.data.model.LoadType
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.editor.EditorCreationCombineResult
import com.socialplay.gpark.data.model.editor.EditorCreationShowInfo
import com.socialplay.gpark.data.model.editor.FormworkList
import com.socialplay.gpark.data.model.editor.ReqFormWorkArchiveBody
import com.socialplay.gpark.data.model.editor.ReqFormWorkV4Body
import com.socialplay.gpark.data.model.editor.TagInfo
import com.socialplay.gpark.data.model.editor.UGCV3Data
import com.socialplay.gpark.data.model.editor.UgcFormWorkArchiveData
import com.socialplay.gpark.data.model.editor.UgcFormWorkV4Data
import com.socialplay.gpark.data.model.editor.UgcGameInfo
import com.socialplay.gpark.data.repository.EditorRepository
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.download.DownloadFileProvider
import com.socialplay.gpark.function.gamereview.LoadMoreHelper
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.editor.create.v4.EditorCreateV4GameAdapter
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.SingleLiveData
import com.socialplay.gpark.util.extension.LifecycleCallback
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.lastOrNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import kotlin.math.min

/**
 * Created by bo.li
 * Date: 2023/2/28
 * Desc:
 */
class EditorCreateViewModel(
    private val metaRepository: com.socialplay.gpark.data.IMetaRepository,
    private val editorInteractor: EditorInteractor,
    private val tTaiInteractor: TTaiInteractor,
) : ViewModel() {

    companion object {
        const val FIRST_EDITION_PAGE_SIZE = 3
    }

    // 错误toast
    private val _errorToastLiveData: SingleLiveData<String?> by lazy { SingleLiveData() }
    val errorToastLiveData: LiveData<String?> = _errorToastLiveData

    // 模板列表：初期写死3个
    private val _templateLiveData: MutableLiveData<DataResult<MutableList<EditorTemplate>?>> by lazy { MutableLiveData() }
    val templateLiveData: LiveData<DataResult<MutableList<EditorTemplate>?>> = _templateLiveData

    // 本地已发布混合创作列表
    private val _creationLiveData: MutableLiveData<Pair<LoadStatus, MutableList<EditorCreationShowInfo>?>> by lazy { MutableLiveData() }
    val creationLiveData: LiveData<Pair<LoadStatus, MutableList<EditorCreationShowInfo>?>> =
        _creationLiveData

    // 创作数量
    private val _creationCountLiveData: MutableLiveData<Int> by lazy { MutableLiveData() }
    val creationCountLiveData: LiveData<Int> = _creationCountLiveData

    // banner
    private val _ugcBannerListLiveData = MutableLiveData<DataResult<List<UniJumpConfig>?>>()
    val ugcBannerListLiveData: LiveData<DataResult<List<UniJumpConfig>?>> get() = _ugcBannerListLiveData

    // 建造模板
    private val _formworkLiveData: MutableLiveData<MutableList<FormworkList.Formwork>?> =
        MutableLiveData()
    val formworkLiveData: LiveData<MutableList<FormworkList.Formwork>?> = _formworkLiveData

    // 建造模板（单模板）
    private val _formworkV3LiveData: MutableLiveData<UGCV3Data> = MutableLiveData()
    val formworkV3LiveData: LiveData<UGCV3Data> = _formworkV3LiveData

    // 建造模板 - 建造游戏
    private val _formworkBuildGameLiveData: MutableLiveData<FormworkList.Formwork?> =
        MutableLiveData()
    val formworkBuildGameLiveData: LiveData<FormworkList.Formwork?> = _formworkBuildGameLiveData

    // 建造模板 - 游戏列表
    private val _formworkGameListLiveData: MutableLiveData<MutableList<FormworkList.FormworkGame>?> =
        MutableLiveData()
    val formworkGameListLiveData: LiveData<MutableList<FormworkList.FormworkGame>?> =
        _formworkGameListLiveData

    // V4建造云存档模板
    private val _formworkV4ListLiveData: SingleLiveData<UgcFormWorkV4Data?> =
        SingleLiveData()
    val formworkV4ListLiveData: LiveData<UgcFormWorkV4Data?> = _formworkV4ListLiveData

    // V4建造云存档模板
    private val _archiveFormWorkLiveData: MutableLiveData<UgcFormWorkArchiveData?> =
        MutableLiveData()
    val archiveFormWorkLiveData: LiveData<UgcFormWorkArchiveData?> = _archiveFormWorkLiveData

    var tagList: List<TagInfo> = emptyList()

    private val _tabPosLiveData: MutableLiveData<Pair<Boolean, Int>> = MutableLiveData()
    val tabPosLiveData: LiveData<Pair<Boolean, Int>> = _tabPosLiveData

    // 创作最大数量
    val maxCreationCountLiveData = _creationCountLiveData.asFlow().combine(flow {
        emit(
            metaRepository.getMaxCloud(EditorConfigJsonEntity.TYPE_NORMAL).data?.maxCloudProject ?: 30
        )
    }) { count, max ->
        count to max
    }.asLiveData()

    // 模板
    val templateCallback: LifecycleCallback<(EditorTemplate) -> Unit> = LifecycleCallback()

    // 加载更多
    val loadMoreCallback: LifecycleCallback<(Int, FormworkList.Formwork) -> Unit> =
        LifecycleCallback()

    // 复制
    val copyCallback: LifecycleCallback<(File?, Int?) -> Unit> = LifecycleCallback()

    // 已发布分页orderId
    private var publishedOrderId: String? = null

    // ugc游戏去重
    private var gameSet: HashSet<String> = hashSetOf()

    var needSwitchToMine = false
    var needRefreshMine = false

    var isFromBottom = false

    fun initArgs(isFromBottom: Boolean) {
        this.isFromBottom = isFromBottom
    }

    fun refresh() {
        publishedOrderId = null
        gameSet.clear()
        viewModelScope.launch {
            getLocalCreationListFlow().combine(
                metaRepository.getPublishedCreationList(
                    null,
                    publishedOrderId
                )
            ) { local, published ->
                local to published
            }.combine(getTemplateListFlow()) { creation, template ->
                Triple(creation.first, creation.second, template)
            }.collect {
                // 混合列表
                handleCreationListRefresh(it.first, it.second)
                // 模板列表
                it.third.let { templateResult ->
                    _templateLiveData.value = templateResult
                    if (!templateResult.succeeded) {
                        _errorToastLiveData.postValue(templateResult.message)
                    }
                }
            }
        }
    }

    private suspend fun handleCreationListRefresh(
        localResult: DataResult<EditorCreationCombineResult?>,
        publishedResult: DataResult<UgcGameInfo>
    ) {
        Analytics.track(EventConstants.EVENT_UGC_CREATE_NUM) {
            put("num_type", "local")
            put("count", localResult.data?.list?.size ?: 0)
        }
        Analytics.track(EventConstants.EVENT_UGC_CREATE_NUM) {
            put("num_type", "published")
            put("count", publishedResult.data?.releaseCount ?: 0)
        }

        // 第一页本地
        val localList = localResult.data?.list ?: mutableListOf()
        // 云备份列表
        if (PandoraToggle.isUgcBackup) {
            metaRepository.mergeCloudList(
                localList,
                EditorConfigJsonEntity.TYPE_NORMAL
            )
        }
        withContext(Dispatchers.Main) {
            // 创作数量
            _creationCountLiveData.value =
                if (localResult.succeeded && publishedResult.succeeded) {
                    localList.size
                } else {
                    0
                }
        }
        // 第二页已发布
        handleCombineList(localList ?: arrayListOf(), true, publishedResult)
    }

    /**
     * 加载下一页创作列表
     */
    fun loadMoreCreationList() {
        viewModelScope.launch {
            metaRepository.getPublishedCreationList(null, publishedOrderId).collect { apiResult ->
                handleCombineList(
                    _creationLiveData.value?.second ?: arrayListOf(),
                    false,
                    apiResult
                )
            }
        }
    }

    /**
     * 处理已发布创作列表
     */
    private suspend fun handleCombineList(
        currentList: MutableList<EditorCreationShowInfo>,
        isRefresh: Boolean,
        apiResult: DataResult<UgcGameInfo>
    ) {
        val status = LoadStatus()
        if (!apiResult.succeeded) {
            status.message = apiResult.message
            status.status = LoadType.Fail
            _errorToastLiveData.postValue(apiResult.message)
            withContext(Dispatchers.Main) {
                _creationLiveData.value = status to currentList
            }
        } else {
            val netList = apiResult.data?.games
            publishedOrderId = netList?.lastOrNull()?.orderId
            if (!netList.isNullOrEmpty()) {
                // 已发布ugc信息赋值给前面的本地工程信息
                currentList.forEach {
                    it.draftInfo?.ugid?.let { ugId ->
                        netList.find { ugcInfo -> ugcInfo.id == ugId }?.apply {
                            it.ugcInfo = this
                            gameSet.add(ugId)
                            netList.remove(this)
                        }
                    }
                }
            }
            val newResult = DataResult.Success(
                EditorCreationCombineResult(
                    list = netList?.filter {
                        val result = !gameSet.contains(it.id)
                        gameSet.add(it.id)
                        result
                    }?.map {
                        EditorCreationShowInfo(draftInfo = null, ugcInfo = it)
                    }?.toMutableList(),
                    end = apiResult.data?.end
                )
            )
            withContext(Dispatchers.Main) {
                if (isRefresh) {
                    if (!PandoraToggle.isUgcBackupNotDeletePublish) {
                        newResult.data?.list?.let {
                            currentList.addAll(it)
                        }
                    } else {
                        newResult.data?.end = true
                    }
                    _creationLiveData.value = LoadMoreHelper.commonFetchLoadMoreData(
                        currentList,
                        currentList,
                        true,
                        newResult,
                        newResult.data.end == true
                    )
                } else {
                    _creationLiveData.value = LoadMoreHelper.commonFetchLoadMoreData(
                        currentList,
                        newResult.data.list,
                        false,
                        newResult,
                        newResult.data.end == true
                    )
                }
            }
        }
    }

    /**
     * 获取模板列表flow
     */
    private suspend fun getTemplateListFlow(): Flow<DataResult<ArrayList<EditorTemplate>?>> {
        return metaRepository.getUgcTemplateList(FIRST_EDITION_PAGE_SIZE, null, 1)
    }

    private fun getLocalCreationListFlow() = editorInteractor.getLocalCreationListFlow(gameSet) {
        _errorToastLiveData.postValue(it)
    }

    fun deleteCreation(item: EditorCreationShowInfo, context: Context) = viewModelScope.launch {
        deleteLocalFlow(item).combine(deletePublished(item)) { local, published ->
            local to published
        }.collect {
            val local = it.first
            val published = it.second
            if (!local) {
                _errorToastLiveData.postValue(context.getString(R.string.delete_draft_failed))
            }
            if (!published.succeeded) {
                _errorToastLiveData.postValue(context.getString(R.string.delete_published_failed))
            }
            if (local && published.succeeded) {
                _creationCountLiveData.value =
                    if (_creationCountLiveData.value == null) 0 else (_creationCountLiveData.value
                        ?: 0) - 1
            }

            val list = _creationLiveData.value?.second
            if (!list.isNullOrEmpty()) {
                if (local && published.succeeded) {
                    // 同时判断fileId和ugId，防止只判断一个id，index匹配错误的问题
                    val index =
                        list.indexOfFirst { it.draftInfo?.jsonConfig?.fileId == item.draftInfo?.jsonConfig?.fileId && it.ugcInfo?.id == item.ugcInfo?.id && it.cloudProject == item.cloudProject }
                    if (index != -1) {
                        list.removeAt(index)
                    }
                } else if (list.isNotEmpty()) {
                    val oldIndex = list.indexOf(item)
                    if (oldIndex >= 0) {
                        list[oldIndex] = item.copy(
                            ugcInfo = if (published.succeeded) null else item.ugcInfo,
                            draftInfo = if (local) null else item.draftInfo,
                        )
                    }
                }
            }
            _creationLiveData.value = LoadStatus(status = LoadType.Update) to list
        }
    }

    private suspend fun deleteLocalFlow(item: EditorCreationShowInfo): Flow<Boolean> = flow {
        val fileId = item.draftInfo?.jsonConfig?.fileId ?: item.cloudProject?.projectId ?: ""
        if (!item.isOnlyCloud() && (item.draftInfo == null || item.draftInfo?.path?.isEmpty() == null)) {
            emit(true)
        } else {
            val src = item.draftInfo?.path?.let { File(it) }
            val localDeleted = withContext(Dispatchers.IO) { src?.deleteRecursively() ?: true }
            emit(metaRepository.deleteAllBackup(EditorConfigJsonEntity.TYPE_NORMAL, fileId).data == true && localDeleted)
        }
    }


    private suspend fun deletePublished(item: EditorCreationShowInfo): Flow<DataResult<Boolean>> {
        val deleteUgid = item.getUgcId()
        return if (!deleteUgid.isNullOrEmpty() && !PandoraToggle.isUgcBackupNotDeletePublish) {
            metaRepository.deleteEditorPublished(deleteUgid)
        } else {
            MutableStateFlow(DataResult.Success(true))
        }
    }

    fun renameLocal(newName: String, path: String) {
        val list = _creationLiveData.value?.second ?: return
        val oldIndex = list.indexOfFirst { it.draftInfo != null && it.draftInfo?.path == path }
        if (oldIndex >= 0) {
            val item = list[oldIndex]
            item.draftInfo?.jsonConfig?.let {
                val newJsonConfig = it.copy(name = newName)
                list[oldIndex] = item.copy(
                    draftInfo = item.draftInfo?.copy(jsonConfig = newJsonConfig),
                )
            }
        }
        _creationLiveData.value = LoadStatus(status = LoadType.Update) to list
    }

    private suspend fun fetchBannerList(): Flow<DataResult<List<UniJumpConfig>?>> {
        return metaRepository.getUgcBannerList()
    }

    fun refreshV2Formwork() {
        viewModelScope.launch {
            getFormworkList(1, null).combine(fetchBannerList()) { formworks, banners ->
                formworks to banners
            }.collect {
                val (formworks, banners) = it
                if (formworks.succeeded) {
                    val result = formworks.data?.list?.toMutableList()
                    _formworkLiveData.value = result
                    _formworkBuildGameLiveData.value = result?.firstOrNull()
                    _formworkGameListLiveData.value =
                        result?.firstOrNull()?.gameList?.toMutableList()


                } else {
                    _formworkLiveData.value = null
                    _formworkBuildGameLiveData.value = null
                    _formworkGameListLiveData.value = null
                }

                if (banners.succeeded) {
                    _ugcBannerListLiveData.value = DataResult.Success(banners.data?.take(10))
                } else {
                    _errorToastLiveData.postValue(banners.message)
                }
            }
        }
    }

    fun loadMoreFormwork(pos: Int, formwork: FormworkList.Formwork) {
        viewModelScope.launch {
            if (formwork.loading || formwork.end) {
                return@launch
            }
            formwork.loading = true
            getFormworkList(formwork.curPage + 1, formwork.formworkCode).collect {
                val formworkList = _formworkLiveData.value ?: return@collect
                val curFormwork = formworkList.getOrNull(pos) ?: return@collect
                if (formwork === curFormwork) {
                    if (it.succeeded && it.data?.list != null) {
                        val newFormwork = it.data?.list?.getOrNull(0)
                        if (newFormwork != null) {
                            formwork.end = newFormwork.end
                            formwork.curPage++
                        }
                        val newList = newFormwork?.gameList ?: emptyList()
                        val list = formwork.gameList?.toMutableList() ?: mutableListOf()
                        val ids = list.map { game -> game.id }.toSet()
                        val filterList = if (BuildConfig.DEBUG) {
                            newList
                        } else {
                            newList.filter { game -> !ids.contains(game.id) }
                        }
                        list.addAll(filterList)
                        formwork.gameList = list
                        formwork.newList = filterList
                        formwork.fail = false
                    } else {
                        formwork.fail = true
                    }
                    formwork.loading = false
                } else {
                    formwork.loading = false
                }
                loadMoreCallback.dispatchOnMainThread {
                    invoke(pos, formwork)
                }
            }
        }
    }

    fun refreshV2Mine() {
        publishedOrderId = null
        gameSet.clear()
        viewModelScope.launch {
            getLocalCreationListFlow().combine(
                metaRepository.getPublishedCreationList(
                    null,
                    publishedOrderId
                )
            ) { local, published ->
                // 混合列表
                handleCreationListRefresh(local, published)
            }.collect()
        }
    }

    fun changeSelectTab(updatePager: Boolean, position: Int) {
        needSwitchToMine = false
        _tabPosLiveData.value = updatePager to position
    }

    suspend fun getFormworkList(page: Int, formworkCode: String?): Flow<DataResult<FormworkList>> {
        return metaRepository.getFormworkList(page, formworkCode)
    }

    suspend fun getFormWorkV4List(page: Int, tagId: Int?): Flow<DataResult<UgcFormWorkV4Data>> {
        return metaRepository.getFormWorkV4List(ReqFormWorkV4Body(page, tagId))
    }

    fun checkFormWorkArchive(gameCode: String, archiveId: String) {
        viewModelScope.launch {
            metaRepository.checkFormWorkArchive(ReqFormWorkArchiveBody(gameCode, archiveId)).collect {
                if (it.succeeded) {
                    _archiveFormWorkLiveData.value = it.data
                } else {
                    _archiveFormWorkLiveData.value = null
                }
            }
        }
    }

    fun requestBannerListApi() {
        viewModelScope.launch {
            fetchBannerList().collect {
                if (it.succeeded) {
                    _ugcBannerListLiveData.value = DataResult.Success(it.data?.take(10))
                } else {
                    _errorToastLiveData.postValue(it.message)
                }
            }
        }
    }

    fun requestFormWorkV4ListApi(adapter: EditorCreateV4GameAdapter) {
        viewModelScope.launch {
            if (adapter.loading) {
                return@launch
            }
            adapter.loading = true
            getFormWorkV4List(adapter.curPageIndex, adapter.curTagIndex).collect { result ->
                adapter.loading = false
                if (result.succeeded) {
                    tagList = result.data?.tagList ?: emptyList()
                    _formworkV4ListLiveData.setValue(result.data)
                    adapter.fail = false
                } else {
                    _formworkV4ListLiveData.setValue(null)
                    adapter.fail = true
                }
            }
        }
    }

    fun getTemplateByGameCode(formwork: FormworkList.Formwork, type: Long) {
        val gameCode = formwork.gameCode ?: return
        getTemplateByGameCode(gameCode, type)
    }

    fun getTemplateByGameCode(gameCode: String, type: Long) = viewModelScope.launch {
        metaRepository.getGameTemplate(gameCode, type).collect {
            if (it.succeeded) {
                it.data?.let {
                    templateCallback.dispatchOnMainThread {
                        invoke(it)
                    }
                }
            } else {
                _errorToastLiveData.postValue(it.message)
            }
        }
    }

    fun copyProject(path: String) = viewModelScope.launch {
        if (PandoraToggle.isUgcBackup) {
            val data = metaRepository.checkMaxCloud(EditorConfigJsonEntity.TYPE_NORMAL)
            if (data.succeeded) {
                if (data.data == true) {
                    copyCallback.dispatchOnMainThread { invoke(null, R.string.ugc_work_amount_reach_limit) }
                    return@launch
                }
            } else {
                copyCallback.dispatchOnMainThread { invoke(null, R.string.failed_get_ugc_work_upper_limit) }
                return@launch
            }
        }
        val source = File(path)
        val copyProjectFiles = EditorLocalHelper.copyProjectFiles(
            source, File(
                DownloadFileProvider.getEditorUserUnzipLocal(), source.name
            )
        )
        copyCallback.dispatchOnMainThread { invoke(copyProjectFiles, null) }
    }

    private val _delayLiveData: MutableLiveData<Boolean> = MutableLiveData()
    val delayLiveData: LiveData<Boolean> = _delayLiveData

    fun delay4Loading() = viewModelScope.launch {
        delay(300L)
        _delayLiveData.value = true
    }

    //获取单模板UGC建造页面的数据,全部走T台
    fun featSingleUgcModeData() = viewModelScope.launch {
        val result = tTaiInteractor.getTTaiValueHelper(TTaiKV.ID_UGC_SINGLE_MODE)
        Timber.tag("featSingleUgcModeData").d("result:$result")
        val formwork =
            GsonUtil.gsonSafeParseCollection<List<FormworkList.Formwork>>(result)?.firstOrNull()
        val ugids = formwork?.ugids ?: listOf()
        val games = getUGCGameListData(ugids, 0, ugids.size) ?: listOf()
        val data = UGCV3Data(formwork = formwork, ugids = formwork?.ugids, games = games)
        _formworkV3LiveData.postValue(data)
    }

    private suspend fun getUGCGameListData(
        data: List<String>, index: Int, count: Int
    ): List<FormworkList.FormworkGame>? {
        if (index >= data.size) return null
        val max = min(data.size, index + count)
        val ids = data.subList(index, max)
        val dataResult = metaRepository.getGameListByIds(ids).lastOrNull()
        return if (dataResult != null && dataResult.succeeded) {
            dataResult.data?.map {
                FormworkList.FormworkGame(
                    id = it.gameId?.toLong() ?: 0,
                    packageName = it.packageName,
                    ugcGameName = it.name,
                    gameCode = it.code,
                    banner = it.cover,
                    userName = it.gameAuthor?.name,
                    userIcon = it.gameAuthor?.avatar,
                    loveQuantity = 0L,
                    likeIt = false,
                    gameIcon = it.icon,
                    releaseTime = 0,
                    pvCount = it.playingCount ?: 0L,
                )
            }
        } else {
            emptyList()
        }
    }

}