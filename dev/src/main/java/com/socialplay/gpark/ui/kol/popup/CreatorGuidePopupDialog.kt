package com.socialplay.gpark.ui.kol.popup

import android.os.CountDownTimer
import android.os.Parcelable
import androidx.fragment.app.Fragment
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.bumptech.glide.Glide
import com.socialplay.gpark.databinding.DialogCreatorGuidePopupBinding
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.parcelize.Parcelize

/**
 * Created by bo.li
 * Date: 2024/8/2
 * Desc: kol创作者社区-新手引导
 * [需求文档] https://meta.feishu.cn/wiki/Q2gKwVmeuiI2vTkY4ZDcNzAYnlc
 */
@Parcelize
data class CreatorGuidePopupDialogArgs(val guideImgUrl: String) : Parcelable
class CreatorGuidePopupDialog : BaseDialogFragment() {

    override val binding by viewBinding(DialogCreatorGuidePopupBinding::inflate)
    private val args: CreatorGuidePopupDialogArgs by args()

    private var countDownTimer: CountDownTimer? = null

    companion object {
        const val COUNT_DOWN_TIME = 3_000L
        const val COUNT_DOWN_INTERVAL = 1_000L

        fun show(fragment: Fragment, args: CreatorGuidePopupDialogArgs) {
            CreatorGuidePopupDialog().apply {
                arguments = args.asMavericksArgs()
            }.show(fragment.childFragmentManager, "CreatorGuidePopupDialog")
        }
    }

    override fun init() {
        Glide.with(this).load(args.guideImgUrl).into(binding.ivImg)
        binding.tvClose.setOnAntiViolenceClickListener {
            // todo 可以兼容一下，如果当前没在对应tab，就跳到那个tab里
            dismissAllowingStateLoss()
        }
        countDown()
    }

    private fun countDown() {
        countDownTimer?.start()
        countDownTimer = object : CountDownTimer(COUNT_DOWN_TIME, COUNT_DOWN_INTERVAL) {
            override fun onTick(millisUntilFinished: Long) {
                val restTime = millisUntilFinished / COUNT_DOWN_INTERVAL
                binding.tvClose.enableWithAlpha(false)
//                binding.tvClose.text = getString(R.string.kol_enter_creator_second_param, restTime.toString())
            }

            override fun onFinish() {
                binding.tvClose.enableWithAlpha(true)
//                binding.tvClose.text = getString(R.string.kol_enter_creator)
            }
        }
    }

    override fun onDestroyView() {
        countDownTimer?.cancel()
        countDownTimer = null
        super.onDestroyView()
    }
}