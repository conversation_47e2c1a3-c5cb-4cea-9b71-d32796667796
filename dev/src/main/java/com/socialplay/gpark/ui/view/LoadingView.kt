package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Parcel
import android.os.Parcelable
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.annotation.AttrRes
import androidx.annotation.RequiresApi
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.view.isGone
import androidx.core.view.isVisible
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ViewLoadingBinding
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.*
import org.koin.core.context.GlobalContext
import kotlin.jvm.Throws

/**
 * create by: bin on 2021/5/19
 */
class LoadingView : ConstraintLayout {
    lateinit var bind: ViewLoadingBinding

    private var loadingColor = ContextCompat.getColor(context, R.color.color_loading_view)
    private var emptyColor = ContextCompat.getColor(context, R.color.textColorSecondary)
    private var loadingText: String? = null
    private var verticalBias: Float? = null

    constructor(context: Context) : super(context) {
        inflateLayout(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        inflateLayout(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, @AttrRes defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        inflateLayout(context, attrs)
    }

    private fun inflateLayout(context: Context, attrs: AttributeSet?) {
        bind = ViewLoadingBinding.inflate(LayoutInflater.from(context), this)
        if (attrs != null) {
            val attr = context.obtainStyledAttributes(attrs, R.styleable.LoadingView)
            loadingColor = attr.getColor(R.styleable.LoadingView_loadingColor, loadingColor)
            emptyColor = attr.getColor(R.styleable.LoadingView_emptyColor, emptyColor)
            loadingText = attr.getString(R.styleable.LoadingView_loadingText) ?: context.getString(R.string.loading)

            if(attr.hasValue(R.styleable.LoadingView_net_error_retry_button_text_color)){
                setNetErrorRetryButtonTextColor(attr.getColor(R.styleable.LoadingView_net_error_retry_button_text_color, Color.WHITE))
            }

            if(attr.hasValue(R.styleable.LoadingView_net_error_retry_button_background)){
                setNetErrorRetryButtonBackground(attr.getDrawable(R.styleable.LoadingView_net_error_retry_button_background))
            }

            if(attr.hasValue(R.styleable.LoadingView_net_error_tip_text_color)){
                setNetErrorTipTextColor(attr.getColor(R.styleable.LoadingView_net_error_tip_text_color,Color.argb(0x80,0xFF,0xFF,0xFF)))
            }

            attr.recycle()
        }
        initView()
    }

    private fun setNetErrorRetryButtonBackground(drawable: Drawable?) {
        bind.tvNetErrorRetry.background = drawable
    }

    private fun setNetErrorRetryButtonTextColor(color: Int) {
        bind.tvNetErrorRetry.setTextColor(color)
    }

    private fun setNetErrorTipTextColor(color: Int) {
        bind.tvNetError.setTextColor(color)
    }

    private fun initView() {
        bind.tvLoading.setTextColor(loadingColor)
        if (loadingText != null) {
            bind.tvLoading.text = loadingText
        }
        bind.tvEmpty.setTextColor(emptyColor)
        bind.pbLoading.visible()
        bind.pbLoading.playAnimation()
    }

    /**
     * 设置布局的竖直方向偏移量，
     * 注意：外层布局必须为ConstraintLayout
     */
    @Throws(ClassCastException::class)
    fun setVerticalBias(bias: Float) {
        verticalBias = bias
        if (verticalBias != 0.5F) {
            val cs = ConstraintSet()
            cs.clone(bind.root as ConstraintLayout)
            cs.setVerticalBias(bind.llNetError.id, bias)
            cs.setVerticalBias(bind.tvLoading.id, bias)
            cs.setVerticalBias(bind.llOtherError.id, bias)
            cs.setVerticalBias(bind.tvEmpty.id, bias)
            cs.applyTo(bind.root as ConstraintLayout)
        }
    }

    fun setEmptyBtnClick(onClick: () -> Unit): LoadingView {
        bind.btnEmpty.setOnAntiViolenceClickListener {
            onClick()
        }
        return this
    }

    fun setRetry(retry: () -> Unit): LoadingView {
        bind.tvOtherErrorRetry.setOnAntiViolenceClickListener {
            showLoading(true)
            retry.invoke()
        }
        bind.llNetError.setOnAntiViolenceClickListener {
            if (!NetUtil.isNetworkAvailable()){
                ToastUtil.showShort(it.context, R.string.abnormal_network)
            } else {
                showLoading(true)
                retry.invoke()
            }
        }
        return this
    }

    fun hide() {
        if (!bind.root.isGone) {
            bind.root.isGone = true
            bind.pbLoading.pauseAnimation()
        }
    }

    fun showError(forceShowNetworkError: Boolean = false) {
        if (!forceShowNetworkError && NetUtil.isNetworkAvailable()) {
            if (!bind.root.isVisible) {
                bind.root.isVisible = true
            }
            bind.root.isClickable = true
            bind.tvLoading.gone()
            bind.pbLoading.gone()
            bind.pbLoading.pauseAnimation()
            bind.llOtherError.visible()
            bind.tvEmpty.gone()
            bind.btnEmpty.gone()
            bind.llNetError.gone()
        } else {
            showNetError()
        }
    }

    fun showLoading(showLoginTips: Boolean = true, msg: String? = null) {
        if (!bind.root.isVisible) {
            bind.root.isVisible = true
        }
        bind.root.isClickable = true
        bind.llOtherError.gone()
        bind.tvEmpty.gone()
        bind.btnEmpty.gone()
        bind.tvLoading.visible(showLoginTips)
        bind.pbLoading.visible()
        bind.pbLoading.resumeAnimation()
        bind.llNetError.gone()
        if (!msg.isNullOrEmpty())
            bind.tvLoading.text = msg
    }

    fun with(loading: Boolean) {
        if (loading) {
            showLoading(true)
        } else {
            hide()
        }
    }

    fun with(state: Async<*>) {
        when (state) {
            is Loading -> showLoading(true)
            is Success -> hide()
            is Fail -> showError()
            else -> hide()
        }
    }

    fun showLoading(showLoginTips: Boolean = true, msg: String? = null, backGroundColor: Int) {
        showLoading(showLoginTips, msg)
        background = ColorDrawable(backGroundColor)
    }

    fun showEmpty(msg: String = GlobalContext.get().get<Context>().getString(R.string.no_data), resId: Int = 0, showBtn: Boolean = false, btnTxt: String? = null, enableClick: Boolean = false) {
        if (!bind.root.isVisible) {
            bind.root.isVisible = true
        }
        bind.root.isClickable = enableClick
        bind.tvEmpty.setCompoundDrawablesWithIntrinsicBounds(0,resId,0,0)
        bind.tvLoading.gone()
        bind.pbLoading.gone()
        bind.llOtherError.gone()
        bind.tvEmpty.visible()
        bind.llNetError.gone()
        bind.tvEmpty.text = msg
        bind.btnEmpty.isVisible = showBtn
        bind.btnEmpty.text = btnTxt
    }

    fun showOtherEmpty(msg: String = GlobalContext.get().get<Context>().getString(R.string.no_data), resId: Int = 0) {
        if (!bind.root.isVisible) {
            bind.root.isVisible = true
        }
        bind.llOtherError.visible()
        bind.imgOtherError.visible()
        bind.tvOtherErrorRetry.gone()
        if(resId!=0) {
            bind.imgOtherError.setImageResource(resId)
        }
        bind.tvOtherError.text = msg
        bind.tvEmpty.gone()
        bind.tvLoading.gone()
        bind.pbLoading.gone()
        bind.llNetError.gone()
        bind.btnEmpty.isVisible = false
    }

    /**
     * 网络链接失败
     */
    private fun showNetError() {
        if (!bind.root.isVisible) {
            bind.root.isVisible = true
        }
        bind.root.isClickable = true
        bind.llNetError.visible()
        bind.tvLoading.gone()
        bind.pbLoading.gone()
        bind.pbLoading.pauseAnimation()
        bind.llOtherError.gone()
        bind.tvEmpty.gone()
        bind.btnEmpty.gone()
    }

    override fun onRestoreInstanceState(state: Parcelable?) {
        // 6.0以下调用super会崩溃, 但是不调用也会崩溃, 所以需要针对性catch
        kotlin.runCatching { super.onRestoreInstanceState(state) }
        state ?: return
        visibility = (state as SavedState).visibility
    }

    override fun onSaveInstanceState(): Parcelable {
        val superState = super.onSaveInstanceState()
        val ss = SavedState(superState)
        ss.visibility = visibility
        return ss
    }

    internal class SavedState : BaseSavedState {
        var visibility: Int = 0

        @RequiresApi(Build.VERSION_CODES.N)
        constructor(source: Parcel?, loader: ClassLoader?) : super(source, loader) {
            visibility = source?.readInt() ?: 0
        }

        constructor(source: Parcel?) : super(source) {
            visibility = source?.readInt() ?: 0
        }

        constructor(superState: Parcelable?) : super(superState)

        override fun writeToParcel(out: Parcel, flags: Int) {
            super.writeToParcel(out, flags)
            out.writeInt(visibility)
        }

        companion object CREATOR : Parcelable.ClassLoaderCreator<SavedState> {

            override fun createFromParcel(source: Parcel?, loader: ClassLoader?): SavedState {
                return if (Build.VERSION.SDK_INT >= 24) SavedState(source, loader) else SavedState(
                    source
                )
            }

            override fun createFromParcel(source: Parcel?): SavedState {
                return SavedState(source)
            }

            override fun newArray(size: Int): Array<SavedState?> {
                return arrayOfNulls(size)
            }
        }

    }

}