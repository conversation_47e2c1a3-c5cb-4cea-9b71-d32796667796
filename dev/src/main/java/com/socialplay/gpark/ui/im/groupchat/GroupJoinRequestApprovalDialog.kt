package com.socialplay.gpark.ui.im.groupchat

import android.os.Bundle
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.navArgs
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.groupchat.GroupChatApplyInfo
import com.socialplay.gpark.databinding.DialogGroupJoinRequestApprovalBinding
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.property.viewBinding

class GroupJoinRequestApprovalDialog : BaseDialogFragment() {
    companion object {
        const val REQUEST_KEY_JOIN_REQUEST_APPROVAL = "request_key_join_request_approval"
        const val KEY_JOIN_REQUEST_APPROVAL_RESULT = "key_join_request_approval_result"
    }

    override val binding: DialogGroupJoinRequestApprovalBinding by viewBinding(
        DialogGroupJoinRequestApprovalBinding::inflate
    )

    override fun maxWidth(): Int {
        val ctx = context
        ctx ?: return 0
        return (ScreenUtil.getScreenWidth(ctx) * 2 / 3).coerceAtLeast(dp(343))
    }

    private val args by navArgs<GroupJoinRequestApprovalDialogArgs>()

    private lateinit var requestInfo: GroupChatApplyInfo

    override fun init() {
        val requestInfo = args.requestInfo
        if (requestInfo == null) {
            dismissAllowingStateLoss()
            return
        }
        this.requestInfo = requestInfo

        binding.root.setOnClickListener {
            dismissAllowingStateLoss()
        }
        binding.dialogLayout.isClickable = true

        Glide.with(this)
            .load(requestInfo.chatGroupIcon)
            .placeholder(R.drawable.icon_item_group_chat_avatar)
            .into(binding.ivAvatar)

        binding.tvUserName.text = requestInfo.userName ?: ""
        binding.tvJoinGroupDesc.setTextWithArgs(
            R.string.request_want_to_join_group_desc,
            requestInfo.chatGroupName ?: ""
        )
        binding.tvJoinGroupRequestInfo.text = requestInfo.reason ?: ""
        binding.tvSave.setOnAntiViolenceClickListener {
            dismissWithResult(true)
        }
        binding.tvCancel.setOnAntiViolenceClickListener {
            dismissWithResult(false)
        }
    }

    private fun dismissWithResult(agree: Boolean) {
        setFragmentResult(REQUEST_KEY_JOIN_REQUEST_APPROVAL, Bundle().apply {
            putInt(
                KEY_JOIN_REQUEST_APPROVAL_RESULT, if (agree) {
                    GroupChatApplyInfo.STATUS_AGREE
                } else {
                    GroupChatApplyInfo.STATUS_REJECT
                }
            )
        })
        dismissAllowingStateLoss()
    }
}