package com.socialplay.gpark.ui.account

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.event.UserDataUpdateEvent
import com.socialplay.gpark.util.SingleLiveData
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch


class NickNameResetViewModel(val repository: com.socialplay.gpark.data.IMetaRepository, val accountInteractor: AccountInteractor) : ViewModel() {


    private val _clearIconFlow = MutableStateFlow(false)
    val clearIconFlow: Flow<Boolean> = _clearIconFlow

    private val _nameLiveData = SingleLiveData<String?>()
    val nameLiveData: LiveData<String?> = _nameLiveData

    private val _nicknameChangeResultFlow = MutableStateFlow<Triple<DataResult<Boolean>, String?, Boolean>?>(null)
    val nicknameChangeResultFlow: Flow<Triple<DataResult<Boolean>, String?, Boolean>?> = _nicknameChangeResultFlow

    private val _accountBanLiveData = SingleLiveData<Boolean>()
    val accountBanLiveData: LiveData<Boolean> = _accountBanLiveData

    fun toggleClearIconVisibility() {
        _clearIconFlow.value = !_clearIconFlow.value
    }

    fun postNameValueChanged(account: String?) {
        _nameLiveData.postValue(account)
    }

    fun updateUserNick(nickname: String) = viewModelScope.launch {
        if (accountInteractor.isAccountBanned()) {
            _accountBanLiveData.postValue(true)
            return@launch
        }
        accountInteractor.updateUser(mNickname = nickname, reviewBirth = false).collect {
            updateResult(it, it.message)
            if (it.data == true) {
                UserDataUpdateEvent.notifyNickname(nickname)
            }
        }
    }

    fun updateUserCity(city: String) = viewModelScope.launch {
        if (accountInteractor.isAccountBanned()) {
            _accountBanLiveData.postValue(true)
            return@launch
        }
        accountInteractor.updateUser(mCity = city, reviewBirth = false).collect {
            updateResult(it, it.message)
        }
    }

    fun updateUserInformation(information: String) = viewModelScope.launch {
        if (accountInteractor.isAccountBanned()) {
            _accountBanLiveData.postValue(true)
            return@launch
        }
        accountInteractor.updateUser(mSignature = information, reviewBirth = false).collect {
            updateResult(it, it.message)
            if (it.data == true) {
                UserDataUpdateEvent.notifySignature(information)
            }
        }
    }

    private fun updateResult(result: DataResult<Boolean>, message: String?) {
        val oldFlag = _nicknameChangeResultFlow.value?.third ?: false
        _nicknameChangeResultFlow.value = Triple(result, message, !oldFlag)
    }

}