package com.socialplay.gpark.ui.outfit.feed

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.meta.pandora.utils.ConcurrentSet
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.model.outfit.UgcDesignFeed
import com.socialplay.gpark.data.model.outfit.UgcDesignLikeRequest
import com.socialplay.gpark.data.repository.UgcRepository
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.util.extension.insertAt
import com.socialplay.gpark.util.extension.replaceAt
import com.socialplay.gpark.util.extension.replaceSingle
import org.koin.android.ext.android.get

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/06
 *     desc   :
 * </pre>
 */
data class UgcAssetRookieTabState(
    val assets: Async<List<UgcDesignFeed>> = Uninitialized,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val page: Int = 1,
    val uniqueTag: Int = 0,
    val guideInvoke: Pair<Int, Boolean>? = null,
) : MavericksState

class UgcAssetRookieTabViewModel(
    initState: UgcAssetRookieTabState,
    val accountInteractor: AccountInteractor,
    private val ugcRepo: UgcRepository,
    private val tTaiInteractor: TTaiInteractor,
) : BaseViewModel<UgcAssetRookieTabState>(initState) {

    val curUuid = accountInteractor.curUuid

    private val assetSet = ConcurrentSet<String>()

    init {
        getFeed(true)
    }

    fun getFeed(isRefresh: Boolean) = withState { s ->
        if (s.loadMore is Loading) return@withState
        val newPage = if (isRefresh) 1 else s.page + 1
        ugcRepo.getUgcRookieFeed(
            newPage,
            PAGE_SIZE
        ).map {
            if (isRefresh) {
                assetSet.clear()
            }
            val newList = it.data?.filter { outfit ->
                val result = !assetSet.contains(outfit.feedId)
                if (result) {
                    assetSet.add(outfit.feedId)
                }
                result
            }
            it.copy(
                data = if (isRefresh) {
                    newList
                } else {
                    oldState.assets().insertAt(-1, newList)
                },
                isLastPage = it.isLastPage || newList.isNullOrEmpty()
            )
        }.execute { result ->
            when (result) {
                is Success -> {
                    val wrapper = result()
                    copy(
                        assets = Success(wrapper.data.orEmpty()),
                        loadMore = Success(LoadMoreState(isEnd = wrapper.isLastPage)),
                        page = newPage,
                        uniqueTag = if (isRefresh) uniqueTag.xor(1) else uniqueTag
                    )
                }

                is Fail -> {
                    copy(
                        assets = if (isRefresh) Fail(result.error, assets()) else assets,
                        loadMore = Fail(
                            result.error,
                            if (isRefresh) LoadMoreState(needRefresh = true) else null
                        )
                    )
                }

                else -> {
                    copy(
                        assets = if (isRefresh) Loading(assets()) else assets,
                        loadMore = Loading()
                    )
                }
            }
        }
    }

    fun likeFeed(outfit: UgcDesignFeed, position: Int) = withState { s ->
        val oldListResult = s.assets
        val oldList = oldListResult()
        val newOutfit = outfit.switchLike()
        val newList = oldList.replaceAt(position, newOutfit)
        val newListResult = oldListResult.copyEx(newList)
        setState { copy(assets = newListResult) }
        ugcRepo.likeUgcDesign(UgcDesignLikeRequest(outfit.feedId, newOutfit.isFavorite))
            .map {
                if (PandoraToggle.enableModuleGuideFirstInteract
                    && newOutfit.isFavorite
                    && accountInteractor.assetFirstInteract
                ) {
                    val newGuideInvoke =
                        BaseAccountInteractor.MODULE_GUIDE_INVOKE_LIKE to !(oldState.guideInvoke?.second
                            ?: false)
                    setState { copy(guideInvoke = newGuideInvoke) }
                }
            }
            .execute {
                this
            }
    }

    fun updateLikeCount(itemId: String, isLike: Boolean, likeCount: Long) = withState { s ->
        val oldListResult = s.assets
        val oldList = oldListResult()
        val (ok, newList) = oldList.replaceSingle(
            predicate = {
                it.feedId == itemId
            },
            transform = {
                it.copy(isFavorite = isLike, favorites = likeCount)
            }
        )
        if (!ok) return@withState
        setState {
            copy(assets = assets.copyEx(newList))
        }
    }

    companion object : KoinViewModelFactory<UgcAssetRookieTabViewModel, UgcAssetRookieTabState>() {

        const val PAGE_SIZE = 20

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: UgcAssetRookieTabState
        ): UgcAssetRookieTabViewModel {
            return UgcAssetRookieTabViewModel(state, get(), get(), get())
        }
    }
}