package com.socialplay.gpark.ui.web.jsinterfaces.ext

import android.content.Context
import com.meta.pandora.Pandora
import com.meta.pandora.data.entity.Event
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.model.API_HEADER_TOKEN
import com.socialplay.gpark.di.CommonParamsProvider
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.developer.DeveloperPandoraToggle
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeApi
import com.socialplay.gpark.util.extension.toMap
import org.json.JSONArray
import org.json.JSONObject
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * app相关接口
 * <AUTHOR>
 * @date 2021/05/21
 */

/**
 * 获取版本code
 * @return 版本code，例：2240000
 */
fun JsBridgeApi.getAppVersionCode() =
    createSuccessResult(data = com.socialplay.gpark.BuildConfig.VERSION_CODE.toString())

/**
 * 获取版本名称
 * @return 版本名称，例：2.24.0.0
 */
fun JsBridgeApi.getAppVersionName() = createSuccessResult(data = com.socialplay.gpark.BuildConfig.VERSION_NAME)

/**
 * 获取渠道名称
 * @return 渠道名称
 */
fun JsBridgeApi.getAppChannelName() = createSuccessResult(data = helper.deviceInteractor.channelId)

/**
 * 获取渠道名称
 * @return 渠道名称
 */
fun JsBridgeApi.getAppChannel() = createSuccessResult(data = helper.deviceInteractor.channelId)


/**
 * 获取包名
 */
fun JsBridgeApi.getAppPackageName() = createSuccessResult(data = GlobalContext.get().get<Context>().packageName)

/**
 * 获取底层通用方法
 */
fun JsBridgeApi.getBaseParams(): String = createSuccessResult(
    data = JSONObject(getCommonParams()).toString()
)

private fun getCommonParams(): Map<String, Any> {
    val params = HashMap<String, Any>()
    val commonParamsProvider = GlobalContext.get().get<CommonParamsProvider>()
    commonParamsProvider.also {
        params["device_id"] = it.deviceId
        params["oaId"] = it.oaId
        params["lang"] = it.languageCode
        params["app_version_code"] = it.appVersionCode
        params["device_name"] = it.deviceName
        params["system_version"] = it.systemVersion
        params["system_version_code"] = it.systemVersionCode
        params["device_brand"] = it.deviceBrand
        params["platform"] = it.platform
        params["self_package_name"] = it.selfPackageName
        params["uid"] = it.uid
        params["network_type"] = it.networkType
        params["pandora_ab_group"] = it.pandora_ab_group
        params["pandora_switch_ab_group"] = it.pandora_switch_ab_group
        params["pandora_switch_new_ab_group"] = it.pandora_switch_new_ab_group
        params["pandora_new_ab_group"] = it.pandora_new_ab_group
        params[API_HEADER_TOKEN] = it.accessToken ?: ""
    }
    return params
}

/**
 * 保存数据
 */
fun JsBridgeApi.saveString(paramArray: JSONArray): String {
    val key = paramArray.optString(0)
    val text = paramArray.optString(1)

    if (key.isBlank() || text.isBlank()) {
        return createErrorResult(msg = "function saveString() params key or text isBlank")
    }

    //保存web传入的数据
    helper.getWebKv().saveWebData(key, text)
    return createSuccessResult()
}

/**
 * 获取数据
 */
fun JsBridgeApi.getString(paramArray: JSONArray): String {
    val key = paramArray.optString(0)
    val result = helper.getWebKv().getWebData(key)
    return createSuccessResult(data = result)
}

/**
 * 原生统计上报
 * @param kind 上报kind
 * @param desc kind的描述信息
 * @param extraJson 扩展参数，map结构json格式
 */
fun JsBridgeApi.nativeAnalytics(paramArray: JSONArray): String {
    val kind = paramArray.optString(0)
    val desc = paramArray.optString(1)
    val extraJson = paramArray.optString(2)

    if (kind.isBlank() || desc.isBlank()) {
        return createErrorResult(msg = "function nativeAnalytics() params kind or desc isBlank")
    }

    val map = mutableMapOf<String, Any>()
    if (extraJson.isNotBlank()) {
        try {
            val paramsJson = JSONObject(extraJson)
            map.putAll(paramsJson.toMap()) 
        } catch (e: Exception) {
            Timber.e("nativeAnalytics: $e")
        }
    }
    //上报数据
    Analytics.track(Event(kind, desc)) {
        putAll(map)
    }
    return createSuccessResult()
}

fun JsBridgeApi.getNativeAbValue(paramArray: JSONArray): String {
    val key: String = paramArray.optString(0)
    val desc: String = paramArray.optString(1)
    val defValue: String = paramArray.optString(2)
    val defType: String = paramArray.optString(3)
    Timber.d("getNativeAbValue key:%s, desc:%s, defValue:%s, defType:%s", key, desc, defValue, defType)
    if (key.isBlank() || defType.isBlank()) return createErrorResult(msg = "function getNativeAbValue() params key or defType is invalid")

    val value: Any
    val defV: Any
    try {
        when {
            "int".equals(defType, true)     -> {
                defV = defValue.toInt()
                value = getValue(key, defV)
            }

            "long".equals(defType, true)    -> {
                defV = defValue.toLong()
                value = getValue(key, defV)
            }

            "boolean".equals(defType, true) -> {
                defV = defValue.toBoolean()
                value = getValue(key, defV)
            }

            "float".equals(defType, true)   -> {
                defV = defValue.toFloat()
                value = getValue(key, defV)
            }

            "double".equals(defType, true)  -> {
                defV = defValue.toDouble()
                value = getValue(key, defV)
            }

            "byte".equals(defType, true)    -> {
                defV = defValue.toByte()
                value = getValue(key, defV)
            }

            "short".equals(defType, true)   -> {
                defV = defValue.toShort()
                value = getValue(key, defV)
            }

            "string".equals(defType, true)  -> {
                defV = defValue
                value = getValue(key, defV)
            }

            else                            -> {
                defV = defValue
                value = getValue(key, defV)
            }
        }
    } catch (e: NumberFormatException) {
        val msg = "getNativeAbValue key:$key, defType:$defType, defValue:$defValue"
        Timber.e("getNativeAbValue value:$defValue , format error $msg")
        if (BuildConfig.DEBUG) {
            throw IllegalStateException(msg, e)
        }
        return createErrorResult(msg = msg, data = defValue)
    }
    Timber.d("getNativeAbValue value:$value")
    return createSuccessResult(data = value)
}

private fun <T> getValue(key: String, defaultValue: T): T {
    return if (DeveloperPandoraToggle.isEnable()) {
        DeveloperPandoraToggle.getValue(key, defaultValue) ?: defaultValue
    } else {
        Pandora.getAbConfig(key, defaultValue)
    }
}

