package com.socialplay.gpark.ui.post.feed.tag

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.CommunityFeedWrapper
import com.socialplay.gpark.data.model.post.request.PostTagFeedRequest
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.post.feed.base.BaseCommunityFeedViewModel
import com.socialplay.gpark.ui.post.feed.base.ICommunityFeedModelState
import com.socialplay.gpark.util.ToastData
import org.koin.android.ext.android.get

/**
 * Created by bo.li
 * Date: 2023/9/15
 * Desc:
 */
data class TagCommunityFeedModelState(
    override val refresh: Async<List<CommunityFeedInfo>> = Uninitialized,
    override val toastMsg: ToastData = ToastData.EMPTY,
    override val loadMore: Async<LoadMoreState> = Uninitialized,
    override val nextPage: Int = 1,
    override val notifyCheckVideo: Async<Long> = Uninitialized,
    val scrollToTop: Async<Boolean> = Uninitialized,
) : ICommunityFeedModelState {

    override fun updateFeedData(list: List<CommunityFeedInfo>): ICommunityFeedModelState {
        return copy(refresh = refresh.copyEx(list))
    }

    override fun toast(toastMsg: ToastData): ICommunityFeedModelState {
        return copy(toastMsg = toastMsg)
    }

    override fun checkVideo(checkVideo: Async<Long>): ICommunityFeedModelState {
        return copy(notifyCheckVideo = checkVideo)
    }

    override fun feedRefresh(result: Async<CommunityFeedWrapper>): ICommunityFeedModelState {
        val newRefresh = result.map { wrapper ->
            wrapper.dataList.distinctBy { it.postId }
        }
        return copy(
            refresh = newRefresh,
            nextPage = nextPage + if (result is Success) 1 else 0,
            loadMore = result.map { LoadMoreState(result()?.end ?: true) }
        )
    }

    override fun feedLoadMore(result: Async<CommunityFeedWrapper>): ICommunityFeedModelState {
        return copy(
            refresh = if (result is Success) {
                val oldList = refresh.invoke()
                result.map { wrapper ->
                    if (oldList.isNullOrEmpty()) {
                        wrapper.dataList
                    } else {
                        oldList + wrapper.dataList
                    }.distinctBy { it.postId }
                }
            } else {
                refresh
            },
            nextPage = nextPage + if (result is Success) 1 else 0,
            loadMore = result.map { LoadMoreState(result()?.end ?: true) }
        )
    }
}

class TagCommunityFeedViewModel(
    private val repository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    initialState: TagCommunityFeedModelState
) : BaseCommunityFeedViewModel<TagCommunityFeedModelState>(repository, accountInteractor, initialState) {

    fun refreshTagFeed(orderType: Int, blockId: Long?, type: String) {
        withState { oldState ->
            if (oldState.loadMore is Loading) return@withState
            // 处理type的string和int转换
            repository.getCommunityFeed(
                orderType,
                PostTagFeedRequest.getTagType(type),
                null,
                blockId,
                PAGE_SIZE,
                1
            ).map {
                notifyCheckVideo()
                it
            }.execute { result ->
                feedRefresh(result) as TagCommunityFeedModelState
            }
        }
    }

    fun loadMoreTagFeed(orderType: Int, blockId: Long?, type: String) {
        withState { oldState ->
            if (oldState.loadMore is Loading) return@withState
            val nextPage = oldState.nextPage
            repository.getCommunityFeed(
                orderType,
                PostTagFeedRequest.getTagType(type),
                null,
                blockId,
                PAGE_SIZE,
                nextPage
            ).execute { result ->
                feedLoadMore(result) as TagCommunityFeedModelState
            }
        }
    }

    companion object : KoinViewModelFactory<TagCommunityFeedViewModel, TagCommunityFeedModelState>() {

        private const val PAGE_SIZE = 20

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: TagCommunityFeedModelState
        ): TagCommunityFeedViewModel {
            return TagCommunityFeedViewModel(get(), get(), state)
        }
    }
}