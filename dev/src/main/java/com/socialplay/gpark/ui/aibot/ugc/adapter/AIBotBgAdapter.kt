package com.socialplay.gpark.ui.aibot.ugc.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bumptech.glide.Glide
import com.socialplay.gpark.data.model.aibot.AIBotCreateImageInfo
import com.socialplay.gpark.databinding.ItemAiBotCreateImageBgBinding
import com.socialplay.gpark.databinding.ItemAiBotCreateImageBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/26
 *     desc   :
 *
 */
class AIBotBgAdapter:BaseAdapter<String,ItemAiBotCreateImageBgBinding> (){
    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): ItemAiBotCreateImageBgBinding {
        return  ItemAiBotCreateImageBgBinding.inflate(layoutInflater,parent,false)
    }

    override fun convert(
        holder: BindingViewHolder<ItemAiBotCreateImageBgBinding>,
        item: String,
        position: Int
    ) {
        Glide.with(context).load(item)
            .into(holder.binding.imgStyle)
    }
}