<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.MetaApp" parent="Theme.MaterialComponents.DayNight.NoActionBar">

        <item name="android:windowLayoutInDisplayCutoutMode" tools:targetApi="o_mr1">
            shortEdges <!-- default, shortEdges, never -->
        </item>

        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:navigationBarColor">@color/navigation_bar_color</item>
        <!-- Remove shadow below action bar Android < 5.0 -->
        <item name="elevation">0dp</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@drawable/window_background</item>
        <item name="appBarLayoutStyle">@drawable/window_background</item>
        <!-- Customize your theme here. -->
        <item name="android:textColorPrimary">@color/textColorPrimary</item>
        <!-- 字体默认-->
        <item name="android:fontFamily">@font/poppins_regular_400</item>
        <item name="android:textColor">@color/textColorPrimary</item>
        <item name="android:textSize">@dimen/textSizeNormal</item>

        <item name="android:buttonStyle">@style/Button</item>
        <item name="materialButtonStyle">@style/Button</item>
        <item name="android:buttonStyleSmall">@style/Button.S14.PoppinsRegular400.Small</item>
        <item name="android:textViewStyle">@style/MetaTextView</item>
        <item name="title_divider_color">#ECEAEA</item>
        <item name="title_text_color">@color/textColorPrimary</item>
        <item name="android:textCursorDrawable">@drawable/bg_cursor</item>
        <item name="colorPrimary">@color/colorPrimary</item>

    </style>

    <style name="Theme.MainActivity" parent="Theme.MetaApp">
        <item name="android:windowBackground">@drawable/bg_splash</item>
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:navigationBarColor">@color/transparent</item>
    </style>

    <style name="Theme.LanguageActivity" parent="Theme.MetaApp">
        <item name="android:windowBackground">@color/white</item>
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:navigationBarColor">@color/transparent</item>
    </style>

    <style name="DialogStyle" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <style name="DialogStyle.Input">
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowSoftInputMode">stateVisible|adjustResize</item>
    </style>

    <style name="DialogStyle.Input.WhiteNavStatusBar">
        <item name="android:statusBarColor">@color/white</item>
        <item name="android:navigationBarColor">@color/white</item>
    </style>

    <style name="DialogStyle.Input.AdjustPan">
        <item name="android:windowSoftInputMode">stateVisible|adjustPan</item>
    </style>

    <style name="DialogStyle.Input.AdjustPan.NavTrans">
        <item name="android:windowSoftInputMode">stateVisible|adjustPan</item>
        <item name="android:navigationBarColor">@color/black_70</item>
    </style>

    <style name="BottomDialogStyleSlow" parent="DialogStyle">
        <item name="android:windowAnimationStyle">@style/AnimBottomSlow</item>
    </style>

    <style name="BottomDialogStyle" parent="DialogStyle">
        <item name="android:windowAnimationStyle">@style/AnimBottom</item>
    </style>

    <style name="BottomDialogStyleOrigin" parent="DialogStyle">
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
    </style>

    <style name="GlobalShareDialogAnimation" parent="Animation.AppCompat.Dialog">
        <item name="android:windowEnterAnimation">@anim/design_bottom_sheet_slide_in_slow</item>
        <item name="android:windowExitAnimation">@anim/abc_popup_exit_slow</item>
    </style>

    <style name="GlobalShareDialog" parent="DialogStyle">
        <item name="android:windowAnimationStyle">@style/GlobalShareDialogAnimation</item>
    </style>

    <style name="BottomDialogStyleOrigin.NoFullScreen">
        <item name="android:windowFullscreen">false</item>
        <item name="android:statusBarColor">@color/transparent</item>
    </style>

    <style name="DetailHalfModeDialogStyle" parent="BottomDialogStyleOrigin">
        <item name="android:windowFullscreen">false</item>
        <item name="android:statusBarColor">@color/transparent</item>
    </style>


    <style name="VideoFeedCommentBottomSheetDialogStyle" parent="BottomDialogStyleOrigin">
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowFullscreen">false</item>
    </style>


    <style name="DialogStyleNonFullScreen" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:navigationBarColor">@color/transparent</item>
        <item name="android:statusBarColor">@color/transparent</item>
    </style>



    <style name="DialogStyleNonFullScreen.NoAnimation">
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@null</item>
    </style>

    <style name="DialogStyleNonFullScreen.NoEnterAnimation">
        <item name="android:windowAnimationStyle">@style/NoDialogEnterAnimation</item>
        <item name="android:windowEnterAnimation">@null</item>
    </style>

    <style name="NoDialogEnterAnimation" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@null</item>
    </style>

    <style name="NoAnimation" parent="@android:style/Animation">
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@null</item>
    </style>

    <style name="OAuthCallbackTheme" parent="Theme.MetaApp">
        <item name="android:windowNoTitle">true</item>
        <item name="colorPrimary">@android:color/background_dark</item>
        <item name="colorPrimaryDark">@android:color/background_dark</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
    </style>


    <style name="wrapperFullScreen" parent="Theme.MetaApp">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>


    <style name="AdFullScreenTheme" parent="Theme.MetaApp">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:statusBarColor" tools:targetApi="l">@android:color/transparent</item>
    </style>

    <style name="Theme.FullScreen" parent="Theme.MetaApp">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowTranslucentStatus">true</item>
    </style>

    <style name="TransparentTheme" parent="Theme.MetaApp">
        <item name="android:windowNoTitle">true</item>
        <item name="colorPrimary">@android:color/background_dark</item>
        <item name="colorPrimaryDark">@android:color/background_dark</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@null</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="PopupAnimationFromRight" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/popup_in_right</item>
        <item name="android:windowExitAnimation">@anim/popup_out_right</item>
    </style>

    <style name="PopupAnimationGameDetailCommon" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/popup_game_detail_common_in</item>
        <item name="android:windowExitAnimation">@anim/popup_game_detail_common_out</item>
    </style>

    <style name="GameBottomDialogStyle" parent="Theme.AppCompat.Dialog">
        <item name="android:windowAnimationStyle">@style/AnimBottom</item>
        <item name="android:background">@color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:gravity">bottom</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0</item>
    </style>



    <style name="PopupAnimationScale" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/popup_in_scale</item>
        <item name="android:windowExitAnimation">@anim/popup_out_scale</item>
    </style>

    <style name="BottomSheetDialog" parent="Theme.MaterialComponents.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
        <item name="enableEdgeToEdge">true</item>
    </style>

    <style name="BottomSheetDialog.NavWhite">
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:navigationBarColor">@color/white</item>
    </style>

    <style name="BottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style>

</resources>