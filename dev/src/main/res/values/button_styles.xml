<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Button">
        <item name="android:background">@drawable/selector_button</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:textSize">@dimen/textSizeNormal</item>
        <item name="android:paddingStart">14dp</item>
        <item name="android:paddingEnd">14dp</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingTop">4dp</item>
        <item name="android:paddingBottom">4dp</item>
        <item name="android:minHeight">@dimen/dp_38</item>
        <item name="android:minWidth">@dimen/dp_111</item>
        <item name="android:fontFamily">@font/poppins_bold_700</item>
        <item name="android:textColor">@color/selector_button_primary_text_color</item>
    </style>

    <style name="Button.S10">
        <item name="android:textSize">@dimen/textSizeTiny</item>
    </style>

    <style name="Button.S12">
        <item name="android:textSize">@dimen/textSizeSmaller</item>
    </style>

    <style name="Button.S14">
        <item name="android:textSize">@dimen/textSizeSmall</item>
    </style>

    <style name="Button.S16">
        <item name="android:textSize">@dimen/textSize16</item>
    </style>

    <style name="Button.S15">
        <item name="android:textSize">@dimen/textSizeNormal</item>
    </style>

    <style name="Button.S18">
        <item name="android:textSize">@dimen/textSizeBig</item>
    </style>

    <style name="Button.S18.PoppinsMedium500">
        <item name="android:textSize">@dimen/textSizeBig</item>
        <item name="android:fontFamily">@font/poppins_medium_500</item>
    </style>

    <style name="Button.S18.PoppinsBold600">
        <item name="android:textSize">@dimen/textSizeBig</item>
        <item name="android:fontFamily">@font/poppins_semi_bold_600</item>
    </style>

    <style name="Button.S18.PoppinsBold700">
        <item name="android:textSize">@dimen/textSizeBig</item>
        <item name="android:fontFamily">@font/poppins_bold_700</item>
    </style>

    <style name="Button.S10.PoppinsBold700">
        <item name="android:fontFamily">@font/poppins_bold_700</item>
    </style>

    <style name="Button.S12.PoppinsMedium500">
        <item name="android:fontFamily">@font/poppins_medium_500</item>
    </style>

    <style name="Button.S12.PoppinsBold600">
        <item name="android:fontFamily">@font/poppins_semi_bold_600</item>
    </style>

    <style name="Button.S12.PoppinsBlack900">
        <item name="android:fontFamily">@font/poppins_black_900</item>
    </style>

    <style name="Button.S14.PoppinsRegular400">
        <item name="android:fontFamily">@font/poppins_regular_400</item>
    </style>

    <style name="Button.S14.PoppinsMedium500">
        <item name="android:fontFamily">@font/poppins_medium_500</item>
    </style>

    <style name="Button.S14.PoppinsBlack900">
        <item name="android:fontFamily">@font/poppins_black_900</item>
    </style>

    <style name="Button.S15.PoppinsRegular400">
        <item name="android:fontFamily">@font/poppins_regular_400</item>
    </style>

    <style name="Button.S15.PoppinsMedium500">
        <item name="android:fontFamily">@font/poppins_medium_500</item>
    </style>


    <style name="Button.S15.PoppinsBold700">
        <item name="android:fontFamily">@font/poppins_bold_700</item>
    </style>

    <style name="Button.S15.PoppinsBlack900">
        <item name="android:fontFamily">@font/poppins_black_900</item>
    </style>

    <style name="Button.S16.PoppinsMedium500">
        <item name="android:fontFamily">@font/poppins_medium_500</item>
    </style>

    <style name="Button.S16.PoppinsBold600">
        <item name="android:fontFamily">@font/poppins_semi_bold_600</item>
    </style>

    <style name="Button.S16.PoppinsBold700">
        <item name="android:fontFamily">@font/poppins_bold_700</item>
    </style>

    <style name="Button.S16.PoppinsBlack900">
        <item name="android:fontFamily">@font/poppins_black_900</item>
    </style>

    <style name="Button.S18.PoppinsBlack900">
        <item name="android:fontFamily">@font/poppins_black_900</item>
    </style>

    <style name="Button.S15.PoppinsRegular400.CancelPrimary">
        <item name="android:background">@drawable/selector_button_cancel</item>
        <item name="android:textColor">@color/textColorPrimary</item>
    </style>

    <style name="Button.S15.PoppinsBold700.Cancel">
        <item name="android:background">@drawable/selector_button_cancel</item>
        <item name="android:textColor">@color/textColorSecondary</item>
    </style>

    <style name="Button.S15.PoppinsBlack900.Cancel">
        <item name="android:background">@drawable/selector_button_cancel</item>
        <item name="android:textColor">@color/textColorSecondary</item>
    </style>

    <style name="Button.S16.PoppinsBlack900.Height28Width74">
        <item name="android:minHeight">@dimen/dp_28</item>
        <item name="android:minWidth">@dimen/dp_58</item>
    </style>

    <style name="Button.S18.PoppinsBlack900.Height48">
        <item name="android:minHeight">@dimen/dp_48</item>
    </style>

    <style name="Button.S16.PoppinsBold600.Height48">
        <item name="android:minHeight">@dimen/dp_48</item>
    </style>

    <style name="Button.S16.PoppinsBold700.Height48">
        <item name="android:minHeight">@dimen/dp_48</item>
    </style>

    <style name="Button.S18.PoppinsBlack900.Height46">
        <item name="android:minHeight">@dimen/dp_46</item>
    </style>

    <style name="Button.S14.PoppinsRegular400.CancelPrimary">
        <item name="android:background">@drawable/selector_button_cancel</item>
        <item name="android:textColor">@color/textColorPrimary</item>
    </style>

    <style name="Button.S16.PoppinsMedium500.CancelPrimary">
        <item name="android:background">@drawable/selector_button_cancel</item>
        <item name="android:textColor">@color/textColorPrimary</item>
    </style>

    <style name="Button.S16.PoppinsBlack900.CancelPrimary">
        <item name="android:background">@drawable/selector_button_cancel</item>
        <item name="android:textColor">@color/textColorPrimary</item>
    </style>

    <style name="Button.S16.PoppinsMedium500.Error">
        <item name="android:background">@drawable/selector_button_error</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="Button.S16.PoppinsMedium500.Warn">
        <item name="android:background">@drawable/selector_button_warn</item>
        <item name="android:textColor">@color/textColorPrimary</item>
    </style>

    <style name="Button.S14.PoppinsRegular400.Small">
        <item name="android:paddingStart">10dp</item>
        <item name="android:minHeight">22dp</item>
        <item name="android:minWidth">60dp</item>
        <item name="android:paddingEnd">10dp</item>
        <item name="android:paddingTop">2dp</item>
        <item name="android:paddingBottom">2dp</item>
    </style>
</resources>