<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="circleStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
        <item name="borderWidth" />
    </style>

    <style name="shapeRound10Style">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">10dp</item>
    </style>

    <style name="shapeRound13Style">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">13dp</item>
    </style>

    <style name="shapeRound16Style">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style>

    <style name="round_corner_4dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">4dp</item>
    </style>

    <style name="round_corner_6dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">6dp</item>
    </style>

    <style name="round_corner_8dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style>

    <style name="round_corner_10dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">10dp</item>
    </style>

    <style name="round_corner_12dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">12dp</item>
    </style>

    <style name="round_corner_top_12dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">12dp</item>
        <item name="cornerSizeTopRight">12dp</item>
    </style>

    <style name="round_corner_14dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">14dp</item>
    </style>

    <style name="round_corner_16dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style>

    <style name="round_corner_bottom_16dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeBottomLeft">16dp</item>
        <item name="cornerSizeBottomRight">16dp</item>
    </style>

    <style name="round_corner_18dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">18dp</item>
    </style>

    <style name="shapeTopRound16Style">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">16dp</item>
        <item name="cornerSizeTopRight">16dp</item>
    </style>

    <style name="shapeTopRound20Style">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">20dp</item>
        <item name="cornerSizeTopRight">20dp</item>
    </style>

    <style name="shapeRound20Style">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">20dp</item>
    </style>

    <style name="shapeRound30Style">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">30dp</item>
    </style>

    <style name="shapeRound45Style">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">45dp</item>
    </style>

    <style name="FolderTextView">
        <item name="android:textSize">12sp</item>
        <item name="tailTextColor">#666666</item>
        <item name="uiLineHeight">@dimen/dp_20</item>
        <item name="android:fontFamily">@font/poppins_regular_400</item>
        <item name="unFoldText">More</item>
    </style>

    <style name="EditText">
        <item name="android:textCursorDrawable">@drawable/bg_cursor</item>
        <item name="android:textColorHint">@color/textColorSecondary</item>
        <item name="android:maxLength">500</item>
        <item name="android:gravity">start|top</item>
        <item name="android:textSize">@dimen/textSizeNormal</item>
        <item name="android:fontFamily">@font/poppins_regular_400</item>
        <item name="android:textColor">@color/colorPrimaryDark</item>
    </style>

    <style name="EditText.Single">
        <item name="singleLine">true</item>
        <item name="maxLines">1</item>
    </style>

    <style name="EditText.Single.Small">
        <item name="android:textSize">@dimen/textSizeSmall</item>
    </style>

    <style name="EditText.SingleLine">
        <item name="android:gravity">left|center_vertical</item>
        <item name="android:fontFamily">@font/poppins_regular_400</item>
        <item name="android:textColor">@color/colorPrimaryDark</item>
        <item name="android:background">@drawable/bg_login_input</item>
        <item name="android:maxLines">1</item>
        <item name="android:singleLine">true</item>
        <item name="android:paddingLeft">@dimen/dp_16</item>
        <item name="android:paddingRight">@dimen/dp_16</item>
        <item name="android:paddingTop">@dimen/dp_12</item>
        <item name="android:paddingBottom">@dimen/dp_12</item>
    </style>

    <style name="Avatar" />

    <style name="Avatar.Round">
        <item name="android:layout_width">56dp</item>
        <item name="android:layout_height">56dp</item>
        <item name="shapeAppearance">@style/circleStyle</item>
    </style>

    <style name="Avatar.Round.Medium">
        <item name="android:layout_width">60dp</item>
        <item name="android:layout_height">60dp</item>
    </style>

    <style name="MainBackground">
        <item name="android:background">@color/colorMainBackground</item>
    </style>

    <style name="SuggestedGameIcon">
        <item name="android:layout_width">@dimen/dp_112</item>
        <item name="android:layout_height">@dimen/dp_112</item>
        <item name="shapeAppearance">@style/shapeRound16Style</item>
    </style>

    <style name="AnimBottom" parent="@android:style/Animation">
        <!--上滑动画-->
        <item name="android:windowEnterAnimation">@anim/pickerview_slide_in_bottom</item>
        <!--下滑动画-->
        <item name="android:windowExitAnimation">@anim/pickerview_slide_out_bottom</item>
    </style>

    <style name="AnimBottomSlow" parent="@android:style/Animation">
        <!--上滑动画-->
        <item name="android:windowEnterAnimation">@anim/slide_in_bottom_slow</item>
        <!--下滑动画-->
        <item name="android:windowExitAnimation">@anim/slide_out_bottom_slow</item>
    </style>

    <style name="BottomSheetAnimNoEnter" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@anim/design_bottom_sheet_slide_out</item>
    </style>



    <!--ShapeableImageView 圆角-->
    <style name="RoundedStyle24">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">24dp</item>
    </style>

    <style name="custom_dialog2" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <!-- Dialog的windowFrame框为无 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 是否漂现在activity上 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 是否半透明 -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <!-- 去除黑色边框的关键设置项 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 屏幕背景是否变暗 -->
        <item name="android:backgroundDimAmount">0.3</item>
    </style>

    <style name="picker_view_scale_anim" mce_bogus="1" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/pickerview_dialog_scale_in</item>
        <item name="android:windowExitAnimation">@anim/pickerview_dialog_scale_out</item>
    </style>


    <style name="picker_view_slide_anim" mce_bogus="1" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/pickerview_slide_in_bottom</item>
        <item name="android:windowExitAnimation">@anim/pickerview_slide_out_bottom</item>
    </style>


    <style name="Widget.AppCompat.ProgressBar.Horizontal.AvatarLoading" parent="Base.Widget.AppCompat.ProgressBar.Horizontal">
        <!--进度条的进度颜色drawable文件-->
        <item name="android:progressDrawable">@drawable/progress_horizontal_avatar_loading</item>
    </style>

    <style name="Widget.AppCompat.ProgressBar.Horizontal.AvatarLoadingScreen" parent="Base.Widget.AppCompat.ProgressBar.Horizontal">
        <!--进度条的进度颜色drawable文件-->
        <item name="android:progressDrawable">@drawable/progress_horizontal_avatar_loading_screen</item>
    </style>

    <style name="hintAppearence" parent="TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/sp_10</item>
        <item name="android:fontFamily">@font/poppins_regular_400</item>
        <item name="android:textColor">@color/color_B3B3B3</item>
        <item name="hintTextColor">@color/color_B3B3B3</item>
        <item name="android:textColorHint">#B3B3B3</item>
    </style>
    <style name="hintLinkAppearence" parent="TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/sp_12</item>
        <item name="android:fontFamily">@font/poppins_regular_400</item>
        <item name="android:textColor">@color/color_757575</item>
        <item name="hintTextColor">@color/color_757575</item>
        <item name="android:textColorHint">@color/color_757575</item>
    </style>

    <declare-styleable name="RoundView">
        <attr name="cornerRadii" format="dimension" />
        <attr name="topLeftRadius" format="dimension" />
        <attr name="topRightRadius" format="dimension" />
        <attr name="bottomLeftRadius" format="dimension" />
        <attr name="bottomRightRadius" format="dimension" />
    </declare-styleable>

    <!--region Dialog-->
    <style name="CustomDialog" parent="Theme.MaterialComponents.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowElevation">0dp</item>
    </style>

    <style name="CustomDialog.Input">
        <item name="android:windowSoftInputMode">stateVisible|adjustResize</item>
    </style>

    <style name="CustomDialog.Input.Always">
        <item name="android:windowSoftInputMode">stateAlwaysVisible|adjustResize</item>
    </style>

    <style name="CustomDialog.Input.HarmonyOs">
        <item name="android:windowSoftInputMode">stateVisible|adjustNothing</item>
        <item name="android:windowIsFloating">false</item>
    </style>

    <style name="CustomDialog.Input.HarmonyOs.Always">
        <item name="android:windowSoftInputMode">stateAlwaysVisible|adjustNothing</item>
    </style>

    <style name="round_corner_top_8">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">@dimen/dp_8</item>
        <item name="cornerSizeTopRight">@dimen/dp_8</item>
    </style>

    <style name="DialogStyleV2" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>
    <style name="DialogStyleV2.Input">
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowSoftInputMode">stateVisible|adjustResize</item>
    </style>

    <style name="DialogStyleV2.Input.NoAnimation">
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@null</item>
    </style>
    <!--endregion-->

    <style name="RealNameInput">
        <item name="android:textSize">15sp</item>
        <item name="android:textColor">@color/color_080D2D_30</item>
        <item name="android:singleLine">true</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="GameDialogStyle" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">stateHidden|adjustPan</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>
</resources>