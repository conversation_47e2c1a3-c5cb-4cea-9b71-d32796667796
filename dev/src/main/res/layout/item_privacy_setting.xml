<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/dp_72"
    android:orientation="horizontal"
    app:layout_minHeight="@dimen/dp_72">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clLabel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/dp_26"
        android:layout_marginStart="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/switch_setting"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_setting_desc"
            style="@style/MetaTextView.S14"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lineSpacingMultiplier="1.2"
            android:maxLines="2"
            android:textColor="@color/color_1A1A1A"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="aaaaaaaaaaaaaaaaaaa" />

        <View
            android:id="@+id/labelRedDot"
            android:layout_width="@dimen/dp_8"
            android:layout_height="@dimen/dp_8"
            android:background="@drawable/bg_red_corner_360"
            android:visibility="invisible"
            app:layout_constraintStart_toEndOf="@id/tv_setting_desc"
            app:layout_constraintTop_toTopOf="@id/tv_setting_desc"
            tools:visibility="visible"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.socialplay.gpark.ui.view.MetaSwitchCompat
        android:id="@+id/switch_setting"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/dp_12"
        android:paddingStart="@dimen/dp_6"
        android:paddingEnd="@dimen/dp_16"
        android:thumb="@drawable/bg_setting_thumb"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:track="@drawable/green_switch_track_selector" />

    <View
        android:id="@+id/v_switch_click"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/switch_setting"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/switch_setting"
        app:layout_constraintTop_toTopOf="@id/switch_setting" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:background="@color/color_F6F6F6"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>