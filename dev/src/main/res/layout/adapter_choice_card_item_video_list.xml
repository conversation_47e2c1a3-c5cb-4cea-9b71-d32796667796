<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/smallCardRoot"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    tools:layout_height="wrap_content"
    tools:layout_gravity="center">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_cover"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_white_round_12"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:clipToOutline="true">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_cover"
            android:layout_width="@dimen/dp_150"
            android:layout_height="@dimen/dp_200"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@mipmap/ic_launcher" />

        <View
            android:id="@+id/v_mask"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_50"
            android:background="@drawable/bg_choice_home_video_feed_mask"
            app:layout_constraintBottom_toBottomOf="@id/cl_cover"
            app:layout_constraintEnd_toEndOf="@id/cl_cover"
            app:layout_constraintStart_toStartOf="@id/cl_cover" />

        <FrameLayout
            android:id="@+id/fl_author_avatar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginBottom="@dimen/dp_10"
            android:background="@drawable/bg_white_circle"
            android:padding="@dimen/dp_1"
            app:layout_constraintBottom_toBottomOf="@id/v_mask"
            app:layout_constraintStart_toStartOf="@id/v_mask">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/iv_author_avatar"
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:background="@color/white"
                android:src="@drawable/placeholder"
                app:shapeAppearance="@style/circleStyle"
                tools:src="@color/black" />
        </FrameLayout>

        <TextView
            android:id="@+id/tv_author_name"
            style="@style/MetaTextView.S12.PoppinsRegular400"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_4"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="@id/fl_author_avatar"
            app:layout_constraintEnd_toEndOf="@id/iv_cover"
            app:layout_constraintStart_toEndOf="@id/fl_author_avatar"
            app:layout_constraintTop_toTopOf="@id/fl_author_avatar"
            tools:text="Bojunyixiao" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/color_1A1A1A"
        app:layout_constraintEnd_toEndOf="@id/cl_cover"
        app:layout_constraintStart_toStartOf="@id/cl_cover"
        app:layout_constraintTop_toBottomOf="@id/cl_cover"
        app:lineHeight="@dimen/sp_18"
        tools:text="Copycat Smoothie smoothie good are jacy more" />


    <ImageView
        android:id="@+id/iv_play"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_8"
        android:src="@drawable/ic_choice_home_video_feed_play"
        app:layout_constraintEnd_toEndOf="@id/cl_cover"
        app:layout_constraintTop_toTopOf="@id/cl_cover" />

    <View
        android:id="@+id/v_spacer"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintBottom_toTopOf="@id/iv_like_count"
        android:layout_width="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintVertical_weight="1"
        android:layout_height="0dp"/>

    <ImageView
        android:id="@+id/iv_like_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:src="@drawable/ic_choice_home_video_feed_heart_gray"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_spacer" />

    <TextView
        android:id="@+id/tv_like_count"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:textColor="@color/color_999999"
        app:layout_constraintBottom_toBottomOf="@id/iv_like_count"
        app:layout_constraintStart_toEndOf="@id/iv_like_count"
        app:layout_constraintTop_toTopOf="@id/iv_like_count"
        tools:text="573" />


</androidx.constraintlayout.widget.ConstraintLayout>