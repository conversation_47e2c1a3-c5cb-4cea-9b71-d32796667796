<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/cl_root"
    android:background="@drawable/bg_f0f0f0_corner_360"
    android:paddingHorizontal="@dimen/dp_8"
    android:paddingVertical="@dimen/dp_4">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/icon_tag"
        android:layout_width="@dimen/dp_15"
        android:layout_height="@dimen/dp_15"
        android:layout_centerVertical="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTableName"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerVertical="true"
        android:fontFamily="@font/poppins_regular_400"
        android:gravity="center"
        android:includeFontPadding="false"
        android:textColor="@color/color_666666"
        android:textSize="@dimen/dp_12"
        android:layout_marginLeft="@dimen/dp_4"
        app:layout_constraintBottom_toBottomOf="@+id/icon_tag"
        app:layout_constraintLeft_toRightOf="@+id/icon_tag"
        app:layout_constraintTop_toTopOf="@+id/icon_tag"
        tools:text="PlayfulPlayfu212424223434345335l" />


</androidx.constraintlayout.widget.ConstraintLayout>
