<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    app:speed_monitor="true">

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintTop_toTopOf="parent"
        app:title_text="@string/community_post_title" />

    <ImageView
        android:id="@+id/iv_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_8"
        android:src="@drawable/ic_detail_share"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tbl"
        tools:visibility="visible" />

    <View
        android:id="@+id/v_red_dot_more"
        android:layout_width="@dimen/dp_10"
        android:layout_height="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/sp_red_dot"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/iv_more"
        app:layout_constraintTop_toTopOf="@id/iv_more"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.refresh.MetaVerticalCoordinatorRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/v_comment_split"
        app:layout_constraintTop_toBottomOf="@id/tbl">

        <com.socialplay.gpark.ui.view.CallbackCoordinatorLayout
            android:id="@+id/cdl"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/abl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                app:layout_behavior="com.google.android.material.appbar.CustomAppBarBehavior"
                tools:expanded="true">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_post_detail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/iv_author_avatar"
                        android:layout_width="@dimen/dp_47"
                        android:layout_height="@dimen/dp_47"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_24"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:shapeAppearance="@style/circleStyle"
                        tools:src="@drawable/placeholder" />

                    <ImageView
                        android:id="@+id/iv_state"
                        android:layout_width="@dimen/dp_10"
                        android:layout_height="@dimen/dp_10"
                        android:layout_marginEnd="@dimen/dp_1"
                        android:layout_marginBottom="@dimen/dp_1"
                        android:src="@drawable/shape_green_point_white_stroke"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
                        app:layout_constraintEnd_toEndOf="@id/iv_author_avatar"
                        tools:visibility="visible" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_author_name"
                        style="@style/MetaTextView.S14.PoppinsSemiBold600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_7"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:singleLine="true"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintBottom_toTopOf="@id/tv_time"
                        app:layout_constraintEnd_toStartOf="@id/iv_official"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toEndOf="@id/iv_author_avatar"
                        app:layout_constraintTop_toTopOf="@id/iv_author_avatar"
                        app:layout_constraintVertical_chainStyle="packed"
                        tools:text="aaa" />

                    <com.socialplay.gpark.ui.view.UserLabelView
                        android:id="@+id/iv_official"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/tv_author_name"
                        app:layout_constraintEnd_toStartOf="@id/space_follow"
                        app:layout_constraintStart_toEndOf="@id/tv_author_name"
                        app:layout_constraintTop_toTopOf="@id/tv_author_name"
                        tools:visibility="visible" />

                    <View
                        android:id="@+id/v_author_click"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
                        app:layout_constraintEnd_toEndOf="@id/tv_author_name"
                        app:layout_constraintStart_toStartOf="@id/iv_author_avatar"
                        app:layout_constraintTop_toTopOf="@id/iv_author_avatar" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_time"
                        style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:singleLine="true"
                        app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
                        app:layout_constraintStart_toStartOf="@id/tv_author_name"
                        app:layout_constraintTop_toBottomOf="@id/tv_author_name"
                        tools:text="Nov 12, 2022 14:12 AM" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_review_status_label"
                        style="@style/MetaTextView.S10"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_4"
                        android:background="@drawable/bg_white_round"
                        android:drawableStart="@drawable/ic_label_under_review"
                        android:drawablePadding="@dimen/dp_4"
                        android:gravity="center"
                        android:paddingStart="@dimen/dp_2"
                        android:paddingEnd="@dimen/dp_6"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/tv_time"
                        app:layout_constraintHeight_min="@dimen/dp_16"
                        app:layout_constraintStart_toEndOf="@id/tv_time"
                        app:layout_constraintTop_toTopOf="@id/tv_time"
                        tools:backgroundTint="@color/color_EBF6FF"
                        tools:text="Under Review"
                        tools:textColor="@color/color_4AB4FF"
                        tools:visibility="visible" />

                    <Space
                        android:id="@+id/space_follow"
                        android:layout_width="@dimen/dp_16"
                        android:layout_height="@dimen/dp_16"
                        app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
                        app:layout_constraintEnd_toStartOf="@id/tv_follow_btn"
                        app:layout_constraintTop_toTopOf="@id/iv_author_avatar"
                        tools:visibility="gone" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_follow_btn"
                        style="@style/MetaTextView.S12.PoppinsMedium500"
                        android:layout_width="@dimen/dp_93"
                        android:layout_height="@dimen/dp_30"
                        android:background="@drawable/bg_ffef30_round_24"
                        android:gravity="center"
                        android:minHeight="@dimen/sp_22"
                        app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
                        app:layout_constraintEnd_toEndOf="@id/guide_right"
                        app:layout_constraintTop_toTopOf="@id/iv_author_avatar"
                        tools:text="Follow"
                        tools:visibility="gone" />

                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/guide_right"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintGuide_end="@dimen/dp_16" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_content"
                        style="@style/MetaTextView.S14"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_12"
                        android:lineSpacingMultiplier="1.2"
                        app:layout_constraintTop_toBottomOf="@id/iv_author_avatar"
                        tools:text="Infinite Borders puts players in the ever captivating Three Kingdoms period of Ancient China, and offers a blend of strategy, RPG city-building, and gacha mechanics. Having spent considerable time in both its native East Asian markets —previously known as Reign of Warlords or ROW: Tam Quốc. — It is now entering a global release during its Closed Beta Test phase. However, the English localization is still a bit spotty." />

                    <com.socialplay.gpark.ui.view.video.VideoPageListView
                        android:id="@+id/vplv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/tv_content"
                        tools:visibility="visible" />

                    <com.airbnb.epoxy.EpoxyRecyclerView
                        android:id="@+id/rv_img"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"
                        android:layout_marginTop="@dimen/dp_4"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:nestedScrollingEnabled="false"
                        android:overScrollMode="never"
                        app:layout_constraintTop_toBottomOf="@id/vplv"
                        tools:visibility="gone" />

                    <com.airbnb.epoxy.EpoxyRecyclerView
                        android:id="@+id/rv_game"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:nestedScrollingEnabled="false"
                        android:overScrollMode="never"
                        app:layout_constraintTop_toBottomOf="@id/rv_img"
                        tools:itemCount="1"
                        tools:layout_marginTop="@dimen/dp_10"
                        tools:listitem="@layout/adapter_publish_post_game"
                        tools:visibility="visible" />


                    <include
                        android:id="@+id/include_moment_take"
                        layout="@layout/item_post_moment_take"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_10"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/rv_game"
                        tools:visibility="visible" />

                    <include
                        android:id="@+id/include_outfit"
                        layout="@layout/item_post_outfit_card_try_on"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_10"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/include_moment_take"
                        tools:visibility="visible" />

                    <include
                        android:id="@+id/include_ugc_design"
                        layout="@layout/item_post_ugc_design_card_edit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_10"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/include_outfit"
                        tools:visibility="visible" />

                    <!--话题需求后不用了这个rv了-->
                    <com.airbnb.epoxy.EpoxyRecyclerView
                        android:id="@+id/rv_topic"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_2"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:nestedScrollingEnabled="false"
                        android:overScrollMode="never"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/include_ugc_design"
                        tools:itemCount="1"
                        tools:layout_marginStart="@dimen/dp_16"
                        tools:layout_marginTop="@dimen/dp_14"
                        tools:listitem="@layout/adapter_publish_post_tag" />

                    <ImageView
                        android:id="@+id/iv_like_count"
                        android:layout_width="@dimen/dp_24"
                        android:layout_height="@dimen/dp_24"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_14"
                        android:src="@drawable/icon_post_like_unselected"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/rv_topic" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_like_count"
                        style="@style/MetaTextView.S12.PoppinsMedium500.Secondary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_2"
                        app:layout_constraintBottom_toBottomOf="@id/iv_like_count"
                        app:layout_constraintStart_toEndOf="@id/iv_like_count"
                        app:layout_constraintTop_toTopOf="@id/iv_like_count"
                        tools:text="26" />

                    <View
                        android:id="@+id/v_like_click"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="@id/iv_like_count"
                        app:layout_constraintEnd_toEndOf="@id/tv_like_count"
                        app:layout_constraintStart_toStartOf="@id/iv_like_count"
                        app:layout_constraintTop_toTopOf="@id/iv_like_count" />

                    <ImageView
                        android:id="@+id/iv_comment_count"
                        android:layout_width="@dimen/dp_24"
                        android:layout_height="@dimen/dp_24"
                        android:layout_marginStart="@dimen/dp_26"
                        android:src="@drawable/ic_post_comment"
                        app:layout_constraintBottom_toBottomOf="@id/iv_like_count"
                        app:layout_constraintStart_toEndOf="@id/tv_like_count"
                        app:layout_constraintTop_toTopOf="@id/iv_like_count" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_comment_count"
                        style="@style/MetaTextView.S12.PoppinsMedium500.Secondary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_2"
                        app:layout_constraintBottom_toBottomOf="@id/iv_comment_count"
                        app:layout_constraintStart_toEndOf="@id/iv_comment_count"
                        app:layout_constraintTop_toTopOf="@id/iv_comment_count"
                        tools:text="26" />

                    <ImageView
                        android:id="@+id/iv_share_count"
                        android:layout_width="@dimen/dp_24"
                        android:layout_height="@dimen/dp_24"
                        android:layout_marginStart="@dimen/dp_26"
                        android:src="@drawable/ic_share_thin"
                        app:layout_constraintBottom_toBottomOf="@id/iv_comment_count"
                        app:layout_constraintStart_toEndOf="@id/tv_comment_count"
                        app:layout_constraintTop_toTopOf="@id/iv_comment_count" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_share_count"
                        style="@style/MetaTextView.S12.PoppinsMedium500.Secondary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_2"
                        app:layout_constraintBottom_toBottomOf="@id/iv_share_count"
                        app:layout_constraintStart_toEndOf="@id/iv_share_count"
                        app:layout_constraintTop_toTopOf="@id/iv_share_count"
                        tools:text="26" />

                    <View
                        android:id="@+id/v_share_click"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="@id/iv_share_count"
                        app:layout_constraintEnd_toEndOf="@id/tv_share_count"
                        app:layout_constraintStart_toStartOf="@id/iv_share_count"
                        app:layout_constraintTop_toTopOf="@id/iv_share_count" />

                    <Space
                        android:id="@+id/space_post_bottom"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_14"
                        app:layout_constraintTop_toBottomOf="@id/iv_like_count" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/color_F0F0F0"
                        app:layout_constraintTop_toBottomOf="@id/space_post_bottom" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_comment_count_hang"
                        style="@style/MetaTextView.S13.PoppinsSemiBold600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_16"
                        android:paddingVertical="@dimen/dp_16"
                        tools:text="26 Comments" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_comment_sort_hang"
                        style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical|end"
                        android:drawableStart="@drawable/ic_post_sort_arrow"
                        android:drawablePadding="@dimen/dp_2"
                        android:paddingHorizontal="@dimen/dp_16"
                        android:paddingVertical="@dimen/dp_12" />

                </FrameLayout>

            </com.google.android.material.appbar.AppBarLayout>

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rv_comment"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />

        </com.socialplay.gpark.ui.view.CallbackCoordinatorLayout>
    </com.socialplay.gpark.ui.view.refresh.MetaVerticalCoordinatorRefreshLayout>

    <View
        android:id="@+id/v_comment_split"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_F0F0F0"
        app:layout_constraintBottom_toTopOf="@id/v_comment_bg" />

    <View
        android:id="@+id/v_comment_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="-12dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="@id/et_comment" />

    <View
        android:id="@+id/v_comment_cover"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/v_comment_split"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/et_comment"
        style="@style/MetaTextView.S14"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginVertical="@dimen/dp_12"
        android:background="@drawable/bg_f0f0f0_corner_20"
        android:gravity="center_vertical"
        android:hint="@string/post_reply"
        android:inputType="textMultiLine"
        android:lineSpacingMultiplier="1.2"
        android:maxHeight="@dimen/dp_186"
        android:maxLength="300"
        android:paddingVertical="@dimen/dp_8"
        android:paddingStart="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_12"
        android:textColorHint="@color/color_666666"
        android:textCursorDrawable="@drawable/bg_cursor"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_goneMarginEnd="@dimen/dp_16" />

    <View
        android:id="@+id/v_et_comment_cover"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/et_comment"
        app:layout_constraintEnd_toEndOf="@id/et_comment"
        app:layout_constraintStart_toStartOf="@id/et_comment"
        app:layout_constraintTop_toTopOf="@id/et_comment" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_send_btn"
        style="@style/MetaTextView.S14.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:gravity="center_vertical"
        android:paddingVertical="@dimen/dp_7"
        android:paddingStart="@dimen/dp_18"
        android:paddingEnd="@dimen/dp_16"
        android:text="@string/reply_cap"
        android:textColor="@color/color_4AB4FF"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/et_comment"
        app:layout_constraintEnd_toEndOf="@id/et_comment"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/lv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl"
        tools:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>