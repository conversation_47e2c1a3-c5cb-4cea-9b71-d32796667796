<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clContent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_12"
        android:isScrollContainer="true"
        android:paddingHorizontal="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_tips"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:textColor="@color/color_666666"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/account_email_address_bind_tips" />

        <EditText
            android:id="@+id/et_email"
            style="@style/EditText.SingleLine"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_13"
            android:hint="@string/enter_email"
            android:inputType="textEmailAddress"
            android:paddingBottom="@dimen/dp_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_tips" />

        <EditText
            android:id="@+id/et_verify_code"
            style="@style/EditText.SingleLine"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_24"
            android:hint="@string/enter_verification_code"
            android:inputType="number"
            android:maxLength="6"
            android:paddingBottom="@dimen/dp_10"
            app:layout_constraintEnd_toStartOf="@+id/tv_send"
            app:layout_constraintHorizontal_weight="11"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/et_email" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_send"
            style="@style/MetaTextView.S15.PoppinsBold700"
            android:layout_width="100dp"
            android:layout_height="@dimen/dp_48"
            android:layout_marginStart="@dimen/dp_12"
            android:background="@drawable/selector_send"
            android:enabled="false"
            android:gravity="center"
            android:text="@string/friend_apply_send"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_15"
            app:layout_constraintBottom_toBottomOf="@+id/et_verify_code"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/et_verify_code"
            app:layout_constraintTop_toTopOf="@id/et_verify_code" />

        <TextView
            android:id="@+id/tvSure"
            style="@style/Button.S18.PoppinsBlack900.Height46"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_40"
            android:background="@drawable/bg_ffef30_round_24"
            android:enabled="false"
            android:minHeight="@dimen/dp_48"
            android:text="@string/text_confirm_uppercase"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_send" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon="@drawable/icon_back_array_bold_black"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintTop_toBottomOf="@+id/sbphv_placeholder"
        app:title_text="@string/forgot_password"
        app:title_text_color="@color/black" />

</androidx.constraintlayout.widget.ConstraintLayout>