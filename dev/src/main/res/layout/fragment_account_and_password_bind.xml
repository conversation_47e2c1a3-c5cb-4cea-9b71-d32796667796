<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/sv_content_area"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:fillViewport="true"
        android:isScrollContainer="true"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dp_16"
            android:paddingTop="@dimen/dp_8">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_tips"
                style="@style/MetaTextView.S16.PoppinsSemiBold600"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_8"
                android:layout_marginTop="@dimen/dp_14"
                android:gravity="left"
                android:text="@string/account_please_input_account_and_password"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


<!--            <EditText-->
<!--                android:id="@+id/et_account"-->
<!--                style="@style/EditText.SingleLine"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="@dimen/dp_48"-->
<!--                android:layout_marginTop="@dimen/dp_12"-->
<!--                android:background="@drawable/bg_login_input"-->
<!--                android:digits="@string/signup_account_input_digits"-->
<!--                android:ellipsize="end"-->
<!--                android:hint="@string/enter_your_account"-->
<!--                android:inputType="text"-->
<!--                android:maxLength="20"-->
<!--                android:singleLine="true"-->
<!--                app:layout_constraintEnd_toEndOf="parent"-->
<!--                app:layout_constraintStart_toStartOf="parent"-->
<!--                app:layout_constraintTop_toBottomOf="@+id/tv_tips" />-->

            <EditText
                android:id="@+id/et_password"
                style="@style/EditText.SingleLine"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_48"
                android:layout_marginTop="@dimen/dp_20"
                android:background="@drawable/bg_login_input"
                android:digits="@string/password_input_digits"
                android:hint="@string/enter_your_password"
                android:inputType="textPassword"
                android:maxLength="16"
                android:paddingEnd="@dimen/dp_60"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_tips" />

            <ImageView
                android:id="@+id/iv_password_visibility"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_20"
                android:src="@drawable/icon_login_visible_password"
                app:layout_constraintBottom_toBottomOf="@id/et_password"
                app:layout_constraintEnd_toEndOf="@+id/et_password"
                app:layout_constraintTop_toTopOf="@id/et_password" />

            <EditText
                android:id="@+id/et_password_2"
                style="@style/EditText.SingleLine"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_48"
                android:layout_marginTop="@dimen/dp_20"
                android:background="@drawable/bg_login_input"
                android:digits="@string/password_input_digits"
                android:hint="@string/account_enter_password_again"
                android:inputType="textPassword"
                android:maxLength="16"
                android:paddingEnd="@dimen/dp_60"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/et_password" />

            <ImageView
                android:id="@+id/iv_password_visibility_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_20"
                android:src="@drawable/icon_login_visible_password"
                app:layout_constraintBottom_toBottomOf="@id/et_password_2"
                app:layout_constraintEnd_toEndOf="@+id/et_password_2"
                app:layout_constraintTop_toTopOf="@id/et_password_2" />

            <TextView
                android:id="@+id/tvSure"
                style="@style/Button.S18.PoppinsBlack900.Height46"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_124"
                android:text="@string/text_confirm_uppercase"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/et_password_2" />

            <LinearLayout
                android:id="@+id/llAccountNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp_32"
                app:layout_constraintBottom_toTopOf="@id/tvSure"
                app:layout_constraintLeft_toLeftOf="@id/tvSure"
                app:layout_constraintRight_toRightOf="@id/tvSure">

                <TextView
                    android:id="@+id/tvAccountNumberDesc"
                    style="@style/MetaTextView.S16.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/account_number_desc"
                    android:textColor="@color/Gray_800" />

                <TextView
                    android:id="@+id/tvAccountNumber"
                    style="@style/MetaTextView.S16.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/Gray_1000"
                    tools:text="6278212" />

                <ImageView
                    android:id="@+id/ivCopyAccountNumber"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:scaleType="center"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dp_16"
                    android:src="@drawable/ic_module_project_copy"
                    app:tint="@color/color_4AB4FF" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon="@drawable/icon_back_array_bold_black"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintTop_toBottomOf="@+id/sbphv_placeholder"
        app:title_text="@string/text_account"
        app:title_text_color="@color/black" />
</androidx.constraintlayout.widget.ConstraintLayout>