<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:isDividerVisible="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statusBar"
        app:title_text="@string/feedback" />

    <com.socialplay.gpark.ui.view.FocusableScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:focusAt="@id/clFeedbackContent"
        app:layout_constraintBottom_toTopOf="@id/vBottom"
        app:layout_constraintTop_toBottomOf="@id/title">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clFeedbackContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvTypeTitle"
                style="@style/MetaTextView.S14.PoppinsBold700"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_8"
                android:text="@string/feedback_type"
                app:layout_constraintTop_toTopOf="parent" />

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rvType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_6"
                android:layout_marginTop="@dimen/dp_12"
                android:nestedScrollingEnabled="false"
                android:overScrollMode="never"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTypeTitle" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvOptionTitle"
                style="@style/MetaTextView.S14.PoppinsBold700"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_12"
                android:text="@string/mandatory_feedback_type"
                app:layout_constraintTop_toBottomOf="@id/rvType" />

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rvOption"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_6"
                android:nestedScrollingEnabled="false"
                android:orientation="vertical"
                android:overScrollMode="never"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvOptionTitle"
                app:spanCount="1"
                tools:itemCount="10"
                tools:listitem="@layout/item_feedback_option" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvDesc"
                style="@style/MetaTextView.S14.PoppinsBold700"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_18"
                android:text="@string/mandatory_description"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rvOption"
                app:layout_goneMarginTop="@dimen/dp_12" />

            <EditText
                android:id="@+id/etFeedbackDesc"
                style="@style/EditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_12"
                android:background="@drawable/bg_game_review_edit"
                android:gravity="top|start"
                android:hint="@string/feedback_desc_hint"
                android:maxLength="500"
                android:minHeight="@dimen/dp_120"
                android:paddingHorizontal="@dimen/dp_16"
                android:paddingTop="@dimen/dp_12"
                android:paddingBottom="@dimen/dp_36"
                android:textSize="@dimen/sp_12"
                app:layout_constraintTop_toBottomOf="@id/tvDesc" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvDescError"
                style="@style/MetaTextView.S12.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_6"
                android:text="@string/feedback_limit_tip"
                android:textColor="@color/color_FF5F42"
                app:layout_constraintEnd_toEndOf="@id/etFeedbackDesc"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="@id/etFeedbackDesc"
                app:layout_constraintTop_toBottomOf="@id/etFeedbackDesc" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvDescCount"
                style="@style/MetaTextView.S13.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="16dp"
                android:paddingBottom="@dimen/dp_12"
                android:textColor="@color/neutral_color_5"
                app:layout_constraintBottom_toBottomOf="@id/etFeedbackDesc"
                app:layout_constraintEnd_toEndOf="@id/etFeedbackDesc" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvImageTitle"
                style="@style/MetaTextView.S14.PoppinsBold700"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_16"
                android:text="@string/image"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvDescError" />

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rvImage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_8"
                android:nestedScrollingEnabled="false"
                android:orientation="horizontal"
                android:overScrollMode="never"
                android:paddingStart="@dimen/dp_12"
                android:paddingEnd="@dimen/dp_11"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvImageTitle" />

            <Space
                android:id="@+id/spaceBottom"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_76"
                app:layout_constraintTop_toBottomOf="@id/rvImage" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.socialplay.gpark.ui.view.FocusableScrollView>

    <View
        android:id="@+id/vBottom"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="-8dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvFeedbackSubmit" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvFeedbackSubmit"
        style="@style/Button.S16.PoppinsBlack900"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_6"
        android:minHeight="@dimen/dp_48"
        android:text="@string/submit"
        app:layout_constraintBottom_toTopOf="@id/tvJoinDiscord"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvJoinDiscord"
        style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_18"
        android:text="@string/join_discord_title"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />

</androidx.constraintlayout.widget.ConstraintLayout>