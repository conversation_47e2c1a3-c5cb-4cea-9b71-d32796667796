<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_50"
    android:layout_height="@dimen/dp_50"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginHorizontal="@dimen/dp_4"
    android:src="@drawable/icon_placeholder_img_default"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    app:shapeAppearance="@style/shapeRound13Style">
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivDress"
        android:layout_width="@dimen/dp_50"
        android:layout_height="@dimen/dp_50"
        tools:src="@drawable/icon_placeholder_img_default"
        android:background="@drawable/bg_dress_up_white_corner_8"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/shapeRound13Style" />
    <ImageView
        android:id="@+id/ivMark"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:layout_gravity="right"
        android:layout_marginTop="@dimen/dp_4"
        android:layout_marginRight="@dimen/dp_4"
        android:src="@drawable/icon_vip_plus_mark_small"
        android:visibility="gone"
        />
</FrameLayout>

