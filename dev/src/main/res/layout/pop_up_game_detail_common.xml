<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_16"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/dp_14"
        app:cardElevation="@dimen/dp_4">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/mtv_default"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp_16"
                android:text="@string/sort_hot"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/v_default_click"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/mtv_default"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/mtv_default" />

            <View
                android:id="@+id/v_divider_1"
                android:layout_width="0dp"
                android:layout_height="0.3dp"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:background="@color/color_E6E6E6"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mtv_default" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/mtv_hot"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp_16"
                android:text="@string/sort_hottest"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/v_divider_1" />

            <View
                android:id="@+id/v_hot_click"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/mtv_hot"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/mtv_hot" />

            <View
                android:id="@+id/v_divider_2"
                android:layout_width="0dp"
                android:layout_height="0.3dp"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:background="@color/color_E6E6E6"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mtv_hot" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/mtv_newest"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp_16"
                android:text="@string/sort_newest"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/v_divider_2" />

            <View
                android:id="@+id/v_newest_click"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/mtv_newest"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/mtv_newest" />

            <View
                android:id="@+id/v_divider_3"
                android:layout_width="0dp"
                android:layout_height="0.3dp"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:background="@color/color_E6E6E6"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mtv_newest" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/mtv_author_only"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp_16"
                android:text="@string/sort_author_only"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/v_divider_3" />

            <View
                android:id="@+id/v_author_only_click"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/mtv_author_only"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/mtv_author_only" />

            <View
                android:id="@+id/v_divider_4"
                android:layout_width="0dp"
                android:layout_height="0.3dp"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:background="@color/color_E6E6E6"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mtv_author_only" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/mtv_self_only"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp_16"
                android:text="@string/sort_self_only"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/v_divider_4" />

            <View
                android:id="@+id/v_self_only_click"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/mtv_self_only"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/mtv_self_only" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</FrameLayout>