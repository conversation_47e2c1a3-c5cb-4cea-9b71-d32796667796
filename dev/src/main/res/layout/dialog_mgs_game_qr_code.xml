<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="horizontal">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clQrCard"
        android:layout_width="@dimen/dp_250"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_mgs_qr_code"
        android:clickable="false">

        <androidx.cardview.widget.CardView
            android:id="@+id/cardInviteQrCode"
            android:layout_width="@dimen/dp_215"
            android:layout_height="@dimen/dp_215"
            android:layout_marginEnd="@dimen/dp_15"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_15"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="@dimen/dp_14"
            app:cardElevation="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvInviteText"
                    style="@style/MetaTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginHorizontal="@dimen/dp_13"
                    android:layout_marginTop="@dimen/dp_16"
                    android:gravity="center"
                    android:text="@string/mgs_game_qr_code_desc"
                    android:textSize="@dimen/sp_11"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/ivInviteQrCode"
                    android:layout_width="@dimen/dp_146"
                    android:layout_height="@dimen/dp_146"
                    android:layout_marginTop="@dimen/dp_16"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvInviteText" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>


        <RelativeLayout
            android:id="@+id/rlInviteScan"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cardInviteQrCode">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvInviteScan"
                style="@style/MetaTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginBottom="@dimen/dp_15"
                android:layout_marginTop="@dimen/dp_8"
                android:drawablePadding="@dimen/dp_9"
                android:drawableStart="@drawable/black_cemera"
                android:text="@string/mgs_game_qr_scan"
                android:textSize="@dimen/sp_13" />
        </RelativeLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>