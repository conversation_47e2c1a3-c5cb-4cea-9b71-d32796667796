<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center">

    <ImageView
        android:id="@+id/iv_background"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/v_top_spacer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintHeight_percent="0.12280701754385964"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.StyledImageView
        android:id="@+id/iv_body"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_top_spacer"
        app:scaleStyle="FixedWidthAlignBottom"
        tools:src="@color/black_10" />

    <LinearLayout
        android:id="@+id/ll_loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/iv_background"
        app:layout_constraintEnd_toEndOf="@+id/iv_background"
        app:layout_constraintStart_toStartOf="@+id/iv_background"
        app:layout_constraintTop_toTopOf="@+id/iv_background"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/iv_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_avatar_share_loading" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_2"
            android:text="@string/loading"
            android:textColor="@color/black_40"
            android:textSize="@dimen/sp_12" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>