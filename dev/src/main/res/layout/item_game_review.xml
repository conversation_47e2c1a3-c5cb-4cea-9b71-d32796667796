<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivGameReviewAvatar"
        android:layout_width="@dimen/dp_38"
        android:layout_height="@dimen/dp_38"
        android:layout_marginTop="@dimen/dp_20"
        android:src="@drawable/placeholder_white_round"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/circleStyle" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvGameReviewName"
        style="@style/MetaTextView.S12.PoppinsMedium500.Third"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_20"
        android:layout_marginStart="@dimen/dp_6"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/color_B3B3B3"
        android:textSize="@dimen/sp_12"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toRightOf="@id/ivGameReviewAvatar"
        app:layout_constraintRight_toLeftOf="@id/label_group"
        app:layout_constraintTop_toTopOf="@id/ivGameReviewAvatar"
        tools:text="hahahahahhahahahhahahahahhahaha" />

    <com.socialplay.gpark.ui.view.UserLabelView
        android:id="@+id/label_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_marginEnd="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="@id/tvGameReviewName"
        app:layout_constraintLeft_toRightOf="@id/tvGameReviewName"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvGameReviewName"
        android:orientation="horizontal" />



    <com.socialplay.gpark.ui.view.MyRatingBar
        android:id="@+id/ratingbar"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_16"
        android:layout_marginStart="@dimen/dp_6"
        android:stepSize="1"
        app:layout_constraintLeft_toRightOf="@id/ivGameReviewAvatar"
        app:layout_constraintTop_toBottomOf="@id/tvGameReviewName"
        app:starCount="5"
        app:starEmpty="@drawable/ic_empty_start_ffb800"
        app:starFill="@drawable/ic_fill_start_ffb800"
        app:starSize="@dimen/dp_16" />

    <com.socialplay.gpark.ui.view.MyRatingBar
        android:id="@+id/ratingbar_aibot"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_8"
        android:layout_marginStart="@dimen/dp_8"
        android:stepSize="1"
        app:layout_constraintLeft_toRightOf="@id/ivGameReviewAvatar"
        app:layout_constraintTop_toBottomOf="@id/tvGameReviewName"
        app:starCount="5"
        app:starEmpty="@drawable/icon_ai_bot_start_unfill"
        app:starFill="@drawable/icon_ai_bot_start_fill"
        app:starSize="@dimen/dp_16" />

    <ImageView
        android:id="@+id/ivMyReviewMore"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:scaleType="fitXY"
        android:src="@drawable/ic_review_more"
        app:layout_constraintBottom_toBottomOf="@id/ivGameReviewAvatar"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/ivGameReviewAvatar" />

    <View
        android:id="@+id/viewGameOpt"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/transparent"
        android:clickable="true"
        app:layout_constraintBottom_toTopOf="@+id/tvGameReviewContent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/ivMyReviewMore"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0" />

    <com.socialplay.gpark.ui.view.ExpandableTextView
        android:id="@+id/tvGameReviewContent"
        style="@style/FolderTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_44"
        android:layout_marginTop="@dimen/dp_10"
        android:textColor="@color/colorAccent"
        android:textSize="@dimen/sp_13"
        app:etv_EnableToggleClick="true"
        app:etv_MaxLinesOnShrink="3"
        app:etv_ToExpandHint="@string/more_with_space"
        app:etv_ToExpandHintColor="@color/neutral_color_5"
        app:etv_ToExpandHintOffset="@dimen/dp_30"
        app:etv_ToExpandHintShow="true"
        app:etv_ToShrinkHint="@string/collapse_with_space"
        app:etv_ToShrinkHintColor="@color/neutral_color_5"
        app:etv_ToShrinkHintShow="true"
        app:etv_WholeToggle="false"
        app:layout_constraintTop_toBottomOf="@id/ivGameReviewAvatar"
        tools:text="Join a secret underground battle club for toys! Rebel Riders is a vehicle combat game where disgruntled toys battle it out in the arena and blow off some steam with high-octane fun." />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_44"
        android:layout_marginTop="@dimen/dp_7"
        android:background="@drawable/bg_f0f0f0_corner_19"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_6"
        android:paddingVertical="1dp"
        android:text="@string/pinned_comment"
        android:textColor="@color/textColorSecondary"
        android:textSize="@dimen/sp_10"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvGameReviewContent"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvPostTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_44"
        android:layout_marginTop="@dimen/dp_10"
        android:gravity="center"
        android:text="@string/game_review_post_time"
        android:textColor="@color/textColorSecondary"
        android:textSize="@dimen/sp_12"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTop" />


    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvLike"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_20"
        android:gravity="center"
        android:minWidth="@dimen/dp_10"
        android:textColor="@color/color_999999"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvPostTime"
        tools:text="998" />

    <ImageView
        android:id="@+id/imgLike"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:layout_marginEnd="@dimen/dp_2"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="@id/tvLike"
        app:layout_constraintEnd_toStartOf="@id/tvLike"
        app:layout_constraintTop_toTopOf="@id/tvLike" />

</androidx.constraintlayout.widget.ConstraintLayout>