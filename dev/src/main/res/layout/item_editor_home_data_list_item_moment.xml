<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    tools:layout_gravity="center">


    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/dp_100"
        android:layout_height="@dimen/dp_100"
        android:src="@drawable/placeholder"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:scaleType="centerCrop"
        app:shapeAppearance="@style/round_corner_12dp" />

    <View
        android:id="@+id/v_shadow_mask"
        app:layout_constraintEnd_toEndOf="@id/iv_icon"
        app:layout_constraintStart_toStartOf="@id/iv_icon"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        android:background="@drawable/bg_bottom_icon_shadow_mask_s12"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_32"/>

    <ImageView
        android:id="@+id/iv_heat_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:src="@drawable/ic_moment_heat"
        app:layout_constraintBottom_toBottomOf="@id/v_shadow_mask"
        app:layout_constraintStart_toStartOf="@id/v_shadow_mask"
        app:layout_constraintTop_toTopOf="@id/v_shadow_mask" />


    <TextView
        android:id="@+id/tv_use_count"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:layout_marginStart="@dimen/dp_2"
        android:gravity="center_vertical"
        android:textColor="@color/white"
        app:layout_constraintTop_toTopOf="@id/iv_heat_icon"
        app:layout_constraintBottom_toBottomOf="@id/iv_heat_icon"
        app:layout_constraintStart_toEndOf="@+id/iv_heat_icon"
        tools:text="9.9k short" />


    <TextView
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:id="@+id/tv_name"
        android:layout_marginTop="@dimen/dp_4"
        android:maxLines="2"
        android:textColor="@color/neutral_color_1"
        app:layout_constraintEnd_toEndOf="@id/iv_icon"
        app:layout_constraintStart_toStartOf="@id/iv_icon"
        app:layout_constraintTop_toBottomOf="@id/iv_icon"
        tools:text="Leslie Alexander" />


</androidx.constraintlayout.widget.ConstraintLayout>