<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    app:speed_monitor="true">

    <com.socialplay.gpark.ui.view.refresh.MetaVerticalCoordinatorRefreshLayout
        android:id="@+id/mrl"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/v_divider_enter"
        app:layout_constraintTop_toBottomOf="@id/tbl">

        <com.socialplay.gpark.ui.view.VerticalCoordinatorLayout
            android:id="@+id/vcl"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/abl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/transparent"
                app:layout_behavior="com.google.android.material.appbar.CustomAppBarBehavior">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_top"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_scrollFlags="scroll">

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_game_name"
                        style="@style/MetaTextView.S20.PoppinsSemiBold600"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_18"
                        android:lineSpacingMultiplier="1.2"
                        android:maxLines="3"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Extreme Long Title" />

                    <androidx.cardview.widget.CardView
                        android:id="@+id/fl_game_banner_container"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_16"
                        app:cardBackgroundColor="@null"
                        app:cardCornerRadius="@dimen/dp_12"
                        app:cardElevation="0dp"
                        app:cornerRadii="@dimen/dp_12"
                        app:layout_constraintDimensionRatio="343:195"
                        app:layout_constraintTop_toBottomOf="@id/tv_game_name"
                        tools:visibility="gone">

                        <ImageView
                            android:id="@+id/iv_game_banner_bg"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent" />

                        <ImageView
                            android:id="@+id/iv_game_banner"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent" />

                        <com.socialplay.gpark.ui.view.MetaTextView
                            android:id="@+id/tv_pv"
                            style="@style/MetaTextView.S16.PoppinsSemiBold600"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="bottom"
                            android:background="@drawable/bg_gradient_black_70"
                            android:paddingHorizontal="@dimen/dp_12"
                            android:paddingTop="@dimen/dp_24"
                            android:paddingBottom="@dimen/dp_16"
                            android:textColor="@color/white_90"
                            tools:text="220 Played" />

                    </androidx.cardview.widget.CardView>

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_mw_not_compatible"
                        style="@style/MetaTextView.S12.PoppinsRegular400"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_24"
                        android:background="@drawable/shape_mw_not_compatible"
                        android:paddingHorizontal="@dimen/dp_16"
                        android:paddingVertical="@dimen/dp_13"
                        android:textColor="@color/color_FF5F42"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/fl_game_banner_container"
                        tools:text="The experience version is outdated. Please enter again after the creator updates the experience."
                        tools:visibility="visible" />

                    <com.socialplay.gpark.ui.view.DownloadProgressButton
                        android:id="@+id/dpbEnter2"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_48"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_16"
                        android:fontFamily="@font/poppins_semi_bold_600"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:text="@string/play"
                        android:textSize="@dimen/sp_15"
                        app:layout_constraintTop_toBottomOf="@id/tv_mw_not_compatible"
                        app:progress_btn_background_color_end="@color/colorAccentPrimary"
                        app:progress_btn_background_color_start="@color/colorAccentPrimary"
                        app:progress_btn_background_second_color="@color/color_game_detail_desc_bg"
                        app:progress_btn_radius="@dimen/dp_24"
                        app:progress_btn_show_bolder="false"
                        app:progress_btn_text_color="@color/textColorPrimary"
                        app:progress_btn_text_cover_color="@color/textColorPrimary"
                        app:progress_btn_text_size="15"
                        tools:background="@drawable/bg_ffef30_round_100" />

                    <ImageView
                        android:id="@+id/iv_avatar"
                        android:layout_width="@dimen/dp_39"
                        android:layout_height="@dimen/dp_39"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_24"
                        android:src="@drawable/icon_default_avatar"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/dpbEnter2" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_username"
                        style="@style/MetaTextView.S14.PoppinsMedium500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_6"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintBottom_toTopOf="@id/tv_portfolio"
                        app:layout_constraintEnd_toStartOf="@id/ulv"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toEndOf="@id/iv_avatar"
                        app:layout_constraintTop_toTopOf="@id/iv_avatar"
                        app:layout_constraintVertical_chainStyle="packed"
                        app:layout_goneMarginEnd="@dimen/dp_16"
                        tools:text="Longlonglonglonglonglonglonglonglonglong" />

                    <com.socialplay.gpark.ui.view.UserLabelView
                        android:id="@+id/ulv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toBottomOf="@id/tv_username"
                        app:layout_constraintEnd_toStartOf="@id/space_follow"
                        app:layout_constraintStart_toEndOf="@id/tv_username"
                        app:layout_constraintTop_toTopOf="@id/tv_username" />

                    <View
                        android:id="@+id/v_author_click"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
                        app:layout_constraintEnd_toEndOf="@id/tv_username"
                        app:layout_constraintStart_toStartOf="@id/iv_avatar"
                        app:layout_constraintTop_toTopOf="@id/iv_avatar" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tvUserId"
                        style="@style/MetaTextView.S10.PoppinsRegular400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp_8"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:textColor="@color/color_999999"
                        android:textSize="@dimen/sp_12"
                        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
                        app:layout_constraintEnd_toStartOf="@id/tv_portfolio"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="@id/tv_username"
                        app:layout_constraintTop_toBottomOf="@id/tv_username"
                        app:layout_goneMarginEnd="@dimen/dp_16"
                        tools:text="ID: 2716834" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_portfolio"
                        style="@style/MetaTextView.S10.PoppinsRegular400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp_8"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:textColor="@color/color_999999"
                        android:textSize="@dimen/sp_12"
                        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
                        app:layout_constraintEnd_toStartOf="@id/tv_follow_btn"
                        app:layout_constraintStart_toEndOf="@id/tvUserId"
                        app:layout_constraintTop_toBottomOf="@id/tv_username"
                        app:layout_goneMarginEnd="@dimen/dp_16"
                        tools:text="Maps: 5" />

                    <Space
                        android:id="@+id/space_follow"
                        android:layout_width="@dimen/dp_8"
                        android:layout_height="@dimen/dp_8"
                        app:layout_constraintBottom_toBottomOf="@id/tv_username"
                        app:layout_constraintEnd_toStartOf="@id/tv_follow_btn"
                        app:layout_constraintTop_toTopOf="@id/tv_username"
                        app:layout_goneMarginEnd="0dp" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_follow_btn"
                        style="@style/MetaTextView.S14.PoppinsMedium500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:background="@drawable/bg_corner_s100_stroke_c1a1a1a_s05"
                        android:drawableStart="@drawable/ic_add_1a1a1a_s12"
                        android:drawablePadding="@dimen/dp_4"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp_13"
                        android:paddingVertical="@dimen/dp_6"
                        android:textColor="@color/color_1A1A1A"
                        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/iv_avatar"
                        tools:text="Follow" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_description_label"
                        style="@style/MetaTextView.S16.PoppinsSemiBold600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_16"
                        android:text="@string/description"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/iv_avatar" />

                    <com.socialplay.gpark.ui.view.ExpandableTextView
                        android:id="@+id/tv_description"
                        style="@style/MetaTextView.S14"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_12"
                        android:lineSpacingMultiplier="1.2"
                        android:textColor="@color/color_212121"
                        android:textColorHighlight="@color/transparent"
                        app:etv_EnableToggleClick="true"
                        app:etv_MaxLinesOnShrink="3"
                        app:etv_ToExpandHint="@string/more_cap"
                        app:etv_ToExpandHintBold="true"
                        app:etv_ToExpandHintColor="@color/color_003B70"
                        app:etv_ToShrinkHint="@string/collapse_cap"
                        app:etv_ToShrinkHintBold="true"
                        app:etv_ToShrinkHintColor="@color/color_003B70"
                        app:etv_ToShrinkHintShow="false"
                        app:layout_constraintTop_toBottomOf="@id/tv_description_label" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_update_time"
                        style="@style/MetaTextView.S12"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_8"
                        android:textColor="@color/color_757575"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_description"
                        tools:text="Update Oct 21, 2024" />

                    <com.airbnb.epoxy.EpoxyRecyclerView
                        android:id="@+id/rv_notice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_24"
                        android:background="@drawable/shape_f5f5f5_corner_8"
                        android:nestedScrollingEnabled="false"
                        android:paddingHorizontal="@dimen/dp_12"
                        android:paddingTop="@dimen/dp_12"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/tv_update_time" />

                    <View
                        android:id="@+id/v_divider_comment"
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginTop="@dimen/dp_24"
                        android:background="@color/color_EEEEEE"
                        app:layout_constraintTop_toBottomOf="@id/rv_notice" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_comment_label"
                        style="@style/MetaTextView.S16.PoppinsSemiBold600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_24"
                        android:text="@string/comment_cap"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/v_divider_comment" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_comment_count"
                        style="@style/MetaTextView.S14"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_6"
                        android:gravity="center_vertical"
                        android:textColor="@color/color_999999"
                        app:layout_constraintBottom_toBottomOf="@id/tv_comment_label"
                        app:layout_constraintStart_toEndOf="@id/tv_comment_label"
                        app:layout_constraintTop_toTopOf="@id/tv_comment_label"
                        tools:text="23" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_sort_btn"
                        style="@style/MetaTextView.S14"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_16"
                        android:drawableEnd="@drawable/ic_game_detail_common_sort_arrow"
                        android:drawablePadding="@dimen/dp_6"
                        android:padding="@dimen/dp_16"
                        android:text="@string/sort_default"
                        android:textColor="@color/color_757575"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/tv_comment_label"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tv_comment_label"
                        tools:visibility="visible" />

                    <ImageView
                        android:id="@+id/iv_my_avatar"
                        android:layout_width="@dimen/dp_38"
                        android:layout_height="@dimen/dp_38"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_24"
                        android:visibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_comment_label"
                        tools:src="@drawable/icon_default_avatar"
                        tools:visibility="visible" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_reply_hint"
                        style="@style/MetaTextView.S14"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_8"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:background="@drawable/shape_f5f5f5_corner_360"
                        android:paddingHorizontal="@dimen/dp_16"
                        android:paddingVertical="@dimen/dp_9"
                        android:text="@string/post_reply"
                        android:textColor="@color/color_BDBDBD"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/iv_my_avatar"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/iv_my_avatar"
                        app:layout_constraintTop_toTopOf="@id/iv_my_avatar"
                        tools:visibility="visible" />

                    <ImageView
                        android:id="@+id/iv_image_btn"
                        android:layout_width="@dimen/dp_36"
                        android:layout_height="0dp"
                        android:paddingStart="@dimen/dp_6"
                        android:paddingEnd="@dimen/dp_6"
                        android:src="@drawable/ic_game_detail_common_image"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/tv_reply_hint"
                        app:layout_constraintEnd_toStartOf="@id/iv_emoji_btn"
                        app:layout_constraintTop_toTopOf="@id/tv_reply_hint"
                        tools:visibility="visible" />

                    <ImageView
                        android:id="@+id/iv_emoji_btn"
                        android:layout_width="@dimen/dp_36"
                        android:layout_height="0dp"
                        android:layout_marginEnd="@dimen/dp_10"
                        android:paddingStart="@dimen/dp_6"
                        android:paddingEnd="@dimen/dp_6"
                        android:src="@drawable/ic_game_detail_common_emoji"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/tv_reply_hint"
                        app:layout_constraintEnd_toEndOf="@id/tv_reply_hint"
                        app:layout_constraintTop_toTopOf="@id/tv_reply_hint"
                        tools:visibility="visible" />

                    <Space
                        android:id="@+id/space_detail_bottom"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_16"
                        app:layout_constraintTop_toBottomOf="@id/iv_my_avatar" />

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintTop_toBottomOf="@id/space_detail_bottom">

                        <LinearLayout
                            android:id="@+id/ll_comment_refresh"
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:gravity="center"
                            android:orientation="vertical">

                            <com.airbnb.lottie.LottieAnimationView
                                android:id="@+id/lav_progress"
                                android:layout_width="@dimen/dp_28"
                                android:layout_height="@dimen/dp_28"
                                app:lottie_loop="true"
                                app:lottie_rawRes="@raw/circle_loading" />

                            <com.socialplay.gpark.ui.view.MetaTextView
                                android:id="@+id/tv_loading"
                                style="@style/MetaTextView.S12"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_4"
                                android:gravity="center_vertical"
                                android:text="@string/loading" />

                        </LinearLayout>

                    </FrameLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.appbar.AppBarLayout>

            <FrameLayout
                android:id="@+id/fl_comment_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

                <com.airbnb.epoxy.EpoxyRecyclerView
                    android:id="@+id/rv_comment"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:overScrollMode="never" />

            </FrameLayout>

        </com.socialplay.gpark.ui.view.VerticalCoordinatorLayout>
    </com.socialplay.gpark.ui.view.refresh.MetaVerticalCoordinatorRefreshLayout>

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/sbphv" />

    <FrameLayout
        android:id="@+id/fl_title_bar"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_48"
        android:layout_marginEnd="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintEnd_toStartOf="@id/tv_build_btn"
        app:layout_constraintStart_toStartOf="@id/tbl"
        app:layout_constraintTop_toTopOf="@id/tbl">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_title_bar_game_name"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:alpha="0"
            android:ellipsize="end"
            android:maxLines="1"
            android:translationY="@dimen/dp_12"
            tools:text="Extreme Long Title" />

    </FrameLayout>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_build_btn"
        style="@style/MetaTextView.S14"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_f6f6f6_round_24"
        android:drawableStart="@drawable/ic_game_detail_common_build"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp_8"
        android:paddingVertical="@dimen/dp_6"
        android:text="@string/create_v2_build"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintEnd_toStartOf="@id/iv_more_btn"
        app:layout_constraintTop_toTopOf="@id/tbl"
        app:layout_goneMarginEnd="@dimen/dp_16" />

    <ImageView
        android:id="@+id/iv_more_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_12"
        android:src="@drawable/ic_detail_share"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tbl" />

    <View
        android:id="@+id/v_divider_enter"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginBottom="@dimen/dp_54"
        android:background="@color/color_EEEEEE"
        app:layout_constraintBottom_toBottomOf="parent" />

    <ImageView
        android:id="@+id/iv_like"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_20"
        android:src="@drawable/ic_game_detail_common_like"
        app:layout_constraintBottom_toTopOf="@id/tv_like"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint2"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_like"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_10"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint2"
        app:layout_constraintEnd_toEndOf="@id/iv_like"
        app:layout_constraintStart_toStartOf="@id/iv_like"
        app:layout_constraintTop_toBottomOf="@id/iv_like"
        tools:text="24" />

    <View
        android:id="@+id/layer_like"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="-10dp"
        android:layout_marginEnd="-10dp"
        app:constraint_referenced_ids="iv_like, tv_like"
        app:layout_constraintBottom_toBottomOf="@id/tv_like"
        app:layout_constraintEnd_toEndOf="@id/iv_like"
        app:layout_constraintStart_toStartOf="@id/iv_like"
        app:layout_constraintTop_toTopOf="@id/iv_like"
        tools:visibility="gone" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lav_like_anim"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_36"
        android:translationX="-0.3dp"
        android:translationY="-0.4dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_like"
        app:layout_constraintEnd_toEndOf="@id/iv_like"
        app:layout_constraintStart_toStartOf="@id/iv_like"
        app:layout_constraintTop_toTopOf="@id/iv_like"
        app:lottie_autoPlay="false"
        app:lottie_fileName="game_detail_like.zip"
        app:lottie_progress="1" />

    <ImageView
        android:id="@+id/iv_light_up"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_20"
        android:src="@drawable/ic_light_up"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/tv_light_up"
        app:layout_constraintStart_toEndOf="@id/iv_like"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint2"
        app:layout_constraintVertical_chainStyle="packed"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_light_up"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_10"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint2"
        app:layout_constraintEnd_toEndOf="@id/iv_light_up"
        app:layout_constraintStart_toStartOf="@id/iv_light_up"
        app:layout_constraintTop_toBottomOf="@id/iv_light_up"
        tools:text="24"
        tools:visibility="visible" />

    <View
        android:id="@+id/layer_light_up"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="-10dp"
        android:layout_marginEnd="-10dp"
        android:visibility="gone"
        app:constraint_referenced_ids="iv_light_up, tv_light_up"
        app:layout_constraintBottom_toBottomOf="@id/tv_light_up"
        app:layout_constraintEnd_toEndOf="@id/iv_light_up"
        app:layout_constraintStart_toStartOf="@id/iv_light_up"
        app:layout_constraintTop_toTopOf="@id/iv_light_up"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/iv_comment"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_20"
        android:src="@drawable/ic_game_detail_common_comment"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/tv_comment"
        app:layout_constraintStart_toEndOf="@id/iv_light_up"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint2"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_comment"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint2"
        app:layout_constraintEnd_toEndOf="@id/iv_comment"
        app:layout_constraintStart_toStartOf="@id/iv_comment"
        app:layout_constraintTop_toBottomOf="@id/iv_comment"
        tools:text="24" />

    <View
        android:id="@+id/layer_comment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="-10dp"
        android:layout_marginEnd="-10dp"
        app:constraint_referenced_ids="iv_comment, tv_comment"
        app:layout_constraintBottom_toBottomOf="@id/tv_comment"
        app:layout_constraintEnd_toEndOf="@id/iv_comment"
        app:layout_constraintStart_toStartOf="@id/iv_comment"
        app:layout_constraintTop_toTopOf="@id/iv_comment"
        tools:visibility="gone" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvReplyHint2"
        style="@style/MetaTextView.S14"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_38"
        android:layout_marginStart="@dimen/dp_20"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_8"
        android:background="@drawable/shape_f5f5f5_corner_360"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_9"
        android:text="@string/post_reply"
        android:textColor="@color/color_BDBDBD"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_comment"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivImageBtn2"
        android:layout_width="@dimen/dp_36"
        android:layout_height="0dp"
        android:paddingStart="@dimen/dp_6"
        android:paddingEnd="@dimen/dp_6"
        android:src="@drawable/ic_game_detail_common_image"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint2"
        app:layout_constraintEnd_toStartOf="@id/ivEmojiBtn2"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint2"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivEmojiBtn2"
        android:layout_width="@dimen/dp_36"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/dp_10"
        android:paddingStart="@dimen/dp_6"
        android:paddingEnd="@dimen/dp_6"
        android:src="@drawable/ic_game_detail_common_emoji"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint2"
        app:layout_constraintEnd_toEndOf="@id/tvReplyHint2"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint2"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/lv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl" />

</androidx.constraintlayout.widget.ConstraintLayout>