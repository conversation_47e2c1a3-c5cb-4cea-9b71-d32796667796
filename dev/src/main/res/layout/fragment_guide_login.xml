<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_0E0922">

    <ImageView
        android:id="@+id/ivBg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/bg_guide_login"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vMaskOld"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/bg_gradient_0e0922_center"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.6"
        tools:visibility="visible" />

    <View
        android:id="@+id/vMaskNew"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/bg_gradient_0e0922"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.5" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTitle"
        style="@style/MetaTextView.S32.PoppinsBold700"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_40"
        android:gravity="center"
        android:text="@string/intl_join_gpark"
        android:textColor="@color/white"
        app:layout_constraintBottom_toTopOf="@id/vContinue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/ivTitle"
        android:layout_width="@dimen/dp_150"
        android:layout_height="@dimen/dp_42"
        android:layout_marginBottom="@dimen/dp_40"
        android:src="@drawable/icon_logo"
        app:layout_constraintBottom_toTopOf="@id/vContinue"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <View
        android:id="@+id/vContinue"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_60"
        android:layout_marginHorizontal="@dimen/dp_32"
        android:layout_marginBottom="@dimen/dp_14"
        android:background="@drawable/bg_red_corner_360"
        android:backgroundTint="@color/white"
        android:minHeight="@dimen/dp_60"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/tvOr"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvContinue"
        style="@style/MetaTextView.S16.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/dp_12"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:singleLine="true"
        android:text="@string/new_player_first_cap"
        android:textColor="@color/neutral_color_1"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/vContinue"
        app:layout_constraintEnd_toEndOf="@id/vContinue"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/ivContinue"
        app:layout_constraintTop_toTopOf="@id/vContinue"
        tools:text="hhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh"
        tools:visibility="visible" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivContinue"
        android:layout_width="@dimen/dp_28"
        android:layout_height="@dimen/dp_28"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_8"
        android:scaleType="centerCrop"
        android:src="@drawable/placeholder_corner_360"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvContinue"
        app:layout_constraintEnd_toStartOf="@id/tvContinue"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@id/vContinue"
        app:layout_constraintTop_toTopOf="@id/tvContinue"
        app:shapeAppearance="@style/circleStyle"
        tools:visibility="visible" />

    <View
        android:id="@+id/vLineOrL"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_1"
        android:background="@color/white_50"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvOr"
        app:layout_constraintEnd_toStartOf="@id/tvOr"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvOr"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvOr"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_14"
        android:paddingHorizontal="@dimen/dp_8"
        android:text="@string/intl_or_all_cap"
        android:textColor="@color/white_50"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/vNewPlayer"
        app:layout_constraintEnd_toStartOf="@id/vLineOrR"
        app:layout_constraintStart_toEndOf="@id/vLineOrL"
        tools:visibility="visible" />

    <View
        android:id="@+id/vLineOrR"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_1"
        android:background="@color/white_50"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvOr"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvOr"
        app:layout_constraintTop_toTopOf="@id/tvOr"
        tools:visibility="visible" />

    <View
        android:id="@+id/vNewPlayer"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_60"
        android:layout_marginHorizontal="@dimen/dp_32"
        android:background="@drawable/bg_round_48"
        android:backgroundTint="@color/white"
        app:layout_constraintBottom_toTopOf="@id/tvReturning"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvNewPlayer"
        style="@style/MetaTextView.S16.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:gravity="center"
        android:text="@string/intl_new_player"
        android:textColor="@color/neutral_color_1"
        app:layout_constraintBottom_toBottomOf="@id/vNewPlayer"
        app:layout_constraintEnd_toEndOf="@id/vNewPlayer"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@id/vNewPlayer"
        app:layout_constraintTop_toTopOf="@id/vNewPlayer" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvReturning"
        style="@style/MetaTextView.S16.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_50"
        android:paddingVertical="@dimen/dp_20"
        android:paddingStart="@dimen/dp_20"
        android:text="@string/intl_already_have_an_account_log_in"
        android:textColor="@color/white_50"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/ivReturning"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/ivReturning"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_1"
        android:paddingStart="@dimen/dp_10"
        android:paddingEnd="@dimen/dp_20"
        android:src="@drawable/ic_right_sharp_arrow"
        app:layout_constraintBottom_toBottomOf="@id/tvReturning"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvReturning"
        app:layout_constraintTop_toTopOf="@id/tvReturning"
        app:tint="@color/white" />

    <View
        android:id="@+id/vReturning"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/tvReturning"
        app:layout_constraintEnd_toEndOf="@id/ivReturning"
        app:layout_constraintStart_toStartOf="@id/tvReturning"
        app:layout_constraintTop_toTopOf="@id/tvReturning" />


    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintTop_toBottomOf="@+id/sbphv_placeholder"
        />

    <ImageView
        android:id="@+id/iv_age_restriction"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dp_20"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintRight_toRightOf="@id/tbl"
        app:layout_constraintTop_toTopOf="@id/tbl"
        tools:visibility="visible" />


    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:visibility="gone"
        app:loadingColor="@color/white" />
</androidx.constraintlayout.widget.ConstraintLayout>