<?xml version="1.0" encoding="UTF-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:baselineAligned="false"
    android:orientation="horizontal"
    tools:background="@color/textColorPrimary">

    <LinearLayout
        android:id="@+id/ll_snapchat_share"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_6"
        android:paddingTop="@dimen/dp_7"
        android:visibility="gone"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/ll_copy_link_share">

        <ImageView
            android:layout_width="@dimen/dp_18"
            android:layout_height="@dimen/dp_18"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/dp_4"
            android:src="@drawable/share_to_snapchat" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_snapchat_share"
            style="@style/MetaTextView.S10.PoppinsRegular400.CenterVertical14"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center_vertical"
            android:text="@string/mgs_share_way_snapchat"
            android:textColor="@color/white_86" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_copy_link_share"
        android:layout_width="0dp"
        android:visibility="gone"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_6"
        android:paddingTop="@dimen/dp_7"
        app:layout_constraintLeft_toRightOf="@id/ll_snapchat_share"
        app:layout_constraintRight_toLeftOf="@id/ll_face_to_face_share">

        <ImageView
            android:layout_width="@dimen/dp_18"
            android:layout_height="@dimen/dp_18"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/dp_4"
            android:src="@drawable/copy_link" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_copy_link_share"
            style="@style/MetaTextView.S10.PoppinsRegular400.CenterVertical14"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:text="@string/mgs_share_way_copy_link"
            android:textColor="@color/white_86" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_face_to_face_share"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintLeft_toRightOf="@id/ll_copy_link_share"
        app:layout_constraintRight_toLeftOf="@id/ll_other_share">

        <ImageView
            android:layout_width="@dimen/dp_18"
            android:layout_height="@dimen/dp_18"
            android:layout_gravity="center_horizontal"
            android:src="@drawable/face_to_face" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_face_to_face_share"
            style="@style/MetaTextView.S10.PoppinsRegular400.CenterVertical14"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:text="@string/mgs_share_way_face_to_face"
            android:textColor="@color/white_86" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_other_share"
        android:layout_width="0dp"
        android:visibility="gone"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp_6"
        android:paddingTop="@dimen/dp_7"
        app:layout_constraintLeft_toRightOf="@id/ll_face_to_face_share"
        app:layout_constraintRight_toRightOf="parent">

        <ImageView
            android:layout_width="@dimen/dp_18"
            android:layout_height="@dimen/dp_18"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/dp_4"
            android:src="@drawable/share_by_system" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_other_share"
            style="@style/MetaTextView.S10.PoppinsRegular400.CenterVertical14"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:text="@string/mgs_share_way_system"
            android:textColor="@color/white_86" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="@color/white_10"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>