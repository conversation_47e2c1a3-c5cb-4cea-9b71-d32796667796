<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:id="@+id/ll_del_area"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_55"
    android:background="@color/color_FF5F42"
    android:gravity="center"
    android:orientation="horizontal"
    android:translationY="@dimen/dp_55"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintLeft_toLeftOf="parent"
    tools:visibility="visible"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lav_delete"
        android:layout_width="@dimen/dp_28"
        android:layout_height="@dimen/dp_28"
        app:lottie_fileName="lottie_trash_can.zip" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_delete_title"
        style="@style/MetaTextView.S16"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_2"
        android:gravity="center"
        android:minHeight="@dimen/dp_24"
        android:text="@string/delete_cap"
        android:textColor="@color/white"
        android:textSize="@dimen/dp_16"
        app:uiLineHeight="@dimen/dp_24" />

</LinearLayout>