<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/dp_8">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivBanner"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        app:layout_constraintDimensionRatio="164:123"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/round_corner_12dp" />

    <ImageView
        android:id="@+id/ivPinLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="3.5dp"
        android:layout_marginTop="@dimen/dp_4"
        android:src="@drawable/ic_profile_maps_pin_tag"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/ivBanner"
        app:layout_constraintTop_toTopOf="@id/ivBanner"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iv_more_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="4.5dp"
        android:paddingVertical="@dimen/dp_4"
        android:src="@drawable/ic_profile_maps_more"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/ivBanner"
        app:layout_constraintTop_toTopOf="@id/ivBanner"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvPv"
        style="@style/MetaTextView.S9.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:background="@drawable/bg_ffdd70_40_round_4"
        android:paddingHorizontal="@dimen/dp_4"
        android:paddingVertical="@dimen/dp_2"
        android:textColor="@color/color_451A03"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivBanner"
        tools:text="@string/x_players" />

    <com.socialplay.gpark.ui.view.FlowLayout
        android:id="@+id/flLabel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_6"
        app:horizontalSpacing="@dimen/dp_4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvPv"
        app:layout_constraintTop_toBottomOf="@id/ivBanner"
        app:maxLines="1"
        app:verticalSpacing="@dimen/dp_4" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvName"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:ellipsize="end"
        android:maxLines="1"
        android:paddingBottom="@dimen/dp_9"
        android:textColor="@color/neutral_color_1"
        app:layout_constraintTop_toBottomOf="@id/tvPv"
        tools:text="Leslie Alexander\n范德萨" />

</androidx.constraintlayout.widget.ConstraintLayout>