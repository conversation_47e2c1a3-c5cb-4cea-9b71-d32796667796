<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginBottom="@dimen/dp_42"
        android:background="@drawable/bg_white_corner_38"
        android:clickable="true"
        android:gravity="bottom|center_horizontal"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_60"
            android:layout_marginTop="@dimen/dp_20"
            android:background="@drawable/bg_e3fff6_round_30"
            android:minWidth="@dimen/dp_60"
            android:padding="@dimen/dp_15" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_title"
            style="@style/MetaTextView.S16.PoppinsBold700"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_16"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textStyle="bold" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_content"
            style="@style/MetaTextView.S14"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_2"
            android:gravity="center"
            android:includeFontPadding="false" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_cancel"
            style="@style/Button.S16.PoppinsMedium500.Warn"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginHorizontal="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_20"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/close" />

    </LinearLayout>

</FrameLayout>