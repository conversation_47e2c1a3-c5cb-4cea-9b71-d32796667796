<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_68"
    android:layout_marginHorizontal="@dimen/dp_16"
    android:layout_marginBottom="@dimen/dp_12"
    android:background="@drawable/bg_f6f6f6_round_14">

    <ImageView
        android:id="@+id/ivLogo"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_36"
        android:layout_marginStart="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTitle"
        style="@style/MetaTextView.S14.PoppinsSemiBold600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_16"
        android:ellipsize="end"
        android:maxLines="1"
        android:minHeight="@dimen/dp_21"
        android:textColor="@color/black"
        android:textSize="@dimen/dp_14"
        app:layout_constraintBottom_toTopOf="@id/tvContent"
        app:layout_constraintEnd_toStartOf="@id/ivArrow"
        app:layout_constraintStart_toEndOf="@id/ivLogo"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:uiLineHeight="@dimen/dp_21"
        tools:text="tiktok" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvContent"
        style="@style/MetaTextView.S11"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_16"
        android:ellipsize="end"
        android:maxLines="1"
        android:minHeight="@dimen/dp_16"
        android:textColor="@color/color_B3B3B3"
        android:textSize="@dimen/dp_11"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/ivArrow"
        app:layout_constraintStart_toEndOf="@id/ivLogo"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        app:uiLineHeight="@dimen/dp_16"
        tools:text="https://v.douyin.com/iraaT4ehttps://v.douyin.com/iraaT4e...https://v.douyin.com/iraaT4e...https://v.douyin.com/iraaT4e..." />


    <ImageView
        android:id="@+id/ivArrow"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_16"
        android:src="@drawable/ic_arrow_right_12_bdbdbd"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>

