<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_84">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivUserAvatar"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:layout_marginStart="@dimen/dp_16"
        android:scaleType="centerCrop"
        android:src="@drawable/icon_item_group_chat_avatar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/shapeRound30Style" />

    <View
        android:id="@+id/viewUserAvatarClick"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvUserName"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvUserName"
        style="@style/MetaTextView.S14.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_8"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_14"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tvJoinApplyDesc"
        app:layout_constraintEnd_toStartOf="@id/tvTagNew"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/ivUserAvatar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_goneMarginEnd="@dimen/dp_20"
        tools:text="404 Brain Not Found" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTagNew"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_17"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/bg_fff1f3_round_19"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_6"
        android:text="@string/request_join_group_tag_new"
        android:textColor="@color/color_FF5F42"
        android:textSize="@dimen/sp_10"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/tvUserName"
        app:layout_constraintEnd_toStartOf="@id/tvMoreBtn"
        app:layout_constraintStart_toEndOf="@id/tvUserName"
        app:layout_constraintTop_toTopOf="@id/tvUserName"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvJoinApplyDesc"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_2"
        android:layout_marginEnd="@dimen/dp_17"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="@string/request_want_to_join_group_desc"
        android:textColor="@color/color_B3B3B3"
        android:textSize="@dimen/sp_12"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvMoreBtn"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@id/tvUserName"
        app:layout_constraintTop_toBottomOf="@id/tvUserName" />

    <View
        android:id="@+id/viewCenterClick"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/spaceMoreBtnStart"
        app:layout_constraintStart_toStartOf="@id/tvUserName"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:id="@+id/spaceMoreBtnStart"
        android:layout_width="@dimen/dp_20"
        android:layout_height="0dp"
        app:layout_constraintEnd_toStartOf="@id/tvMoreBtn"
        app:layout_constraintTop_toTopOf="@id/tvMoreBtn" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvMoreBtn"
        style="@style/MetaTextView.S12.PoppinsSemiBold600"
        android:layout_width="@dimen/dp_86"
        android:layout_height="@dimen/dp_28"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/bg_ffef30_round_40"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_8"
        android:text="@string/request_join_group_agree"
        android:textColor="@color/color_212121"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewMoreClick"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/spaceMoreBtnStart"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>