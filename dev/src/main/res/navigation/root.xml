<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    app:startDestination="@id/splash">
    <include app:graph="@navigation/mw_develop_navigation" />
    <include app:graph="@navigation/custom_root" />

    <fragment
        android:id="@+id/splash"
        android:name="com.socialplay.gpark.ui.splash.SplashFragment"
        android:label="SplashFragment"
        tools:layout="@layout/fragment_splash">
        <action
            android:id="@+id/action_splashFragment_to_homeFragment"
            app:destination="@id/main"
            app:enterAnim="@anim/ps_anim_fade_in"
            app:exitAnim="@anim/ps_anim_fade_out" />
    </fragment>

    <fragment
        android:id="@+id/login_by_phone"
        android:name="com.socialplay.gpark.ui.login.LoginByPhoneFragment"
        tools:layout="@layout/fragment_login_by_phone">
        <argument
            android:name="source"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="phoneNumber"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="onlyLogin"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="successToMain"
            android:defaultValue="false"
            app:argType="boolean" />
    </fragment>
    <fragment
        android:id="@+id/main"
        android:name="com.socialplay.gpark.ui.main.MainFragment"
        tools:layout="@layout/fragment_main" />

    <dialog
        android:id="@+id/dialog_simple"
        android:name="com.socialplay.gpark.ui.dialog.ConfirmDialog"
        tools:layout="@layout/dialog_fragment_simple">

        <argument
            android:name="imageId"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="content"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="contentColor"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="contentGravity"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="contentSpace"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="title"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="tips"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="leftBtnText"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="rightBtnText"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="showTitle"
            android:defaultValue="false"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="showContent"
            android:defaultValue="false"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="showTips"
            android:defaultValue="false"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="showLeftBtn"
            android:defaultValue="true"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="showRightBtn"
            android:defaultValue="true"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="leftLightBackground"
            android:defaultValue="false"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="rightLightBackground"
            android:defaultValue="true"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="leftTextColor"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="rightTextColor"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="isBold"
            android:defaultValue="false"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="dialogWidth"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="isCancelable"
            android:defaultValue="true"
            app:argType="boolean" />
        <argument
            android:name="isCanceledOnTouchOutside"
            android:defaultValue="true"
            app:argType="boolean" />
        <argument
            android:name="isSignOut"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="isRed"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="dimAmount"
            android:defaultValue="-1"
            app:argType="float" />
        <argument
            android:name="style"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="height"
            android:defaultValue="-1"
            app:argType="integer" />
    </dialog>


    <fragment
        android:id="@+id/devBuildConfigFragment"
        android:name="com.socialplay.gpark.ui.developer.BuildConfigFragment"
        android:label="DevBuildConfigFragment" />

    <fragment
        android:id="@+id/developerFragment"
        android:name="com.socialplay.gpark.ui.developer.DeveloperFragment"
        tools:layout="@layout/fragment_developer" />

    <fragment
        android:id="@+id/devDemoFragment"
        android:name="com.socialplay.gpark.ui.developer.DemoFragment"
        tools:layout="@layout/fragment_demo" />

    <fragment
        android:id="@+id/devPandoraToggleFragment"
        android:name="com.socialplay.gpark.ui.developer.DevPandoraToggleFragment"
        tools:layout="@layout/fragment_developer_pandora" />

    <fragment
        android:id="@+id/devAppParamsFragment"
        android:name="com.socialplay.gpark.ui.developer.DevAppParamsFragment"
        tools:layout="@layout/fragment_developer_app_params" />

    <fragment
        android:id="@+id/appSigningInfoFragment"
        android:name="com.socialplay.gpark.ui.developer.AppSigningInfoFragment"
        tools:layout="@layout/fragment_app_signing_info" />

    <fragment
        android:id="@+id/devShareFragment"
        android:name="com.socialplay.gpark.ui.developer.DevShareFragment"
        tools:layout="@layout/fragment_developer_share" />

    <fragment
        android:id="@+id/devMwFragment"
        android:name="com.socialplay.gpark.ui.developer.DeveloperMWFragment"
        tools:layout="@layout/fragment_developer_mw" />

    <fragment
        android:id="@+id/devMMKVFragment"
        android:name="com.socialplay.gpark.ui.developer.MmkvManagementFragment" />

    <fragment
        android:id="@+id/devReviewGame"
        android:name="com.socialplay.gpark.ui.developer.DeveloperReviewGameFragment"
        tools:layout="@layout/fragment_developer_review_game">

        <argument
            android:name="token"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />
    </fragment>


    <dialog
        android:id="@+id/permissionRequest"
        android:name="com.socialplay.gpark.ui.permission.PermissionDialogFragment"
        tools:layout="@layout/dialog_fragment_permission">

        <argument
            android:name="permissions"
            android:defaultValue="@null"
            app:argType="string[]"
            app:nullable="true" />

        <argument
            android:name="des"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />

    </dialog>

    <fragment
        android:id="@+id/search"
        android:name="com.socialplay.gpark.ui.search.SearchFragment"
        tools:layout="@layout/fragment_search" />
    <fragment
        android:id="@+id/ai_bot_conversation"
        android:name="com.socialplay.gpark.ui.aibot.AiBotConversationFragment"></fragment>
    <fragment
        android:id="@+id/ai_bot_detail"
        android:name="com.socialplay.gpark.ui.aibot.detail.AiBotDetailFragment"></fragment>


    <fragment
        android:id="@+id/web"
        android:name="com.socialplay.gpark.ui.web.WebFragment"
        tools:layout="@layout/fragment_web">

        <argument
            android:name="title"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="url"
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="showTitle"
            android:defaultValue="true"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="statusBarColor"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="showStatusBar"
            android:defaultValue="true"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="extra"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
    </fragment>
    <fragment
        android:id="@+id/about_us"
        android:name="com.socialplay.gpark.ui.aboutus.AboutUsFragment"
        tools:layout="@layout/fragment_about_us" />
    <fragment
        android:id="@+id/login"
        android:name="com.socialplay.gpark.ui.login.LoginFragment"
        tools:layout="@layout/fragment_login">
        <argument
            android:name="source"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="gid"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="onlyLogin"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="continueAccountInfo"
            android:defaultValue="@null"
            app:argType="com.socialplay.gpark.data.model.user.ContinueAccountInfo"
            app:nullable="true" />
        <argument
            android:name="successToMain"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="lastLoginType"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/sign_up"
        android:name="com.socialplay.gpark.ui.account.SignUpFragment"
        tools:layout="@layout/fragment_sign_up">
        <argument
            android:name="source"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/setting"
        android:name="com.socialplay.gpark.ui.account.setting.SettingFragment"
        tools:layout="@layout/fragment_setting">
        <argument
            android:name="source"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="gameId"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/account_setting"
        android:name="com.socialplay.gpark.ui.account.AccountSettingFragment"
        tools:layout="@layout/fragment_account_setting">
        <argument
            android:name="source"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="gameId"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/friend_request_list"
        android:name="com.socialplay.gpark.ui.im.friendrequest.FriendRequestListFragment" />

    <fragment
        android:id="@+id/addFriend"
        android:name="com.socialplay.gpark.ui.friend.AddFriendAndContactsTabFragment" />

    <fragment
        android:id="@+id/conversation_fragment"
        android:name="com.socialplay.gpark.ui.im.conversation.ConversationFragment"
        tools:layout="@layout/fragment_conversation">
        <argument
            android:name="otherUid"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="title"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="shareContent"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="userLabelInfo"
            app:argType="com.socialplay.gpark.data.model.user.SimpleUserLabelInfo"
            app:nullable="true" />
    </fragment>
    <fragment
        android:id="@+id/private_chat_fragment"
        android:name="com.socialplay.gpark.ui.im.conversation.PrivateChatFragment"
        tools:layout="@layout/fragment_conversation_v2">
        <argument
            android:name="otherUid"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="title"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="shareContent"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="userLabelInfo"
            app:argType="com.socialplay.gpark.data.model.user.SimpleUserLabelInfo"
            app:nullable="true" />
    </fragment>
    <fragment
        android:id="@+id/group_chat_fragment"
        android:name="com.socialplay.gpark.ui.im.conversation.GroupChatFragment"
        tools:layout="@layout/fragment_conversation_v2">
        <argument
            android:name="groupChatId"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="imId"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="groupName"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="shareContent"
            app:argType="string"
            app:nullable="true" />
    </fragment>
    <fragment
        android:id="@+id/searchFriend"
        android:name="com.socialplay.gpark.ui.im.friendsearch.FriendSearchFragment" />

    <dialog
        android:id="@+id/myQRCode"
        android:name="com.socialplay.gpark.ui.im.friendadd.MyQRCodeFragment"
        tools:layout="@layout/fragment_my_qr_code" />

    <fragment
        android:id="@+id/qr_code_scan"
        android:name="com.socialplay.gpark.ui.qrcode.QRCodeScanFragment"
        tools:layout="@layout/fragment_qr_code_scan">

        <argument
            android:name="scanResultKey"
            app:argType="string" />

        <argument
            android:name="entry"
            app:argType="com.socialplay.gpark.data.model.qrcode.ScanEntry" />

        <argument
            android:name="customData"
            app:argType="android.os.Bundle" />

        <argument
            android:name="packageName"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="gameId"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="gameType"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <dialog
        android:id="@+id/camera_permission_dialog"
        android:name="com.socialplay.gpark.ui.qrcode.CameraPermissionDialog"
        tools:layout="@layout/dialog_add_friend">
        <argument
            android:name="goSettings"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="scanResultKey"
            app:argType="string" />
        <argument
            android:name="packageName"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="gameId"
            app:argType="string"
            app:nullable="true" />
    </dialog>

    <fragment
        android:id="@+id/friend_apply"
        android:name="com.socialplay.gpark.ui.friend.friendapply.FriendApplyFragment"
        tools:layout="@layout/fragment_friend_apply">

        <argument
            android:name="uuid"
            app:argType="string" />

        <argument
            android:name="userNumber"
            app:argType="string"
            app:nullable="true" />

        <argument
            android:name="userName"
            app:argType="string"
            app:nullable="true" />

        <argument
            android:name="gamePackageName"
            app:argType="string" />

        <argument
            android:name="avatar"
            app:argType="string"
            app:nullable="true" />

        <argument
            android:name="userLabelInfo"
            app:argType="com.socialplay.gpark.data.model.user.SimpleUserLabelInfo"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/chatSetting"
        android:name="com.socialplay.gpark.ui.im.setting.ChatSettingFragment"
        tools:layout="@layout/fragment_chat_setting">
        <argument
            android:name="uuid"
            app:argType="string"
            app:nullable="true" />

        <argument
            android:name="chatSettingResultKey"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/remarkAlert"
        android:name="com.socialplay.gpark.ui.im.setting.RemarkAlertFragment"
        tools:layout="@layout/fragment_remark_alert">
        <argument
            android:name="userName"
            app:argType="string" />
        <argument
            android:name="remark"
            app:argType="string" />
        <argument
            android:name="uuid"
            app:argType="string" />
        <argument
            android:name="remarkResultKey"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/login_confirm"
        android:name="com.socialplay.gpark.ui.auth.LoginConfirmFragment">
        <argument
            android:name="ssoLoginRequest"
            app:argType="com.socialplay.gpark.data.model.SsoLoginRequest"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/account_email_verify"
        android:name="com.socialplay.gpark.ui.account.EmailVerifyFragment">
        <argument
            android:name="verifyScene"
            app:argType="com.socialplay.gpark.data.model.account.EmailScene" />
        <argument
            android:name="oldEmailCode"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="requestResultKey"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="source"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="gameId"
            app:argType="string"
            app:nullable="true" />
    </fragment>
    <fragment
        android:id="@+id/password_set"
        android:name="com.socialplay.gpark.ui.account.PasswordSetFragment">
        <argument
            android:name="email"
            app:argType="string" />
        <argument
            android:name="code"
            app:argType="string" />
        <argument
            android:name="source"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="gameId"
            app:argType="string"
            app:nullable="true" />
    </fragment>
    <fragment
        android:id="@+id/password_change"
        android:name="com.socialplay.gpark.ui.account.PasswordChangeFragment">
        <argument
            android:name="source"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="gameId"
            app:argType="string"
            app:nullable="true" />
    </fragment>
    <fragment
        android:id="@+id/nickname_reset"
        android:name="com.socialplay.gpark.ui.account.NickNameResetFragment">
        <argument
            android:name="oldNickName"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/city_reset"
        android:name="com.socialplay.gpark.ui.account.CityResetFragment">
        <argument
            android:name="oldCityName"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/information_reset"
        android:name="com.socialplay.gpark.ui.account.InformationResetFragment">
        <argument
            android:name="oldInformation"
            app:argType="string"
            app:nullable="true" />
    </fragment>


    <fragment
        android:id="@+id/account_cancellation"
        android:name="com.socialplay.gpark.ui.account.AccountCancellationFragment">
        <argument
            android:name="code"
            app:argType="string" />
        <argument
            android:name="source"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="gameId"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/editProfile"
        android:name="com.socialplay.gpark.ui.account.EditProfileFragment"
        tools:layout="@layout/fragment_edit_profile">
        <argument
            android:name="gameId"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="packageName"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/connected_accounts"
        android:name="com.socialplay.gpark.ui.account.ConnectedAccountsFragment"
        tools:layout="@layout/fragment_connected_accounts">
        <argument
            android:name="source"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="gameId"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/bind_account_and_password"
        android:name="com.socialplay.gpark.ui.account.AccountAndPasswordBindFragment">
        <argument
            android:name="source"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="gameId"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <dialog
        android:id="@+id/gameReviewDialog"
        android:name="com.socialplay.gpark.ui.gamereview.dialog.GameReviewDialog">
        <argument
            android:name="gameId"
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="gameName"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="gameIcon"
            app:argType="string"
            app:nullable="true" />
    </dialog>

    <dialog
        android:id="@+id/deleteReviewDialog"
        android:name="com.socialplay.gpark.ui.gamereview.dialog.DeleteMyReviewDialog"></dialog>

    <fragment
        android:id="@+id/edit_game_review"
        android:name="com.socialplay.gpark.ui.gamereview.edit.EditGameReviewFragment">
        <argument
            android:name="gameId"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="needFetchReview"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="attitude"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="lengthLimit"
            app:argType="long" />
        <argument
            android:name="errorMessage"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="botId"
            app:argType="string"
            app:nullable="true" />

    </fragment>

    <fragment
        android:id="@+id/reviewList"
        android:name="com.socialplay.gpark.ui.gamereview.ReviewListFragment">
        <argument
            android:name="gameId"
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="reviewCount"
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="canWriteReview"
            app:argType="boolean"
            app:nullable="false" />
    </fragment>
    <fragment
        android:id="@+id/pay"
        android:name="com.socialplay.gpark.ui.pay.PayFragment" />

    <fragment
        android:id="@+id/webviewDialog"
        android:name="com.socialplay.gpark.ui.dialog.WebViewDialog">

        <argument
            android:name="url"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="extra"
            android:defaultValue=""
            app:argType="string"
            app:nullable="true" />

    </fragment>

    <fragment
        android:id="@+id/noticeList"
        android:name="com.socialplay.gpark.ui.notice.editor.EditorNoticeListFragment"
        tools:layout="@layout/fragment_editor_notice" />

    <fragment
        android:id="@+id/operationList"
        android:name="com.socialplay.gpark.ui.notice.operation.OperationNoticeListFragment" />

    <dialog
        android:id="@+id/dialog_simple_select_txt"
        android:name="com.socialplay.gpark.ui.developer.dialog.SimpleSelectTxtDialogFragment"
        tools:layout="@layout/dialog_fragment_simple_select_txt">

        <argument
            android:name="title"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="leftBtnText"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="rightBtnText"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="showTitle"
            android:defaultValue="true"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="showLeftBtn"
            android:defaultValue="true"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="showRightBtn"
            android:defaultValue="true"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="leftLightBackground"
            android:defaultValue="false"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="rightLightBackground"
            android:defaultValue="true"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="leftTextColor"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="rightTextColor"
            android:defaultValue="0"
            app:argType="integer" />
        <argument
            android:name="isClickOutsideDismiss"
            android:defaultValue="true"
            app:argType="boolean" />
        <argument
            android:name="isBackPressedDismiss"
            android:defaultValue="true"
            app:argType="boolean" />

        <argument
            android:name="titleSize"
            android:defaultValue="15"
            app:argType="float" />

        <argument
            android:name="selectItems"
            app:argType="string[]"
            app:nullable="false" />

        <argument
            android:name="selectTxt"
            app:argType="string" />
    </dialog>

    <fragment
        android:id="@+id/devEnvFragment"
        android:name="com.socialplay.gpark.ui.developer.DeveloperEnvFragment"
        android:label="DeveloperEnvFragment" />
    <dialog
        android:id="@+id/newSuggestedDialog"
        android:name="com.socialplay.gpark.ui.suggestion.NewGameSuggestionDialog">
        <argument
            android:name="games"
            app:argType="com.socialplay.gpark.data.model.GameSuggestionInfo[]" />
        <argument
            android:name="dataSource"
            app:argType="string" />
    </dialog>

    <fragment
        android:id="@+id/myProfile"
        android:name="com.socialplay.gpark.ui.profile.MeProfileFragment">
        <argument
            android:name="entry"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="from"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="isFromBottom"
            android:defaultValue="false"
            app:argType="boolean" />
    </fragment>

    <fragment
        android:id="@+id/otherProfile"
        android:name="com.socialplay.gpark.ui.profile.HeProfileFragment">
        <argument
            android:name="uuid"
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="from"
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="checkFollow"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="isFromBottom"
            android:defaultValue="false"
            app:argType="boolean" />
    </fragment>

    <dialog
        android:id="@+id/img_pre_dialog"
        android:name="com.socialplay.gpark.ui.imgpre.ImgPreDialogFragment"
        tools:layout="@layout/dialog_img_pre">

        <argument
            android:name="imgUrls"
            app:argType="string[]"
            app:nullable="false" />

        <argument
            android:name="position"
            app:argType="integer"
            app:nullable="false" />

        <argument
            android:name="showSave"
            app:argType="boolean"
            app:nullable="false" />

        <argument
            android:name="imgAction"
            app:argType="string"
            app:nullable="true" />

        <argument
            android:name="from"
            app:argType="string"
            app:nullable="false" />

        <argument
            android:name="skipCache"
            app:argType="boolean"
            app:nullable="false" />
    </dialog>

    <dialog
        android:id="@+id/dialogReport"
        android:name="com.socialplay.gpark.ui.reportBlock.ReportReasonDialog"
        tools:layout="@layout/dialog_report_reason">
        <!--举报类型：评论举报&用户举报-->
        <argument
            android:name="reportType"
            app:argType="com.socialplay.gpark.data.model.reportBlock.ReportType"
            app:nullable="false" />
        <!--和举报类型对应：评论id&用户id&房间id-->
        <argument
            android:name="reportTargetId"
            app:argType="string"
            app:nullable="false" />
        <!--如果是评论举报则传 gameid，用户举报不需要传-->
        <argument
            android:name="gameid"
            app:argType="string"
            app:nullable="true" />
        <!--举报房间的房主id-->
        <argument
            android:name="reportUuid"
            app:argType="string"
            app:nullable="true" />
        <!--是否需要举报结果-->
        <argument
            android:name="needResult"
            android:defaultValue="false"
            app:argType="boolean"
            app:nullable="false" />
    </dialog>

    <fragment
        android:id="@+id/videoPlayerFullScreen"
        android:name="com.socialplay.gpark.ui.exoplayer.VideoPlayerFullScreenFragment">
        <argument
            android:name="analytic_from"
            android:defaultValue="unknown"
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="analytic_extra"
            android:defaultValue="@null"
            app:argType="android.os.Bundle"
            app:nullable="true" />
    </fragment>

    <dialog
        android:id="@+id/dialogRenameLocal"
        android:name="com.socialplay.gpark.ui.editor.legecy.RenameLocalDialog">

        <argument
            android:name="requestKey"
            app:argType="string" />

        <argument
            android:name="currentName"
            app:argType="string" />

        <argument
            android:name="filePath"
            app:argType="string" />
    </dialog>

    <fragment
        android:id="@+id/editor_create"
        android:name="com.socialplay.gpark.ui.editor.create.EditorCreateFragment">
        <argument
            android:name="fromBottomTab"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="source"
            android:defaultValue="null"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/contact_list_fragment"
        android:name="com.socialplay.gpark.ui.friend.contacts.ContactListFragment"
        tools:layout="@layout/fragment_contact_list">
        <argument
            android:name="shareContent"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="shareRequestKey"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="needBackRole"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="needBackGame"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="fromGameId"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="title"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="isCreateGroup"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="isInviteGroupMembers"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="groupId"
            app:argType="long" />
        <argument
            android:name="maxSelectCount"
            app:argType="integer" />
    </fragment>

    <fragment
        android:id="@+id/migrate_editor_local"
        android:name="com.socialplay.gpark.ui.developer.migrate.MigrateLocalDraftFragment" />

    <fragment
        android:id="@+id/editor_create_v2"
        android:name="com.socialplay.gpark.ui.editor.create.EditorCreateV2Fragment">
        <argument
            android:name="initTab"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="categoryId"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="fromBottomTab"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="source"
            android:defaultValue="null"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/sys_activities_fragment"
        android:name="com.socialplay.gpark.ui.im.activities.SysDetailListFragment">
        <argument
            android:name="groupId"
            app:argType="long"
            app:nullable="false" />
        <argument
            android:name="groupContentType"
            app:argType="integer"
            app:nullable="false" />
        <argument
            android:name="title"
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="unReadCount"
            app:argType="long"
            app:nullable="false" />

        <argument
            android:name="type"
            app:argType="long" />
        <argument
            android:name="gameId"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="TabList"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="source"
            app:argType="long" />
        <argument
            android:name="avatar"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/all_room"
        android:name="com.socialplay.gpark.ui.room.all.HomeAllRoomListFragment" />

    <fragment
        android:id="@+id/ugc_all"
        android:name="com.socialplay.gpark.ui.editorschoice.ugc.UgcAllFragment" />

    <fragment
        android:id="@+id/create_room"
        android:name="com.socialplay.gpark.ui.room.create.HomeCreateRoomFragment" />

    <!--这里只是用Args,不用id跳转-->
    <activity
        android:id="@+id/ignore_id"
        android:name="com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivity">
        <argument
            android:name="status"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="fromGameId"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="fromGamePkg"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="targetGameId"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="targetGamePkg"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="category_id"
            app:argType="integer" />
        <argument
            android:name="roomIdFromCp"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="inviteOpenId"
            android:defaultValue=""
            app:argType="string" />
        <argument
            android:name="tryOn"
            android:defaultValue="@null"
            app:argType="com.socialplay.gpark.function.editor.RoleGameTryOn"
            app:nullable="true" />
    </activity>

    <fragment
        android:id="@+id/devDemoListFragment"
        android:name="com.socialplay.gpark.ui.developer.DemoListFragment"
        tools:layout="@layout/fragment_demo_list" />

    <fragment
        android:id="@+id/feedback"
        android:name="com.socialplay.gpark.ui.feedback.FeedbackFragment" />
    <fragment
        android:id="@+id/cottageAllRoom"
        android:name="com.socialplay.gpark.ui.cottage.CottageAllRoomFragment" />
    <fragment
        android:id="@+id/cottageRoomDetail"
        android:name="com.socialplay.gpark.ui.cottage.detail.CottageRoomDetailDialog">

    </fragment>

    <fragment
        android:id="@+id/publish_post"
        android:name="com.socialplay.gpark.ui.post.v2.PublishPostFragment" />

    <fragment
        android:id="@+id/post_detail"
        android:name="com.socialplay.gpark.ui.post.v2.PostDetailFragment" />

    <fragment
        android:id="@+id/notification_setting"
        android:name="com.socialplay.gpark.ui.account.setting.NotificationSettingFragment" />

    <fragment
        android:id="@+id/startupCreateAvatarV2"
        android:name="com.socialplay.gpark.ui.account.startup.CreateAvatarV2Fragment" />

    <fragment
        android:id="@+id/startupEnterName"
        android:name="com.socialplay.gpark.ui.account.startup.EnterNameFragment" />

    <fragment
        android:id="@+id/startupSelectBirthDay"
        android:name="com.socialplay.gpark.ui.account.startup.SelectBirthdayFragment" />

    <fragment
        android:id="@+id/startupSelectMode"
        android:name="com.socialplay.gpark.ui.account.startup.SelectModeFragment" />

    <fragment
        android:id="@+id/startupSelectModeGame"
        android:name="com.socialplay.gpark.ui.account.startup.SelectModeGameFragment" />

    <fragment
        android:id="@+id/guideLogin"
        android:name="com.socialplay.gpark.ui.account.guide.GuideLoginFragment"
        tools:layout="@layout/fragment_guide_login"></fragment>

    <fragment
        android:id="@+id/topic_detail"
        android:name="com.socialplay.gpark.ui.post.topic.detail.TopicDetailTabFragment" />

    <fragment
        android:id="@+id/topic_square"
        android:name="com.socialplay.gpark.ui.post.topic.square.TopicSquareFragment" />

    <fragment
        android:id="@+id/languageSetting"
        android:name="com.socialplay.gpark.ui.locale.LanguageSettingFragment" />

    <!-- 仅使用args -->
    <dialog
        android:id="@+id/basic_input_dialog"
        android:name="com.socialplay.gpark.ui.videofeed.comment.BasicInputDialog">
        <argument
            android:name="hint"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="sendText"
            app:argType="string"
            app:nullable="true" />

        <argument
            android:name="dimAmount"
            android:defaultValue="0"
            app:argType="float" />

        <argument
            android:name="requestKey"
            app:argType="string" />

    </dialog>

    <dialog
        android:id="@+id/community_share_dialog"
        android:name="com.socialplay.gpark.ui.share.CommunityShareDialog"></dialog>
    <dialog
        android:id="@+id/aiBotTagDialog"
        android:name="com.socialplay.gpark.ui.aibot.tag.AiBotTagDialog"></dialog>

    <fragment
        android:id="@+id/privacySetting"
        android:name="com.socialplay.gpark.ui.account.setting.privacysetting.PrivacySettingFragment" />
    <!-- 仅使用args -->

    <fragment
        android:id="@+id/recommendVideoList"
        android:name="com.socialplay.gpark.ui.videofeed.recommend.RecommendVideoListFragment">

        <argument
            android:name="mavericks:arg"
            app:argType="android.os.Parcelable"
            app:nullable="false" />
    </fragment>

    <fragment
        android:id="@+id/recommendVideoFeed"
        android:name="com.socialplay.gpark.ui.videofeed.recommend.RecommendVideoFeedFragment" />

    <dialog
        android:id="@+id/kolGameLabelDialog"
        android:name="com.socialplay.gpark.ui.kol.popup.KolGameLabelDialog" />

    <fragment
        android:id="@+id/kolMoreUgc"
        android:name="com.socialplay.gpark.ui.kol.game.KolMoreUgcFragment" />

    <fragment
        android:id="@+id/kolTypeCreator"
        android:name="com.socialplay.gpark.ui.kol.creator.type.TypeKoCreatorParentFragment" />

    <fragment
        android:id="@+id/kolLabelCreator"
        android:name="com.socialplay.gpark.ui.kol.creator.label.LabelKolCreatorFragment" />

    <fragment
        android:id="@+id/choice_home_fragment"
        android:name="com.socialplay.gpark.ui.editorschoice.ChoiceHomeFragment" />


    <fragment
        android:id="@+id/appReportUser"
        android:name="com.socialplay.gpark.ui.reportBlock.user.AppReportUserFragment">
        <argument
            android:name="targetUid"
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="targetUerName"
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="source"
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="requestKey"
            app:argType="string"
            app:nullable="false" />
    </fragment>

    <fragment
        android:id="@+id/continueManage"
        android:name="com.socialplay.gpark.ui.manage.ContinueManageFragment"></fragment>
    <fragment
        android:id="@+id/edit_link"
        android:name="com.socialplay.gpark.ui.profile.link.EditLinkFragment">

    </fragment>
    <fragment
        android:id="@+id/add_link"
        android:name="com.socialplay.gpark.ui.profile.link.AddLinkDialog">

    </fragment>

    <fragment
        android:id="@+id/fragment_ai_bot_ugc"
        android:name="com.socialplay.gpark.ui.aibot.ugc.AIBotUGCCreateFragment">
        <argument
            android:name="gender"
            app:argType="string"
            app:nullable="false" />

    </fragment>

    <fragment
        android:id="@+id/fragment_ai_bot_ugc_result"
        android:name="com.socialplay.gpark.ui.aibot.ugc.create.AIBotUGCCreateResultFragment">
        <argument
            android:name="data"
            app:argType="string"
            app:nullable="true" />
    </fragment>
    <fragment
        android:id="@+id/fragment_ai_bot_ugc_select"
        android:name="com.socialplay.gpark.ui.aibot.ugc.select.AIBotUGCSelectFragment">
        <argument
            android:name="data"
            app:argType="string"
            app:nullable="false" />
    </fragment>
    <fragment
        android:id="@+id/fragmentAiBotClip"
        android:name="com.socialplay.gpark.ui.aibot.ugc.clip.AIBotUGCImageClipFragment"></fragment>
    <fragment
        android:id="@+id/fragment_ai_bot_generate"
        android:name="com.socialplay.gpark.ui.aibot.ugc.generate.AIBotUGCGenerateFragment">
        <argument
            android:name="data"
            app:argType="string"
            app:nullable="false" />
    </fragment>
    <fragment
        android:id="@+id/stub_start"
        android:name="com.socialplay.gpark.ui.base.StubStartFragment"></fragment>
    <fragment
        android:id="@+id/ugc_backup_fragment"
        android:name="com.socialplay.gpark.ui.editor.backups.UgcBackupFragment" />
    <fragment
        android:id="@+id/profile_v2"
        android:name="com.socialplay.gpark.ui.editor.home.v2.EditorHomeFragmentV3">
        <argument
            android:name="isFromBottom"
            android:defaultValue="false"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="isMe"
            android:defaultValue="false"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="uuid"
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="profileArgs"
            android:defaultValue="@null"
            app:argType="android.os.Bundle"
            app:nullable="true" />
    </fragment>
    <fragment
        android:id="@+id/chat_tab"
        android:name="com.socialplay.gpark.ui.im.ConversationListFragment">
        <argument
            android:name="isFromBottom"
            android:defaultValue="false"
            app:argType="boolean"
            app:nullable="false" />
    </fragment>
    <fragment
        android:id="@+id/community_personal_posts"
        android:name="com.socialplay.gpark.ui.profile.post.PersonalArticleFragment">
        <argument
            android:name="uuid"
            app:argType="string" />
        <argument
            android:name="isMe"
            app:argType="boolean" />
    </fragment>
    <fragment
        android:id="@+id/moments"
        android:name="com.socialplay.gpark.ui.moments.MomentsFragment">
        <argument
            android:name="categoryId"
            android:defaultValue="-1"
            app:argType="integer" />

        <argument
            android:name="finishMain"
            android:defaultValue="false"
            app:argType="boolean"
            app:nullable="false" />
    </fragment>
    <fragment
        android:id="@+id/moments_main"
        android:name="com.socialplay.gpark.ui.moments.main.MomentsMainFragment">
        <argument
            android:name="categoryId"
            android:defaultValue="-1"
            app:argType="integer" />
    </fragment>
    <fragment
        android:id="@+id/moments_list"
        android:name="com.socialplay.gpark.ui.moments.list.MomentsListFragment">
        <argument
            android:name="categoryId"
            android:defaultValue="-1"
            app:argType="integer" />
    </fragment>
    <fragment
        android:id="@+id/moments_type"
        android:name="com.socialplay.gpark.ui.moments.list.MomentsTypeListFragment">
        <argument
            android:name="title"
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="type"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="categoryId"
            android:defaultValue="-1"
            app:argType="integer" />
    </fragment>
    <fragment
        android:id="@+id/ugc_design_detail"
        android:name="com.socialplay.gpark.ui.outfit.UgcDesignDetailFragment"></fragment>
    <fragment
        android:id="@+id/ugc_design_edit"
        android:name="com.socialplay.gpark.ui.outfit.UgcDesignEditFragment"></fragment>
    <fragment
        android:id="@+id/pgc_game_detail"
        android:name="com.socialplay.gpark.ui.gamedetail.unify.PgcGameDetailFragment" />
    <fragment
        android:id="@+id/ugc_game_detail"
        android:name="com.socialplay.gpark.ui.gamedetail.unify.UgcGameDetailFragment" />
    <fragment
        android:id="@+id/build_guide"
        android:name="com.socialplay.gpark.ui.account.startup.BuildGuideFragment" />

    <fragment
        android:id="@+id/real_name_party"
        android:name="com.socialplay.gpark.ui.realname.RealNameFragment"
        tools:layout="@layout/fragment_real_name_party" />

    <fragment
        android:id="@+id/real_name_show"
        android:name="com.socialplay.gpark.ui.realname.RealNameShowFragment" />

    <dialog
        android:id="@+id/rechargeConfirmDialogFragment"
        android:name="com.socialplay.gpark.ui.pay.RechargeConfirmDialogFragment"
        tools:layout="@layout/view_pay_recharge_confirm_dialog" />

    <fragment
        android:id="@+id/ugcAssetList"
        android:name="com.socialplay.gpark.ui.outfit.UgcAssetListFragment"
        tools:layout="@layout/fragment_ugc_asset_list" />

    <fragment
        android:id="@+id/ugcModuleTab"
        android:name="com.socialplay.gpark.ui.editor.module.UgcModuleTabFragment"
        tools:layout="@layout/fragment_ugc_module_tab" />

    <fragment
        android:id="@+id/buyCoinsPage"
        android:name="com.socialplay.gpark.ui.pay.BuyCoinsFragment">
        <argument
            android:name="pageSource"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="gameCode"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
    </fragment>
    <fragment
        android:id="@+id/transactionDetailsPage"
        android:name="com.socialplay.gpark.ui.pay.TransactionDetailsFragment" />
    <fragment
        android:id="@+id/ugcAssetRookie"
        android:name="com.socialplay.gpark.ui.editor.module.rookie.UgcAssetRookieFragment" />
    <fragment
        android:id="@+id/heGroupsPage"
        android:name="com.socialplay.gpark.ui.im.groupchat.HeGroupsListFragment">
        <argument
            android:name="targetUid"
            android:defaultValue=""
            app:argType="string"
            app:nullable="false" />
    </fragment>
    <fragment
        android:id="@+id/myGroupsPage"
        android:name="com.socialplay.gpark.ui.im.groupchat.MyGroupsParentFragment">
        <argument
            android:name="targetUid"
            android:defaultValue=""
            app:argType="string"
            app:nullable="false" />
    </fragment>
    <fragment
        android:id="@+id/groupProfilePage"
        android:name="com.socialplay.gpark.ui.im.conversation.GroupChatProfileFragment">
        <argument
            android:name="groupId"
            android:defaultValue=""
            app:argType="string"
            app:nullable="false" />
    </fragment>
    <fragment
        android:id="@+id/groupProfileGuestPage"
        android:name="com.socialplay.gpark.ui.im.conversation.GroupChatProfileGuestFragment">
        <argument
            android:name="groupInfo"
            app:argType="com.socialplay.gpark.data.model.groupchat.GroupChatInfo"
            app:nullable="true" />
    </fragment>

    <dialog
        android:id="@+id/dialogEditGroupName"
        android:name="com.socialplay.gpark.ui.im.conversation.EditGroupChatNameDialog"
        tools:layout="@layout/dialog_edit_group_chat_name">
        <argument
            android:name="groupId"
            app:argType="long" />
        <argument
            android:name="currentGroupName"
            android:defaultValue=""
            app:argType="string"
            app:nullable="false" />
    </dialog>

    <fragment
        android:id="@+id/editGroupDescPage"
        android:name="com.socialplay.gpark.ui.im.conversation.EditGroupDescFragment">
        <argument
            android:name="groupId"
            app:argType="long" />
        <argument
            android:name="currentGroupDesc"
            android:defaultValue=""
            app:argType="string"
            app:nullable="false" />
        <argument
            android:name="enableEdit"
            android:defaultValue="false"
            app:argType="boolean" />
    </fragment>

    <dialog
        android:id="@+id/dialogEditGroupJoinInviteRule"
        android:name="com.socialplay.gpark.ui.im.conversation.EditGroupChatJoinInviteRuleDialog"
        tools:layout="@layout/dialog_edit_group_chat_join_invite_rule">
        <argument
            android:name="groupId"
            app:argType="long" />
        <argument
            android:name="currentJoinType"
            app:argType="integer" />
        <argument
            android:name="currentInviteType"
            app:argType="integer" />
    </dialog>

    <fragment
        android:id="@+id/fragmentRequestJoinGroup"
        android:name="com.socialplay.gpark.ui.im.conversation.RequestJoinGroupFragment"
        tools:layout="@layout/fragment_request_join_group_chat">
        <argument
            android:name="groupInfo"
            app:argType="com.socialplay.gpark.data.model.groupchat.GroupChatInfo"
            app:nullable="true" />
    </fragment>

    <dialog
        android:id="@+id/dialogRequestJoinGroupApproval"
        android:name="com.socialplay.gpark.ui.im.groupchat.GroupJoinRequestApprovalDialog"
        tools:layout="@layout/dialog_group_join_request_approval">
        <argument
            android:name="requestInfo"
            app:argType="com.socialplay.gpark.data.model.groupchat.GroupChatApplyInfo"
            app:nullable="true" />
    </dialog>

    <fragment
        android:id="@+id/friendsGroupsRequestPage"
        android:name="com.socialplay.gpark.ui.im.friendrequest.FriendsAndGroupsRequestParentFragment">
        <argument
            android:name="targetUid"
            android:defaultValue=""
            app:argType="string"
            app:nullable="false" />
    </fragment>
    <fragment
        android:id="@+id/groupChatMembersPage"
        android:name="com.socialplay.gpark.ui.im.groupchat.GroupChatMembersFragment">
        <argument
            android:name="groupDetail"
            app:argType="com.socialplay.gpark.data.model.groupchat.GroupChatDetailInfo"
            app:nullable="true" />
    </fragment>
    <fragment
        android:id="@+id/acc_pwd_v7_fragment"
        android:name="com.socialplay.gpark.ui.account.AccPwdV7Fragment" />
</navigation>