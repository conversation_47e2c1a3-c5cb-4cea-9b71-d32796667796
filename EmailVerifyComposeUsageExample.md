# EmailVerify Compose UI 使用指南

这是 `fragment_account_email_verify.xml` 的 Compose UI 实现版本，提供了与原有 Fragment 相同的功能和事件处理。

## 文件结构

```
EmailVerifyComposeScreen.kt          # 主要的 Compose UI 组件
EmailVerifyComposeViewModel.kt       # ViewModel，处理业务逻辑
EmailVerifyComposeActivity.kt        # Activity 示例，展示如何使用
EmailVerifyComposeUsageExample.md    # 使用指南（本文件）
```

## 主要特性

### 1. 完整的 UI 复现
- ✅ 状态栏占位符
- ✅ 标题栏（带返回按钮）
- ✅ 提示文本
- ✅ 邮箱输入框
- ✅ 验证码输入框
- ✅ 发送验证码按钮（带倒计时）
- ✅ 确认按钮
- ✅ 所有原有的样式和尺寸

### 2. 事件处理
- ✅ 返回按钮点击
- ✅ 发送验证码（带60秒倒计时）
- ✅ 确认按钮点击
- ✅ 文本输入监听
- ✅ 不同场景的处理逻辑

### 3. 状态管理
- ✅ 使用 StateFlow 管理状态
- ✅ 响应式 UI 更新
- ✅ 加载状态处理
- ✅ 错误状态处理

## 使用方法

### 1. 基本使用

```kotlin
@Composable
fun MyScreen() {
    EmailVerifyComposeScreen(
        verifyScene = EmailScene.RetrievePassword,
        onBackClick = { /* 处理返回 */ },
        onSendCodeClick = { email -> /* 发送验证码 */ },
        onConfirmClick = { email, code -> /* 确认操作 */ },
        onEmailChanged = { email -> /* 邮箱输入变化 */ },
        onVerifyCodeChanged = { code -> /* 验证码输入变化 */ },
        initialEmail = "<EMAIL>",
        isEmailEnabled = true
    )
}
```

### 2. 在 Activity 中使用

```kotlin
class MyActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            MetaAppTheme {
                EmailVerifyComposeScreen(
                    verifyScene = EmailScene.RetrievePassword,
                    onBackClick = { finish() },
                    onSendCodeClick = { email ->
                        // 调用发送验证码 API
                        viewModel.sendEmail(email, EmailScene.RetrievePassword)
                    },
                    onConfirmClick = { email, code ->
                        // 调用验证 API
                        viewModel.checkEmail(email, code, EmailScene.RetrievePassword)
                    },
                    onEmailChanged = { email ->
                        viewModel.onEmailChanged(email)
                    },
                    onVerifyCodeChanged = { code ->
                        viewModel.onVerifyCodeChanged(code)
                    }
                )
            }
        }
    }
}
```

### 3. 与 ViewModel 集成

```kotlin
@Composable
fun EmailVerifyWithViewModel(
    verifyScene: EmailScene,
    viewModel: EmailVerifyComposeViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val emailSendResult by viewModel.emailSendResult.collectAsState()
    val emailCheckResult by viewModel.emailCheckResult.collectAsState()
    
    // 处理结果
    LaunchedEffect(emailSendResult) {
        emailSendResult?.let { result ->
            when (result) {
                is EmailSendResult.Success -> {
                    // 显示成功消息
                }
                is EmailSendResult.Failed -> {
                    // 显示错误消息
                }
            }
        }
    }
    
    EmailVerifyComposeScreen(
        verifyScene = verifyScene,
        onBackClick = { /* 返回 */ },
        onSendCodeClick = { email ->
            viewModel.sendEmail(email, verifyScene)
        },
        onConfirmClick = { email, code ->
            when (verifyScene) {
                EmailScene.BindEmail -> {
                    viewModel.bindEmail(email, code, verifyScene)
                }
                else -> {
                    viewModel.checkEmail(email, code, verifyScene)
                }
            }
        },
        onEmailChanged = viewModel::onEmailChanged,
        onVerifyCodeChanged = viewModel::onVerifyCodeChanged
    )
}
```

## 支持的场景

所有原有的 EmailScene 都得到支持：

- `EmailScene.RetrievePassword` - 忘记密码
- `EmailScene.BindEmail` - 绑定邮箱
- `EmailScene.ChangeEmailOld` - 更换邮箱（验证旧邮箱）
- `EmailScene.ChangeEmailNew` - 更换邮箱（绑定新邮箱）
- `EmailScene.ChangeParentEmailNew` - 更换监护人邮箱
- `EmailScene.BindParentEmail` - 绑定监护人邮箱
- `EmailScene.DieOut` - 账号注销

每个场景都有对应的标题、提示文本和处理逻辑。

## 样式定制

### 颜色
```kotlin
object EmailVerifyColors {
    val White = Color(0xFFFFFFFF)
    val Black = Color(0xFF000000)
    val Gray666 = Color(0xFF666666)
    val GrayF0F2F4 = Color(0xFFF0F2F4)
    val YellowFFEF30 = Color(0xFFFFEF30)
    // ... 更多颜色
}
```

### 尺寸
```kotlin
object EmailVerifyDimens {
    val dp_12 = 12.dp
    val dp_16 = 16.dp
    val dp_48 = 48.dp
    val sp_14 = 14.sp
    val sp_15 = 15.sp
    val sp_18 = 18.sp
    // ... 更多尺寸
}
```

## 与原有代码的对比

| 功能 | 原有 Fragment | Compose 版本 |
|------|---------------|--------------|
| 布局定义 | XML | Composable 函数 |
| 状态管理 | LiveData | StateFlow |
| 事件处理 | 回调函数 | Lambda 参数 |
| 倒计时 | CountDownTimer | Coroutine + StateFlow |
| 样式定义 | XML styles | Kotlin 对象 |
| 预览支持 | ❌ | ✅ @Preview |

## 迁移建议

1. **渐进式迁移**：可以先在新功能中使用 Compose 版本
2. **保持兼容**：原有的 Fragment 版本可以继续使用
3. **共享 ViewModel**：可以复用现有的业务逻辑
4. **测试覆盖**：确保 Compose 版本的行为与原版一致

## 注意事项

1. **依赖项**：需要添加 Compose 相关依赖
2. **主题适配**：确保 Compose 主题与应用主题一致
3. **字体资源**：需要确保 Poppins 字体在 Compose 中可用
4. **图标资源**：确保所有图标资源可以在 Compose 中访问

## 预览支持

```kotlin
@Preview(showBackground = true)
@Composable
fun EmailVerifyPreview() {
    MetaAppTheme {
        EmailVerifyComposeScreen(
            verifyScene = EmailScene.RetrievePassword,
            onBackClick = {},
            onSendCodeClick = {},
            onConfirmClick = { _, _ -> },
            onEmailChanged = {},
            onVerifyCodeChanged = {}
        )
    }
}
```

这样可以在 Android Studio 中直接预览 UI 效果，提高开发效率。
