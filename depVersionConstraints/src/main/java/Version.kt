/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/09
 * desc   :
 * </pre>
 */


object Version {

    private const val NAME = "2.7.2"
    private const val CODE = 20602
    private val app_version_name = System.getenv("app_version_name") ?: NAME
    private val app_version_code = System.getenv("app_version_code")?.toIntOrNull() ?: CODE


    const val COMPILE_SDK = 34
    const val TARGET_SDK = 34
    const val MIN_SDK = 23


    const val KOTLIN = "2.0.0"
    const val NAVIGATION = "2.5.1"

    const val GLIDE = "4.16.0"

    val GPARK_MW_ENGINE_VERSION = System.getenv("mw_engine_version") ?: "v0.44.0.1.20250401085753"
    val PARTY_MW_ENGINE_VERSION = System.getenv("mw_engine_version") ?: "v0.43.0.0.20250211132010"

    const val GPARK_MW_ENGINE_EXT = "zip"
    const val PARTY_MW_ENGINE_EXT = "oodle_chunk"

    const val GPARK_MW_ENGINE_FILE_NAME = "M1H4XF2E"
    const val PARTY_MW_ENGINE_FILE_NAME = "M1H4XF2E"

    const val GPARK_MW_ENGINE_COMPRESS_TYPE = "M1H4XF2EC6ST2E"
    const val PARTY_MW_ENGINE_COMPRESS_TYPE = "M1H4XF2EC6ST2E"

    const val GPARK_MW_ENGINE_VERSION_FILE = "H4XV5NF2E"
    const val PARTY_MW_ENGINE_VERSION_FILE = "H4XV5NF2E"

    const val GPARK_MW_ENGINE_VERSION_CODE_FILE = "H4XV5NC2E"
    const val PARTY_MW_ENGINE_VERSION_CODE_FILE = "H4XV5NC2E"

    const val GPARK_MW_ENGINE_OBB_FILE_NAME = "M3.O2.P2"
    const val PARTY_MW_ENGINE_OBB_FILE_NAME = "main.obb.png"

    fun varyName(flavor: Flavor): String {
        return if (flavor == Flavor.PARTY) {
            if (app_version_name.split(".").size > 3) {
                // 填写的版本号本身就是对的，可以直接返回
                return app_version_name
            }
            "$app_version_name.0"
        } else {
            app_version_name
        }
    }

    fun varyCode(flavor: Flavor): Int {
        return if (flavor == Flavor.PARTY) {
            if (app_version_code.toString().length > 6) {
                // 填写的版本号本身就是对的，可以直接返回
                return app_version_code
            }
            app_version_code * 100
        } else {
            app_version_code
        }
    }
}
