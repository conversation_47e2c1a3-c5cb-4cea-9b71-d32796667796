package bundle

import org.gradle.api.Project
import com.android.build.gradle.AppExtension
import com.android.build.gradle.api.ApplicationVariant
import org.gradle.api.Task
import org.gradle.kotlin.dsl.getByType
import util.upperFirst
import java.io.File
import util.TaskUtil

class BundleTasks(private val project: Project) {

    private val rootDir: File = project.rootDir
    private val buildDir: File = project.buildDir
    private val rootBuildDir: File = project.rootProject.buildDir

    private val bundleToolPath = File(rootDir, "external/bundletool-all-1.8.0.jar")
    private val taskGroupName = "bundle"

    private val android = project.extensions.getByType<AppExtension>()

    //获取连接的设备信息
    private val getDeviceSpecTask: Task = project.task("getDeviceSpec") {
        group = taskGroupName
        val deviceSpecFile = File(rootBuildDir, "device_spec.json")
        outputs.file(deviceSpecFile)

        doLast {
            project.javaexec {
                mainClass.set( "-jar")
                args("$bundleToolPath", "get-device-spec", "--overwrite", "--output=$deviceSpecFile")
            }

            println("Device spec json save to $deviceSpecFile")
        }
    }

    fun createdTasks() = project.afterEvaluate {
        val applicationVariants = android.applicationVariants

        applicationVariants.forEach { createTaskForVariant(it, false) }
        applicationVariants.forEach { createTaskForVariant(it, true) }
    }

    private fun createTaskForVariant(variant: ApplicationVariant, forObfuscatedAab: Boolean = false) {
        val variantNameUpperFirst = variant.name.upperFirst()

        val prefix = if(forObfuscatedAab) "obfuscated_" else ""

        //从aab文件创建apks文件
        val getInputBundleFile: () -> File = {
            if(forObfuscatedAab){
                getObfuscatedBundleFile(variant)
            }else{
                getOutputBundleFile(variant)
            }
        }

        val buildApkTask = project.task("${prefix}build${variantNameUpperFirst}Apks") {
            group = taskGroupName
            dependsOn("bundle$variantNameUpperFirst")

            doLast {
                val bundleFile = getInputBundleFile()
                val apksFile = getOutputApksFile(bundleFile)
                val signingConfig = variant.signingConfig

                assert(bundleFile.exists() && bundleToolPath.exists())

                if (apksFile.exists() && apksFile.delete()) {
                    println("Delete exists apks files $apksFile")
                }

                project.javaexec {
                    mainClass.set( "-jar")
                    args(
                        "$bundleToolPath",
                        "build-apks",
                        "--connected-device",
                        "--bundle=$bundleFile",
                        "--output=$apksFile", "--ks=${signingConfig.storeFile}",
                        "--ks-pass=pass:${signingConfig.storePassword}",
                        "--ks-key-alias=${signingConfig.keyAlias}",
                        "--key-pass=pass:${signingConfig.keyPassword}"
                    )
                }
            }
        }

        //将apks文件安装到手机上
        project.task("${prefix}install${variantNameUpperFirst}Apks") {
            group = taskGroupName
            dependsOn(buildApkTask)

            doLast {
                val bundleFile = getInputBundleFile()
                val apksFile = getOutputApksFile(bundleFile)

                project.javaexec {
                    mainClass.set( "-jar")
                    args("$bundleToolPath", "install-apks", "--apks=$apksFile")
                }
            }
        }

        //将apks分离为适合当前设备的apk
        val extractApksTask = project.task("${prefix}extract${variantNameUpperFirst}Apks") {
            group = taskGroupName
            dependsOn(getDeviceSpecTask, buildApkTask)

            doLast {
                val apksFile = getOutputApksFile(getInputBundleFile())
                val extractedOutDir = getOutputExtractedDir(apksFile)
                val deviceSpecFile: File = getDeviceSpecTask.outputs.files.singleFile

                project.javaexec {
                    mainClass.set( "-jar")
                    args(
                        "$bundleToolPath",
                        "extract-apks",
                        "--device-spec=$deviceSpecFile",
                        "--apks=$apksFile",
                        "--output-dir=$extractedOutDir"
                    )
                }
            }
        }

        project.task("${prefix}install${variantNameUpperFirst}ExtractedApks") {
            group = taskGroupName
            dependsOn(extractApksTask)

            doLast {
                val extractedOutDir = getOutputExtractedDir(getOutputApksFile(getInputBundleFile()))
                project.exec {
                    executable = "adb"
                    args("install-multiple", *extractedOutDir.listFiles())
                }
            }
        }
    }

    private fun getOutputExtractedDir(apksFile: File): File {
        return File(apksFile.parentFile, "${apksFile.nameWithoutExtension}_extracted")
    }

    private fun getOutputApksFile(bundleFile: File): File {
        return File(bundleFile.parent, bundleFile.nameWithoutExtension + ".apks")
    }

    private fun getOutputBundleFile(variant: ApplicationVariant): File {
        // val outputBundleName = "${project.name}-${variant.baseName}.abb"
        val packageBundleTask = project.tasks.getByName(TaskUtil.computeTaskName(variant, "sign", "Bundle"))
        return packageBundleTask.outputs.files.singleFile
    }

    private fun getObfuscatedBundleFile(variant: ApplicationVariant): File {
        val packageBundleTask = project.tasks.getByName(TaskUtil.computeTaskName(variant, "aabresguard", ""))
        return packageBundleTask.outputs.files.singleFile
    }
}